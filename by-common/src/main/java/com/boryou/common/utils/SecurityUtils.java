package com.boryou.common.utils;

import com.boryou.common.constant.HttpStatus;
import com.boryou.common.core.domain.model.LoginUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.spring.SpringUtils;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.crypto.bcrypt.BCryptPasswordEncoder;

/**
 * 安全服务工具类
 *
 * <AUTHOR>
 */
public class SecurityUtils {

    /**
     * 解决 common 模块无法直接引用 framework 模块
     */
    public interface AuthenticationTokenProvider {
        Object getAuthenticationToken(Long userId);
    }

    /**
     * 获取用户账户
     **/
    public static String getUsername() {
        try {
            return getLoginUser().getUsername();
        } catch (Exception e) {
            throw new CustomException("获取用户账户异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取用户
     **/
    public static LoginUser getLoginUser() {
        try {
            return (LoginUser) getAuthentication().getPrincipal();
        } catch (Exception e) {
            throw new CustomException("获取用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    public static String getUserId() {
        try {
            return ((LoginUser) getAuthentication().getPrincipal()).getUser().getUserIdStr();
        } catch (Exception e) {
            throw new CustomException("获取用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    public static String getUserId(Long userId) {
        boolean isAuthentication = false;
        Authentication authentication = getAuthentication();
        if (authentication != null) {
            isAuthentication = !authentication.getName().contains("anonymousUser");
        }

        if (isAuthentication) {
            return SecurityUtils.getUserId();
        }
        if (userId == null) {
            return null;
        }
        return String.valueOf(userId);
    }

    public static Long getUserIdL() {
        try {
            return ((LoginUser) getAuthentication().getPrincipal()).getUser().getUserId();
        } catch (Exception e) {
            throw new CustomException("获取用户信息异常", HttpStatus.UNAUTHORIZED);
        }
    }

    /**
     * 获取Authentication
     */
    public static Authentication getAuthentication() {
        return SecurityContextHolder.getContext().getAuthentication();
    }

    /**
     * 设置Authentication
     */
    public static void setAuthentication(Long userId) {
        AuthenticationManager authenticationManager = SpringUtils.getBean(AuthenticationManager.class);
        AuthenticationTokenProvider provider = SpringUtils.getBean(AuthenticationTokenProvider.class);

        Object authenticationToken = provider.getAuthenticationToken(userId);
        Authentication authenticate = authenticationManager.authenticate((Authentication) authenticationToken);
        SecurityContextHolder.getContext().setAuthentication(authenticate);
    }

    /**
     * 生成BCryptPasswordEncoder密码
     *
     * @param password 密码
     * @return 加密字符串
     */
    public static String encryptPassword(String password) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.encode(password);
    }

    /**
     * 判断密码是否相同
     *
     * @param rawPassword     真实密码
     * @param encodedPassword 加密后字符
     * @return 结果
     */
    public static boolean matchesPassword(String rawPassword, String encodedPassword) {
        BCryptPasswordEncoder passwordEncoder = new BCryptPasswordEncoder();
        return passwordEncoder.matches(rawPassword, encodedPassword);
    }

    /**
     * 是否为管理员
     *
     * @param userId 用户ID
     * @return 结果
     */
    public static boolean isAdmin(Long userId) {
        return userId != null && 1L == userId;
    }

    /**
     * 获取用户id
     **/
    public static String getUserIdStr() {
        return getUserId() + "";
    }
}
