package com.boryou.common.utils;

import com.boryou.common.utils.http.HttpUtils;

/**
 * 获取雪花ID
 *
 * <AUTHOR>
 */
public class IdUtil {

    // TODO 路径修改，尾key走配置文件
    private static final String url = "http://192.168.10.53:9244/api/snowflake/get/proj-init";

    public static Long nextLong() {
        return Long.valueOf(HttpUtils.sendGet(url, ""));
    }

    public static String nextString() {
        return HttpUtils.sendGet(url, "");
    }

}
