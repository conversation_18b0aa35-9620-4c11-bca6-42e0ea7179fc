package com.boryou.common.utils;

import cn.hutool.core.lang.UUID;
import cn.hutool.core.util.IdUtil;
import com.jcraft.jsch.*;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.nio.file.Files;
import java.util.Properties;
import java.util.Vector;

/**
 * @author: hjf
 * @description: TODO
 * @date: 2023/3/28 14:57
 * @version: 1.0
 */
@Component
@Slf4j
public class FtpJSch {
    //文件前缀
    private static final String BASE_URL_PREFIX = "https://file.boryou.com/neter/";
    /*
   缺关闭连接
    */
    private static ChannelSftp sftp = null;
    private static Channel channel = null;
    private static Session sshSession = null;
    //账号
    private static String user = "boryouer";
    //主机ip
    private static String host = "**************";
    //密码
    private static String password = "k93T2ZMw7wJsyVfH";
    //端口
    private static int port = 22722;
    //上传地址
    private static String directory = "/var/www/html/neter/";
    //下载目录
    private static String saveFile = "/var/www/html/neter/";

    public static void getConnect() {
        try {
            JSch jsch = new JSch();
            //获取sshSession 账号-ip-端口
            sshSession = jsch.getSession(user, host, port);
            //添加密码
            sshSession.setPassword(password);
            Properties sshConfig = new Properties();
            //严格主机密钥检查
            sshConfig.put("StrictHostKeyChecking", "no");

            sshSession.setConfig(sshConfig);
            //开启sshSession链接
            sshSession.connect();
            //获取sftp通道
            channel = sshSession.openChannel("sftp");
            //开启
            channel.connect();
            sftp = (ChannelSftp) channel;
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private static void closeChannel(Channel channel) {
        if (channel != null) {
            if (channel.isConnected()) {
                channel.disconnect();
            }
        }
    }

    private static void closeSession(Session session) {
        if (session != null) {
            if (session.isConnected()) {
                session.disconnect();
            }
        }
    }

    public static String url2BaseUrl(String fileUrl) {
        //创建一个文件对象用来保存图片，默认保存当前工程根目录，起名叫xxx.jpg
        String path = IdUtil.getSnowflakeNextIdStr() + ".jpg";
        File imageFile = new File(path);
        try {
            URL url = new URL(fileUrl);
            //打开连接
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            //设置请求方式为"GET"
            conn.setRequestMethod("GET");
            //超时响应时间为10秒
            conn.setConnectTimeout(60 * 1000);
            //通过输入流获取图片数据
            InputStream is = conn.getInputStream();
            //得到图片的二进制数据，以二进制封装得到数据，具有通用性
            byte[] data = readInputStream(is);
            //创建输出流
            FileOutputStream outStream = new FileOutputStream(imageFile);
            //写入数据
            outStream.write(data);
            //关闭输出流，释放资源
            outStream.close();
            FtpJSch ftpJSch = new FtpJSch();
            return BASE_URL_PREFIX + ftpJSch.upload(path);
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            imageFile.delete();
        }
        return null;
    }

    public static byte[] readInputStream(InputStream inStream) throws Exception {
        ByteArrayOutputStream outStream = new ByteArrayOutputStream();
        //创建一个Buffer字符串
        byte[] buffer = new byte[6024];
        //每次读取的字符串长度，如果为-1，代表全部读取完毕
        int len;
        //使用一个输入流从buffer里把数据读取出来
        while ((len = inStream.read(buffer)) != -1) {
            //用输出流往buffer里写入数据，中间参数代表从哪个位置开始读，len代表读取的长度
            outStream.write(buffer, 0, len);
        }
        //关闭输入流
        inStream.close();
        //把outStream里的数据写入内存
        return outStream.toByteArray();
    }

    /**
     * @param uploadFile 上传文件的路径
     * @return 服务器上文件名
     */
    public String upload(String uploadFile) {
        getConnect();
        File file = null;
        String fileName = null;
        try {
            sftp.cd(directory);
            file = new File(uploadFile);
            //获取随机文件名
            fileName = UUID.randomUUID().toString() + file.getName().substring(file.getName().length() - 5);
            //文件名是 随机数加文件名的后5位
            sftp.put(Files.newInputStream(file.toPath()), fileName);
        } catch (Exception e) {
            e.printStackTrace();
        }
        closeChannel(channel);
        closeSession(sshSession);
        return file == null ? null : fileName;
    }

    /**
     * 下载文件
     */
    public void download(String downloadFileName) {
        try {
            getConnect();
            sftp.cd(directory);
            File file = new File(saveFile + File.separator + downloadFileName);
            sftp.get(downloadFileName, new FileOutputStream(file));
            closeChannel(channel);
            closeSession(sshSession);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 删除文件
     *
     * @param deleteFile 要删除的文件名
     */
    public void delete(String deleteFile) {
        try {
            sftp.cd(directory);
            sftp.rm(deleteFile);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    /**
     * 列出目录下的文件
     * <p>
     * 要列出的目录
     *
     * @return
     * @throws SftpException
     */
    public Vector listFiles()
            throws SftpException {
        getConnect();
        Vector ls = sftp.ls(directory);
        closeChannel(channel);
        closeSession(sshSession);
        return ls;
    }

}
