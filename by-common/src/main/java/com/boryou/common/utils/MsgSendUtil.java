package com.boryou.common.utils;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;


/***
 * <AUTHOR>
 * @description //账号ID获取工具
 * @date 16:10 2021/10/13
 */
public class MsgSendUtil {

    private static final String URL = "http://**************:36509/sms/send";

    public static String sendMsg(String phoneNumber, String nickName) {
        JSONObject json = JSONUtil.createObj();
        json.set("type", "GZAipGFhY3hjZ8ac9aqwa29mbXNkbTEyMzMxWPRWTS");
        json.set("phone", phoneNumber);
        json.set("p1", nickName);
        json.set("p2", "1");
        json.set("p3", "系统、APP或小程序");
        HttpResponse resJsonStr = HttpRequest.post(URL).header(Header.AUTHORIZATION, "WPXTdDAQWRadcGFhYzMyZm1zZG0xMzEyACF")
                .body(json.toString())
                .execute();
        System.out.println(resJsonStr.body());
        JSONObject object = JSONUtil.parseObj(resJsonStr.body());
        return object.get("code").toString();
    }


    public static void main(String[] args) {
        String result = sendMsg("15105584645", "张三");
        System.out.println(result);
    }
}
