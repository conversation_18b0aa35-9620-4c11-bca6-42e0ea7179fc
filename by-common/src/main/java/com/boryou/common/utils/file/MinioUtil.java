package com.boryou.common.utils.file;

import com.boryou.common.constant.Constants;
import com.boryou.common.exception.CustomException;
import com.boryou.common.exception.file.FileNameLengthLimitExceededException;
import com.boryou.common.exception.file.FileSizeLimitExceededException;
import com.boryou.common.exception.file.InvalidExtensionException;
import com.boryou.common.utils.ServletUtils;
import com.boryou.common.utils.spring.SpringUtils;
import io.minio.*;
import io.minio.errors.*;
import io.minio.http.Method;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;

import static com.boryou.common.config.MinioConfig.getBucketName;

/**
 * Minio 文件存储工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class MinioUtil {

    private MinioUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 获取文件信息, 如果抛出异常则说明文件不存在
     *
     * @param bucketName 存储桶
     * @param objectName 文件名称
     * @return
     */
    public static StatObjectResponse getFileInfo(String bucketName, String objectName) {
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        try {
            return minioClient.statObject(
                    StatObjectArgs.builder()
                            .bucket(bucketName)
                            .object(objectName)
                            .build());
        } catch (Exception e) {
            log.warn("文件不存在: {}", objectName);
            throw new RuntimeException(e);
        }
    }

    /**
     * 上传文件
     *
     * @param bucketName 桶名称
     * @throws IOException
     */
    public static String uploadMinio(String bucketName, MultipartFile multipartFile) throws IOException {
        String fileName = FileUploadUtils.extractFilename(multipartFile);
        //String url = "";
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        try (InputStream inputStream = multipartFile.getInputStream()) {
            ObjectWriteResponse objectWriteResponse = minioClient.putObject(PutObjectArgs.builder().bucket(bucketName)
                    .object(fileName).stream(inputStream, multipartFile.getSize(), -1)
                    .contentType(multipartFile.getContentType())
                    .build());
            String etag = objectWriteResponse.etag();
            //url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder().bucket(bucketName).object(fileName).method(Method.GET).build());
            //url = url.substring(0, url.indexOf('?'));
            //return URLDecoder.decode(url, Constants.UTF8);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
        return fileName;
    }

    /**
     * 以默认BucketName配置上传到Minio服务器
     *
     * @return 文件名称
     * @throws Exception
     */
    public static void uploadMinio(String originalFilename, InputStream in, Long fileLength, String contentType) throws IOException {
        if (StringUtils.isBlank(originalFilename)) {
            throw new CustomException("文件名不能为空!");
        }
        if (originalFilename.length() > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        try (InputStream inputStream = in) {
            minioClient.putObject(PutObjectArgs.builder().bucket(getBucketName())
                    .object(originalFilename).stream(inputStream, fileLength, -1)
                    .contentType(contentType)
                    .build());
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    public static String uploadMinio(MultipartFile file) throws IOException {
        try {
            return uploadMinio(getBucketName(), file, MimeTypeUtils.DEFAULT_ALLOWED_EXTENSION);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    public static String uploadMinio(String bucketName, MultipartFile file, String[] allowedExtension) throws FileSizeLimitExceededException, IOException, FileNameLengthLimitExceededException, InvalidExtensionException {
        String originalFilename = file.getOriginalFilename();
        if (StringUtils.isBlank(originalFilename)) {
            throw new CustomException("文件名不能为空!");
        }
        int fileNameLength = originalFilename.length();
        if (fileNameLength > FileUploadUtils.DEFAULT_FILE_NAME_LENGTH) {
            throw new FileNameLengthLimitExceededException(FileUploadUtils.DEFAULT_FILE_NAME_LENGTH);
        }
        FileUploadUtils.assertAllowed(file, allowedExtension);
        try {
            return MinioUtil.uploadMinio(bucketName, file);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 下载文件
     *
     * @param bucket
     * @param objectName
     * @return
     * @throws Exception
     */
    public static void download(String fileName, String originalName, HttpServletResponse response) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(originalName, Constants.UTF8));
        response.setCharacterEncoding(Constants.UTF8);
        StatObjectResponse fileSInfo = getFileInfo(getBucketName(), fileName);
        String contentType = fileSInfo.contentType();
        response.setContentType(contentType);
        InputStream in = minioClient.getObject(GetObjectArgs.builder()
                .bucket(getBucketName()).object(fileName).build());
        ServletOutputStream out = response.getOutputStream();
        IOUtils.copy(in, out);
    }

    public static InputStream downloadInputStream(String fileName) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        return minioClient.getObject(GetObjectArgs.builder()
                .bucket(getBucketName()).object(fileName).build());
    }

    /**
     * 获取某个桶下某个对象的URL
     *
     * @param bucket     桶名称
     * @param objectName 对象名 (文件夹名 + 文件名)
     * @return
     */
    public static String getUrlByFileName(String fileName) {
        try {
            MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
            String url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder().bucket(getBucketName())
                    .object(fileName).method(Method.GET).build());
            //url = url.substring(0, url.indexOf('?'));
            //return URLDecoder.decode(url, Constants.UTF8);
            return url;
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 上传文件
     *
     * @param bucketName 桶名称
     * @param fileName
     * @throws IOException
     */
    public static String uploadFile(String bucketName, String fileName, MultipartFile multipartFile) throws IOException {
        String url;
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        try (InputStream inputStream = multipartFile.getInputStream()) {
            minioClient.putObject(PutObjectArgs.builder().bucket(bucketName).object(fileName).stream(inputStream, multipartFile.getSize(), -1).contentType(multipartFile.getContentType()).build());
            url = minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder().bucket(bucketName).object(fileName).method(Method.GET).build());
            url = url.substring(0, url.indexOf('?'));
            return ServletUtils.urlDecode(url);
        } catch (Exception e) {
            throw new IOException(e.getMessage(), e);
        }
    }

    /**
     * 下载文件
     *
     * @param bucket
     * @param objectName
     * @return
     * @throws Exception
     */
    public static InputStream download(String bucket, String objectName) throws Exception {
        MinioClient minioClient = SpringUtils.getBean(MinioClient.class);
        InputStream stream = minioClient.getObject(GetObjectArgs.builder().bucket(bucket).object(objectName).build());
        return stream;
    }

}
