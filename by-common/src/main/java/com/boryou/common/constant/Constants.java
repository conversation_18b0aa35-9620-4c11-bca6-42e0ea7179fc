package com.boryou.common.constant;

/**
 * 通用常量信息
 *
 * <AUTHOR>
 */
public class Constants {


    /**
     * UTF-8 字符集
     */
    public static final String UTF8 = "UTF-8";

    //浙江高院code
    public static final Long ZJGY_AREA_CODE = 330000L;
    /**
     * GBK 字符集
     */
    public static final String GBK = "GBK";
    /**
     * http请求
     */
    public static final String HTTP = "http://";
    /**
     * https请求
     */
    public static final String HTTPS = "https://";
    /**
     * 通用成功标识
     */
    public static final String SUCCESS = "0";
    /**
     * 通用失败标识
     */
    public static final String FAIL = "1";
    /**
     * 通用 true 标识
     */
    public static final String STR_TRUE = "1";
    /**
     * 通用 false 标识
     */
    public static final String STR_FALSE = "0";
    /**
     * 登录成功
     */
    public static final String LOGIN_SUCCESS = "Success";
    /**
     * 注销
     */
    public static final String LOGOUT = "Logout";
    /**
     * 登录失败
     */
    public static final String LOGIN_FAIL = "Error";
    /**
     * 验证码有效期（分钟）
     */
    public static final Integer CAPTCHA_EXPIRATION = 2;
    /**
     * 令牌
     */
    public static final String TOKEN = "token";
    /**
     * 令牌前缀
     */
    public static final String TOKEN_PREFIX = "Bearer ";
    /**
     * 令牌前缀
     */
    public static final String LOGIN_USER_KEY = "login_user_key";
    /**
     * 用户ID
     */
    public static final String JWT_USERID = "userid";
    /**
     * 用户名称
     */
    public static final String JWT_USERNAME = "sub";
    /**
     * 用户头像
     */
    public static final String JWT_AVATAR = "avatar";
    /**
     * 创建时间
     */
    public static final String JWT_CREATED = "created";
    /**
     * 用户权限
     */
    public static final String JWT_AUTHORITIES = "authorities";
    /**
     * 资源映射路径 前缀
     */
    public static final String RESOURCE_PREFIX = "/profile";
    /**
     * 手机号 参数名称
     */
    public static final String PHONE_NUM_PARAMETER = "phone";
    /**
     * 项目Redis公共前缀
     */
    public static final String PROJECT_PREFIX = "yq-boryou:";
    /**
     * 验证码 redis key
     */
    public static final String CAPTCHA_CODE_KEY = PROJECT_PREFIX + "captcha_codes:";
    /**
     * 登录用户 redis key
     */
    public static final String LOGIN_TOKEN_KEY = PROJECT_PREFIX + "login_tokens:";
    /**
     * 防重提交 redis key
     */
    public static final String REPEAT_SUBMIT_KEY = PROJECT_PREFIX + "repeat_submit:";
    /**
     * 参数管理 cache key
     */
    public static final String SYS_CONFIG_KEY = PROJECT_PREFIX + "sys_config:";
    /**
     * 字典管理 cache key
     */
    public static final String SYS_DICT_KEY = PROJECT_PREFIX + "sys_dict:";
    /**
     * 用户习惯
     */
    public static final String USER_HABITS_KEY = PROJECT_PREFIX + "user_habits:";

    /**
     * 过期任务 redis key
     */
    public static final String EXPIRE_TASK_KEY = PROJECT_PREFIX + "expire_task";

    public static final String ENABLE = "1";
    public static final String DISABLE = "0";
    /**
     * 文件传输
     */
    public static final int FILE_WJCS = 1;
    /**
     * 普通文件
     */
    public static final int FILE_NORMAL = 2;

    /**
     * 管理者状态- 0待审核 1 待处理 2已处理3 已过期  4已完结
     */
    public static final int GL_DSH = 0;
    public static final int GL_DCL = 1;
    public static final int GL_YCL = 2;
    public static final int GL_YGQ = 3;
    public static final int GL_YWJ = 4;

    /**
     * 处置者状态-1全部 0默认无状态 1待处理 2已处理 3.已转派 4已过期 5已完结
     */
    public static final int CZ_DCL = 1;
    public static final int CZ_YCL = 2;
    public static final int CZ_YZP = 3;
    public static final int CZ_YGQ = 4;
    public static final int CZ_YWJ = 5;

    /**
     * 媒体类型
     */
    public static final String MEDIA_TYPE = "media_type";
    public static final String MEDIA_TYPE_OTHER = "0";

    /**
     * 新狗自定义key
     *
     * @date 2023/11/17 10:23
     **/
    public static final String DEFIN_EKEY = "12345678abcdefgh";

    public static final String WS_TOKEN = "sec-websocket-protocol";
    public static final String WS_URL = "/socket";

}
