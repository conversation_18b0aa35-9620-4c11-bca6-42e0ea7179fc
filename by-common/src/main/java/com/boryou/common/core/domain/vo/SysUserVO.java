package com.boryou.common.core.domain.vo;

import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.domain.entity.SysRole;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * 用户对象 sys_user
 *
 * <AUTHOR>
 */
@Data
public class SysUserVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    private Long deptId;

    private String userName;

    private String nickName;

    private String email;

    private String phonenumber;

    private String sex;

    /**
     * 用户头像
     */
    private String avatar;

    private String status;

    /**
     * 删除标志（0代表存在 2代表删除）
     */
    private String delFlag;


    private SysDept dept;


    private List<SysRole> roles;

}
