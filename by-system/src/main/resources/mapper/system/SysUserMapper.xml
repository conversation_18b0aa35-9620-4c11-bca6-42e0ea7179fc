<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.system.mapper.SysUserMapper">
    <resultMap type="com.boryou.common.core.domain.entity.SysUser" id="SysUserResult">
        <id property="userId" column="user_id"/>
        <result property="deptId" column="dept_id"/>
        <result property="userName" column="user_name"/>
        <result property="nickName" column="nick_name"/>
        <result property="email" column="email"/>
        <result property="phonenumber" column="phonenumber"/>
        <result property="sex" column="sex"/>
        <result property="avatar" column="avatar"/>
        <result property="password" column="password"/>
        <result property="status" column="status"/>
        <result property="delFlag" column="del_flag"/>
        <result property="loginIp" column="login_ip"/>
        <result property="loginDate" column="login_date"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="expireTime" column="expire_time"/>
        <result property="remark" column="remark"/>
        <result property="userGrade" column="user_grade"/>
        <result property="relaDeptId" column="rela_dept_id"/>
        <association property="dept" column="dept_id" javaType="com.boryou.common.core.domain.entity.SysDept"
                     resultMap="deptResult"/>
        <collection property="roles" javaType="java.util.List" resultMap="RoleResult"/>
    </resultMap>

    <resultMap id="deptResult" type="com.boryou.common.core.domain.entity.SysDept">
        <id property="deptId" column="dept_id"/>
        <result property="parentId" column="parent_id"/>
        <result property="deptName" column="dept_name"/>
        <result property="orderNum" column="order_num"/>
        <result property="leader" column="leader"/>
        <result property="status" column="dept_status"/>
        <result property="areaCode" column="area_code"/>
    </resultMap>

    <resultMap id="RoleResult" type="com.boryou.common.core.domain.entity.SysRole">
        <id property="roleId" column="role_id"/>
        <result property="roleName" column="role_name"/>
        <result property="roleKey" column="role_key"/>
        <result property="roleSort" column="role_sort"/>
        <result property="dataScope" column="data_scope"/>
        <result property="status" column="role_status"/>
    </resultMap>

    <sql id="selectUserVo">
        select u.user_grade, u.user_id, u.dept_id, u.user_name, u.nick_name, u.email, u.avatar, u.phonenumber,
               u.password,
               u.sex,
               u.status,
               u.del_flag,
               u.login_ip,
               u.login_date,
               u.create_by,
               u.create_time,
               u.expire_time,
               u.remark,
        d.dept_id, d.parent_id, d.dept_name, d.order_num, d.leader, d.status as dept_status,
        r.role_id, r.role_name, r.role_key, r.role_sort, r.data_scope, r.status as
        role_status,d.area_code,u.rela_dept_id
        from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
    </sql>

    <select id="selectUserList" parameterType="com.boryou.common.core.domain.entity.SysUser" resultMap="SysUserResult">
        select r.role_name, u.user_id, u.user_grade, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar,
        u.phonenumber, u.password, u.sex, u.status, u.del_flag, u.login_ip, u.login_date, u.expire_time, u.create_by,
        u.create_time,
        u.remark, d.dept_name, d.leader from sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id = ur.user_id
        left join sys_role r on r.role_id = ur.role_id
        where u.del_flag = '0'
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''">
            <!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''">
            <!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
        <!-- 数据范围过滤 -->
        ${params.dataScope}
    </select>

    <select id="selectUserByUserName" parameterType="String" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_name = #{userName}
    </select>

    <select id="selectUserById" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId}
    </select>

    <select id="selectUserByIdStr" parameterType="string" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.user_id = #{userId}
    </select>

    <select id="selectUserListByDeptId" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.dept_id = #{deptId}
    </select>

    <select id="selectAllUserIdByDeptId" parameterType="Long" resultType="java.lang.Long">
        SELECT distinct user_id FROM sys_dept d LEFT JOIN sys_user s ON s.dept_id = d.dept_id
        WHERE FIND_IN_SET(#{deptId},d.ancestors) or d.dept_id = #{deptId}
    </select>

    <select id="selectUserListByDeptIdNew" parameterType="Long" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.del_flag = '0' and u.status = '0' and u.dept_id = #{deptId}
    </select>

    <select id="checkUserNameUnique" parameterType="String" resultType="int">
        select count(1) from sys_user where user_name = #{userName} limit 1
    </select>

    <select id="checkPhoneUnique" parameterType="String" resultMap="SysUserResult">
        select user_id, phonenumber from sys_user where phonenumber = #{phonenumber} limit 1
    </select>

    <select id="checkEmailUnique" parameterType="String" resultMap="SysUserResult">
        select user_id, email from sys_user where email = #{email} limit 1
    </select>
    <select id="getUserByTelephone" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.phonenumber = #{phone}
    </select>

    <insert id="insertUser" parameterType="com.boryou.common.core.domain.entity.SysUser" useGeneratedKeys="true"
            keyProperty="userId">
        insert into sys_user(
        <if test="userId != null and userId != 0">
            user_id,
        </if>
        <if test="deptId != null and deptId != 0">
            dept_id,
        </if>
        <if test="userName != null and userName != ''">
            user_name,
        </if>
        <if test="nickName != null and nickName != ''">
            nick_name,
        </if>
        <if test="email != null and email != ''">
            email,
        </if>
        <if test="avatar != null and avatar != ''">
            avatar,
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            phonenumber,
        </if>
        <if test="sex != null and sex != ''">
            sex,
        </if>
        <if test="password != null and password != ''">
            password,
        </if>
        <if test="status != null and status != ''">
            status,
        </if>
        <if test="createBy != null and createBy != ''">
            create_by,
        </if>
        <if test="remark != null and remark != ''">
            remark,
        </if>
        <if test="userGrade != null and userGrade != ''">
            user_grade,
        </if>
        <if test="expireTime != null">
            expire_time,
        </if>
        create_time,
        rela_dept_id
        )values(
        <if test="userId != null and userId != ''">
            #{userId},
        </if>
        <if test="deptId != null and deptId != ''">
            #{deptId},
        </if>
        <if test="userName != null and userName != ''">
            #{userName},
        </if>
        <if test="nickName != null and nickName != ''">
            #{nickName},
        </if>
        <if test="email != null and email != ''">
            #{email},
        </if>
        <if test="avatar != null and avatar != ''">
            #{avatar},
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            #{phonenumber},
        </if>
        <if test="sex != null and sex != ''">
            #{sex},
        </if>
        <if test="password != null and password != ''">
            #{password},
        </if>
        <if test="status != null and status != ''">
            #{status},
        </if>
        <if test="createBy != null and createBy != ''">
            #{createBy},
        </if>
        <if test="remark != null and remark != ''">
            #{remark},
        </if>
        <if test="userGrade != null and userGrade != ''">
            #{userGrade},
        </if>
        <if test="expireTime != null">
            #{expireTime},
        </if>
        sysdate(),#{relaDeptId}
        )
    </insert>

    <update id="updateUser" parameterType="com.boryou.common.core.domain.entity.SysUser">
        update sys_user
        <set>
            <if test="deptId != null and deptId != 0">
                dept_id = #{deptId},
            </if>
            <if test="userName != null and userName != ''">
                user_name = #{userName},
            </if>
            <if test="nickName != null and nickName != ''">
                nick_name = #{nickName},
            </if>
            <if test="email != null ">
                email = #{email},
            </if>
            <if test="phonenumber != null ">
                phonenumber = #{phonenumber},
            </if>
            <if test="sex != null and sex != ''">
                sex = #{sex},
            </if>
            <if test="avatar != null and avatar != ''">
                avatar = #{avatar},
            </if>
            <if test="password != null and password != ''">
                password = #{password},
            </if>
            <if test="status != null and status != ''">
                status = #{status},
            </if>
            <if test="loginIp != null and loginIp != ''">
                login_ip = #{loginIp},
            </if>
            <if test="loginDate != null">
                login_date = #{loginDate},
            </if>
            <if test="updateBy != null and updateBy != ''">
                update_by = #{updateBy},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="userGrade != null and userGrade != ''">
                user_grade = #{userGrade},
            </if>
            <if test="expireTime != null">
                expire_time = #{expireTime},
            </if>
            rela_dept_id = #{relaDeptId},
            update_time = sysdate()
        </set>
        where user_id = #{userId}
    </update>

    <!--	<update id="updateUserStatus" parameterType="com.boryou.common.core.domain.entity.SysUser">-->
    <!-- 		update sys_user set status = #{status} where user_id = #{userId}-->
    <!--	</update>-->

    <update id="updateUserAvatar" parameterType="com.boryou.common.core.domain.entity.SysUser">
        update sys_user set avatar = #{avatar} where user_name = #{userName}
    </update>

    <update id="resetUserPwd" parameterType="com.boryou.common.core.domain.entity.SysUser">
        update sys_user set password = #{password} where user_name = #{userName}
    </update>

    <delete id="deleteUserById" parameterType="Long">
        delete from sys_user where user_id = #{userId}
    </delete>

    <delete id="deleteUserByIds" parameterType="Long">
        delete from sys_user where user_id in
        <foreach collection="array" item="userId" open="(" separator="," close=")">
            #{userId}
        </foreach>
    </delete>
    <select id="getDeptUser" resultType="com.boryou.common.core.domain.entity.SysUser">
        select user_id,dept_id from sys_user where status='0' and del_flag='0'
        and dept_id in
        <foreach collection="deptIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="selectDeptUserByDeptIds" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.status='0' and u.del_flag='0'
        and u.dept_id in
        <foreach collection="deptIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>

    <select id="fxsList" resultType="java.lang.String">
        SELECT user_id
        FROM sys_user WHERE rela_dept_id = #{deptId} and user_id in (
        SELECT user_id
        FROM sys_user_role ur
        WHERE ur.role_id = (SELECT role_id from sys_role WHERE role_name = '分析师')
        )
    </select>
    <select id="shryList" resultType="java.lang.String">
        SELECT sys_user.user_id
        FROM sys_user
        LEFT JOIN sys_user_role role on role.user_id = sys_user.user_id
        WHERE dept_id = #{deptId}
        and role_id = (SELECT role_id FROM sys_role WHERE role_name = '审核人员')
    </select>

    <select id="selectUserLists" parameterType="SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name, d.leader from
        sys_user u
        left join sys_dept d on u.dept_id = d.dept_id
        left join sys_user_role ur on u.user_id=ur.user_id
        left join sys_role r on r.role_id=ur.role_id
        where u.del_flag = '0'
        <if test="userName != null and userName != ''">
            AND u.user_name like concat('%', #{userName}, '%')
        </if>
        <if test="status != null and status != ''">
            AND u.status = #{status}
        </if>
        <if test="phonenumber != null and phonenumber != ''">
            AND u.phonenumber like concat('%', #{phonenumber}, '%')
        </if>
        <if test="params.beginTime != null and params.beginTime != ''"><!-- 开始时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
        </if>
        <if test="params.endTime != null and params.endTime != ''"><!-- 结束时间检索 -->
            AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
        </if>
        <if test="deptId != null and deptId != 0">
            AND (u.dept_id = #{deptId} OR u.dept_id IN ( SELECT t.dept_id FROM sys_dept t WHERE find_in_set(#{deptId},
            ancestors) ))
        </if>
        <if test="deptIds != null and deptIds != 0">
            AND u.dept_id = #{deptIds}
        </if>
        <if test="roleIds != null">
            and r.role_id in
            <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
    </select>

    <select id="selectUserListByRole" parameterType="SysUser" resultMap="SysUserResult">
        select u.user_id, u.dept_id, u.nick_name, u.user_name, u.email, u.avatar, u.phonenumber, u.password, u.sex,
        u.status, u.del_flag, u.login_ip, u.login_date, u.create_by, u.create_time, u.remark, d.dept_name, d.leader
        from sys_user u
        inner join sys_user_role ur on u.user_id=ur.user_id
        inner join sys_role r on r.role_id=ur.role_id
        inner join sys_dept d on d.dept_id=u.dept_id
        where u.del_flag = '0'
        <if test="roleIds != null">
            and r.role_id in
            <foreach collection="roleIds" item="roleId" open="(" separator="," close=")">
                #{roleId}
            </foreach>
        </if>
        <if test="deptIds != null and deptIds != 0">
            AND u.dept_id = #{deptIds}
        </if>
    </select>

    <select id="getUserByUserIdList" resultMap="SysUserResult">
        <include refid="selectUserVo"/>
        where u.status='0' and u.del_flag='0'
        and u.user_id in
        <foreach collection="userIds" item="item" open="(" separator="," close=")">
            #{item}
        </foreach>
    </select>


    <select id="selectRealUserIds" resultType="long">
        select u.user_id
        from sys_user u
        where u.del_flag = '0'
          and u.status = '0'
          and expire_time > now()
    </select>
</mapper>
