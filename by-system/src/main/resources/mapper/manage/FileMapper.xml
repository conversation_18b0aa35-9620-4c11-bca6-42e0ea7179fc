<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.manage.mapper.FileMapper">
    <resultMap type="File" id="FileResult">
        <result property="fileId" column="file_id"/>
        <result property="fileName" column="file_name"/>
        <result property="bucketName" column="bucket_name"/>
        <result property="original" column="original"/>
        <result property="type" column="type"/>
        <result property="fileSize" column="file_size"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>

    <sql id="selectFileVo">
        select file_id, file_name, bucket_name, original, type, file_size, del_flag, create_time, update_time,
        create_by, update_by from by_file
    </sql>

    <select id="selectFileList" parameterType="File" resultMap="FileResult">
        <include refid="selectFileVo"/>
        <where>
            <if test="fileName != null  and fileName != ''">
                and file_name like concat('%', #{fileName}, '%')
            </if>
            <if test="bucketName != null  and bucketName != ''">
                and bucket_name like concat('%', #{bucketName}, '%')
            </if>
            <if test="original != null  and original != ''">
                and original = #{original}
            </if>
            <if test="type != null  and type != ''">
                and type = #{type}
            </if>
            <if test="fileSize != null ">
                and file_size = #{fileSize}
            </if>
        </where>
    </select>

    <select id="selectFileById" parameterType="Long" resultMap="FileResult">
        <include refid="selectFileVo"/>
        where file_id = #{fileId}
    </select>

    <insert id="insertFile" parameterType="File">
        insert into by_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="fileId != null">
                file_id,
            </if>
            <if test="fileName != null">
                file_name,
            </if>
            <if test="bucketName != null">
                bucket_name,
            </if>
            <if test="original != null">
                original,
            </if>
            <if test="type != null">
                type,
            </if>
            <if test="fileSize != null">
                file_size,
            </if>
            <if test="delFlag != null">
                del_flag,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="createBy != null">
                create_by,
            </if>
            <if test="updateBy != null">
                update_by,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="fileId != null">
                #{fileId},
            </if>
            <if test="fileName != null">
                #{fileName},
            </if>
            <if test="bucketName != null">
                #{bucketName},
            </if>
            <if test="original != null">
                #{original},
            </if>
            <if test="type != null">
                #{type},
            </if>
            <if test="fileSize != null">
                #{fileSize},
            </if>
            <if test="delFlag != null">
                #{delFlag},
            </if>
            <if test="createTime != null">
                #{createTime},
            </if>
            <if test="updateTime != null">
                #{updateTime},
            </if>
            <if test="createBy != null">
                #{createBy},
            </if>
            <if test="updateBy != null">
                #{updateBy},
            </if>
        </trim>
    </insert>

    <update id="updateFile" parameterType="File">
        update by_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileName != null">
                file_name = #{fileName},
            </if>
            <if test="bucketName != null">
                bucket_name = #{bucketName},
            </if>
            <if test="original != null">
                original = #{original},
            </if>
            <if test="type != null">
                type = #{type},
            </if>
            <if test="fileSize != null">
                file_size = #{fileSize},
            </if>
            <if test="delFlag != null">
                del_flag = #{delFlag},
            </if>
            <if test="createTime != null">
                create_time = #{createTime},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime},
            </if>
            <if test="createBy != null">
                create_by = #{createBy},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy},
            </if>
        </trim>
        where file_id = #{fileId}
    </update>

    <delete id="deleteFileById" parameterType="Long">
        delete from by_file where file_id = #{fileId}
    </delete>

    <delete id="deleteFileByIds" parameterType="String">
        delete from by_file where file_id in
        <foreach item="fileId" collection="array" open="(" separator="," close=")">
            #{fileId}
        </foreach>
    </delete>
    <insert id="insertFileList">
        INSERT INTO by_file
        (file_id, file_name, bucket_name, original, type, file_size, del_flag, create_time, update_time,
        create_by, update_by)
        VALUES
        <foreach collection="infoList" item="info" separator="," index="index">
            (#{info.fileId}, #{info.fileName}, #{info.bucketName}, #{info.original}, #{info.type}, #{info.fileSize},
            #{info.delFlag}
            , #{info.createTime}, #{info.updateTime}, #{info.createBy}, #{info.updateBy})
        </foreach>
    </insert>

    <select id="selectFileByFileName" parameterType="string" resultMap="FileResult">
        <include refid="selectFileVo"/>
        where file_name = #{fileName}
    </select>
</mapper>