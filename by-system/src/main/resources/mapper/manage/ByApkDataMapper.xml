<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.manage.mapper.ByApkDataMapper">
    <resultMap type="com.boryou.manage.domain.ByApkData" id="ByApkDataResult">
        <result property="id" column="id"/>
        <result property="versionName" column="version_name"/>
        <result property="versionNum" column="version_num"/>
        <result property="versionDescribe" column="version_describe"/>
        <result property="versionFileId" column="version_file_id"/>
        <result property="ctime" column="ctime"/>
    </resultMap>

    <sql id="selectByApkDataVo">
        select id, version_name, version_num, version_describe, version_file_id, ctime from by_apk_data
    </sql>

    <select id="selectByApkDataList" parameterType="com.boryou.manage.domain.ByApkData" resultMap="ByApkDataResult">
        <include refid="selectByApkDataVo"/>
        <where>
            <if test="versionName != null  and versionName != ''">
                and version_name like concat('%', #{versionName}, '%')
            </if>
            <if test="versionNum != null  and versionNum != ''">
                and version_num = #{versionNum}
            </if>
            <if test="versionDescribe != null  and versionDescribe != ''">
                and version_describe = #{versionDescribe}
            </if>
            <if test="versionFileId != null  and versionFileId != ''">
                and version_file_id = #{versionFileId}
            </if>
            <if test="ctime != null ">
                and ctime = #{ctime}
            </if>
        </where>
        order by ctime desc
    </select>

    <select id="selectByApkDataById" parameterType="Long" resultMap="ByApkDataResult">
        <include refid="selectByApkDataVo"/>
        where id = #{id}
    </select>

    <insert id="insertByApkData" parameterType="com.boryou.manage.domain.ByApkData">
        insert into by_apk_data
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="versionName != null">
                version_name,
            </if>
            <if test="versionNum != null">
                version_num,
            </if>
            <if test="versionDescribe != null">
                version_describe,
            </if>
            <if test="versionFileId != null">
                version_file_id,
            </if>
            <if test="ctime != null">
                ctime,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="versionName != null">
                #{versionName},
            </if>
            <if test="versionNum != null">
                #{versionNum},
            </if>
            <if test="versionDescribe != null">
                #{versionDescribe},
            </if>
            <if test="versionFileId != null">
                #{versionFileId},
            </if>
            <if test="ctime != null">
                #{ctime},
            </if>
        </trim>
    </insert>

    <update id="updateByApkData" parameterType="com.boryou.manage.domain.ByApkData">
        update by_apk_data
        <trim prefix="SET" suffixOverrides=",">
            <if test="versionName != null">
                version_name = #{versionName},
            </if>
            <if test="versionNum != null">
                version_num = #{versionNum},
            </if>
            <if test="versionDescribe != null">
                version_describe = #{versionDescribe},
            </if>
            <if test="versionFileId != null">
                version_file_id = #{versionFileId},
            </if>
            <if test="ctime != null">
                ctime = #{ctime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteByApkDataById" parameterType="Long">
        delete from by_apk_data where id = #{id}
    </delete>

    <delete id="deleteByApkDataByIds" parameterType="String">
        delete from by_apk_data where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
