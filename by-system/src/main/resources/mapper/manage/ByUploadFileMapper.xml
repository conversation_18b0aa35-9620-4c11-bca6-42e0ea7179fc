<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.manage.mapper.ByUploadFileMapper">
    <resultMap type="com.boryou.manage.domain.ByUploadFile" id="ByUploadFileResult">
        <result property="id" column="id"/>
        <result property="address" column="address"/>
        <result property="fileName" column="file_name"/>
        <result property="fileType" column="file_type"/>
        <result property="fileSize" column="file_size"/>
        <result property="deleteStatus" column="delete_status"/>
        <result property="uploadTime" column="upload_time"/>
    </resultMap>

    <sql id="selectByUploadFileVo">
        select id, address, file_name, file_type, file_size, delete_status, upload_time
        from by_upload_file
    </sql>

    <select id="selectByUploadFileList" parameterType="com.boryou.manage.domain.ByUploadFile"
            resultMap="ByUploadFileResult">
        <include refid="selectByUploadFileVo"/>
        <where>
            <if test="address != null  and address != ''">
                and address = #{address}
            </if>
            <if test="fileName != null  and fileName != ''">
                and file_name like concat('%', #{fileName}, '%')
            </if>
            <if test="fileType != null  and fileType != ''">
                and file_type = #{fileType}
            </if>
            <if test="fileSize != null ">
                and file_size = #{fileSize}
            </if>
            <if test="deleteStatus != null ">
                and delete_status = #{deleteStatus}
            </if>
            <if test="uploadTime != null ">
                and upload_time = #{uploadTime}
            </if>
        </where>
    </select>

    <select id="selectByUploadFileById" parameterType="Long" resultMap="ByUploadFileResult">
        <include refid="selectByUploadFileVo"/>
        where id = #{id}
    </select>


    <select id="selectByUploadFileByIds" parameterType="String" resultMap="ByUploadFileResult">
        <include refid="selectByUploadFileVo"/>
        where find_in_set(id,#{id})
    </select>

    <insert id="insertByUploadFile" parameterType="com.boryou.manage.domain.ByUploadFile">
        insert into by_upload_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="address != null and address != ''">
                address,
            </if>
            <if test="fileName != null and fileName != ''">
                file_name,
            </if>
            <if test="fileType != null and fileType != ''">
                file_type,
            </if>
            <if test="fileSize != null">
                file_size,
            </if>
            <if test="deleteStatus != null">
                delete_status,
            </if>
            <if test="uploadTime != null">
                upload_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="address != null and address != ''">
                #{address},
            </if>
            <if test="fileName != null and fileName != ''">
                #{fileName},
            </if>
            <if test="fileType != null and fileType != ''">
                #{fileType},
            </if>
            <if test="fileSize != null">
                #{fileSize},
            </if>
            <if test="deleteStatus != null">
                #{deleteStatus},
            </if>
            <if test="uploadTime != null">
                #{uploadTime},
            </if>
        </trim>
    </insert>

    <update id="updateByUploadFile" parameterType="com.boryou.manage.domain.ByUploadFile">
        update by_upload_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="address != null and address != ''">
                address = #{address},
            </if>
            <if test="fileName != null and fileName != ''">
                file_name = #{fileName},
            </if>
            <if test="fileType != null and fileType != ''">
                file_type = #{fileType},
            </if>
            <if test="fileSize != null">
                file_size = #{fileSize},
            </if>
            <if test="deleteStatus != null">
                delete_status = #{deleteStatus},
            </if>
            <if test="uploadTime != null">
                upload_time = #{uploadTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteByUploadFileById" parameterType="Long">
        delete
        from by_upload_file
        where id = #{id}
    </delete>

    <delete id="deleteByUploadFileByIds" parameterType="String">
        delete from by_upload_file where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
