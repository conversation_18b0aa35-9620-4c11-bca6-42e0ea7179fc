<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.manage.mapper.ByTaskToUserMapper">
    <resultMap type="com.boryou.manage.domain.ByTaskToUser" id="ByTaskToUserResult">
        <result property="id" column="id"/>
        <result property="taskId" column="task_id"/>
        <result property="userTaskStatus" column="user_task_status"/>
        <result property="opUserId" column="op_user_id"/>
        <result property="opUserDeptId" column="op_user_dept_id"/>
        <result property="taskGrade" column="task_grade"/>
        <result property="finishTime" column="finish_time"/>
        <result property="fileId" column="file_id"/>
        <result property="picId" column="pic_id"/>
        <result property="remark" column="remark"/>
        <result property="ctime" column="ctime"/>
        <result property="createUserId" column="create_user_id"/>
    </resultMap>

    <sql id="selectByTaskToUserVo">
        select id, task_id, user_task_status, op_user_id, op_user_dept_id, task_grade, finish_time, file_id, pic_id,
        remark, ctime, create_user_id from by_task_to_user
    </sql>

    <select id="selectByTaskToUserList" parameterType="com.boryou.manage.domain.ByTaskToUser"
            resultMap="ByTaskToUserResult">
        select t.id, t.task_id, t.user_task_status, t.op_user_id, op_user_dept_id, t.task_grade, t.finish_time,
        t.file_id, t.pic_id,
        t.remark, t.ctime, t.create_user_id, u.nick_name createUserName from by_task_to_user t left join sys_user u on
        t.create_user_id = u.user_id
        <where>
            <if test="taskId != null ">
                and t.task_id = #{taskId}
            </if>
            <if test="userTaskStatus != null ">
                and t.user_task_status = #{userTaskStatus}
            </if>
            <if test="opUserId != null ">
                and t.op_user_id = #{opUserId}
            </if>
            <if test="opUserDeptId != null ">
                and t.op_user_dept_id = #{opUserDeptId}
            </if>
            <if test="taskGrade != null ">
                and t.task_grade = #{taskGrade}
            </if>
            <if test="finishTime != null ">
                and t.finish_time = #{finishTime}
            </if>
            <if test="fileId != null ">
                and t.file_id = #{fileId}
            </if>
            <if test="picId != null ">
                and t.pic_id = #{picId}
            </if>
            <if test="ctime != null ">
                and t.ctime = #{ctime}
            </if>
            <if test="createUserId != null ">
                and t.create_user_id = #{createUserId}
            </if>
            <if test="selectStartTime != null ">
                and t.finish_time &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null ">
                and t.finish_time &lt;= #{selectEndTime}
            </if>
        </where>
        order by ctime desc
    </select>

    <select id="selectByTaskToUserById" parameterType="Long" resultMap="ByTaskToUserResult">
        <include refid="selectByTaskToUserVo"/>
        where id = #{id}
    </select>

    <select id="selectUserTaskList" resultType="com.boryou.manage.domain.ByTaskToUser">
        select t.id
        , t.id as taskToUserId
        , n.task_num taskNum
        , n.title title
        , n.task_introduce taskIntroduce
        , n.original_link originalLink
        , n.task_type taskType
        , n.sub_task_type subTaskType
        , n.notice_type noticeType
        , n.media_type mediaType
        , n.accomplish_time accomplishTime
        , n.task_status taskStatus
        , n.ctime ctime
        , n.score_mode scoreMode
        , n.create_user_id createUserId
        , u.nick_name createUserName
        , t.finish_time finishTime
        , n.id taskId
        , t.user_task_status userTaskStatus
        , t.op_user_id opUserId
        , t.op_user_dept_id opUserDeptId
        , t.task_grade taskGrade
        , t.file_id fileId
        , t.pic_id picId
        , t.remark remark
        , n.score_type as scoreType
        from by_network_review_task n
        left join by_task_to_user t on n.id = t.task_id
        left join sys_user u on n.create_user_id = u.user_id
        <where>
            <if test="taskId != null  and taskId != ''">
                and t.task_id = #{taskId}
            </if>
            <if test="userTaskStatus != null ">
                and t.user_task_status = #{userTaskStatus}
            </if>
            <if test="taskNum != null  and taskNum != '' and (title == null or title == '')">
                and n.task_num like CONCAT("%",CONCAT(#{taskNum},"%"))
            </if>
            <if test="title != null and title != '' and (taskNum == null or taskNum == '') ">
                and n.title like CONCAT("%",CONCAT(#{title},"%"))
            </if>
            <if test="title != null and title != '' and taskNum != null and taskNum != '' ">
                and (n.title like CONCAT("%",CONCAT(#{title},"%")) or n.task_num like
                CONCAT("%",CONCAT(#{taskNum},"%")))
            </if>
            <if test="taskIntroduce != null  and taskIntroduce != ''">
                and n.task_introduce = #{taskIntroduce}
            </if>
            <if test="originalLink != null  and originalLink != ''">
                and n.original_link = #{originalLink}
            </if>
            <if test="taskType != null ">
                and n.task_type = #{taskType}
            </if>
            <if test="noticeType != null ">
                and FIND_IN_SET(#{noticeType},n.notice_type)
            </if>
            <if test="mediaType != null ">
                and n.media_type = #{mediaType}
            </if>
            <if test="accomplishTime != null ">
                and n.accomplish_time = #{accomplishTime}
            </if>
            <if test="taskStatus != null ">
                and n.task_status = #{taskStatus}
            </if>
            <if test="ctime != null ">
                and n.ctime = #{ctime}
            </if>
            <if test="createUserId != null ">
                and n.create_user_id = #{createUserId}
            </if>
            <if test="opUserId != null and opUserId != ''">
                and t.op_user_id = #{opUserId}
            </if>
            <if test="selectStartTime != null and selectStartTime != ''">
                and n.accomplish_time &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null and selectEndTime != ''">
                and n.accomplish_time &lt;= #{selectEndTime}
            </if>
            <if test="scoreType != null and scoreType != ''">
                and n.score_type = #{scoreType}
            </if>
            <if test="scoreMode != null and scoreMode != ''">
                and n.score_mode = #{scoreMode}
            </if>
        </where>
        order by
        <if test="sortWay != null  and sortWay != ''">
            t.finish_time ${sortWay} ,
        </if>
        <if test="taskNumSort != null  and taskNumSort != ''">
            n.task_num ${taskNumSort} ,
        </if>
        n.ctime desc
    </select>

    <select id="selectUserTaskById" resultType="com.boryou.manage.domain.ByTaskToUser">
        select t.id
        , n.task_num taskNum
        , n.title title
        , n.task_introduce taskIntroduce
        , n.original_link originalLink
        , n.task_type taskType
        , n.sub_task_type subTaskType
        , n.notice_type noticeType
        , n.media_type mediaType
        , n.accomplish_time accomplishTime
        , n.task_status taskStatus
        , n.ctime ctime
        , n.score_mode scoreMode
        , n.create_user_id createUserId
        , u.nick_name createUserName
        , t.finish_time finishTime
        , n.id taskId
        , t.user_task_status userTaskStatus
        , t.op_user_id opUserId
        , t.op_user_dept_id opUserDeptId
        , t.task_grade taskGrade
        , t.file_id fileId
        , t.pic_id picId
        , t.remark remark
        from by_network_review_task n
        left join by_task_to_user t on n.id = t.task_id
        left join sys_user u on n.create_user_id = u.user_id
        where t.id = #{id}
    </select>

    <insert id="insertByTaskToUser" parameterType="com.boryou.manage.domain.ByTaskToUser">
        insert into by_task_to_user
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskId != null">
                task_id,
            </if>
            <if test="userTaskStatus != null">
                user_task_status,
            </if>
            <if test="opUserId != null">
                op_user_id,
            </if>
            <if test="opUserDeptId != null">
                op_user_dept_id,
            </if>
            <if test="taskGrade != null">
                task_grade,
            </if>
            <if test="finishTime != null">
                finish_time,
            </if>
            <if test="fileId != null">
                file_id,
            </if>
            <if test="picId != null">
                pic_id,
            </if>
            <if test="remark != null">
                remark,
            </if>
            <if test="ctime != null">
                ctime,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="taskId != null">
                #{taskId},
            </if>
            <if test="userTaskStatus != null">
                #{userTaskStatus},
            </if>
            <if test="opUserId != null">
                #{opUserId},
            </if>
            <if test="opUserDeptId != null">
                #{opUserDeptId},
            </if>
            <if test="taskGrade != null">
                #{taskGrade},
            </if>
            <if test="finishTime != null">
                #{finishTime},
            </if>
            <if test="fileId != null">
                #{fileId},
            </if>
            <if test="picId != null">
                #{picId},
            </if>
            <if test="remark != null">
                #{remark},
            </if>
            <if test="ctime != null">
                #{ctime},
            </if>
            <if test="createUserId != null">
                #{createUserId},
            </if>
        </trim>
    </insert>

    <update id="updateByTaskToUser" parameterType="com.boryou.manage.domain.ByTaskToUser">
        update by_task_to_user
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskId != null">
                task_id = #{taskId},
            </if>
            <if test="userTaskStatus != null">
                user_task_status = #{userTaskStatus},
            </if>
            <if test="opUserId != null">
                op_user_id = #{opUserId},
            </if>
            <if test="taskGrade != null">
                task_grade = #{taskGrade},
            </if>
            <if test="finishTime != null">
                finish_time = #{finishTime},
            </if>
            <if test="fileId != null">
                file_id = #{fileId},
            </if>
            <if test="picId != null">
                pic_id = #{picId},
            </if>
            <if test="remark != null">
                remark = #{remark},
            </if>
            <if test="ctime != null">
                ctime = #{ctime},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="cancelByTaskToUser">
        update by_task_to_user set user_task_status = #{userTaskStatus},task_grade = 0
        where task_id = #{taskId}
    </update>

    <update id="exceedTimeByTaskToUser">
        update by_task_to_user set user_task_status = #{userTaskStatus}
        where task_id = #{taskId} and user_task_status != 2
    </update>

    <delete id="deleteByTaskToUserById" parameterType="Long">
        delete from by_task_to_user where id = #{id}
    </delete>

    <delete id="deleteByTaskToUserByIds" parameterType="String">
        delete from by_task_to_user where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
