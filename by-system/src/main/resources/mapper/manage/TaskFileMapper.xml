<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.manage.mapper.TaskFileMapper">
    <resultMap type="TaskFile" id="TaskFileResult">
        <result property="taskId" column="task_id"/>
        <result property="fileId" column="file_id"/>
    </resultMap>
    <resultMap type="File" id="FileResult">
        <result property="fileId" column="file_id"/>
        <result property="fileName" column="file_name"/>
        <result property="bucketName" column="bucket_name"/>
        <result property="original" column="original"/>
        <result property="type" column="type"/>
        <result property="fileSize" column="file_size"/>
        <result property="delFlag" column="del_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="updateTime" column="update_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateBy" column="update_by"/>
    </resultMap>
    <sql id="selectTaskFileVo">
        select task_id, file_id from by_task_file
    </sql>

    <select id="selectTaskFileList" parameterType="TaskFile" resultMap="TaskFileResult">
        <include refid="selectTaskFileVo"/>
    </select>

    <select id="selectTaskFileById" resultMap="FileResult">
        SELECT f.file_id ,file_name,
        bucket_name,
        original,
        type,
        file_size,
        del_flag
        FROM
        by_file f LEFT JOIN by_task_file tf ON f.file_id = tf.file_id
        WHERE
        task_id = #{taskId} AND del_flag = 0
    </select>

    <insert id="insertTaskFile" parameterType="TaskFile">
        insert into by_task_file
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                task_id,
            </if>
            <if test="fileId != null">
                file_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="taskId != null">
                #{taskId},
            </if>
            <if test="fileId != null">
                #{fileId},
            </if>
        </trim>
    </insert>

    <update id="updateTaskFile" parameterType="TaskFile">
        update by_task_file
        <trim prefix="SET" suffixOverrides=",">
            <if test="fileId != null">
                file_id = #{fileId},
            </if>
        </trim>
        where task_id = #{taskId}
    </update>

    <delete id="deleteTaskFileById" parameterType="Long">
        delete from by_task_file where task_id = #{taskId}
    </delete>

    <delete id="deleteTaskFileByIds" parameterType="String">
        delete from by_task_file where task_id in
        <foreach item="taskId" collection="array" open="(" separator="," close=")">
            #{taskId}
        </foreach>
    </delete>

    <insert id="insertTaskFileList">
        INSERT INTO by_task_file(task_id, file_id) VALUES
        <foreach collection="infoList" item="info" separator="," index="index">
            (#{info.taskId}, #{info.fileId})
        </foreach>
    </insert>
</mapper>