<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.manage.mapper.ByNetworkReviewTaskMapper">
    <resultMap type="com.boryou.manage.domain.ByNetworkReviewTask" id="ByNetworkReviewTaskResult">
        <result property="id" column="id"/>
        <result property="taskNum" column="task_num"/>
        <result property="title" column="title"/>
        <result property="taskIntroduce" column="task_introduce"/>
        <result property="originalLink" column="original_link"/>
        <result property="taskType" column="task_type"/>
        <result property="subTaskType" column="sub_task_type"/>
        <result property="noticeType" column="notice_type"/>
        <result property="mediaType" column="media_type"/>
        <result property="accomplishTime" column="accomplish_time"/>
        <result property="actualCompletionTime" column="actual_completion_time"/>
        <result property="taskStatus" column="task_status"/>
        <result property="ctime" column="ctime"/>
        <result property="createUserId" column="create_user_id"/>
        <result property="orgUserArr" column="org_user_arr"/>
        <result property="scoreMode" column="score_mode"/>
        <result property="scoreType" column="score_type"/>
        <result property="scoreGrade" column="score_grade"/>
        <result property="completeDegree" column="complete_degree"/>
    </resultMap>

    <sql id="selectByNetworkReviewTaskVo">
        select score_type, score_grade, complete_degree, score_mode, id, task_num, title, task_introduce, original_link,
        task_type, sub_task_type, notice_type, media_type, accomplish_time,
        actual_completion_time, task_status, ctime, create_user_id, org_user_arr from by_network_review_task
    </sql>

    <select id="selectByNetworkReviewTaskList" parameterType="com.boryou.manage.domain.ByNetworkReviewTask"
            resultMap="ByNetworkReviewTaskResult">
        <include refid="selectByNetworkReviewTaskVo"/>
        <where>
            <if test="taskNum != null  and taskNum != ''">
                and task_num like concat('%', #{taskNum}, '%')
            </if>
            <if test="title != null  and title != ''">
                and title like concat('%', #{title}, '%')
            </if>
            <if test="taskIntroduce != null  and taskIntroduce != ''">
                and task_introduce = #{taskIntroduce}
            </if>
            <if test="originalLink != null  and originalLink != ''">
                and original_link = #{originalLink}
            </if>
            <if test="taskType != null ">
                and task_type = #{taskType}
            </if>
            <if test="noticeType != null  and noticeType != ''">
                and notice_type = #{noticeType}
            </if>
            <if test="mediaType != null ">
                and media_type = #{mediaType}
            </if>
            <if test="scoreMode != null ">
                and score_mode = #{scoreMode}
            </if>
            <if test="accomplishTime != null ">
                and accomplish_time &lt; #{accomplishTime}
            </if>
            <if test="selectStartTime != null and selectStartTime != ''">
                and accomplish_time > #{selectStartTime}
            </if>
            <if test="selectEndTime != null and selectEndTime != ''">
                and accomplish_time &lt; #{selectEndTime}
            </if>
            <if test="taskStatus != null ">
                and task_status = #{taskStatus}
            </if>
            <if test="ctime != null ">
                and ctime = #{ctime}
            </if>
            <if test="createUserId != null ">
                and create_user_id = #{createUserId}
            </if>
            <if test="deptUserIdList != null and deptUserIdList != ''">
                and create_user_id in
                <foreach item="id" collection="deptUserIdList" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>
        </where>
        order by
        <if test="sortWay != null  and sortWay != ''">
            accomplish_time ${sortWay} ,
        </if>
        <if test="taskNumSort != null  and taskNumSort != ''">
            task_num ${taskNumSort} ,
        </if>
        ctime desc
    </select>

    <select id="selectByNetworkReviewTaskById" parameterType="Long" resultMap="ByNetworkReviewTaskResult">
        <include refid="selectByNetworkReviewTaskVo"/>
        where id = #{id}
    </select>

    <insert id="insertByNetworkReviewTask" parameterType="com.boryou.manage.domain.ByNetworkReviewTask">
        insert into by_network_review_task
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">
                id,
            </if>
            <if test="taskNum != null">
                task_num,
            </if>
            <if test="title != null">
                title,
            </if>
            <if test="taskIntroduce != null">
                task_introduce,
            </if>
            <if test="originalLink != null">
                original_link,
            </if>
            <if test="taskType != null">
                task_type,
            </if>
            <if test="subTaskType != null">
                sub_task_type,
            </if>
            <if test="noticeType != null">
                notice_type,
            </if>
            <if test="mediaType != null">
                media_type,
            </if>
            <if test="accomplishTime != null">
                accomplish_time,
            </if>
            <if test="taskStatus != null">
                task_status,
            </if>
            <if test="ctime != null">
                ctime,
            </if>
            <if test="createUserId != null">
                create_user_id,
            </if>
            <if test="orgUserArr != null">
                org_user_arr,
            </if>
            <if test="scoreMode != null">
                score_mode,
            </if>
            <if test="scoreType != null">
                score_type,
            </if>
            <if test="scoreGrade != null">
                score_grade,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">
                #{id},
            </if>
            <if test="taskNum != null">
                #{taskNum},
            </if>
            <if test="title != null">
                #{title},
            </if>
            <if test="taskIntroduce != null">
                #{taskIntroduce},
            </if>
            <if test="originalLink != null">
                #{originalLink},
            </if>
            <if test="taskType != null">
                #{taskType},
            </if>
            <if test="subTaskType != null">
                #{subTaskType},
            </if>
            <if test="noticeType != null">
                #{noticeType},
            </if>
            <if test="mediaType != null">
                #{mediaType},
            </if>
            <if test="accomplishTime != null">
                #{accomplishTime},
            </if>
            <if test="taskStatus != null">
                #{taskStatus},
            </if>
            <if test="ctime != null">
                #{ctime},
            </if>
            <if test="createUserId != null">
                #{createUserId},
            </if>
            <if test="orgUserArr != null">
                #{orgUserArr},
            </if>
            <if test="scoreMode != null">
                #{scoreMode},
            </if>
            <if test="scoreType != null">
                #{scoreType},
            </if>
            <if test="scoreGrade != null">
                #{scoreGrade},
            </if>
        </trim>
    </insert>

    <update id="updateByNetworkReviewTask" parameterType="com.boryou.manage.domain.ByNetworkReviewTask">
        update by_network_review_task
        <trim prefix="SET" suffixOverrides=",">
            <if test="taskNum != null">
                task_num = #{taskNum},
            </if>
            <if test="title != null">
                title = #{title},
            </if>
            <if test="taskIntroduce != null">
                task_introduce = #{taskIntroduce},
            </if>
            <if test="originalLink != null">
                original_link = #{originalLink},
            </if>
            <if test="taskType != null">
                task_type = #{taskType},
            </if>
            <if test="noticeType != null">
                notice_type = #{noticeType},
            </if>
            <if test="mediaType != null">
                media_type = #{mediaType},
            </if>
            <if test="accomplishTime != null">
                accomplish_time = #{accomplishTime},
            </if>
            <if test="actualCompletionTime != null">
                actual_completion_time = #{actualCompletionTime},
            </if>
            <if test="taskStatus != null">
                task_status = #{taskStatus},
            </if>
            <if test="ctime != null">
                ctime = #{ctime},
            </if>
            <if test="createUserId != null">
                create_user_id = #{createUserId},
            </if>
            <if test="orgUserArr != null">
                org_user_arr = #{orgUserArr},
            </if>
            <if test="scoreType != null">
                score_type = #{scoreType},
            </if>
            <if test="scoreGrade != null">
                score_grade = #{scoreGrade},
            </if>
            <if test="completeDegree != null">
                complete_degree = #{completeDegree},
            </if>
        </trim>
        where id = #{id}
    </update>

    <update id="updateActualCompletionTime" parameterType="Long">
        update by_network_review_task set actual_completion_time = now() , task_status = 3
        where id = #{id}
    </update>

    <update id="updateCompleteDegree" parameterType="com.boryou.manage.domain.ByNetworkReviewTask">
        update by_network_review_task set complete_degree = #{completeDegree}
        where id = #{id}
    </update>

    <delete id="deleteByNetworkReviewTaskById" parameterType="Long">
        delete from by_network_review_task where id = #{id}
    </delete>

    <delete id="deleteByNetworkReviewTaskByIds" parameterType="String">
        delete from by_network_review_task where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>

    <select id="selectByNetworkReviewTaskNum" parameterType="com.boryou.manage.domain.ByNetworkReviewTask"
            resultType="int">
        select count(1) from by_network_review_task
        <where>
            <if test="taskType != null ">
                and task_type = #{taskType}
            </if>
            <if test="mediaType != null ">
                and media_type = #{mediaType}
            </if>
            <if test="taskStatus != null ">
                and task_status = #{taskStatus}
            </if>
        </where>
    </select>
</mapper>
