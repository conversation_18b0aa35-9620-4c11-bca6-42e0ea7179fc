<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.stats.mapper.StatisticalDataMapper">
    <resultMap type="com.boryou.stats.domain.StatisticalDataDto" id="StatisticalDataResult">
        <result property="type" column="type"/>
        <result property="num" column="num"/>
        <result property="taskId" column="taskId"/>
        <result property="taskName" column="taskName"/>
    </resultMap>

    <resultMap type="com.boryou.stats.domain.AchievementsDataDto" id="AchievementsDataDtoResult">
        <result property="userName" column="userName"/>
        <result property="deptName" column="deptName"/>
        <result property="achievementsResult" column="achievementsResult"/>
        <result property="queryPersonName" column="queryPersonName"/>
        <result property="deptIds" column="deptIds"/>
        <result property="selectStartTime" column="selectStartTime"/>
        <result property="selectEndTime" column="selectEndTime"/>
    </resultMap>


    <select id="selectByNetworkReviewTaskNum" resultMap="StatisticalDataResult">
        select task_status as type, count(1) as num
        from by_network_review_task
        <if test="deptUserIdArr != null and deptUserIdArr != ''">
            where create_user_id in
            <foreach item="id" collection="deptUserIdArr" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY task_status
        ORDER BY count(1) desc
    </select>

    <select id="selectByNetworkReviewTaskMediaNum" resultMap="StatisticalDataResult">
        select media_type as type, count(1) as num
        from by_network_review_task
        <if test="deptUserIdArr != null and deptUserIdArr != ''">
            where create_user_id in
            <foreach item="id" collection="deptUserIdArr" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY media_type
        ORDER BY count(1) desc , media_type desc
    </select>

    <select id="selectPersonTaskNum" parameterType="com.boryou.stats.domain.StatisticalDataDto"
            resultMap="StatisticalDataResult">
        select op_user_id as type, count(1) as num
        from by_task_to_user
        where user_task_status = 2
        and finish_time &lt; #{selectEndTime}
        and finish_time > #{selectStartTime}
        <if test="deptUserIdList != null and deptUserIdList != ''">
            and op_user_id in
            <foreach item="id" collection="deptUserIdList" open="(" separator="," close=")">
                #{id}
            </foreach>
        </if>
        GROUP BY op_user_id
        ORDER BY count(1) desc
    </select>

    <select id="getManageAchievementsData" parameterType="com.boryou.stats.domain.AchievementsDataDto"
            resultMap="AchievementsDataDtoResult">
        select su.nick_name as userName , sd.dept_name as deptName , IFNULL(sum(bttu.task_grade),0) as
        achievementsResult
        from by_task_to_user bttu left join sys_user su on bttu.op_user_id = su.user_id
        left join sys_dept sd on su.dept_id = sd.dept_id
        where bttu.user_task_status = 2
        and FIND_IN_SET(su.dept_id,#{deptIds})
        <if test="queryPersonName != null and queryPersonName != ''">
            and su.nick_name like concat('%',
            #{queryPersonName}, '%')
        </if>
        <if test="selectStartTime != null ">
            and bttu.finish_time > #{selectStartTime}
        </if>
        <if test="selectEndTime != null ">
            and bttu.finish_time &lt; #{selectEndTime}
        </if>
        GROUP BY bttu.op_user_id ORDER BY count(1) desc
    </select>

    <select id="getScheduleTask" parameterType="com.boryou.stats.domain.OrgScheduleDto"
            resultMap="StatisticalDataResult">
        select DISTINCT bnrt.id as taskId, bnrt.title as taskName from by_network_review_task bnrt left join
        by_task_to_user bttu
        on bnrt.id = bttu.task_id where (task_status = 3 or task_status = 2) and bnrt.score_mode = 1
        <if test="deptId != null and deptId != ''">
            and bttu.op_user_dept_id in
            <foreach collection="deptIdLongArr" item="deptId" open="(" separator="," close=")">
                #{deptId}
            </foreach>
        </if>
        <if test="selectStartTime != null ">
            and bnrt.ctime > #{selectStartTime}
        </if>
        <if test="selectEndTime != null ">
            and bnrt.ctime &lt; #{selectEndTime}
        </if>
    </select>
</mapper>
