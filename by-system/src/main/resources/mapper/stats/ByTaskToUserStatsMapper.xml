<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.stats.mapper.ByTaskToUserStatsMapper">
    <select id="selectAcceptTaskNum" resultType="int">
        select IFNULL(count(0),0) allCount from by_task_to_user t left join by_network_review_task n on n.id = t.task_id
        <where>
            <if test="taskId != null ">
                and t.task_id = #{taskId}
            </if>
            <if test="opUserId != null ">
                and t.op_user_id = #{opUserId}
            </if>
            <if test="userTaskStatus != null ">
                and t.user_task_status = #{userTaskStatus}
            </if>
            <if test="taskStatus != null or 1==1 ">
                and n.task_status != 4
            </if>
            <if test="selectStartTime != null ">
                and n.ctime &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null ">
                and n.ctime &lt;= #{selectEndTime}
            </if>
            <if test="taskType != null ">
                and n.task_type = #{taskType}
            </if>
            <if test="noticeType != null  and noticeType != ''">
                and FIND_IN_SET(#{noticeType},n.notice_type)
            </if>
            <if test="mediaType != null ">
                and n.media_type = #{mediaType}
            </if>
            <if test="taskNum != null  and taskNum != '' and (title == null or title == '')">
                and n.task_num like CONCAT("%",CONCAT(#{taskNum},"%"))
            </if>
            <if test="title != null and title != '' and (taskNum == null or taskNum == '') ">
                and n.title like CONCAT("%",CONCAT(#{title},"%"))
            </if>
            <if test="title != null and title != '' and taskNum != null and taskNum != '' ">
                and (n.title like CONCAT("%",CONCAT(#{title},"%")) or n.task_num like
                CONCAT("%",CONCAT(#{taskNum},"%")))
            </if>
        </where>
    </select>

    <select id="selectTaskNum" resultType="int">
        select IFNULL(count(0),0) allCount from by_task_to_user t left join by_network_review_task n on n.id = t.task_id
        <where>
            <if test="taskId != null ">
                and t.task_id = #{taskId}
            </if>
            <if test="opUserId != null ">
                and t.op_user_id = #{opUserId}
            </if>
            <if test="userTaskStatus != null ">
                and t.user_task_status = #{userTaskStatus}
            </if>
            <if test="taskStatus != null ">
                and n.task_status = #{taskStatus}
            </if>
            <if test="selectStartTime != null ">
                and t.finish_time &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null ">
                and t.finish_time &lt;= #{selectEndTime}
            </if>
            <if test="taskType != null ">
                and n.task_type = #{taskType}
            </if>
            <if test="noticeType != null  and noticeType != ''">
                and FIND_IN_SET(#{noticeType},n.notice_type)
            </if>
            <if test="mediaType != null ">
                and n.media_type = #{mediaType}
            </if>
            <if test="taskNum != null  and taskNum != '' and (title == null or title == '')">
                and n.task_num like CONCAT("%",CONCAT(#{taskNum},"%"))
            </if>
            <if test="title != null and title != '' and (taskNum == null or taskNum == '') ">
                and n.title like CONCAT("%",CONCAT(#{title},"%"))
            </if>
            <if test="title != null and title != '' and taskNum != null and taskNum != '' ">
                and (n.title like CONCAT("%",CONCAT(#{title},"%")) or n.task_num like
                CONCAT("%",CONCAT(#{taskNum},"%")))
            </if>
        </where>
    </select>

    <select id="selectSumGrade" resultType="int">
        select IFNULL(sum(task_grade),0) grade from by_task_to_user t left join by_network_review_task n on n.id =
        t.task_id
        <where>
            <if test="taskId != null ">
                and t.task_id = #{taskId}
            </if>
            <if test="opUserId != null ">
                and t.op_user_id = #{opUserId}
            </if>
            <if test="userTaskStatus != null ">
                and t.user_task_status = #{userTaskStatus}
            </if>
            <if test="taskStatus != null ">
                and n.task_status = #{taskStatus}
            </if>
            <if test="selectStartTime != null ">
                and t.finish_time &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null ">
                and t.finish_time &lt;= #{selectEndTime}
            </if>
            <if test="taskType != null ">
                and n.task_type = #{taskType}
            </if>
            <if test="noticeType != null  and noticeType != ''">
                and FIND_IN_SET(#{noticeType},n.notice_type)
            </if>
            <if test="mediaType != null ">
                and n.media_type = #{mediaType}
            </if>
            <if test="taskNum != null  and taskNum != '' and (title == null or title == '')">
                and n.task_num like CONCAT("%",CONCAT(#{taskNum},"%"))
            </if>
            <if test="title != null and title != '' and (taskNum == null or taskNum == '') ">
                and n.title like CONCAT("%",CONCAT(#{title},"%"))
            </if>
            <if test="title != null and title != '' and taskNum != null and taskNum != '' ">
                and (n.title like CONCAT("%",CONCAT(#{title},"%")) or n.task_num like
                CONCAT("%",CONCAT(#{taskNum},"%")))
            </if>
        </where>
    </select>

    <select id="selectMediaTypeCount" resultType="com.boryou.stats.domain.ByTaskToUserStatsDto">
        select media_type taskType, IFNULL(count(0),0) allCount from by_task_to_user t left join by_network_review_task
        n on n.id = t.task_id
        <where>
            <if test="taskId != null ">
                and t.task_id = #{taskId}
            </if>
            <if test="opUserId != null ">
                and t.op_user_id = #{opUserId}
            </if>
            <if test="userTaskStatus != null ">
                and t.user_task_status = #{userTaskStatus}
            </if>
            <if test="taskStatus != null ">
                and n.task_status = #{taskStatus}
            </if>
            <if test="selectStartTime != null ">
                and t.finish_time &gt;= #{selectStartTime}
            </if>
            <if test="selectEndTime != null ">
                and t.finish_time &lt;= #{selectEndTime}
            </if>
            <if test="taskType != null ">
                and n.task_type = #{taskType}
            </if>
            <if test="noticeType != null  and noticeType != ''">
                and FIND_IN_SET(#{noticeType},n.notice_type)
            </if>
            <if test="mediaType != null ">
                and n.media_type = #{mediaType}
            </if>
            <if test="taskNum != null  and taskNum != '' and (title == null or title == '')">
                and n.task_num like CONCAT("%",CONCAT(#{taskNum},"%"))
            </if>
            <if test="title != null and title != '' and (taskNum == null or taskNum == '') ">
                and n.title like CONCAT("%",CONCAT(#{title},"%"))
            </if>
            <if test="title != null and title != '' and taskNum != null and taskNum != '' ">
                and (n.title like CONCAT("%",CONCAT(#{title},"%")) or n.task_num like
                CONCAT("%",CONCAT(#{taskNum},"%")))
            </if>
        </where>
        group by media_type
    </select>
</mapper>
