package com.boryou.upload.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.upload.domain.BUploadFile;

import java.util.List;

/***
 * <AUTHOR>
 * @description //文件上传Mapper接口
 * @date 11:20 2021/12/6
 * @param
 */
public interface BUploadFileMapper extends BaseMapper<BUploadFile> {
    /**
     * 查询文件上传
     *
     * @param id 文件上传ID
     * @return 文件上传
     */
    public BUploadFile selectBUploadFileById(Long id);

    /**
     * 查询文件上传列表
     *
     * @param bUploadFile 文件上传
     * @return 文件上传集合
     */
    public List<BUploadFile> selectBUploadFileList(BUploadFile bUploadFile);

    /**
     * 新增文件上传
     *
     * @param bUploadFile 文件上传
     * @return 结果
     */
    public int insertBUploadFile(BUploadFile bUploadFile);

    /**
     * 修改文件上传
     *
     * @param bUploadFile 文件上传
     * @return 结果
     */
    public int updateBUploadFile(BUploadFile bUploadFile);

    /**
     * 删除文件上传
     *
     * @param id 文件上传ID
     * @return 结果
     */
    public int deleteBUploadFileById(Long id);

    /**
     * 批量删除文件上传
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteBUploadFileByIds(Long[] ids);

    /**
     * 获取保存路径
     *
     * @param id 文件id
     * @return 保存文件路径
     * <AUTHOR>
     */
    String getAddress(Long id);
}
