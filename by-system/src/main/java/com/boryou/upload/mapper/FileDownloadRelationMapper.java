package com.boryou.upload.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.upload.domain.FileDownloadRelation;

import java.util.List;

/**
 * 文件与下载用户关系Mapper接口
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
public interface FileDownloadRelationMapper extends BaseMapper<FileDownloadRelation> {
    /**
     * 查询文件与下载用户关系
     *
     * @param id 文件与下载用户关系ID
     * @return 文件与下载用户关系
     */
    public FileDownloadRelation selectFileDownloadRelationById(Long id);

    /**
     * 查询文件与下载用户关系列表
     *
     * @param fileDownloadRelation 文件与下载用户关系
     * @return 文件与下载用户关系集合
     */
    public List<FileDownloadRelation> selectFileDownloadRelationList(FileDownloadRelation fileDownloadRelation);

    /**
     * 新增文件与下载用户关系
     *
     * @param fileDownloadRelation 文件与下载用户关系
     * @return 结果
     */
    public int insertFileDownloadRelation(FileDownloadRelation fileDownloadRelation);

    /**
     * 修改文件与下载用户关系
     *
     * @param fileDownloadRelation 文件与下载用户关系
     * @return 结果
     */
    public int updateFileDownloadRelation(FileDownloadRelation fileDownloadRelation);

    /**
     * 删除文件与下载用户关系
     *
     * @param id 文件与下载用户关系ID
     * @return 结果
     */
    public int deleteFileDownloadRelationById(Long id);

    /**
     * 删除文件与下载用户关系
     *
     * @param fileId 文件与下载用户关系  文件ID
     * @return 结果
     */
    public int deleteFileDownloadRelationByFileId(Long fileId);

    /**
     * 批量删除文件与下载用户关系
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteFileDownloadRelationByIds(Long[] ids);
}
