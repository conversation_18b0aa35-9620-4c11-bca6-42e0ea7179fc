package com.boryou.upload.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.common.annotation.Excel;
import com.boryou.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/***
 * <AUTHOR>
 * @description //文件上传对象 by_upload_file
 * @date 11:19 2021/12/6
 * @param
 */
@Data
@TableName("by_upload_file")
public class BUploadFile extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 服务器存放地址
     */
    @Excel(name = "服务器存放地址")
    private String address;

    /**
     * 文件名称
     */
    @Excel(name = "文件名称")
    private String fileName;

    /**
     * 文件类型
     */
    @Excel(name = "文件类型")
    private String fileType;

    /**
     * 单位：B
     */
    @Excel(name = "单位：B")
    private Long fileSize;

    /**
     * 是否删除 0未删除  1已删除  （逻辑删除）
     */
    @Excel(name = "是否删除 0未删除  1已删除  ", readConverterExp = "逻=辑删除")
    private Integer deleteStatus;

    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uploadTime;

    /**
     * 用户ID--当前登录用户ID--查询条件
     */
    @TableField(exist = false)
    private Long userId;

    /**
     * 当前文件的可下载用户
     */
    @TableField(exist = false)
    private List<Long> downloadUserIds;

    /**
     * 文件码
     */
    private String base64;

    /**
     * 文件状态 0老文件 1新文件
     */
    private Integer status;

    public BUploadFile() {
    }

    public BUploadFile(Long id, String address, String fileName, String fileType, Long fileSize, Integer deleteStatus, Date uploadTime) {
        this.id = id;
        this.address = address;
        this.fileName = fileName;
        this.fileType = fileType;
        this.fileSize = fileSize;
        this.deleteStatus = deleteStatus;
        this.uploadTime = uploadTime;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("address", getAddress())
                .append("fileName", getFileName())
                .append("fileType", getFileType())
                .append("fileSize", getFileSize())
                .append("deleteStatus", getDeleteStatus())
                .append("uploadTime", getUploadTime())
                .toString();
    }
}
