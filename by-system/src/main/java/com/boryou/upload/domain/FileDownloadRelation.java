package com.boryou.upload.domain;

import com.alibaba.fastjson.annotation.JSONField;
import com.baomidou.mybatisplus.annotation.FieldFill;
import com.baomidou.mybatisplus.annotation.TableField;
import com.boryou.common.annotation.Excel;
import com.boryou.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * 文件与下载用户关系对象 file_download_relation
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
@Data
public class FileDownloadRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 下载用户ID
     */
    @Excel(name = "下载用户ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long downloadUserId;

    /**
     * 可下载文件ID
     */
    @Excel(name = "可下载文件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long downloadFileId;

    @Excel(name = "可下载文件ID")
    @TableField(fill = FieldFill.INSERT)
    @JSONField(format = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 批量添加多个用户
     */
    @TableField(exist = false)
    private List<Long> downloadUserIds;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("downloadUserId", getDownloadUserId())
                .append("downloadFileId", getDownloadFileId())
                .toString();
    }
}
