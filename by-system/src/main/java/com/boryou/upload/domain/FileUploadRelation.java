package com.boryou.upload.domain;

import com.boryou.common.annotation.Excel;
import com.boryou.common.core.domain.BaseEntity;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

/**
 * 文件与上传用户关系对象 file_upload_relation
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
public class FileUploadRelation extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 上传人ID
     */
    @Excel(name = "上传人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long uploadUserId;

    /**
     * 上传文件ID
     */
    @Excel(name = "上传文件ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long uploadFileId;

    public void setId(Long id) {
        this.id = id;
    }

    public Long getId() {
        return id;
    }

    public void setUploadUserId(Long uploadUserId) {
        this.uploadUserId = uploadUserId;
    }

    public Long getUploadUserId() {
        return uploadUserId;
    }

    public void setUploadFileId(Long uploadFileId) {
        this.uploadFileId = uploadFileId;
    }

    public Long getUploadFileId() {
        return uploadFileId;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("uploadUserId", getUploadUserId())
                .append("uploadFileId", getUploadFileId())
                .toString();
    }
}
