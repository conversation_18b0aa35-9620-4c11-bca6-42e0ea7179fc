package com.boryou.upload.service;

import com.boryou.upload.domain.FileUploadRelation;

import java.util.List;


/**
 * 文件与上传用户关系Service接口
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
public interface IFileUploadRelationService {
    /**
     * 查询文件与上传用户关系
     *
     * @param id 文件与上传用户关系ID
     * @return 文件与上传用户关系
     */
    public FileUploadRelation selectFileUploadRelationById(Long id);

    /**
     * 查询文件与上传用户关系列表
     *
     * @param fileUploadRelation 文件与上传用户关系
     * @return 文件与上传用户关系集合
     */
    public List<FileUploadRelation> selectFileUploadRelationList(FileUploadRelation fileUploadRelation);

    /**
     * 新增文件与上传用户关系
     *
     * @param fileUploadRelation 文件与上传用户关系
     * @return 结果
     */
    public int insertFileUploadRelation(FileUploadRelation fileUploadRelation);

    /**
     * 修改文件与上传用户关系
     *
     * @param fileUploadRelation 文件与上传用户关系
     * @return 结果
     */
    public int updateFileUploadRelation(FileUploadRelation fileUploadRelation);

    /**
     * 批量删除文件与上传用户关系
     *
     * @param ids 需要删除的文件与上传用户关系ID
     * @return 结果
     */
    public int deleteFileUploadRelationByIds(Long[] ids);

    /**
     * 删除文件与上传用户关系信息
     *
     * @param id 文件与上传用户关系ID
     * @return 结果
     */
    public int deleteFileUploadRelationById(Long id);
}
