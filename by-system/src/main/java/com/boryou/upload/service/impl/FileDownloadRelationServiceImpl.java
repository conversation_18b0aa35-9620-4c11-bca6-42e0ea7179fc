package com.boryou.upload.service.impl;

import com.boryou.common.utils.IdUtil;
import com.boryou.upload.domain.FileDownloadRelation;
import com.boryou.upload.mapper.FileDownloadRelationMapper;
import com.boryou.upload.service.IFileDownloadRelationService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 文件与下载用户关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
@Service
public class FileDownloadRelationServiceImpl implements IFileDownloadRelationService {
    @Resource
    private FileDownloadRelationMapper fileDownloadRelationMapper;

    /**
     * 查询文件与下载用户关系
     *
     * @param id 文件与下载用户关系ID
     * @return 文件与下载用户关系
     */
    @Override
    public FileDownloadRelation selectFileDownloadRelationById(Long id) {
        return fileDownloadRelationMapper.selectFileDownloadRelationById(id);
    }

    /**
     * 查询文件与下载用户关系列表
     *
     * @param fileDownloadRelation 文件与下载用户关系
     * @return 文件与下载用户关系
     */
    @Override
    public List<FileDownloadRelation> selectFileDownloadRelationList(FileDownloadRelation fileDownloadRelation) {
        return fileDownloadRelationMapper.selectFileDownloadRelationList(fileDownloadRelation);
    }

    /**
     * 新增文件与下载用户关系
     *
     * @param fileDownloadRelation 文件与下载用户关系
     * @return 结果
     */
    @Override
    public int insertFileDownloadRelation(FileDownloadRelation fileDownloadRelation) {
        //删除原有与该文件绑定人员
        deleteFileDownloadRelationByFileId(fileDownloadRelation.getDownloadFileId());
        try {
            //循环插入
            for (Long downloadUserId : fileDownloadRelation.getDownloadUserIds()) {
                fileDownloadRelation.setDownloadUserId(downloadUserId);
                //主键
                fileDownloadRelation.setId(IdUtil.nextLong());
                fileDownloadRelationMapper.insertFileDownloadRelation(fileDownloadRelation);
            }
        } catch (Exception e) {
            return 0;
        }
        return 1;
    }

    /**
     * 修改文件与下载用户关系
     *
     * @param fileDownloadRelation 文件与下载用户关系
     * @return 结果
     */
    @Override
    public int updateFileDownloadRelation(FileDownloadRelation fileDownloadRelation) {
        //删除原有与该文件绑定人员
        deleteFileDownloadRelationByFileId(fileDownloadRelation.getDownloadFileId());
        try {
            //循环插入
            for (Long downloadUserId : fileDownloadRelation.getDownloadUserIds()) {
                fileDownloadRelation.setDownloadUserId(downloadUserId);
                //主键
                fileDownloadRelation.setId(IdUtil.nextLong());
                fileDownloadRelationMapper.insertFileDownloadRelation(fileDownloadRelation);
            }
        } catch (Exception e) {
            return 0;
        }
        return 1;
    }

    /**
     * 批量删除文件与下载用户关系
     *
     * @param ids 主键ID
     * @return 结果
     */
    @Override
    public int deleteFileDownloadRelationByIds(Long[] ids) {
        return fileDownloadRelationMapper.deleteFileDownloadRelationByIds(ids);
    }

    /**
     * 删除文件与下载用户关系信息
     *
     * @param id 主键ID
     * @return 结果
     */
    @Override
    public int deleteFileDownloadRelationById(Long id) {
        return fileDownloadRelationMapper.deleteFileDownloadRelationById(id);
    }

    /**
     * 删除文件与下载用户关系信息
     *
     * @param fileId 文件与下载用户关系 的文件ID--删除该文件关联的所有下载用户
     * @return 结果
     */
    @Override
    public int deleteFileDownloadRelationByFileId(Long fileId) {
        return fileDownloadRelationMapper.deleteFileDownloadRelationByFileId(fileId);
    }
}
