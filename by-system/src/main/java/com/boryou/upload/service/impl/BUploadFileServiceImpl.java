package com.boryou.upload.service.impl;

import com.boryou.common.utils.SecurityUtils;
import com.boryou.upload.domain.BUploadFile;
import com.boryou.upload.domain.FileDownloadRelation;
import com.boryou.upload.mapper.BUploadFileMapper;
import com.boryou.upload.service.IBUploadFileService;
import com.boryou.upload.service.IFileDownloadRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/***
 * <AUTHOR>
 * @description //文件上传Service业务层处理
 * @date 11:20 2021/12/6
 * @param
 */
@Service
public class BUploadFileServiceImpl implements IBUploadFileService {
    @Resource
    private BUploadFileMapper bUploadFileMapper;
    @Autowired
    private IFileDownloadRelationService fileDownloadRelationService;

    /**
     * 查询文件上传
     *
     * @param id 文件上传ID
     * @return 文件上传
     */
    @Override
    public BUploadFile selectBUploadFileById(Long id) {
        return bUploadFileMapper.selectBUploadFileById(id);
    }

    /**
     * 查询文件上传列表
     *
     * @param bUploadFile 文件上传
     * @return 文件上传
     */
    @Override
    public List<BUploadFile> selectBUploadFileList(BUploadFile bUploadFile) {
        //如果不是管理员就只能查看自己上传的和自己能下载的文件
        if (SecurityUtils.getLoginUser().getUser().getUserId() != 1) {
            bUploadFile.setUserId(SecurityUtils.getLoginUser().getUser().getUserId());
        } else {
            bUploadFile.setUserId(0L);
        }
        List<BUploadFile> bUploadFileList = bUploadFileMapper.selectBUploadFileList(bUploadFile);
        for (BUploadFile bUploadFile1 : bUploadFileList) {
            FileDownloadRelation fileDownloadRelation = new FileDownloadRelation();
            fileDownloadRelation.setDownloadFileId(bUploadFile1.getId());
            List<FileDownloadRelation> fileDownloadRelationList = fileDownloadRelationService.selectFileDownloadRelationList(fileDownloadRelation);
            List<Long> list = new ArrayList<>();
            for (FileDownloadRelation fileDownloadRelation1 : fileDownloadRelationList) {
                list.add(fileDownloadRelation1.getDownloadUserId());
            }
            bUploadFile1.setDownloadUserIds(list);
        }
        return bUploadFileList;
    }

    /**
     * 新增文件上传
     *
     * @param bUploadFile 文件上传
     * @return 结果
     */
    @Override
    public int insertBUploadFile(BUploadFile bUploadFile) {
        return bUploadFileMapper.insertBUploadFile(bUploadFile);
    }

    /**
     * 修改文件上传
     *
     * @param bUploadFile 文件上传
     * @return 结果
     */
    @Override
    public int updateBUploadFile(BUploadFile bUploadFile) {
        return bUploadFileMapper.updateBUploadFile(bUploadFile);
    }

    /**
     * 批量删除文件上传
     *
     * @param ids 需要删除的文件上传ID
     * @return 结果
     */
    @Override
    public int deleteBUploadFileByIds(Long[] ids) {
        return bUploadFileMapper.deleteBUploadFileByIds(ids);
    }

    /**
     * 删除文件上传信息
     *
     * @param id 文件上传ID
     * @return 结果
     */
    @Override
    public int deleteBUploadFileById(Long id) {
        return bUploadFileMapper.deleteBUploadFileById(id);
    }
}
