package com.boryou.upload.service.impl;

import com.boryou.upload.domain.FileUploadRelation;
import com.boryou.upload.mapper.FileUploadRelationMapper;
import com.boryou.upload.service.IFileUploadRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文件与上传用户关系Service业务层处理
 *
 * <AUTHOR>
 * @date 2021-12-06
 */
@Service
public class FileUploadRelationServiceImpl implements IFileUploadRelationService {
    @Autowired
    private FileUploadRelationMapper fileUploadRelationMapper;

    /**
     * 查询文件与上传用户关系
     *
     * @param id 文件与上传用户关系ID
     * @return 文件与上传用户关系
     */
    @Override
    public FileUploadRelation selectFileUploadRelationById(Long id) {
        return fileUploadRelationMapper.selectFileUploadRelationById(id);
    }

    /**
     * 查询文件与上传用户关系列表
     *
     * @param fileUploadRelation 文件与上传用户关系
     * @return 文件与上传用户关系
     */
    @Override
    public List<FileUploadRelation> selectFileUploadRelationList(FileUploadRelation fileUploadRelation) {
        return fileUploadRelationMapper.selectFileUploadRelationList(fileUploadRelation);
    }

    /**
     * 新增文件与上传用户关系
     *
     * @param fileUploadRelation 文件与上传用户关系
     * @return 结果
     */
    @Override
    public int insertFileUploadRelation(FileUploadRelation fileUploadRelation) {
        return fileUploadRelationMapper.insertFileUploadRelation(fileUploadRelation);
    }

    /**
     * 修改文件与上传用户关系
     *
     * @param fileUploadRelation 文件与上传用户关系
     * @return 结果
     */
    @Override
    public int updateFileUploadRelation(FileUploadRelation fileUploadRelation) {
        return fileUploadRelationMapper.updateFileUploadRelation(fileUploadRelation);
    }

    /**
     * 批量删除文件与上传用户关系
     *
     * @param ids 需要删除的文件与上传用户关系ID
     * @return 结果
     */
    @Override
    public int deleteFileUploadRelationByIds(Long[] ids) {
        return fileUploadRelationMapper.deleteFileUploadRelationByIds(ids);
    }

    /**
     * 删除文件与上传用户关系信息
     *
     * @param id 文件与上传用户关系ID
     * @return 结果
     */
    @Override
    public int deleteFileUploadRelationById(Long id) {
        return fileUploadRelationMapper.deleteFileUploadRelationById(id);
    }
}
