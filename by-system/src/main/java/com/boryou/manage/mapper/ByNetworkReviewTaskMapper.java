package com.boryou.manage.mapper;

import com.boryou.manage.domain.ByNetworkReviewTask;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网评任务Mapper接口
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
public interface ByNetworkReviewTaskMapper {
    /**
     * 查询网评任务
     *
     * @param id 网评任务ID
     * @return 网评任务
     */
    public ByNetworkReviewTask selectByNetworkReviewTaskById(Long id);

    /**
     * 查询网评任务列表
     *
     * @param byNetworkReviewTask 网评任务
     * @return 网评任务集合
     */
    public List<ByNetworkReviewTask> selectByNetworkReviewTaskList(ByNetworkReviewTask byNetworkReviewTask);

    /**
     * 新增网评任务
     *
     * @param byNetworkReviewTask 网评任务
     * @return 结果
     */
    public int insertByNetworkReviewTask(ByNetworkReviewTask byNetworkReviewTask);

    /**
     * 修改网评任务
     *
     * @param byNetworkReviewTask 网评任务
     * @return 结果
     */
    public int updateByNetworkReviewTask(ByNetworkReviewTask byNetworkReviewTask);

    /**
     * 修改网评任务完成时间并置状态为已完成
     *
     * @param id 网评任务ID
     * @return 结果
     */
    public int updateActualCompletionTime(Long id);

    /**
     * 修改网评任务团队模式下的完成度
     *
     * @param id 网评任务ID
     * @return 结果
     */
    public int updateCompleteDegree(@Param("id") Long id, @Param("completeDegree") String completeDegree);

    /**
     * 删除网评任务
     *
     * @param id 网评任务ID
     * @return 结果
     */
    public int deleteByNetworkReviewTaskById(Long id);

    /**
     * 批量删除网评任务
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteByNetworkReviewTaskByIds(Long[] ids);

    /**
     * 查询网评任务各状态数量
     *
     * @param byNetworkReviewTask 网评任务
     */
    public int selectByNetworkReviewTaskNum(ByNetworkReviewTask byNetworkReviewTask);

}
