package com.boryou.manage.mapper;


import com.boryou.manage.domain.File;
import com.boryou.manage.domain.TaskFile;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 关联表Mapper接口
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
public interface TaskFileMapper {
    /**
     * 查询关联表
     *
     * @param taskId 关联表ID
     * @return 关联表
     */
    List<File> selectTaskFileById(Long taskId);

    /**
     * 查询关联表列表
     *
     * @param taskFile 关联表
     * @return 关联表集合
     */
    List<TaskFile> selectTaskFileList(TaskFile taskFile);

    /**
     * 新增关联表
     *
     * @param taskFile 关联表
     * @return 结果
     */
    int insertTaskFile(TaskFile taskFile);

    /**
     * 修改关联表
     *
     * @param taskFile 关联表
     * @return 结果
     */
    int updateTaskFile(TaskFile taskFile);

    /**
     * 删除关联表
     *
     * @param taskId 关联表ID
     * @return 结果
     */
    int deleteTaskFileById(Long taskId);

    /**
     * 批量删除关联表
     *
     * @param taskIds 需要删除的数据ID
     * @return 结果
     */
    int deleteTaskFileByIds(Long[] taskIds);

    int insertTaskFileList(@Param("infoList") List<TaskFile> taskFiles);
}
