package com.boryou.manage.mapper;

import com.boryou.manage.domain.ByTaskToUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 网评员Mapper接口
 *
 * <AUTHOR>
 */
public interface ByTaskToUserMapper {

    /**
     * 查询任务与人员关联
     *
     * @param id 任务与人员关联ID
     * @return 任务与人员关联
     */
    public ByTaskToUser selectByTaskToUserById(Long id);

    /**
     * 查询任务与人员关联列表
     *
     * @param byTaskToUser 任务与人员关联
     * @return 任务与人员关联集合
     */
    public List<ByTaskToUser> selectByTaskToUserList(ByTaskToUser byTaskToUser);

    /**
     * 网评员查询自己的任务信息列表
     *
     * @param byTaskToUser 任务与人员
     * @return 任务集合
     */
    public List<ByTaskToUser> selectUserTaskList(ByTaskToUser byTaskToUser);

    /**
     * 网评员查询自己的任务信息
     *
     * @param id 任务ID
     * @return 任务
     */
    public ByTaskToUser selectUserTaskById(Long id);

    /**
     * 新增任务与人员关联
     *
     * @param byTaskToUser 任务与人员关联
     * @return 结果
     */
    public int insertByTaskToUser(ByTaskToUser byTaskToUser);

    /**
     * 修改任务与人员关联
     *
     * @param byTaskToUser 任务与人员关联
     * @return 结果
     */
    public int updateByTaskToUser(ByTaskToUser byTaskToUser);

    /**
     * 作废任务与人员关联
     *
     * @param taskId         主任务ID
     * @param userTaskStatus 子任务状态
     * @return 结果
     */
    public int cancelByTaskToUser(@Param("taskId") Long taskId, @Param("userTaskStatus") Integer userTaskStatus);

    /**
     * 作废任务与人员关联
     *
     * @param taskId         主任务ID
     * @param userTaskStatus 子任务状态
     * @return 结果
     */
    public int exceedTimeByTaskToUser(@Param("taskId") Long taskId, @Param("userTaskStatus") Integer userTaskStatus);

    /**
     * 删除任务与人员关联
     *
     * @param id 任务与人员关联ID
     * @return 结果
     */
    public int deleteByTaskToUserById(Long id);

    /**
     * 批量删除任务与人员关联
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteByTaskToUserByIds(Long[] ids);

}
