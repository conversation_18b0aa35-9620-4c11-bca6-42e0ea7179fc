package com.boryou.manage.mapper;

import com.boryou.manage.domain.ByApkData;

import java.util.List;

/**
 * apk版本数据Mapper接口
 *
 * <AUTHOR>
 * @date 2023-04-18
 */
public interface ByApkDataMapper {
    /**
     * 查询apk版本数据
     *
     * @param id apk版本数据ID
     * @return apk版本数据
     */
    public ByApkData selectByApkDataById(Long id);

    /**
     * 查询apk版本数据列表
     *
     * @param byApkData apk版本数据
     * @return apk版本数据集合
     */
    public List<ByApkData> selectByApkDataList(ByApkData byApkData);

    /**
     * 新增apk版本数据
     *
     * @param byApkData apk版本数据
     * @return 结果
     */
    public int insertByApkData(ByApkData byApkData);

    /**
     * 修改apk版本数据
     *
     * @param byApkData apk版本数据
     * @return 结果
     */
    public int updateByApkData(ByApkData byApkData);

    /**
     * 删除apk版本数据
     *
     * @param id apk版本数据ID
     * @return 结果
     */
    public int deleteByApkDataById(Long id);

    /**
     * 批量删除apk版本数据
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteByApkDataByIds(Long[] ids);
}
