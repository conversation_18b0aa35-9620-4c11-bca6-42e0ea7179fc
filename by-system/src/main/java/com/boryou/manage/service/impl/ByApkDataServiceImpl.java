package com.boryou.manage.service.impl;

import cn.hutool.core.util.StrUtil;
import com.boryou.common.utils.DateUtils;
import com.boryou.common.utils.IdUtil;
import com.boryou.manage.domain.ByApkData;
import com.boryou.manage.domain.ByUploadFile;
import com.boryou.manage.mapper.ByApkDataMapper;
import com.boryou.manage.mapper.ByUploadFileMapper;
import com.boryou.manage.service.IByApkDataService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * apk版本数据Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-04-18
 */
@Service
public class ByApkDataServiceImpl implements IByApkDataService {
    @Resource
    private ByApkDataMapper byApkDataMapper;
    @Resource
    private ByUploadFileMapper byUploadFileMapper;

    /**
     * 查询apk版本数据
     *
     * @param id apk版本数据ID
     * @return apk版本数据
     */
    @Override
    public ByApkData selectByApkDataById(Long id) {
        ByApkData byApkData = byApkDataMapper.selectByApkDataById(id);
        if (StrUtil.isNotEmpty(byApkData.getVersionFileId())) {
            ByUploadFile byUploadFile = byUploadFileMapper.selectByUploadFileById(Long.parseLong(byApkData.getVersionFileId()));
            byApkData.setByUploadFile(byUploadFile);
        }
        return byApkData;
    }

    /**
     * 查询apk版本数据列表
     *
     * @param byApkData apk版本数据
     * @return apk版本数据
     */
    @Override
    public List<ByApkData> selectByApkDataList(ByApkData byApkData) {
        List<ByApkData> byApkDataList = byApkDataMapper.selectByApkDataList(byApkData);
        for (ByApkData byApkData1 : byApkDataList) {
            if (StrUtil.isNotEmpty(byApkData1.getVersionFileId())) {
                ByUploadFile byUploadFile = byUploadFileMapper.selectByUploadFileById(Long.parseLong(byApkData1.getVersionFileId()));
                byApkData1.setByUploadFile(byUploadFile);
            }
        }
        return byApkDataList;
    }

    /**
     * 新增apk版本数据
     *
     * @param byApkData apk版本数据
     * @return 结果
     */
    @Override
    public int insertByApkData(ByApkData byApkData) {
        byApkData.setCtime(DateUtils.getNowDate());
        byApkData.setId(IdUtil.nextLong());
        return byApkDataMapper.insertByApkData(byApkData);
    }

    /**
     * 修改apk版本数据
     *
     * @param byApkData apk版本数据
     * @return 结果
     */
    @Override
    public int updateByApkData(ByApkData byApkData) {
        return byApkDataMapper.updateByApkData(byApkData);
    }

    /**
     * 批量删除apk版本数据
     *
     * @param ids 需要删除的apk版本数据ID
     * @return 结果
     */
    @Override
    public int deleteByApkDataByIds(Long[] ids) {
        return byApkDataMapper.deleteByApkDataByIds(ids);
    }

    /**
     * 删除apk版本数据信息
     *
     * @param id apk版本数据ID
     * @return 结果
     */
    @Override
    public int deleteByApkDataById(Long id) {
        return byApkDataMapper.deleteByApkDataById(id);
    }
}
