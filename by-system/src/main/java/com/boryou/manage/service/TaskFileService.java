package com.boryou.manage.service;

import com.boryou.manage.domain.File;
import com.boryou.manage.domain.TaskFile;

import java.util.List;

/**
 * 关联表Service接口
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
public interface TaskFileService {
    /**
     * 查询关联表
     *
     * @param taskId 关联表ID
     * @return 关联表
     */
    List<File> selectTaskFileById(Long taskId);

    /**
     * 查询关联表列表
     *
     * @param taskFile 关联表
     * @return 关联表集合
     */
    public List<TaskFile> selectTaskFileList(TaskFile taskFile);

    /**
     * 新增关联表
     *
     * @param taskFile 关联表
     * @return 结果
     */
    public int insertTaskFile(TaskFile taskFile);

    /**
     * 修改关联表
     *
     * @param taskFile 关联表
     * @return 结果
     */
    public int updateTaskFile(TaskFile taskFile);

    /**
     * 批量删除关联表
     *
     * @param taskIds 需要删除的关联表ID
     * @return 结果
     */
    public int deleteTaskFileByIds(Long[] taskIds);

    /**
     * 删除关联表信息
     *
     * @param taskId 关联表ID
     * @return 结果
     */
    public int deleteTaskFileById(Long taskId);

    int insertTaskFileList(List<TaskFile> taskFiles);
}
