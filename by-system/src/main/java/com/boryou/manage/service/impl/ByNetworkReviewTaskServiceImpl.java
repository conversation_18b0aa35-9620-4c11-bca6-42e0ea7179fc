package com.boryou.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.*;
import com.boryou.manage.domain.ByNetworkReviewTask;
import com.boryou.manage.domain.ByTaskToUser;
import com.boryou.manage.domain.File;
import com.boryou.manage.domain.TaskFile;
import com.boryou.manage.domain.vo.FileVO;
import com.boryou.manage.mapper.ByNetworkReviewTaskMapper;
import com.boryou.manage.mapper.ByTaskToUserMapper;
import com.boryou.manage.service.FileService;
import com.boryou.manage.service.IByNetworkReviewTaskService;
import com.boryou.manage.service.TaskFileService;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.mapper.SysDictDataMapper;
import com.boryou.system.mapper.SysUserMapper;
import com.boryou.system.service.ISysDeptService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 网评任务Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
@Service
public class ByNetworkReviewTaskServiceImpl implements IByNetworkReviewTaskService {
    @Resource
    private ByNetworkReviewTaskMapper byNetworkReviewTaskMapper;
    @Autowired
    private RedisCache redisCache;
    @Resource
    private ByTaskToUserMapper byTaskToUserMapper;
    @Resource
    private SysDictDataMapper dictDataMapper;
    @Resource
    private SysUserMapper userMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private ISysDeptService deptService;
    @Resource
    private FileService fileService;
    @Resource
    private TaskFileService taskFileService;

    /**
     * 查询网评任务详情
     *
     * @param id 网评任务ID
     * @return 网评任务
     */
    @Override
    public ByNetworkReviewTask selectByNetworkReviewTaskById(Long id, Integer subTaskStatus) {
        if (StringUtils.isNull(subTaskStatus)) {
            throw new CustomException("请选择子任务状态");
        }
        if (StringUtils.isNull(id)) {
            throw new CustomException("请传入主任务ID");
        }
        ByNetworkReviewTask byNetworkReviewTask = byNetworkReviewTaskMapper.selectByNetworkReviewTaskById(id);
        //任务类型字典转换
        byNetworkReviewTask.setTaskTypeName(dictDataMapper.selectDictLabel("task_type", String.valueOf(byNetworkReviewTask.getTaskType())));
        /**任务类型为综合时进行子任务名称转换*/
        if (byNetworkReviewTask.getTaskType() == 0) {
            //子任务类型有值
            if (StrUtil.isNotEmpty(byNetworkReviewTask.getSubTaskType())) {
                String[] subTaskTypeArr = byNetworkReviewTask.getSubTaskType().split(",");
                String subTaskTypeName = "";
                for (String type : subTaskTypeArr) {
                    subTaskTypeName += dictDataMapper.selectDictLabel("task_type", type) + "+";
                }
                byNetworkReviewTask.setSubTaskTypeName(subTaskTypeName.substring(0, subTaskTypeName.length() - 1));
                byNetworkReviewTask.setTaskTypeName(byNetworkReviewTask.getTaskTypeName() + "(" + subTaskTypeName.substring(0, subTaskTypeName.length() - 1) + ")");
            }
        }
        //任务状态字典值转换
        byNetworkReviewTask.setScoreModeName(dictDataMapper.selectDictLabel("score_mode", String.valueOf(byNetworkReviewTask.getScoreMode())));
        //任务状态字典值转换
        byNetworkReviewTask.setTaskStatusName(dictDataMapper.selectDictLabel("task_status", String.valueOf(byNetworkReviewTask.getTaskStatus())));
        //媒体类型字典值转换
        byNetworkReviewTask.setMediaTypeName(dictDataMapper.selectDictLabel("media_type", String.valueOf(byNetworkReviewTask.getMediaType())));
        //绩点类型字典值转换
        byNetworkReviewTask.setScoreTypeName(dictDataMapper.selectDictLabel("scoring_type", String.valueOf(byNetworkReviewTask.getScoreType())));
        //获取该任务的所有分配人员
        ByTaskToUser byTaskToUser = new ByTaskToUser();
        //主任务ID
        byTaskToUser.setTaskId(id);
        //任务状态 -1代表查所有
        if (subTaskStatus != -1) {
            byTaskToUser.setUserTaskStatus(subTaskStatus);
        }
        List<ByTaskToUser> list = byTaskToUserMapper.selectByTaskToUserList(byTaskToUser);
        //将数据进行字典值转换
        for (ByTaskToUser byTaskToUser1 : list) {
            //子单任务状态名称
            byTaskToUser1.setUserTaskStatusName(dictDataMapper.selectDictLabel("sub_task_status", String.valueOf(byTaskToUser1.getUserTaskStatus())));
            //网评员名称
            SysUser sysUser = userMapper.selectUserById(byTaskToUser1.getOpUserId());
            if (sysUser != null) {
                byTaskToUser1.setOpUserName(sysUser.getNickName());
            }
        }
        byNetworkReviewTask.setByTaskToUserList(list);
        byNetworkReviewTask.setFiles(this.getTaskFileList(id));
        return byNetworkReviewTask;
    }

    public List<FileVO> getTaskFileList(Long id) {
        List<FileVO> fileVOList = new ArrayList<>();
        List<File> taskFileList = taskFileService.selectTaskFileById(id);
        if (CollUtil.isNotEmpty(taskFileList)) {
            for (File file : taskFileList) {
                String fileName = file.getFileName();
                // String url = MinioUtil.getUrlByFileName(fileName);
                String original = file.getOriginal();
                String type = file.getType();
                Long fileId = file.getFileId();
                FileVO fileVO = new FileVO();
                fileVO.setFileId(fileId);
                fileVO.setFileName(original);
                fileVO.setFileUrl(fileName);
                fileVO.setType(type);
                fileVOList.add(fileVO);
            }
        }
        return fileVOList;
    }

    /**
     * 根据任务ID查询网评任务各组织进度
     *
     * @param id 网评任务ID
     * @return 网评任务
     */
    @Override
    public List<Map<String, String>> selectByNetworkReviewTaskScheduleById(Long id) {
        if (StringUtils.isNull(id)) {
            throw new CustomException("请传入主任务ID");
        }
        ByTaskToUser byTaskToUser = new ByTaskToUser();
        //主任务ID
        byTaskToUser.setTaskId(id);
        List<ByTaskToUser> list = byTaskToUserMapper.selectByTaskToUserList(byTaskToUser);
        List<Map<String, String>> mapList = new ArrayList<>();
        //对每个部门的实际参与人数量进行统计
        Map<Long, Long> map = list.stream().collect(Collectors.groupingBy(ByTaskToUser::getOpUserDeptId, Collectors.counting()));
        //遍历map,对每个组织单位进度进行计算
        Set keySet = map.keySet();
        for (Object key : keySet) {
            Long count = list.stream().filter(s -> String.valueOf(key).equals(String.valueOf(s.getOpUserDeptId())) && "2".equals(String.valueOf(s.getUserTaskStatus()))).count();
            SysDept sysDept = sysDeptMapper.selectDeptById(Long.parseLong(key.toString()));
            Map<String, String> mapResult = new HashMap<>();
            mapResult.put("name", sysDept.getDeptName());
            mapResult.put("value", String.valueOf((count * 100) / map.get(key)));
            mapList.add(mapResult);
        }
        return mapList;
    }

    /**
     * 查询网评任务列表
     *
     * @param byNetworkReviewTask 网评任务
     * @return 网评任务
     */
    @Override
    public List<ByNetworkReviewTask> selectByNetworkReviewTaskList(ByNetworkReviewTask byNetworkReviewTask) {
        List<ByNetworkReviewTask> list = byNetworkReviewTaskMapper.selectByNetworkReviewTaskList(byNetworkReviewTask);
        for (ByNetworkReviewTask byNetworkReviewTask1 : list) {
            //任务类型字典转换
            byNetworkReviewTask1.setTaskTypeName(dictDataMapper.selectDictLabel("task_type", String.valueOf(byNetworkReviewTask1.getTaskType())));
            /**任务类型为综合时进行子任务名称转换*/
            if (byNetworkReviewTask1.getTaskType() == 0) {
                //子任务类型有值
                if (StrUtil.isNotEmpty(byNetworkReviewTask1.getSubTaskType())) {
                    String[] subTaskTypeArr = byNetworkReviewTask1.getSubTaskType().split(",");
                    String subTaskTypeName = "";
                    for (String type : subTaskTypeArr) {
                        subTaskTypeName += dictDataMapper.selectDictLabel("task_type", type) + "+";
                    }
                    byNetworkReviewTask1.setSubTaskTypeName(subTaskTypeName.substring(0, subTaskTypeName.length() - 1));
                    byNetworkReviewTask1.setTaskTypeName(byNetworkReviewTask1.getTaskTypeName() + "(" + subTaskTypeName.substring(0, subTaskTypeName.length() - 1) + ")");
                }
            }
            //操作权限--同级别人员可以操作，非同级别不可操作-上级也不允许操作
            SysUser sysUser1 = userMapper.selectUserById(byNetworkReviewTask1.getCreateUserId());
            SysDept sysDeptOpera = sysDeptMapper.selectDeptById(sysUser1.getDeptId());
            SysDept sysDeptLogin = sysDeptMapper.selectDeptById(SecurityUtils.getLoginUser().getUser().getDeptId());
            if (Arrays.asList(sysDeptOpera.getAncestors().split(",")).size() == Arrays.asList(sysDeptLogin.getAncestors().split(",")).size()) {
                byNetworkReviewTask1.setOperationPermissions(1);
            }
            //任务状态字典值转换
            byNetworkReviewTask1.setScoreModeName(dictDataMapper.selectDictLabel("score_mode", String.valueOf(byNetworkReviewTask1.getScoreMode())));
            //任务状态字典值转换
            byNetworkReviewTask1.setTaskStatusName(dictDataMapper.selectDictLabel("task_status", String.valueOf(byNetworkReviewTask1.getTaskStatus())));
            //媒体类型字典值转换
            byNetworkReviewTask1.setMediaTypeName(dictDataMapper.selectDictLabel("media_type", String.valueOf(byNetworkReviewTask1.getMediaType())));
            //创建人姓名
            byNetworkReviewTask1.setCreateUserName(userMapper.selectUserById(byNetworkReviewTask1.getCreateUserId()).getNickName());
            //绩点类型字典值转换
            byNetworkReviewTask1.setScoreTypeName(dictDataMapper.selectDictLabel("scoring_type", String.valueOf(byNetworkReviewTask1.getScoreType())));
            //获取子任务人员名称
            ByTaskToUser byTaskToUser = new ByTaskToUser();
            byTaskToUser.setTaskId(byNetworkReviewTask1.getId());
            List<ByTaskToUser> byTaskToUserList = byTaskToUserMapper.selectByTaskToUserList(byTaskToUser);
            //子任务操作员名称集合
            List<String> stringList = new ArrayList<>();
            for (ByTaskToUser byTaskToUser1 : byTaskToUserList) {
                SysUser sysUser = userMapper.selectUserById(byTaskToUser1.getOpUserId());
                if (sysUser != null) {
                    stringList.add(sysUser.getNickName());
                }
            }
            byNetworkReviewTask1.setUserNameList(stringList);
        }
        return list;
    }

    /**
     * 新增网评任务
     *
     * @param byNetworkReviewTask 网评任务
     * @return 结果
     */
    @Override
    @Transactional
    public int insertByNetworkReviewTask(ByNetworkReviewTask byNetworkReviewTask) {
        String title = byNetworkReviewTask.getTitle();
        if (CharSequenceUtil.isEmpty(title) || title.length() > 20) {
            throw new CustomException("请填写任务名称且在20字以内");
        }
        List<String> originalLinkList = byNetworkReviewTask.getOriginalLinkList();
        if (CollUtil.isNotEmpty(originalLinkList)) {
            //有链接
            for (String link : originalLinkList) {
                if (!(link.length() > 10 && link.length() < 1000)) {
                    throw new CustomException("链接长度请保持在10~1000");
                }
            }
            List<String> originalLinkListType = byNetworkReviewTask.getOriginalLinkListType();
            if (originalLinkList.size() != originalLinkListType.size()) {
                throw new CustomException("链接数量与链接类型数量不匹配");
            }
            dealHasLink(byNetworkReviewTask, originalLinkList);
        } else {
            //没有链接
            Long id = IdUtil.nextLong();
            buildTaskParam(byNetworkReviewTask, id);
            //没有链接默认为其他
            byNetworkReviewTask.setMediaType(0);
            byNetworkReviewTaskMapper.insertByNetworkReviewTask(byNetworkReviewTask);
            //处理文件关联
            List<Long> fileIds = byNetworkReviewTask.getFileIds();
            dealFile(fileIds, id);
            //每生成一条任务记录则要对应生成多条执行人任务子单
            List<String> userIdList = byNetworkReviewTask.getUserIdList();
            dealTaskUser(byNetworkReviewTask, userIdList);
        }

        return 1;
    }

    private void dealHasLink(ByNetworkReviewTask byNetworkReviewTask, List<String> originalLinkList) {
        //循环获取链接个数依次插入数据库
        for (int a = 0; a < originalLinkList.size(); a++) {
            String originalLink = byNetworkReviewTask.getOriginalLinkList().get(a);
            //平台类型
            byNetworkReviewTask.setMediaType(Integer.parseInt(byNetworkReviewTask.getOriginalLinkListType().get(a)));
            //填写链接
            byNetworkReviewTask.setOriginalLink(originalLink);

            Long id = IdUtil.nextLong();
            buildTaskParam(byNetworkReviewTask, id);
            byNetworkReviewTaskMapper.insertByNetworkReviewTask(byNetworkReviewTask);
            //处理文件关联
            List<Long> fileIds = byNetworkReviewTask.getFileIds();
            dealFile(fileIds, id);
            //每生成一条任务记录则要对应生成多条执行人任务子单
            List<String> userIdList = byNetworkReviewTask.getUserIdList();
            dealTaskUser(byNetworkReviewTask, userIdList);
        }
    }

    private void dealFile(List<Long> fileIds, Long id) {
        if (CollUtil.isNotEmpty(fileIds)) {
            List<TaskFile> taskFiles = new ArrayList<>();
            for (Long fileId : fileIds) {
                TaskFile taskFile = new TaskFile();
                taskFile.setTaskId(id);
                taskFile.setFileId(fileId);
                taskFiles.add(taskFile);
            }
            taskFileService.insertTaskFileList(taskFiles);
        }
    }

    private void dealTaskUser(ByNetworkReviewTask byNetworkReviewTask, List<String> userIdList) {
        for (String userId : userIdList) {
            //根据发布任务创建子任务单
            ByTaskToUser byTaskToUser = new ByTaskToUser();
            //主键
            byTaskToUser.setId(IdUtil.nextLong());
            //主任务ID
            byTaskToUser.setTaskId(byNetworkReviewTask.getId());
            //子任务状态 进行中
            byTaskToUser.setUserTaskStatus(1);
            //分配的网评员ID
            byTaskToUser.setOpUserId(Long.parseLong(userId));
            //根据人员ID获取该人员部门ID--此处和发短信公用只查询一次
            SysUser sysUser = userMapper.selectUserById(Long.parseLong(userId));
            byTaskToUser.setOpUserDeptId(sysUser.getDeptId());
            //创建时间
            byTaskToUser.setCreateTime(new Date());
            //创建人
            byTaskToUser.setCreateUserId(SecurityUtils.getLoginUser().getUser().getUserId());
            byTaskToUserMapper.insertByTaskToUser(byTaskToUser);
            //给分配人员进行短信通知
            //手机号为空则不发送
            String phonenumber = sysUser.getPhonenumber();
            if (StrUtil.isNotEmpty(phonenumber)) {
                MsgSendUtil.sendMsg(phonenumber, sysUser.getNickName());
            }
        }
    }

    private void buildTaskParam(ByNetworkReviewTask byNetworkReviewTask, Long id) {
        //生成ID
        byNetworkReviewTask.setId(id);
        //生成任务编号
        String dateTime = DateUtils.dateTime();
        Object taskNum = "";
        try {
            taskNum = redisCache.getCacheObject("task_num_" + dateTime);
        } catch (Exception e) {
        }
        /**如果redis没有生成任务编号则开始生成当日第一组编号*/
        if (taskNum == null || StrUtil.isEmpty(taskNum.toString())) {
            taskNum = dateTime + "001";
        }
        byNetworkReviewTask.setTaskNum(taskNum.toString());
        //使用后需要加一
        redisCache.setCacheObject("task_num_" + dateTime, Long.parseLong(taskNum.toString()) + 1);
        //任务状态进行中
        byNetworkReviewTask.setTaskStatus(1);
        //创建时间
        byNetworkReviewTask.setCtime(new Date());
        //创建人ID
        byNetworkReviewTask.setCreateUserId(SecurityUtils.getLoginUser().getUser().getUserId());
    }

    /**
     * 修改网评任务
     *
     * @param byNetworkReviewTask 网评任务
     * @return 结果
     */
    @Override
    public int updateByNetworkReviewTask(ByNetworkReviewTask byNetworkReviewTask) {
        /**先去除所有人员判断已经录入人员，再将未录入人员进行录入*/
        for (String userId : byNetworkReviewTask.getUserIdList()) {
            //
            ByTaskToUser byTaskToUser = new ByTaskToUser();
            //操作人ID
            byTaskToUser.setOpUserId(Long.parseLong(userId));
            //主任务ID
            byTaskToUser.setTaskId(byNetworkReviewTask.getId());
            //查询是否存在
            List<ByTaskToUser> list = byTaskToUserMapper.selectByTaskToUserList(byTaskToUser);
            //不存在则添加
            if (list.size() < 1) {
                //根据发布任务创建子任务单
                ByTaskToUser byTaskToUserNew = new ByTaskToUser();
                //主键
                byTaskToUserNew.setId(IdUtil.nextLong());
                //主任务ID
                byTaskToUserNew.setTaskId(byNetworkReviewTask.getId());
                //子任务状态 进行中
                byTaskToUserNew.setUserTaskStatus(1);
                //分配的网评员ID
                byTaskToUserNew.setOpUserId(Long.parseLong(userId));
                //根据人员ID获取该人员部门ID--此处和发短信公用只查询一次
                SysUser sysUser = userMapper.selectUserById(Long.parseLong(userId));
                byTaskToUser.setOpUserDeptId(sysUser.getDeptId());
                //创建时间
                byTaskToUserNew.setCtime(new Date());
                //创建人
                byTaskToUserNew.setCreateUserId(SecurityUtils.getLoginUser().getUser().getUserId());
                /**给分配人员进行短信通知*/
                //手机号为空则不发送
                if (StrUtil.isNotEmpty(sysUser.getPhonenumber())) {
                    MsgSendUtil.sendMsg(sysUser.getPhonenumber(), sysUser.getNickName());
                }
                byTaskToUserMapper.insertByTaskToUser(byTaskToUserNew);
            }
        }
        return byNetworkReviewTaskMapper.updateByNetworkReviewTask(byNetworkReviewTask);
    }

    /**
     * 修改网评任务完成时间并置状态为已完成
     *
     * @param taskId 主任务ID
     * @return 结果
     */
    @Override
    public int updateActualCompletionTime(Long taskId) {
        //所有任务已完成更新主任务完成时间
        return byNetworkReviewTaskMapper.updateActualCompletionTime(taskId);
    }

    /**
     * 作废网评任务
     *
     * @param taskIds 主任务ID数组
     * @return 结果
     */
    @Override
    @Transactional
    public int cancelByNetworkReviewTaskId(Long[] taskIds) {
        for (Long taskId : taskIds) {
            //将所有子任务更新为作废
            byTaskToUserMapper.cancelByTaskToUser(taskId, 4);
            ByNetworkReviewTask byNetworkReviewTask = new ByNetworkReviewTask();
            //主键
            byNetworkReviewTask.setId(taskId);
            //主任务状态--改为已作废
            byNetworkReviewTask.setTaskStatus(4);
            byNetworkReviewTaskMapper.updateByNetworkReviewTask(byNetworkReviewTask);
        }
        return 1;
    }

    /**
     * 批量删除网评任务
     *
     * @param ids 需要删除的网评任务ID
     * @return 结果
     */
    @Override
    public int deleteByNetworkReviewTaskByIds(Long[] ids) {
        return byNetworkReviewTaskMapper.deleteByNetworkReviewTaskByIds(ids);
    }

    /**
     * 删除网评任务信息
     *
     * @param id 网评任务ID
     * @return 结果
     */
    @Override
    public int deleteByNetworkReviewTaskById(Long id) {
        return byNetworkReviewTaskMapper.deleteByNetworkReviewTaskById(id);
    }

    /**
     * 获取链接类型
     *
     * @param url 链接地址
     * @return 返回此链接的媒体类型
     */
    @Override
    public String getLinkType(String url) {
        //指定初始下标为超出链接长的的数值,指定默认类型为其它
        int indexNum = 100000;
        String mediaType = "0";
        //任务媒体类型
        if (url.contains("weixin")) {
            //如果下标靠前则使用当前媒体类型
            if (url.indexOf("weixin") < indexNum && url.indexOf("weixin") > 0) {
                mediaType = "1";
                indexNum = url.indexOf("weixin");
            }
        }
        if (url.contains("weibo")) {
            //如果下标靠前则使用当前媒体类型
            if (url.indexOf("weibo") < indexNum && url.indexOf("weibo") > 0) {
                mediaType = "2";
                indexNum = url.indexOf("weibo");
            }
        }
        if (url.contains("kuaishou")) {
            //如果下标靠前则使用当前媒体类型
            if (url.indexOf("kuaishou") < indexNum && url.indexOf("kuaishou") > 0) {
                mediaType = "3";
                indexNum = url.indexOf("kuaishou");
            }
        }
        if (url.contains("xiaohongshu") || url.contains("xhs")) {
            //如果下标靠前则使用当前媒体类型
            if (url.indexOf("xiaohongshu") < indexNum && url.indexOf("xiaohongshu") > 0) {
                mediaType = "4";
                indexNum = url.indexOf("xiaohongshu");
            }
            if (url.indexOf("xhs") < indexNum && url.indexOf("xhs") > 0) {
                mediaType = "4";
                indexNum = url.indexOf("xhs");
            }
        }
        if (url.contains("douyin")) {
            //如果下标靠前则使用当前媒体类型
            if (url.indexOf("douyin") < indexNum && url.indexOf("douyin") > 0) {
                mediaType = "5";
                indexNum = url.indexOf("douyin");
            }
        }
        if (url.contains("toutiao")) {
            //如果下标靠前则使用当前媒体类型
            if (url.indexOf("toutiao") < indexNum && url.indexOf("toutiao") > 0) {
                mediaType = "6";
                indexNum = url.indexOf("toutiao");
            }
        }
        if (url.contains("tencent") || url.contains("news.qq") || url.contains("new.qq")) {
            //如果下标靠前则使用当前媒体类型
            if (url.indexOf("tencent") < indexNum && url.indexOf("tencent") > 0) {
                mediaType = "7";
                indexNum = url.indexOf("tencent");
            }
            if (url.indexOf("news.qq") < indexNum && url.indexOf("news.qq") > 0) {
                mediaType = "7";
                indexNum = url.indexOf("news.qq");
            }
            if (url.indexOf("new.qq") < indexNum && url.indexOf("new.qq") > 0) {
                mediaType = "7";
                indexNum = url.indexOf("new.qq");
            }
        }
        return mediaType;
    }
}
