package com.boryou.manage.service.impl;

import com.boryou.manage.domain.File;
import com.boryou.manage.domain.TaskFile;
import com.boryou.manage.mapper.TaskFileMapper;
import com.boryou.manage.service.TaskFileService;
import org.apache.ibatis.session.SqlSessionFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * 关联表Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Service
public class TaskFileServiceImpl implements TaskFileService {
    @Autowired
    private TaskFileMapper taskFileMapper;
    @Resource
    private SqlSessionFactory sqlSessionFactory;

    /**
     * 查询关联表
     *
     * @param taskId 关联表ID
     * @return 关联表
     */
    @Override
    public List<File> selectTaskFileById(Long taskId) {
        return taskFileMapper.selectTaskFileById(taskId);
    }

    /**
     * 查询关联表列表
     *
     * @param taskFile 关联表
     * @return 关联表
     */
    @Override
    public List<TaskFile> selectTaskFileList(TaskFile taskFile) {
        return taskFileMapper.selectTaskFileList(taskFile);
    }

    /**
     * 新增关联表
     *
     * @param taskFile 关联表
     * @return 结果
     */
    @Override
    public int insertTaskFile(TaskFile taskFile) {
        return taskFileMapper.insertTaskFile(taskFile);
    }

    /**
     * 修改关联表
     *
     * @param taskFile 关联表
     * @return 结果
     */
    @Override
    public int updateTaskFile(TaskFile taskFile) {
        return taskFileMapper.updateTaskFile(taskFile);
    }

    /**
     * 批量删除关联表
     *
     * @param taskIds 需要删除的关联表ID
     * @return 结果
     */
    @Override
    public int deleteTaskFileByIds(Long[] taskIds) {
        return taskFileMapper.deleteTaskFileByIds(taskIds);
    }

    /**
     * 删除关联表信息
     *
     * @param taskId 关联表ID
     * @return 结果
     */
    @Override
    public int deleteTaskFileById(Long taskId) {
        return taskFileMapper.deleteTaskFileById(taskId);
    }

    @Override
    public int insertTaskFileList(List<TaskFile> taskFiles) {
        return taskFileMapper.insertTaskFileList(taskFiles);
    }
}
