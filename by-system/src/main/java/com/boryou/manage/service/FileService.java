package com.boryou.manage.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.manage.domain.File;
import com.boryou.manage.domain.vo.FileV2VO;
import io.minio.errors.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.List;

/**
 * 文件管理Service接口
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
public interface FileService extends IService<File> {
    /**
     * 查询文件管理
     *
     * @param fileId 文件管理ID
     * @return 文件管理
     */
    public File selectFileById(Long fileId);

    File selectFileByFileName(String fileName);

    /**
     * 查询文件管理列表
     *
     * @param file 文件管理
     * @return 文件管理集合
     */
    public List<File> selectFileList(File file);

    /**
     * 新增文件管理
     *
     * @param file 文件管理
     * @return 结果
     */
    public int insertFile(File file);

    int insertFileList(List<File> file);

    /**
     * 修改文件管理
     *
     * @param file 文件管理
     * @return 结果
     */
    public int updateFile(File file);

    /**
     * 批量删除文件管理
     *
     * @param fileIds 需要删除的文件管理ID
     * @return 结果
     */
    public int deleteFileByIds(Long[] fileIds);

    /**
     * 删除文件管理信息
     *
     * @param fileId 文件管理ID
     * @return 结果
     */
    public int deleteFileById(Long fileId);

    /**
     * 普通文件上传
     *
     * @param file
     * @return
     * @throws IOException
     */
    String uploadFile(MultipartFile file) throws Exception;


    /**
     * 获取预览地址
     *
     * @param id
     * @return
     */
    String getUrlById(Long id) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException;

    /**
     * 下载 minio 文件
     *
     * @param id
     * @param response
     */
    void downloadFile(String id, HttpServletResponse response) throws Exception;

    List<FileV2VO> getUrlByFileIds(List<String> files);

    void filePublic(String fileId, HttpServletResponse response);
}
