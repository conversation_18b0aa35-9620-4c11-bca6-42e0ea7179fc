package com.boryou.manage.service.impl;

import com.boryou.common.utils.DateUtils;
import com.boryou.manage.domain.ByUploadFile;
import com.boryou.manage.mapper.ByUploadFileMapper;
import com.boryou.manage.service.IByUploadFileService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * 文件上传Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
@Service
public class ByUploadFileServiceImpl implements IByUploadFileService {
    @Autowired
    private ByUploadFileMapper byUploadFileMapper;

    /**
     * 查询文件上传
     *
     * @param id 文件上传ID
     * @return 文件上传
     */
    @Override
    public ByUploadFile selectByUploadFileById(Long id) {
        return byUploadFileMapper.selectByUploadFileById(id);
    }

    /**
     * 查询文件上传列表
     *
     * @param byUploadFile 文件上传
     * @return 文件上传
     */
    @Override
    public List<ByUploadFile> selectByUploadFileList(ByUploadFile byUploadFile) {
        return byUploadFileMapper.selectByUploadFileList(byUploadFile);
    }

    /**
     * 新增文件上传
     *
     * @param byUploadFile 文件上传
     * @return 结果
     */
    @Override
    public int insertByUploadFile(ByUploadFile byUploadFile) {
        byUploadFile.setDeleteStatus(0L);
        byUploadFile.setUploadTime(DateUtils.getNowDate());
        return byUploadFileMapper.insertByUploadFile(byUploadFile);
    }

    /**
     * 修改文件上传
     *
     * @param byUploadFile 文件上传
     * @return 结果
     */
    @Override
    public int updateByUploadFile(ByUploadFile byUploadFile) {
        byUploadFile.setUploadTime(DateUtils.getNowDate());
        return byUploadFileMapper.updateByUploadFile(byUploadFile);
    }

    /**
     * 批量删除文件上传
     *
     * @param ids 需要删除的文件上传ID
     * @return 结果
     */
    @Override
    public int deleteByUploadFileByIds(Long[] ids) {
        return byUploadFileMapper.deleteByUploadFileByIds(ids);
    }

    /**
     * 删除文件上传信息
     *
     * @param id 文件上传ID
     * @return 结果
     */
    @Override
    public int deleteByUploadFileById(Long id) {
        return byUploadFileMapper.deleteByUploadFileById(id);
    }
}
