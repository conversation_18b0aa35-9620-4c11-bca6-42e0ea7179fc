package com.boryou.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.boryou.common.config.MinioConfig;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.file.MinioUtil;
import com.boryou.manage.domain.File;
import com.boryou.manage.service.FileService;
import com.boryou.manage.service.MinioFileService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;

@Slf4j
@Service/*("fileServiceImpl")*/
public class MinioFileServiceImpl implements MinioFileService {
    @Resource
    private FileService fileService;

    @Override
    public File getFileById(Long id) {
        return null;
    }

    @Override
    public List<String> uploadFiles(MultipartFile[] file) {
        List<String> idList = new ArrayList<>();
        List<File> fileList = new ArrayList<>();
        try {
            String bucketName = MinioConfig.getBucketName();
            DateTime date = DateUtil.date();
            Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
            for (MultipartFile multipartFile : file) {
                String fileName = MinioUtil.uploadMinio(multipartFile);
                String originalFilename = multipartFile.getOriginalFilename();
                long size = multipartFile.getSize();
                File fileSql = new File();
                long snowflakeNextId = IdUtil.getSnowflakeNextId();
                idList.add(String.valueOf(snowflakeNextId));
                fileSql.setFileId(snowflakeNextId);
                fileSql.setFileName(fileName);
                fileSql.setBucketName(bucketName);
                fileSql.setOriginal(originalFilename);
                String type = CharSequenceUtil.subAfter(originalFilename, ".", true);
                fileSql.setType(type);
                fileSql.setFileSize(size);
                fileSql.setDelFlag("0");
                fileSql.setCreateBy(userId);
                fileSql.setCreateTime(date);
                fileSql.setUpdateBy(userId);
                fileSql.setUpdateTime(date);
                fileList.add(fileSql);
            }
        } catch (IOException e) {
            log.warn("上传失败: {}", e.getMessage(), e);
            throw new CustomException("上传失败");
        }
        if (CollUtil.isNotEmpty(fileList)) {
            fileService.insertFileList(fileList);
        }
        return idList;
    }

    @Override
    public void downloadFile(File file, HttpServletResponse response) {
        if (Objects.isNull(file)) {
            throw new CustomException("找不到文件!");
        }
        String fileName = file.getFileName();
        String original = file.getOriginal();
        if (CharSequenceUtil.isBlank(fileName)) {
            throw new CustomException("找不到文件!");
        }
        try {
            MinioUtil.download(fileName, original, response);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("下载失败!");
        }
    }

    @Override
    public InputStream downloadInputStream(String fileName) {
        if (CharSequenceUtil.isBlank(fileName)) {
            throw new CustomException("找不到文件!");
        }
        try {
            return MinioUtil.downloadInputStream(fileName);
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("下载失败!");
        }
    }
}
