package com.boryou.manage.service;

import com.boryou.manage.domain.ByTaskToUser;

import java.util.List;

/**
 * 网评员Service接口
 *
 * <AUTHOR>
 */
public interface IByTaskToUserService {

    /**
     * 查询任务与人员关联
     *
     * @param id 任务与人员关联ID
     * @return 任务与人员关联
     */
    public ByTaskToUser selectByTaskToUserById(Long id);

    /**
     * 查询任务与人员关联列表
     *
     * @param byTaskToUser 任务与人员关联
     * @return 任务与人员关联集合
     */
    public List<ByTaskToUser> selectByTaskToUserList(ByTaskToUser byTaskToUser);

    /**
     * 网评员查询自己的任务信息列表
     *
     * @param byTaskToUser 任务
     * @return 任务集合
     */
    public List<ByTaskToUser> selectUserTaskList(ByTaskToUser byTaskToUser);

    /**
     * 网评员查询自己的任务信息详情
     *
     * @param id 任务ID
     * @return 任务与人员关联集合
     */
    public ByTaskToUser selectUserTaskById(Long id, Long userId);

    /**
     * 新增任务与人员关联
     *
     * @param byTaskToUser 任务与人员关联
     * @return 结果
     */
    public int insertByTaskToUser(ByTaskToUser byTaskToUser);

    /**
     * 修改任务与人员关联
     *
     * @param byTaskToUser 任务与人员关联
     * @return 结果
     */
    public int updateByTaskToUser(ByTaskToUser byTaskToUser);

    /**
     * 网评员提交完成任务
     *
     * @param byTaskToUser 任务与人员关联
     * @return 结果
     */
    public int finishTaskByTaskToUser(ByTaskToUser byTaskToUser);

    /**
     * 批量删除任务与人员关联
     *
     * @param ids 需要删除的任务与人员关联ID
     * @return 结果
     */
    public int deleteByTaskToUserByIds(Long[] ids);

    /**
     * 删除任务与人员关联信息
     *
     * @param id 任务与人员关联ID
     * @return 结果
     */
    public int deleteByTaskToUserById(Long id);

}
