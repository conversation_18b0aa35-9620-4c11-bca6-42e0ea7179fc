package com.boryou.manage.service;

import com.boryou.manage.domain.File;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.util.List;

public interface MinioFileService {
    File getFileById(Long id);

    List<String> uploadFiles(MultipartFile[] file);

    void downloadFile(File file, HttpServletResponse response);

    InputStream downloadInputStream(String fileName);
}
