package com.boryou.manage.service.impl;

import cn.hutool.core.date.BetweenFormatter;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.DateUtils;
import com.boryou.common.utils.IdUtil;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.manage.domain.ByNetworkReviewTask;
import com.boryou.manage.domain.ByTaskToUser;
import com.boryou.manage.domain.ByUploadFile;
import com.boryou.manage.mapper.ByNetworkReviewTaskMapper;
import com.boryou.manage.mapper.ByTaskToUserMapper;
import com.boryou.manage.mapper.ByUploadFileMapper;
import com.boryou.manage.service.IByNetworkReviewTaskService;
import com.boryou.manage.service.IByTaskToUserService;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.mapper.SysDictDataMapper;
import com.boryou.system.mapper.SysUserMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 网评员Service业务层处理
 *
 * <AUTHOR>
 */
@Service
public class ByTaskToUserServiceImpl implements IByTaskToUserService {

    @Resource
    private ByTaskToUserMapper byTaskToUserMapper;
    @Resource
    private SysDictDataMapper dictDataMapper;
    @Resource
    private ByNetworkReviewTaskMapper byNetworkReviewTaskMapper;
    @Resource
    private ByUploadFileMapper byUploadFileMapper;
    @Resource
    private SysUserMapper userMapper;
    @Resource
    private SysDeptMapper sysDeptMapper;
    @Resource
    private IByNetworkReviewTaskService byNetworkReviewTaskService;

    /**
     * 查询任务与人员关联
     *
     * @param id 任务与人员关联ID
     * @return 任务与人员关联
     */
    @Override
    public ByTaskToUser selectByTaskToUserById(Long id) {
        return byTaskToUserMapper.selectByTaskToUserById(id);
    }

    /**
     * 查询任务与人员关联列表
     *
     * @param byTaskToUser 任务与人员关联
     * @return 任务与人员关联
     */
    @Override
    public List<ByTaskToUser> selectByTaskToUserList(ByTaskToUser byTaskToUser) {
        /**如果查询的子任务状态值为-1则置为空查询全部状态*/
        if (byTaskToUser.getUserTaskStatus() == -1) {
            byTaskToUser.setUserTaskStatus(null);
        }
        List<ByTaskToUser> list = byTaskToUserMapper.selectByTaskToUserList(byTaskToUser);
        for (ByTaskToUser taskToUser : list) {
            //任务类型字典转换
            taskToUser.setTaskTypeName(dictDataMapper.selectDictLabel("task_type", String.valueOf(taskToUser.getTaskType())));
            //任务状态字典值转换
            taskToUser.setTaskStatusName(dictDataMapper.selectDictLabel("task_status", String.valueOf(taskToUser.getTaskStatus())));
            //媒体类型字典值转换
            taskToUser.setMediaTypeName(dictDataMapper.selectDictLabel("media_type", String.valueOf(taskToUser.getMediaType())));
            //网评员任务状态字典值转换
            taskToUser.setUserTaskStatusName(dictDataMapper.selectDictLabel("sub_task_status", String.valueOf(taskToUser.getUserTaskStatus())));
            /**团队模式下需要展示部门名称*/
            //网评员所在部门名称
            taskToUser.setOpUserDeptName(sysDeptMapper.selectDeptById(taskToUser.getOpUserDeptId()).getDeptName());
            List<ByUploadFile> fileList = byUploadFileMapper.selectByUploadFileByIds(taskToUser.getFileId());
            List<String> files = fileList.stream().map(ByUploadFile::getAddress).collect(Collectors.toList());
            taskToUser.setFileUrls(String.join(",", files));
            List<ByUploadFile> picList = byUploadFileMapper.selectByUploadFileByIds(taskToUser.getPicId());
            List<String> pics = picList.stream().map(ByUploadFile::getAddress).collect(Collectors.toList());
            taskToUser.setPicUrls(String.join(",", pics));
            //网评员名称
            SysUser sysUser = userMapper.selectUserById(taskToUser.getOpUserId());
            if (sysUser != null) {
                taskToUser.setOpUserName(sysUser.getNickName());
            }
        }
        return list;
    }

    /**
     * 网评员查询自己的任务信息列表
     *
     * @param byTaskToUser 任务
     * @return 任务集合
     */
    @Override
    public List<ByTaskToUser> selectUserTaskList(ByTaskToUser byTaskToUser) {
        List<ByTaskToUser> byTaskToUsers = byTaskToUserMapper.selectUserTaskList(byTaskToUser);
        for (ByTaskToUser taskToUser : byTaskToUsers) {
            //任务类型字典转换
            taskToUser.setTaskTypeName(dictDataMapper.selectDictLabel("task_type", String.valueOf(taskToUser.getTaskType())));
            /**任务类型为综合时进行子任务名称转换*/
            if (taskToUser.getTaskType() == 0) {
                //子任务类型有值
                if (StrUtil.isNotEmpty(taskToUser.getSubTaskType())) {
                    String[] subTaskTypeArr = taskToUser.getSubTaskType().split(",");
                    String subTaskTypeName = "";
                    for (String type : subTaskTypeArr) {
                        subTaskTypeName += dictDataMapper.selectDictLabel("task_type", type) + "+";
                    }
                    taskToUser.setSubTaskTypeName(subTaskTypeName.substring(0, subTaskTypeName.length() - 1));
                    taskToUser.setTaskTypeName(taskToUser.getTaskTypeName() + "(" + subTaskTypeName.substring(0, subTaskTypeName.length() - 1) + ")");
                }
            }
            //任务状态字典值转换
            taskToUser.setScoreModeName(dictDataMapper.selectDictLabel("score_mode", String.valueOf(taskToUser.getScoreMode())));
            //任务状态字典值转换
            taskToUser.setTaskStatusName(dictDataMapper.selectDictLabel("task_status", String.valueOf(taskToUser.getTaskStatus())));
            //媒体类型字典值转换
            taskToUser.setMediaTypeName(dictDataMapper.selectDictLabel("media_type", String.valueOf(taskToUser.getMediaType())));
            //网评员任务状态字典值转换
            taskToUser.setUserTaskStatusName(dictDataMapper.selectDictLabel("sub_task_status", String.valueOf(taskToUser.getUserTaskStatus())));
            //绩点类型字典值转换
            taskToUser.setScoreTypeName(dictDataMapper.selectDictLabel("scoring_type", String.valueOf(taskToUser.getScoreType())));

            taskToUser.setTaskAge(DateUtil.formatBetween(taskToUser.getCtime(), DateUtil.date(), BetweenFormatter.Level.DAY));
        }
        return byTaskToUsers;
    }

    /**
     * 网评员查询自己的任务信息
     *
     * @param id,userId 任务ID,登录人ID
     * @return 任务集合
     */
    @Override
    public ByTaskToUser selectUserTaskById(Long id, Long userId) {
        ByTaskToUser byTaskToUser = byTaskToUserMapper.selectUserTaskById(id);
        //任务类型字典转换
        byTaskToUser.setTaskTypeName(dictDataMapper.selectDictLabel("task_type", String.valueOf(byTaskToUser.getTaskType())));
        /**任务类型为综合时进行子任务名称转换*/
        if (byTaskToUser.getTaskType() == 0) {
            //子任务类型有值
            if (StrUtil.isNotEmpty(byTaskToUser.getSubTaskType())) {
                String[] subTaskTypeArr = byTaskToUser.getSubTaskType().split(",");
                String subTaskTypeName = "";
                for (String type : subTaskTypeArr) {
                    subTaskTypeName += dictDataMapper.selectDictLabel("task_type", type) + "+";
                }
                byTaskToUser.setSubTaskTypeName(subTaskTypeName.substring(0, subTaskTypeName.length() - 1));
                byTaskToUser.setTaskTypeName(byTaskToUser.getTaskTypeName() + "(" + subTaskTypeName.substring(0, subTaskTypeName.length() - 1) + ")");
            }
        }
        //任务状态字典值转换
        byTaskToUser.setScoreModeName(dictDataMapper.selectDictLabel("score_mode", String.valueOf(byTaskToUser.getScoreMode())));
        //任务状态字典值转换
        byTaskToUser.setTaskStatusName(dictDataMapper.selectDictLabel("task_status", String.valueOf(byTaskToUser.getTaskStatus())));
        //媒体类型字典值转换
        byTaskToUser.setMediaTypeName(dictDataMapper.selectDictLabel("media_type", String.valueOf(byTaskToUser.getMediaType())));
        //网评员任务状态字典值转换
        byTaskToUser.setUserTaskStatusName(dictDataMapper.selectDictLabel("sub_task_status", String.valueOf(byTaskToUser.getUserTaskStatus())));
        Long taskId = byTaskToUser.getTaskId();
        byTaskToUser.setFiles(byNetworkReviewTaskService.getTaskFileList(taskId));
        return byTaskToUser;
    }

    /**
     * 新增任务与人员关联
     *
     * @param byTaskToUser 任务与人员关联
     * @return 结果
     */
    @Override
    public int insertByTaskToUser(ByTaskToUser byTaskToUser) {
        byTaskToUser.setId(IdUtil.nextLong());
        byTaskToUser.setCtime(DateUtils.getNowDate());
        byTaskToUser.setUserTaskStatus(1);
        return byTaskToUserMapper.insertByTaskToUser(byTaskToUser);
    }

    /**
     * 修改任务与人员关联
     *
     * @param byTaskToUser 任务与人员关联
     * @return 结果
     */
    @Override
    public int updateByTaskToUser(ByTaskToUser byTaskToUser) {
        return byTaskToUserMapper.updateByTaskToUser(byTaskToUser);
    }

    /**
     * 网评员提交完成任务
     *
     * @param byTaskToUser 任务与人员关联
     * @return 结果
     */
    @Override
    public int finishTaskByTaskToUser(ByTaskToUser byTaskToUser) {
        byTaskToUser.setFinishTime(DateUtils.getNowDate());
        byTaskToUser.setUserTaskStatus(2);
        /**现在逻辑调整为具体任务得分
         * 得分数值为主任务类型与人员自身等级
         * 当主任务为直接任务时 直接就获取主任务分配分数
         * 当主任务为影响任务时 分数为影响占比系数*人员自身等级*/
        ByNetworkReviewTask bnrt = byNetworkReviewTaskMapper.selectByNetworkReviewTaskById(byTaskToUser.getTaskId());
        //计分类型为1时直接计分
        if (1 == bnrt.getScoreType()) {
            byTaskToUser.setTaskGrade(bnrt.getScoreGrade());
        }
        //计分类型为2时影响力计分
        if (2 == bnrt.getScoreType()) {
            SysUser sysUser = SecurityUtils.getLoginUser().getUser();
            System.out.println(sysUser.toString());
            byTaskToUser.setTaskGrade(String.valueOf(Integer.parseInt(bnrt.getScoreGrade()) * SecurityUtils.getLoginUser().getUser().getUserGrade()));
        }
        if (byTaskToUserMapper.updateByTaskToUser(byTaskToUser) > 0) {
            byTaskToUser = byTaskToUserMapper.selectByTaskToUserById(byTaskToUser.getId());
            ByTaskToUser byTaskToUser2 = new ByTaskToUser();
            /**查询任务下所有的网评员任务*/
            byTaskToUser2.setTaskId(byTaskToUser.getTaskId());
            List<ByTaskToUser> taskToUsers = byTaskToUserMapper.selectUserTaskList(byTaskToUser2);
            //对未完成的人员进行统计
            int incompleteCount = 0;
            boolean finishState = true;
            for (ByTaskToUser taskToUser : taskToUsers) {
                if (taskToUser.getUserTaskStatus() != 2) {
                    incompleteCount++;
                    finishState = false;
                }
            }
            int completeDegree = (incompleteCount * 100) / taskToUsers.size();
            //团队模式式加入任务总进度
            if (bnrt.getScoreMode() == 1) {
                //将完成度更新
                byNetworkReviewTaskMapper.updateCompleteDegree(byTaskToUser.getTaskId(), String.valueOf(completeDegree));
            }
            if (finishState) {
                /**将主任务改为已完成*/
                ByNetworkReviewTask byNetworkReviewTask = new ByNetworkReviewTask();
                byNetworkReviewTask.setId(byTaskToUser.getTaskId());
                byNetworkReviewTask.setActualCompletionTime(DateUtils.getNowDate());
                byNetworkReviewTask.setTaskStatus(3);
                return byNetworkReviewTaskMapper.updateByNetworkReviewTask(byNetworkReviewTask);
            } else {
                return 1;
            }
        }
        return 0;
    }

    /**
     * 批量删除任务与人员关联
     *
     * @param ids 需要删除的任务与人员关联ID
     * @return 结果
     */
    @Override
    public int deleteByTaskToUserByIds(Long[] ids) {
        return byTaskToUserMapper.deleteByTaskToUserByIds(ids);
    }

    /**
     * 删除任务与人员关联信息
     *
     * @param id 任务与人员关联ID
     * @return 结果
     */
    @Override
    public int deleteByTaskToUserById(Long id) {
        return byTaskToUserMapper.deleteByTaskToUserById(id);
    }

}
