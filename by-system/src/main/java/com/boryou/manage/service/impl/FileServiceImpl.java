package com.boryou.manage.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.DateUtils;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.file.FileUploadUtils;
import com.boryou.common.utils.file.MinioUtil;
import com.boryou.manage.constant.FileConstant;
import com.boryou.manage.domain.File;
import com.boryou.manage.domain.vo.FileV2VO;
import com.boryou.manage.mapper.FileMapper;
import com.boryou.manage.service.FileService;
import io.minio.GetPresignedObjectUrlArgs;
import io.minio.MinioClient;
import io.minio.errors.*;
import io.minio.http.Method;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.net.URLEncoder;
import java.security.InvalidKeyException;
import java.security.NoSuchAlgorithmException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static com.boryou.common.config.MinioConfig.getBucketName;

/**
 * 文件管理Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-01-26
 */
@Service
@RequiredArgsConstructor
@Slf4j
public class FileServiceImpl extends ServiceImpl<FileMapper, File> implements FileService {

    @Value("${minio.bucket-name}")
    private String bucket;
    private final FileMapper fileMapper;
    private final MinioClient minioClient;

    @Override
    public String uploadFile(MultipartFile file) throws Exception {
        // 上传到 minio
        String fileName = FileUploadUtils.uploadMinio(file);

        File item = new File();
        item.setBucketName(getBucketName());
        item.setOriginal(file.getOriginalFilename());
        item.setFileName(fileName.substring(fileName.lastIndexOf(bucket) + bucket.length()));
        item.setType(fileName.substring(fileName.lastIndexOf(".") + 1));
        item.setDelFlag("0");
        item.setFileSize(file.getSize() / 1024);

        // 设置创建人信息
        SysUser user = SecurityUtils.getLoginUser().getUser();
        item.setCreateBy(user.getUserId());
        item.setUpdateBy(user.getUserId());

        this.save(item);
        return String.valueOf(item.getFileId());
    }

    @Override
    public String getUrlById(Long id) throws ServerException, InsufficientDataException, ErrorResponseException, IOException, NoSuchAlgorithmException, InvalidKeyException, InvalidResponseException, XmlParserException, InternalException {
        File file = this.getById(id);
        if (file == null) {
            throw new CustomException("文件不存在");
        }
        return minioClient.getPresignedObjectUrl(GetPresignedObjectUrlArgs.builder()
                .method(Method.GET)
                .bucket(bucket)
                .object(file.getFileName())
                .build());
    }

    @Override
    public void downloadFile(String id, HttpServletResponse response) throws Exception {
        File file = this.getById(id);
        String objectName = file.getFileName();

        InputStream stream = MinioUtil.download(bucket, objectName);
        ServletOutputStream output = response.getOutputStream();

        response.setHeader("Content-Disposition", "attachment;filename=" + URLEncoder.encode(file.getOriginal(), "UTF-8"));
        response.setContentType("application/octet-stream");
        response.setCharacterEncoding("UTF-8");
        IOUtils.copy(stream, output);
    }


    /**
     * 查询文件管理
     *
     * @param fileId 文件管理ID
     * @return 文件管理
     */
    @Override
    public File selectFileById(Long fileId) {
        return fileMapper.selectFileById(fileId);
    }

    @Override
    public File selectFileByFileName(String fileName) {
        return fileMapper.selectFileByFileName(fileName);
    }

    /**
     * 查询文件管理列表
     *
     * @param file 文件管理
     * @return 文件管理
     */
    @Override
    public List<File> selectFileList(File file) {
        return fileMapper.selectFileList(file);
    }

    /**
     * 新增文件管理
     *
     * @param file 文件管理
     * @return 结果
     */
    @Override
    public int insertFile(File file) {
        file.setCreateTime(DateUtils.getNowDate());
        return fileMapper.insertFile(file);
    }

    @Override
    public int insertFileList(List<File> file) {
        return fileMapper.insertFileList(file);
    }

    /**
     * 修改文件管理
     *
     * @param file 文件管理
     * @return 结果
     */
    @Override
    public int updateFile(File file) {
        file.setUpdateTime(DateUtils.getNowDate());
        return fileMapper.updateFile(file);
    }

    /**
     * 批量删除文件管理
     *
     * @param fileIds 需要删除的文件管理ID
     * @return 结果
     */
    @Override
    public int deleteFileByIds(Long[] fileIds) {
        return fileMapper.deleteFileByIds(fileIds);
    }

    /**
     * 删除文件管理信息
     *
     * @param fileId 文件管理ID
     * @return 结果
     */
    @Override
    public int deleteFileById(Long fileId) {
        return fileMapper.deleteFileById(fileId);
    }

    @Override
    public List<FileV2VO> getUrlByFileIds(List<String> fileIds) {
        if (CollUtil.isEmpty(fileIds)) {
            throw new CustomException("id不能为空");
        }
        List<File> files = this.lambdaQuery()
                .in(File::getFileId, fileIds)
                .eq(File::getDelFlag, 0)
                .list();
        if (CollUtil.isEmpty(files)) {
            return Collections.emptyList();
        }
        List<FileV2VO> fileV2VOList = new ArrayList<>();
        for (File file : files) {
            Long fileId = file.getFileId();
            String fileName = file.getFileName();
            String original = file.getOriginal();
            String type = file.getType();
            // String urlByFileName = MinioUtil.getUrlByFileName(fileName);
            fileV2VOList.add(FileV2VO.builder()
                    .key(String.valueOf(fileId))
                    .name(original)
                    .url(FileConstant.FILE_URL_PRE + fileId)
                    .type(type)
                    .build());
        }
        return fileV2VOList;
    }

    public void filePublic(String fileId, HttpServletResponse response) {
        if (CharSequenceUtil.isBlank(fileId)) {
            return;
        }
        File one = this.lambdaQuery()
                .eq(File::getFileId, fileId)
                .eq(File::getDelFlag, 0)
                .one();

        if (one == null) {
            return;
        }
        String fileKey = one.getFileName();
        String url = MinioUtil.getUrlByFileName(fileKey);

        String fileName = one.getFileName();

        try (HttpResponse execute = HttpUtil.createGet(url).execute()) {
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(fileName, Constants.UTF8));
            response.setCharacterEncoding(Constants.UTF8);

            //response.setContentType(contentType);
            InputStream in = execute.bodyStream();
            ServletOutputStream out = response.getOutputStream();
            org.apache.commons.compress.utils.IOUtils.copy(in, out);
        } catch (Exception e) {
            log.error("filePublic 有问题: {}", e.getMessage(), e);
            throw new CustomException("下载失败!");
        }

    }

}
