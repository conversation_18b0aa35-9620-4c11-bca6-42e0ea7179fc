package com.boryou.manage.service;

import com.boryou.manage.domain.ByNetworkReviewTask;
import com.boryou.manage.domain.vo.FileVO;

import java.util.List;
import java.util.Map;

/**
 * 网评任务Service接口
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
public interface IByNetworkReviewTaskService {
    /**
     * 查询网评任务
     *
     * @param id 网评任务ID
     * @return 网评任务
     */
    public ByNetworkReviewTask selectByNetworkReviewTaskById(Long id, Integer subTaskStatus);

    List<FileVO> getTaskFileList(Long id);

    /**
     * 根据任务ID查询网评任务各组织进度
     *
     * @param id 网评任务ID
     * @return 网评任务
     */
    public List<Map<String, String>> selectByNetworkReviewTaskScheduleById(Long id);

    /**
     * 查询网评任务列表
     *
     * @param byNetworkReviewTask 网评任务
     * @return 网评任务集合
     */
    public List<ByNetworkReviewTask> selectByNetworkReviewTaskList(ByNetworkReviewTask byNetworkReviewTask);

    /**
     * 新增网评任务
     *
     * @param byNetworkReviewTask 网评任务
     * @return 结果
     */
    public int insertByNetworkReviewTask(ByNetworkReviewTask byNetworkReviewTask);

    /**
     * 修改网评任务 --只做人员修改
     *
     * @param byNetworkReviewTask 网评任务
     * @return 结果
     */
    public int updateByNetworkReviewTask(ByNetworkReviewTask byNetworkReviewTask);


    /**
     * 更新网评主任务完成时间
     *
     * @param taskId 主任务ID
     * @return 结果
     */
    public int updateActualCompletionTime(Long taskId);

    /**
     * 作废网评任务
     *
     * @param taskId 主任务ID
     * @return 结果
     */
    public int cancelByNetworkReviewTaskId(Long[] taskId);

    /**
     * 批量删除网评任务
     *
     * @param ids 需要删除的网评任务ID
     * @return 结果
     */
    public int deleteByNetworkReviewTaskByIds(Long[] ids);

    /**
     * 删除网评任务信息
     *
     * @param id 网评任务ID
     * @return 结果
     */
    public int deleteByNetworkReviewTaskById(Long id);

    /**
     * 获取链接类型
     *
     * @param url 链接地址
     * @return 结果
     */
    public String getLinkType(String url);
}
