package com.boryou.manage.domain;

import com.boryou.common.annotation.Excel;
import com.boryou.common.core.domain.BaseEntity;
import com.boryou.manage.domain.vo.FileVO;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;
import java.util.List;

/**
 * 网评任务对象 by_network_review_task
 *
 * <AUTHOR>
 * @date 2023-03-16
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ByNetworkReviewTask extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 任务编号
     */
    @Excel(name = "任务编号")
    private String taskNum;

    /**
     * 任务标题
     */
    @Excel(name = "任务标题")
    private String title;

    /**
     * 任务介绍
     */
    @Excel(name = "任务介绍")
    private String taskIntroduce;

    /**
     * 原文链接
     */
    @Excel(name = "原文链接")
    private String originalLink;

    /**
     * 任务类型  点赞 举报 转发 综合 等 单选
     */
//    @Excel(name = "任务类型  综合 点赞 举报 转发 综合 等 单选")
    private Integer taskType;

    /**
     * 任务类型  点赞 举报 转发 综合 等 单选
     */
//    @Excel(name = "任务类型  点赞 举报 转发 点赞 阅读  子任务可多选依赖于综合任务")
    private String subTaskType;

    /**
     * 任务类型名称
     */
    private String taskTypeName;

    /**
     * 子任务类型名称
     */
    private String subTaskTypeName;

    /**
     * 通知类型 系统通知 短信通知 可多选
     */
    @Excel(name = "通知类型 系统通知 短信通知 可多选")
    private String noticeType;

    /**
     * 媒体类型  微博 微信 抖音 快手 其它 等
     */
    @Excel(name = "媒体类型  微博 微信 抖音 快手 其它 等")
    private Integer mediaType;

    /**
     * 媒体类型名称
     */
    private String mediaTypeName;

    /**
     * 任务截止完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务截止完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date accomplishTime;

    /**
     * 任务实际完成时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "任务截止完成时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date actualCompletionTime;

    /**
     * 任务状态 未发布 进行中 已完成 已作废
     */
    @Excel(name = "任务状态 未发布 进行中 已完成 已作废")
    private Integer taskStatus;

    /**
     * 任务状态名称
     */
    private String taskStatusName;

    /**
     * 信息创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "信息创建时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date ctime;

    /**
     * 创建人ID
     */
    @Excel(name = "创建人ID")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long createUserId;

    /**
     * 创建人名称
     */
    private String createUserName;

    /**
     * 多条链接同传
     */
    private List<String> originalLinkList;

    /**
     * 多条链接同传
     */
    private List<String> originalLinkListType;

    /**
     * 多名网评员ID
     */
    private List<String> userIdList;

    /**
     * 部门下员工ID
     */
    private Long[] deptUserIdList;

    /**
     * 多名网评员名称
     */
    private List<String> userNameList;

    /**
     * 查询开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String selectStartTime;

    /**
     * 查询结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String selectEndTime;
    /**
     * 子任务数据列表
     */
    private List<ByTaskToUser> byTaskToUserList;

    /**
     * 组织与用户的数组结构--记录到数据库给前端用作回显
     */
    private String orgUserArr;

    /**
     * 排序方式
     */
    private String sortWay;

    /**
     * 任务编码增加排序
     */
    private String taskNumSort;

    /**
     * 评分模式
     */
    private Integer scoreMode;

    /**
     * 评分模式名称
     */
    private String scoreModeName;

    /**
     * 评分类型
     */
    @Excel(name = "评分类型")
    private Integer scoreType;

    /**
     * 评分类型名称
     */
    private String scoreTypeName;

    /**
     * 评分绩点
     */
    @Excel(name = "评分绩点")
    private String scoreGrade;

    /**
     * 任务完成度
     */
    private String completeDegree;

    /**
     * 操作权限  1可操作  0不可操作
     */
    private Integer operationPermissions = 0;
    private List<Long> fileIds;
    private List<FileVO> files;
}
