package com.boryou.manage.domain;

import com.boryou.common.annotation.Excel;
import com.boryou.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 文件上传对象 by_upload_file
 *
 * <AUTHOR>
 * @date 2023-03-22
 */
@Data
public class ByUploadFile extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 服务器存放地址
     */
    @Excel(name = "服务器存放地址")
    private String address;

    /**
     * 文件名称
     */
    @Excel(name = "文件名称")
    private String fileName;

    /**
     * 文件类型
     */
    @Excel(name = "文件类型")
    private String fileType;

    /**
     * 单位：B
     */
    @Excel(name = "单位：B")
    private Long fileSize;

    /**
     * 是否删除：0未删除 1已删除（逻辑删除）
     */
    @Excel(name = "是否删除：0未删除 1已删除", readConverterExp = "逻=辑删除")
    private Long deleteStatus;

    /**
     * 上传时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd")
    @Excel(name = "上传时间", width = 30, dateFormat = "yyyy-MM-dd")
    private Date uploadTime;
}
