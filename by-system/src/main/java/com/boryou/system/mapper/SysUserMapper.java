package com.boryou.system.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.common.core.domain.entity.SysUser;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 用户表 数据层
 *
 * <AUTHOR>
 */
public interface SysUserMapper extends BaseMapper<SysUser> {
    /**
     * 根据条件分页查询用户列表
     *
     * @param sysUser 用户信息
     * @return 用户信息集合信息
     */
    public List<SysUser> selectUserList(SysUser sysUser);

    /**
     * 通过用户名查询用户
     *
     * @param userName 用户名
     * @return 用户对象信息
     */
    public SysUser selectUserByUserName(String userName);

    /**
     * 通过用户ID查询用户
     *
     * @param userId 用户ID
     * @return 用户对象信息
     */
    public SysUser selectUserById(Long userId);

    public SysUser selectUserByIdStr(String userId);

    /**
     * 通过用户部门ID查询用户
     *
     * @param deptId 部门ID
     * @return 用户对象信息
     */
    public List<SysUser> selectUserListByDeptId(Long deptId);

    List<Long> selectAllUserIdByDeptId(Long deptId);

    /**
     * 通过用户部门ID查询用户--只查询状态正常用户
     *
     * @param deptId 部门ID
     * @return 用户对象信息
     */
    public List<SysUser> selectUserListByDeptIdNew(Long deptId);

    /**
     * 新增用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int insertUser(SysUser user);

    /**
     * 修改用户信息
     *
     * @param user 用户信息
     * @return 结果
     */
    public int updateUser(SysUser user);

    /**
     * 修改用户头像
     *
     * @param userName 用户名
     * @param avatar   头像地址
     * @return 结果
     */
    public int updateUserAvatar(@Param("userName") String userName, @Param("avatar") String avatar);

    /**
     * 重置用户密码
     *
     * @param userName 用户名
     * @param password 密码
     * @return 结果
     */
    public int resetUserPwd(@Param("userName") String userName, @Param("password") String password);

    /**
     * 通过用户ID删除用户
     *
     * @param userId 用户ID
     * @return 结果
     */
    public int deleteUserById(Long userId);

    /**
     * 批量删除用户信息
     *
     * @param userIds 需要删除的用户ID
     * @return 结果
     */
    public int deleteUserByIds(Long[] userIds);

    /**
     * 校验用户名称是否唯一
     *
     * @param userName 用户名称
     * @return 结果
     */
    public int checkUserNameUnique(String userName);

    /**
     * 校验手机号码是否唯一
     *
     * @param phonenumber 手机号码
     * @return 结果
     */
    public SysUser checkPhoneUnique(String phonenumber);

    /**
     * 校验email是否唯一
     *
     * @param email 用户邮箱
     * @return 结果
     */
    public SysUser checkEmailUnique(String email);

    /**
     * 根据手机号查询用户信息
     *
     * @param phone
     * @return
     */
    SysUser getUserByTelephone(String phone);

    List<SysUser> getDeptUser(@Param("deptIds") List<Long> deptIds);

    List<SysUser> selectDeptUserByDeptIds(@Param("deptIds") List<String> deptIds);


    /**
     * 查找服务于某公司的所有分析师
     *
     * @param deptId
     * @return
     */
    List<String> fxsList(Long deptId);

    /**
     * 获取审核人员
     *
     * @param deptId
     * @return
     */
    List<String> shryList(Long deptId);


    List<SysUser> selectUserLists(SysUser user);

    List<SysUser> selectUserListByRole(SysUser user);

    List<SysUser> getUserByUserIdList(@Param("userIds") List<String> userIds);

    List<Long> selectRealUserIds();
}
