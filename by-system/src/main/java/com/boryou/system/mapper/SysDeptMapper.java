package com.boryou.system.mapper;

import java.util.List;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.common.core.domain.vo.OpenCustomizationVO;
import org.apache.ibatis.annotations.Param;
import com.boryou.common.core.domain.entity.SysDept;

/**
 * 部门管理 数据层
 *
 * <AUTHOR>
 */
public interface SysDeptMapper extends BaseMapper<SysDept> {
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 查询部门管理数据--带权限
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptListAuth(SysDept dept);


    /**
     * 查询部门数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectPermiDeptList(SysDept dept);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId            角色ID
     * @param deptCheckStrictly 部门树选择项是否关联显示
     * @return 选中部门列表
     */
    public List<Integer> selectDeptListByRoleId(@Param("roleId") Long roleId, @Param("deptCheckStrictly") boolean deptCheckStrictly);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDept selectDeptById(Long deptId);

    /**
     * 根据多个部门ID查询信息
     *
     * @param deptIds 部门ID
     * @return 部门信息
     */
    public List<SysDept> selectDeptByIds(Long[] deptIds);

    /**
     * 根据ID查询所有子部门
     *
     * @param deptId 部门ID
     * @return 部门列表
     */
    public List<SysDept> selectChildrenDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param deptName 部门名称
     * @param parentId 父部门ID
     * @return 结果
     */
    public SysDept checkDeptNameUnique(@Param("deptName") String deptName, @Param("parentId") Long parentId);

    /**
     * 新增部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int insertDept(SysDept dept);

    /**
     * 修改部门信息
     *
     * @param dept 部门信息
     * @return 结果
     */
    public int updateDept(SysDept dept);

    /**
     * 修改所在部门的父级部门状态
     *
     * @param dept 部门
     */
    public void updateDeptStatus(SysDept dept);

    /**
     * 修改子元素关系
     *
     * @param depts 子元素
     * @return 结果
     */
    public int updateDeptChildren(@Param("depts") List<SysDept> depts);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);

    /**
     * 根据 system_id 获取组织定制信息
     *
     * @param linkTag
     * @return
     */
    OpenCustomizationVO getDeptSaasConfig(String linkTag);

    /**
     * 根据 dept_id 获取父级组织定制信息
     *
     * @return
     */
    OpenCustomizationVO getSaasConfig(Long deptId);

    /**
     * 根据systemId获取deptId
     *
     * @param systemId
     * @return 结果
     */
    Long getDeptIdBySystemId(String systemId);

    /**
     * 更新sass信息
     *
     * @param map
     * @return
     */
    int updateSassConfig(@Param("map") OpenCustomizationVO map);

    List<Long> selectManageDept(Long deptId);

    List<Long> selectManageDeptUsers(Long deptId);

    List<String> selectManageDeptString(Long deptId);

    /**
     * 检查systemId是否唯一
     *
     * @param systemId
     * @return
     */
    int checkSystemId(String systemId);

    public List<SysDept> selectDeptLists(SysDept dept);

    List<SysDept> getDeptsByRoleKey(@Param("roleKey") String roleKey, @Param("deptId") Long deptId);

    List<SysDept> getRootDeptsByRole(@Param("roleKey") String roleKey, @Param("root") String root);


    /**
     * 查询当前部门树
     *
     * @param deptId
     * @return
     */
    List<SysDept> deptTree(Long deptId);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    String selectSystemIdById(Long deptId);
}
