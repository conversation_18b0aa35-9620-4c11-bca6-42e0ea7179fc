package com.boryou.system.service;

import cn.hutool.core.lang.tree.Tree;
import com.boryou.common.core.domain.TreeSelect;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.domain.vo.OpenCustomizationVO;

import java.util.List;

/**
 * 部门管理 服务层
 *
 * <AUTHOR>
 */
public interface ISysDeptService {
    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptList(SysDept dept);

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptListAuth(SysDept dept);

    /**
     * 根据当前人员的部门获取子级部门数据
     *
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptListForPerson(Long userId);

    /**
     * 根据当前人员的父部门获取子级部门数据
     *
     * @return 部门信息集合
     */
    public List<SysDept> selectDeptListForParent(Long userId);

    /**
     * 查询部门数据--仅限个人的最高组织机构及其子机构
     *
     * @return 部门信息集合
     */
    public List<SysDept> selectPermiDeptList();

    /**
     * 查询部门数据--仅限个人的最高组织机构及其子机构并叠加机构下人员
     *
     * @return 部门信息集合
     */
    public List<TreeSelect> selectTreeOrgForPerson();

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    public List<SysDept> buildDeptTree(List<SysDept> depts);

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts);

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    public List<Integer> selectDeptListByRoleId(Long roleId);

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    public SysDept selectDeptById(Long deptId);

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    public int selectNormalChildrenDeptById(Long deptId);

    /**
     * 是否存在部门子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public boolean hasChildByDeptId(Long deptId);

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    public boolean checkDeptExistUser(Long deptId);

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    public String checkDeptNameUnique(SysDept dept);

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @param vo
     * @return 结果
     */
    public boolean insertDept(SysDept dept, OpenCustomizationVO vo);

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @param vo
     * @return 结果
     */
    public int updateDept(SysDept dept, OpenCustomizationVO vo);

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    public int deleteDeptById(Long deptId);

    /**
     * 根据部门ID获取该部门的二级部门下所有员工ID
     *
     * @param deptId 部门ID
     * @return 部门下所有员工
     */
    public List<Long> selectUserIdForDept(Long deptId);

    List<TreeSelect> getDeptTree(List<SysDept> depts);


    List<SysDept> getDeptsByRoleKey(String roleKey, Long deptId);

    List<SysDept> getRootDeptsByRole(String roleKey, String root);

    void addPeopleByRole(List<TreeSelect> treeSelects, SysUser sysUser);

    /**
     * 获取当前组织树
     *
     * @return
     */
    List<Tree<String>> deptTree(String id);


    /**
     * 构建前端所需要下拉树结构--部门加人
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    public List<TreeSelect> buildDeptTreeSelectForPeople(List<SysDept> depts);


    List<SysDept> selectDeptLists(SysDept dept);

    /**
     * 查询所有上级及下级部门(不含组织架构 0)
     *
     * @param deptId 部门id
     * @return 所有部门id
     */
    List<String> selectDescendants(Long deptId);
}
