package com.boryou.system.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.lang.tree.Tree;
import cn.hutool.core.lang.tree.TreeNode;
import cn.hutool.core.lang.tree.TreeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boryou.common.annotation.DataScope;
import com.boryou.common.constant.UserConstants;
import com.boryou.common.core.domain.TreeSelect;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.domain.entity.SysRole;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.domain.vo.OpenCustomizationVO;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.mapper.SysRoleMapper;
import com.boryou.system.mapper.SysUserMapper;
import com.boryou.system.service.ISysDeptService;
import com.boryou.system.service.ISysUserService;
import com.boryou.upload.domain.FileDownloadRelation;
import com.boryou.upload.mapper.FileDownloadRelationMapper;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.boryou.common.enums.UserStatus.OK;

/**
 * 部门管理 服务实现
 *
 * <AUTHOR>
 */
@Service
public class SysDeptServiceImpl implements ISysDeptService {
    @Resource
    private SysDeptMapper deptMapper;

    @Resource
    private SysRoleMapper roleMapper;
    @Resource
    private ISysUserService userService;
    @Resource
    private ISysDeptService deptService;

    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    private FileDownloadRelationMapper downloadRelationMapper;

    /**
     * 查询部门管理数据
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    @DataScope(deptAlias = "d")
    public List<SysDept> selectDeptList(SysDept dept) {
        return deptMapper.selectDeptList(dept);
    }

    /**
     * 查询部门管理数据--带权限
     *
     * @param dept 部门信息
     * @return 部门信息集合
     */
    @Override
    public List<SysDept> selectDeptListAuth(SysDept dept) {
        dept.setDeptId(SecurityUtils.getLoginUser().getUser().getDept().getDeptId());
        return deptMapper.selectDeptListAuth(dept);
    }

    /**
     * 根据当前人员的部门获取子级部门数据
     *
     * @return 部门信息集合
     */
    @Override
    public List<SysDept> selectDeptListForPerson(Long userId) {
        SysDept dept = new SysDept();
        SysUser sysUser = sysUserMapper.selectUserById(userId);
        dept.setParentId(sysUser.getDeptId());
        return deptMapper.selectDeptList(dept);
    }

    /**
     * 根据当前人员的父部门获取子级部门数据
     *
     * @return 部门信息集合
     */
    @Override
    public List<SysDept> selectDeptListForParent(Long userId) {
        SysDept dept = new SysDept();
        SysUser sysUser = sysUserMapper.selectUserById(userId);
        dept.setParentId(sysUser.getDept().getParentId());
        return deptMapper.selectDeptList(dept);
    }

    /**
     * 查询部门数据
     *
     * @return 部门信息集合
     */
    @Override
    public List<SysDept> selectPermiDeptList() {
        SysDept dept = new SysDept();
        dept.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        return deptMapper.selectPermiDeptList(dept);
    }

    /**
     * 查询部门数据并叠加机构下人员
     *
     * @return 部门信息集合
     */
    @Override
    public List<TreeSelect> selectTreeOrgForPerson() {
        SysDept dept = new SysDept();
        dept.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        List<SysDept> depts = deptMapper.selectPermiDeptList(dept);
        // 获取所有机构信息--对机构遍历填充人员
        List<TreeSelect> treeSelectList = deptService.buildDeptTreeSelect(depts);
        // 数据处理
        DataPadding(treeSelectList);
        return treeSelectList;
    }

    /**
     * 对人员数据进行填充
     */
    private void DataPadding(List<TreeSelect> treeSelectList) {
        for (TreeSelect treeSelect : treeSelectList) {
            if (treeSelect.getChildren().size() > 0 && treeSelect.getPanelPointFlag() == 0) {
                // 还有子机构就继续填充
                DataPadding(treeSelect.getChildren());
                /**填充人员数据*/
                List<SysUser> userList = sysUserMapper.selectUserListByDeptIdNew(treeSelect.getId());
                List<TreeSelect> treeSelectListForPerson = new ArrayList<>();
                for (SysUser sysUser : userList) {
                    TreeSelect ts = new TreeSelect();
                    ts.setId(sysUser.getUserId());
                    ts.setLabel(sysUser.getNickName());
                    ts.setPanelPointFlag(1);
                    treeSelectListForPerson.add(ts);
                }
                treeSelectListForPerson.addAll(treeSelect.getChildren());
                treeSelect.setChildren(treeSelectListForPerson);
            } else {
                /**填充人员数据*/
                List<SysUser> userList = sysUserMapper.selectUserListByDeptIdNew(treeSelect.getId());
                List<TreeSelect> treeSelectListForPerson = new ArrayList<>();
                for (SysUser sysUser : userList) {
                    TreeSelect ts = new TreeSelect();
                    ts.setId(sysUser.getUserId());
                    ts.setLabel(sysUser.getNickName());
                    ts.setPanelPointFlag(1);
                    treeSelectListForPerson.add(ts);
                }
                treeSelect.setChildren(treeSelectListForPerson);
            }
        }
    }

    /**
     * 构建前端所需要树结构
     *
     * @param depts 部门列表
     * @return 树结构列表
     */
    @Override
    public List<SysDept> buildDeptTree(List<SysDept> depts) {
        List<SysDept> returnList = new ArrayList<SysDept>();
        List<Long> tempList = new ArrayList<Long>();
        for (SysDept dept : depts) {
            tempList.add(dept.getDeptId());
        }
        for (Iterator<SysDept> iterator = depts.iterator(); iterator.hasNext(); ) {
            SysDept dept = (SysDept) iterator.next();
            // 如果是顶级节点, 遍历该父节点的所有子节点
            if (!tempList.contains(dept.getParentId())) {
                recursionFn(depts, dept);
                returnList.add(dept);
            }
        }
        if (returnList.isEmpty()) {
            returnList = depts;
        }
        return returnList;
    }

    /**
     * 构建前端所需要下拉树结构
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelect(List<SysDept> depts) {
        List<SysDept> deptTrees = buildDeptTree(depts);
        return deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
    }

    /**
     * 根据角色ID查询部门树信息
     *
     * @param roleId 角色ID
     * @return 选中部门列表
     */
    @Override
    public List<Integer> selectDeptListByRoleId(Long roleId) {
        SysRole role = roleMapper.selectRoleById(roleId);
        return deptMapper.selectDeptListByRoleId(roleId, role.isDeptCheckStrictly());
    }

    /**
     * 根据部门ID查询信息
     *
     * @param deptId 部门ID
     * @return 部门信息
     */
    @Override
    public SysDept selectDeptById(Long deptId) {
        return deptMapper.selectDeptById(deptId);
    }

    /**
     * 根据ID查询所有子部门（正常状态）
     *
     * @param deptId 部门ID
     * @return 子部门数
     */
    @Override
    public int selectNormalChildrenDeptById(Long deptId) {
        return deptMapper.selectNormalChildrenDeptById(deptId);
    }

    /**
     * 是否存在子节点
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public boolean hasChildByDeptId(Long deptId) {
        int result = deptMapper.hasChildByDeptId(deptId);
        return result > 0 ? true : false;
    }

    /**
     * 查询部门是否存在用户
     *
     * @param deptId 部门ID
     * @return 结果 true 存在 false 不存在
     */
    @Override
    public boolean checkDeptExistUser(Long deptId) {
        int result = deptMapper.checkDeptExistUser(deptId);
        return result > 0 ? true : false;
    }

    /**
     * 校验部门名称是否唯一
     *
     * @param dept 部门信息
     * @return 结果
     */
    @Override
    public String checkDeptNameUnique(SysDept dept) {
        Long deptId = StringUtils.isNull(dept.getDeptId()) ? -1L : dept.getDeptId();
        SysDept info = deptMapper.checkDeptNameUnique(dept.getDeptName(), dept.getParentId());
        if (StringUtils.isNotNull(info) && info.getDeptId().longValue() != deptId.longValue()) {
            return UserConstants.NOT_UNIQUE;
        }
        return UserConstants.UNIQUE;
    }

    /**
     * 新增保存部门信息
     *
     * @param dept 部门信息
     * @param vo
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean insertDept(SysDept dept, OpenCustomizationVO vo) {
        SysDept info = deptMapper.selectDeptById(dept.getParentId());
        // 如果父节点不为正常状态,则不允许新增子节点
        if (!UserConstants.DEPT_NORMAL.equals(info.getStatus())) {
            throw new CustomException("部门停用，不允许新增");
        }
        dept.setAncestors(info.getAncestors() + "," + dept.getParentId());
        if (deptMapper.insertDept(dept) > 0) {
            vo.setDeptId(dept.getDeptId());
            deptMapper.updateSassConfig(vo);
        }
        return true;
    }

    /**
     * 修改保存部门信息
     *
     * @param dept 部门信息
     * @param vo
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateDept(SysDept dept, OpenCustomizationVO vo) {
        SysDept newParentDept = deptMapper.selectDeptById(dept.getParentId());
        SysDept oldDept = deptMapper.selectDeptById(dept.getDeptId());
        if (StringUtils.isNotNull(newParentDept) && StringUtils.isNotNull(oldDept)) {
            String newAncestors = newParentDept.getAncestors() + "," + newParentDept.getDeptId();
            String oldAncestors = oldDept.getAncestors();
            dept.setAncestors(newAncestors);
            updateDeptChildren(dept.getDeptId(), newAncestors, oldAncestors);
        }
        int result = deptMapper.updateDept(dept);
        if (UserConstants.DEPT_NORMAL.equals(dept.getStatus())) {
            // 如果该部门是启用状态，则启用该部门的所有上级部门
            updateParentDeptStatus(dept);
        }
        deptMapper.updateSassConfig(vo);
        return result;
    }

    /**
     * 修改该部门的父级部门状态
     *
     * @param dept 当前部门
     */
    private void updateParentDeptStatus(SysDept dept) {
        String updateBy = dept.getUpdateBy();
        dept = deptMapper.selectDeptById(dept.getDeptId());
        dept.setUpdateBy(updateBy);
        deptMapper.updateDeptStatus(dept);
    }

    /**
     * 修改子元素关系
     *
     * @param deptId       被修改的部门ID
     * @param newAncestors 新的父ID集合
     * @param oldAncestors 旧的父ID集合
     */
    public void updateDeptChildren(Long deptId, String newAncestors, String oldAncestors) {
        List<SysDept> children = deptMapper.selectChildrenDeptById(deptId);
        for (SysDept child : children) {
            child.setAncestors(child.getAncestors().replace(oldAncestors, newAncestors));
        }
        if (children.size() > 0) {
            deptMapper.updateDeptChildren(children);
        }
    }

    /**
     * 删除部门管理信息
     *
     * @param deptId 部门ID
     * @return 结果
     */
    @Override
    public int deleteDeptById(Long deptId) {
        return deptMapper.deleteDeptById(deptId);
    }

    /**
     * 根据部门ID获取该部门的二级部门下所有员工ID
     *
     * @param deptId 部门ID
     * @return 部门下所有员工
     */
    @Override
    public List<Long> selectUserIdForDept(Long deptId) {
        SysDept sysDept = deptMapper.selectDeptById(deptId);
        // 获取二级部门
        String[] depts = sysDept.getAncestors().split(",");
        ArrayList<String> arrayList = new ArrayList<String>(depts.length);
        Collections.addAll(arrayList, depts);
        Long parentDeptId;
        // 当机构长度小于3说明当前为二级部门直接取当前部门否则取数组第三个则为二级部门
        if (arrayList.size() < 3) {
            parentDeptId = sysDept.getDeptId();
        } else {
            parentDeptId = Long.parseLong(arrayList.get(2));
        }
        // 查询该部门ID下用户信息
        SysUser sysUser = new SysUser();
        sysUser.setDeptId(parentDeptId);
        List<SysUser> sysUserList = sysUserMapper.selectUserList(sysUser);
        List<Long> userIdList = sysUserList.stream().map(SysUser::getUserId).collect(Collectors.toList());
        return userIdList;
    }

    /**
     * 递归列表
     */
    private void recursionFn(List<SysDept> list, SysDept t) {
        // 得到子节点列表
        List<SysDept> childList = getChildList(list, t);
        t.setChildren(childList);
        for (SysDept tChild : childList) {
            if (hasChild(list, tChild)) {
                recursionFn(list, tChild);
            }
        }
    }

    /**
     * 得到子节点列表
     */
    private List<SysDept> getChildList(List<SysDept> list, SysDept t) {
        List<SysDept> tlist = new ArrayList<SysDept>();
        Iterator<SysDept> it = list.iterator();
        while (it.hasNext()) {
            SysDept n = (SysDept) it.next();
            if (StringUtils.isNotNull(n.getParentId()) && n.getParentId().longValue() == t.getDeptId().longValue()) {
                tlist.add(n);
            }
        }
        return tlist;
    }

    /**
     * 判断是否有子节点
     */
    private boolean hasChild(List<SysDept> list, SysDept t) {
        return getChildList(list, t).size() > 0 ? true : false;
    }


    @Override
    public List<Tree<String>> deptTree(String id) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        List<SysDept> deptList = deptMapper.deptTree(deptId);
        List<Long> collect = deptList.stream().map(SysDept::getDeptId).collect(Collectors.toList());
        // 找出所有用户
        List<SysUser> userList = userService.list(Wrappers.<SysUser>lambdaQuery()
                .in(SysUser::getDeptId, collect)
                .eq(SysUser::getDelFlag, OK.getCode())
                .ne(SysUser::getUserName, "admin")
                .ne(SysUser::getUserId, SecurityUtils.getLoginUser().getUser().getUserId())
                .select(SysUser::getUserId, SysUser::getNickName, SysUser::getDeptId)
        );
        List<FileDownloadRelation> relations = downloadRelationMapper.selectList(Wrappers.<FileDownloadRelation>lambdaQuery()
                .in(FileDownloadRelation::getDownloadFileId, id)
        );

        // 构建node列表
        List<TreeNode<String>> nodeList = CollUtil.newArrayList();
        deptList.forEach(x -> {
            // 去除没有用户的最底层部门
            Optional<SysDept> any = deptList.stream().filter(y -> y.getParentId().equals(x.getDeptId())).findAny();
            if (!any.isPresent()) {
                if (userList.stream().noneMatch(u -> u.getDeptId().equals(x.getDeptId()))) {
                    return;
                }
            }

            TreeNode<String> treeNode = new TreeNode<>(x.getDeptId().toString(), x.getParentId().toString(), x.getDeptName(), 0);
            nodeList.add(treeNode);
        });
        userList.forEach(x -> {
            TreeNode<String> treeNode = new TreeNode<>(x.getUserId().toString(), x.getDeptId().toString(), x.getNickName(), 1);
            Optional<FileDownloadRelation> any = relations.stream().filter(r -> r.getDownloadUserId().equals(x.getUserId())).findAny();

            HashMap<String, Object> map = new HashMap<>();
            map.put("check", any.isPresent());
            map.put("flag", true);

            treeNode.setExtra(map);
            nodeList.add(treeNode);
        });

        return TreeUtil.build(nodeList, "0");
    }

    @Override
    public List<SysDept> getDeptsByRoleKey(String roleKey, Long deptId) {
        return deptMapper.getDeptsByRoleKey(roleKey, deptId);
    }

    @Override
    public List<SysDept> getRootDeptsByRole(String roleKey, String root) {
        return deptMapper.getRootDeptsByRole(roleKey, root);
    }

    /**
     * 构建前端所需要下拉树结构--部门加人
     *
     * @param depts 部门列表
     * @return 下拉树结构列表
     */
    @Override
    public List<TreeSelect> buildDeptTreeSelectForPeople(List<SysDept> depts) {
        List<TreeSelect> treeSelectList = getDeptTree(depts);
        addPeople(treeSelectList);
        return treeSelectList;
    }

    /***
     * <AUTHOR>
     * @description //将树结构的每一层都加上人
     * @date 14:08 2021/12/7
     */
    private void addPeople(List<TreeSelect> treeSelects) {
        for (TreeSelect treeSelect : treeSelects) {
            if (treeSelect.getChildren().size() > 0 && treeSelect.getFlag() == 0) {
                addPeople(treeSelect.getChildren());
            }
            SysUser sysUser = new SysUser();
            // 获取部门ID
            sysUser.setDeptIds(treeSelect.getId());
            List<SysUser> userList = userService.selectUserList(sysUser);
//            //如果机构下面没有给个空的
//            if (userList.size() == 0) {
//                TreeSelect select = new TreeSelect();
//                select.setId(null);
//                select.setLabel(null);
//                select.setFlag(1);
//                treeSelect.getChildren().add(select);
//            }
            // 将人添加到部门下
            for (SysUser user : userList) {
                TreeSelect select = new TreeSelect();
                select.setId(user.getUserId());
                select.setLabel(user.getNickName());
                select.setFlag(1);
                treeSelect.getChildren().add(select);
            }
        }
    }

    /***
     * <AUTHOR>
     * @description //将树结构的每一层都加上人
     * @date 14:08 2021/12/7
     */
    @Override
    public void addPeopleByRole(List<TreeSelect> treeSelects, SysUser sysuser) {
        for (TreeSelect treeSelect : treeSelects) {
            if (treeSelect.getChildren().size() > 0 && treeSelect.getFlag() == 0) {
                addPeopleByRole(treeSelect.getChildren(), sysuser);
            }
            SysUser sysUser = new SysUser();
            // 获取部门ID
            sysUser.setDeptIds(treeSelect.getId());
            sysUser.setRoleIds(sysuser.getRoleIds());
            List<SysUser> userList = userService.selectUserListByRole(sysUser);
            for (SysUser user : userList) {
                TreeSelect select = new TreeSelect();
                select.setId(user.getUserId());
                select.setLabel(user.getNickName());
                select.setFlag(1);
                treeSelect.getChildren().add(select);
            }
        }
    }

    @Override
    public List<TreeSelect> getDeptTree(List<SysDept> depts) {
        List<SysDept> deptTrees = buildDeptTree(depts);
        List<TreeSelect> treeSelectList = deptTrees.stream().map(TreeSelect::new).collect(Collectors.toList());
        return treeSelectList;
    }

    @Override
    public List<SysDept> selectDeptLists(SysDept dept) {
        return deptMapper.selectDeptLists(dept);
    }

    @Override
    public List<String> selectDescendants(Long deptId) {
        SysDept sysDept = deptMapper.selectDeptById(deptId);
        if (sysDept == null) {
            return Collections.emptyList();
        }
        Set<String> deptList = new HashSet<>();
        String ancestors = sysDept.getAncestors();
        if (CharSequenceUtil.isNotBlank(ancestors)) {
            deptList.addAll(CharSequenceUtil.splitTrim(ancestors, ","));
        }
        List<String> deptIds = deptMapper.selectManageDeptString(deptId);
        deptList.addAll(deptIds);
        if (CollUtil.isEmpty(deptIds)) {
            return Collections.emptyList();
        }
        if (deptId == 0 || deptId == 1) {
            // 挂在组织架构不需要过滤
            return CollUtil.newArrayList(deptList);
        }
        return deptList.stream().filter(item -> !item.equals("0") && !item.equals("1")).collect(Collectors.toList());
    }
}
