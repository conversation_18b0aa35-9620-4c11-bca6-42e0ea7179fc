<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0"
         xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <parent>
        <groupId>com.boryou</groupId>
        <artifactId>yq-boryou</artifactId>
        <version>1.0.0</version>
    </parent>

    <description>业务模块</description>
    <artifactId>by-submit</artifactId>

    <properties>
        <maven.compiler.source>8</maven.compiler.source>
        <maven.compiler.target>8</maven.compiler.target>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
    </properties>

    <dependencies>
        <dependency>
            <groupId>com.boryou</groupId>
            <artifactId>by-common</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.boryou</groupId>
            <artifactId>by-system</artifactId>
            <version>1.0.0</version>
        </dependency>
        <dependency>
            <groupId>com.boryou</groupId>
            <artifactId>by-framework</artifactId>
            <version>1.0.0</version>
        </dependency>

        <!-- feign -->
        <dependency>
            <groupId>org.springframework.cloud</groupId>
            <artifactId>spring-cloud-starter-openfeign</artifactId>
            <version>2.1.5.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>net.lingala.zip4j</groupId>
            <artifactId>zip4j</artifactId>
            <version>2.11.5</version>
        </dependency>
        <dependency>
            <groupId>com.boryou</groupId>
            <artifactId>honglian-sms-boryou-yuqing</artifactId>
            <version>1.4.0</version>
        </dependency>
    </dependencies>
    <repositories>
        <repository>
            <id>alimaven</id>
            <url>https://maven.aliyun.com/nexus/content/groups/public/</url>
        </repository>
    </repositories>
</project>
