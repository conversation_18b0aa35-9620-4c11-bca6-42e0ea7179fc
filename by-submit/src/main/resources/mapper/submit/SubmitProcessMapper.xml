<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.submit.mapper.SubmitProcessMapper">

    <resultMap type="SubmitProcess" id="SubmitProcessResult">
        <result property="id" column="id"/>
        <result property="infoId" column="info_id"/>
        <result property="actName" column="act_name"/>
        <result property="createBy" column="create_by"/>
        <result property="createById" column="create_by_name"/>
        <result property="createTime" column="create_time"/>
        <result property="comment" column="comment"/>
        <result property="description" column="description"/>
        <result property="processResult" column="process_result"/>
        <result property="scopeType" column="scope_type"/>
        <result property="managerUser" column="manager_user"/>
        <result property="dispenserId" column="dispenser_id"/>
    </resultMap>

    <sql id="selectSubmitProcessVo">
        select id, info_id, act_name, create_by, create_by_id, create_time, comment, description,
        process_result,scope_type,dispenser_id,manager_user from by_submit_process
    </sql>

    <select id="selectSubmitProcessList" parameterType="SubmitProcess" resultMap="SubmitProcessResult">
        <include refid="selectSubmitProcessVo"/>
        <where>
            <if test="infoId != null ">and info_id = #{infoId}</if>
            <if test="actName != null  and actName != ''">and act_name like concat('%', #{actName}, '%')</if>
            <if test="comment != null  and comment != ''">and comment = #{comment}</if>
            <if test="description != null  and description != ''">and description = #{description}</if>
            <if test="processResult != null  and processResult != ''">and process_result = #{processResult}</if>
            <if test="scopeType != null">and scope_type = #{scopeType}</if>
        </where>
    </select>


</mapper>
