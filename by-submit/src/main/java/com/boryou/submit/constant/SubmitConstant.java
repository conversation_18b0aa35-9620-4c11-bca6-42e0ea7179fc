package com.boryou.submit.constant;

/**
 * <AUTHOR>
 * @date 2023-12-28 14:35
 */
public class SubmitConstant {

    /**
     * 分析师工作流
     */
    public static final String FENXISHI_FLOW = "fenxishi_flow";
    public static final String MANAGER_FLOW = "manager_flow";


    /**
     * 流程节点-申请人
     */
    public static final String ANALYST_APPLICAT = "analyst_applicat";


    public static final int SCOPE_FXS = 0;
    public static final int SCOPE_SHARE = 1;


    public static final String FLOW_ACT_APPLY = "上报";
    public static final String FLOW_ACT_MODITY = "修改";
    public static final String FLOW_ACT_SUPPLY = "补充材料";


    public static final String FLOW_ACT_DISTRIBUTE = "分发处置";

    /**
     * 没有实际业务意义，代表无需处置，意料之外的值
     */
    public static final int NO_BUSINESS_CODE = -2;
}

