package com.boryou.submit.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.utils.DateUtils;
import com.boryou.domain.SFile;
import com.boryou.manage.domain.File;
import com.boryou.manage.service.FileService;
import com.boryou.service.SFileService;
import com.boryou.submit.domain.SubmitFileRelation;
import com.boryou.submit.domain.SubmitProcess;
import com.boryou.submit.dto.SubmitProcessDTO;
import com.boryou.submit.mapper.SubmitProcessMapper;
import com.boryou.submit.service.ISubmitFileRelationService;
import com.boryou.submit.service.ISubmitInfoService;
import com.boryou.submit.service.ISubmitProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 报送节点过程记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@Service
public class SubmitProcessServiceImpl extends ServiceImpl<SubmitProcessMapper, SubmitProcess> implements ISubmitProcessService {
    @Autowired
    @Lazy
    private ISubmitInfoService submitInfoService;

    @Autowired
    private ISubmitFileRelationService fileRelationService;

    @Autowired
    private FileService fileService;


    @Autowired
    private SFileService sFileService;


    /**
     * 查询报送节点过程记录
     *
     * @param id 报送节点过程记录ID
     * @return 报送节点过程记录
     */
    @Override
    public SubmitProcess selectSubmitProcessById(Long id) {
        SubmitProcess submitProcess = baseMapper.selectSubmitProcessById(id);
        return submitProcess;
    }

    /**
     * 查询报送节点过程记录列表
     *
     * @param submitProcess 报送节点过程记录
     * @return 报送节点过程记录
     */
    @Override
    public List<SubmitProcessDTO> selectSubmitProcessList(SubmitProcessDTO submitProcess) {
        List<SubmitProcessDTO> datas = new ArrayList<SubmitProcessDTO>();
        submitProcess.setScopeType(0);
        int i = submitInfoService.validPermission();
        if (i != 0) {
            //管理者和处置者都看共有附件
            submitProcess.setScopeType(1);
        }
        List<SubmitProcess> submitProcesses = baseMapper.selectSubmitProcessList(submitProcess);
        for (SubmitProcess process : submitProcesses) {
            SubmitProcessDTO dto = JSONObject.parseObject(JSONObject.toJSONString(process), SubmitProcessDTO.class);
            SubmitFileRelation submitFileRelation = new SubmitFileRelation();
            submitFileRelation.setInfoId(process.getInfoId());
            submitFileRelation.setProcessId(process.getId());
            List<SubmitFileRelation> submitFileRelations = fileRelationService.selectSubmitFileRelationList(submitFileRelation);
            List<SFile> files = new ArrayList<>();
            for (SubmitFileRelation fileRelation : submitFileRelations) {
                if (fileRelation.getFileId() == null) {
                    continue;
                }
                SFile file = sFileService.getById(fileRelation.getFileId());
                if (null != file) {
                    files.add(file);
                }
            }
            dto.setFiles(files);
            datas.add(dto);
        }
        return datas;
    }

    /**
     * 新增报送节点过程记录
     *
     * @param submitProcess 报送节点过程记录
     * @return 结果
     */
    @Override
    public int insertSubmitProcess(SubmitProcess submitProcess) {
        submitProcess.setCreateTime(DateUtils.getNowDate());
        return baseMapper.insertSubmitProcess(submitProcess);
    }

    /**
     * 修改报送节点过程记录
     *
     * @param submitProcess 报送节点过程记录
     * @return 结果
     */
    @Override
    public int updateSubmitProcess(SubmitProcess submitProcess) {
        return baseMapper.updateSubmitProcess(submitProcess);
    }

    /**
     * 批量删除报送节点过程记录
     *
     * @param ids 需要删除的报送节点过程记录ID
     * @return 结果
     */
    @Override
    public int deleteSubmitProcessByIds(Long[] ids) {
        return baseMapper.deleteSubmitProcessByIds(ids);
    }

    /**
     * 删除报送节点过程记录信息
     *
     * @param id 报送节点过程记录ID
     * @return 结果
     */
    @Override
    public int deleteSubmitProcessById(Long id) {
        return baseMapper.deleteSubmitProcessById(id);
    }
}
