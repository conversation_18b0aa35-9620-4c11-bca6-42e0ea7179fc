package com.boryou.quartz.task;

import com.boryou.common.utils.StringUtils;
import com.boryou.manage.domain.ByNetworkReviewTask;
import com.boryou.manage.mapper.ByNetworkReviewTaskMapper;
import com.boryou.manage.mapper.ByTaskToUserMapper;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 定时任务调度测试
 *
 * <AUTHOR>
 */
@Component("byTask")
public class ByTask {

    @Resource
    private ByNetworkReviewTaskMapper byNetworkReviewTaskMapper;
    @Resource
    private ByTaskToUserMapper byTaskToUserMapper;

    public void byMultipleParams(String s, Boolean b, Long l, Double d, Integer i) {
        System.out.println(StringUtils.format("执行多参方法： 字符串类型{}，布尔类型{}，长整型{}，浮点型{}，整形{}", s, b, l, d, i));
    }

    public void byParams(String params) {
        System.out.println("执行有参方法：" + params);
    }

    public void byNoParams() {
        System.out.println("执行无参方法");
    }

    /**
     * 主任务到时间定期检测
     */
    public void taskToTimeCheck() {
        System.out.println("开始执行过期扫描任务");
        ByNetworkReviewTask byNetworkReviewTask = new ByNetworkReviewTask();
        //主任务状态为进行中
        byNetworkReviewTask.setTaskStatus(1);
        //完成时间大于当前时间的
        byNetworkReviewTask.setAccomplishTime(new Date());
        //获取所有进行中的任务且已超时的
        List<ByNetworkReviewTask> list = byNetworkReviewTaskMapper.selectByNetworkReviewTaskList(byNetworkReviewTask);
        for (ByNetworkReviewTask byNetworkReviewTask1 : list) {
            //将主任务状态改为未完成
            byNetworkReviewTask1.setTaskStatus(2);
            byNetworkReviewTaskMapper.updateByNetworkReviewTask(byNetworkReviewTask1);
            //再将所有子任务也改为已过期--只修改未完成的子任务
            byTaskToUserMapper.exceedTimeByTaskToUser(byNetworkReviewTask1.getId(), 3);
        }
        System.out.println("过期扫描任务执行结束");
    }
}
