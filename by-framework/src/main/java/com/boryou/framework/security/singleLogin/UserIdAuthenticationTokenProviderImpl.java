package com.boryou.framework.security.singleLogin;

import com.boryou.common.utils.SecurityUtils;
import org.springframework.stereotype.Component;

/**
 * 解决 common 模块无法直接引用 framework 模块
 *
 * <AUTHOR>
 * @date 2024/9/3 下午3:28
 */
@Component
public class UserIdAuthenticationTokenProviderImpl implements SecurityUtils.AuthenticationTokenProvider {

    @Override
    public UserIdAuthenticationToken getAuthenticationToken(Long userId) {
        // 创建并返回 UserIdAuthenticationToken 实例
        return new UserIdAuthenticationToken(userId);
    }
}
