package com.boryou.framework.security.singleLogin;

import cn.hutool.core.util.ObjectUtil;
import org.springframework.security.authentication.AuthenticationProvider;
import org.springframework.security.authentication.InternalAuthenticationServiceException;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.core.userdetails.UserDetailsService;
import org.springframework.stereotype.Component;

/**
 * 登录校验器
 *
 * <AUTHOR>
 * @date 2024/4/7 17:06
 */
@Component
public class PhoneAuthenticationProvider implements AuthenticationProvider {

    private UserDetailsService userDetailsService;

    public PhoneAuthenticationProvider(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }

    /**
     * 重写 authenticate方法，实现身份验证逻辑。
     *
     * @param authentication
     * @return
     * @throws AuthenticationException
     */
    @Override
    public Authentication authenticate(Authentication authentication) throws AuthenticationException {
        PhoneAuthenticationToken authenticationToken = (PhoneAuthenticationToken) authentication;
        // 获取凭证也就是用户的手机号
        String telephone = (String) authenticationToken.getPrincipal();
        // 根据手机号查询用户信息UserDetails
        UserDetails userDetails = userDetailsService.loadUserByUsername(telephone);
        if (ObjectUtil.isEmpty(userDetails)) {
            throw new InternalAuthenticationServiceException("用户不存在");
        }
        // 鉴权成功，返回一个拥有鉴权的 AbstractAuthenticationToken
        PhoneAuthenticationToken phoneAuthenticationToken = new PhoneAuthenticationToken(userDetails, userDetails.getAuthorities());
        phoneAuthenticationToken.setDetails(authenticationToken.getDetails());
        return phoneAuthenticationToken;
    }

    /**
     * 重写supports方法，指定此 AuthenticationProvider 仅支持短信验证码身份验证。
     *
     * @param authentication
     * @return
     */
    @Override
    public boolean supports(Class<?> authentication) {
        return PhoneAuthenticationToken.class.isAssignableFrom(authentication);
    }

    public UserDetailsService getUserDetailsService() {
        return userDetailsService;
    }

    public void setUserDetailsService(UserDetailsService userDetailsService) {
        this.userDetailsService = userDetailsService;
    }
}
