package com.boryou.framework.web.service;

import com.anji.captcha.service.CaptchaCacheService;
import com.boryou.common.constant.Constants;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;

import java.util.concurrent.TimeUnit;

public class CaptchaRedisService implements CaptchaCacheService {
    @Autowired
    private StringRedisTemplate stringRedisTemplate;

    @Override
    public void set(String key, String value, long expiresInSeconds) {
        key = Constants.PROJECT_PREFIX + key;
        stringRedisTemplate.opsForValue().set(key, value, expiresInSeconds, TimeUnit.SECONDS);
    }

    @Override
    public boolean exists(String key) {
        key = Constants.PROJECT_PREFIX + key;
        return stringRedisTemplate.hasKey(key);
    }

    @Override
    public void delete(String key) {
        key = Constants.PROJECT_PREFIX + key;
        stringRedisTemplate.delete(key);
    }

    @Override
    public String get(String key) {
        key = Constants.PROJECT_PREFIX + key;
        return stringRedisTemplate.opsForValue().get(key);
    }

    @Override
    public Long increment(String key, long val) {
        key = Constants.PROJECT_PREFIX + key;
        return stringRedisTemplate.opsForValue().increment(key, val);
    }

    @Override
    public String type() {
        return "redis";
    }
}
