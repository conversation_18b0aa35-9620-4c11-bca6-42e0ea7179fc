package com.boryou.framework.web.service;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.domain.model.LoginUser;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.exception.CustomException;
import com.boryou.common.exception.user.UserPasswordNotMatchException;
import com.boryou.common.utils.MessageUtils;
import com.boryou.framework.manager.AsyncManager;
import com.boryou.framework.manager.factory.AsyncFactory;
import com.boryou.framework.security.singleLogin.PhoneAuthenticationToken;
import com.boryou.framework.security.singleLogin.UserIdAuthenticationToken;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.mapper.SysUserMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.BadCredentialsException;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Date;

/**
 * 登录校验方法
 *
 * <AUTHOR>
 */
@Component
public class SysLoginService {
    @Autowired
    private TokenService tokenService;

    @Resource
    private AuthenticationManager authenticationManager;

    @Autowired
    private RedisCache redisCache;

    @Autowired
    private SysUserMapper userMapper;

    @Autowired
    private SysDeptMapper deptMapper;

    /**
     * 登录验证
     *
     * @param username 用户名
     * @param password 密码
     * @param code     验证码
     * @param deptId
     * @return 结果
     */
    public String login(String username, String password, String code, Long deptId) {
        /*String verifyKey = Constants.CAPTCHA_CODE_KEY + uuid;
        String captcha = redisCache.getCacheObject(verifyKey);
        redisCache.deleteObject(verifyKey);
        if (captcha == null)
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.expire")));
            throw new CaptchaExpireException();
        }
        if (!code.equalsIgnoreCase(captcha))
        {
            AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            throw new CaptchaException();
        }*/
        // 用户验证
        Authentication authentication = null;
        try {
            // 该方法会去调用UserDetailsServiceImpl.loadUserByUsername
            authentication = authenticationManager
                    .authenticate(new UsernamePasswordAuthenticationToken(username, password));
            // if (deptId != null) {
            //     // 校验当前用户是否是当前url所属组织的用户
            //     String jsonStr = JSONUtil.toJsonStr(authentication.getPrincipal());
            //     Long userDeptId = JSONUtil.parseObj(jsonStr).getJSONObject("user").get("deptId", Long.class);
            //     String userName = JSONUtil.parseObj(jsonStr).getJSONObject("user").get("userName", String.class);
            //
            //     if (userDeptId == null) {
            //         throw new CustomException("当前用户没有绑定组织");
            //     }
            //
            //     // 如果用户所在的组织 systemId 为null,则递归查找父级组织直到systemId不为null
            //     userDeptId = findRecursion(userDeptId);
            //
            //     // 放开admin
            //     if (!userDeptId.equals(deptId) && !"admin".equals(userName)) {
            //         AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.jcaptcha.error")));
            //         throw new CustomException("当前用户不属于当前系统");
            //     }
            // }
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, MessageUtils.message("user.password.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_FAIL, e.getMessage()));
                throw new CustomException(e.getMessage());
            }
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(username, Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));
        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        if (loginUser.getUser().getExpireTime() != null) {
            Date expireTime = loginUser.getUser().getExpireTime();
            DateTime date = DateUtil.date();
            if (expireTime.before(date)) {
                throw new CustomException("该账号已到期");
            }
        }
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 如果用户所在的组织 systemId 为null，则递归查找父级组织直到systemId不为null
     *
     * @param userDeptId
     */
    private Long findRecursion(Long userDeptId) {
        // 如果用户所在的组织 systemId 为null
        String systemId = deptMapper.selectSystemIdById(userDeptId);
        if (systemId == null) {
            SysDept sysDept = deptMapper.selectDeptById(userDeptId);
            userDeptId = sysDept.getParentId();
            if (sysDept.getSystemId() == null) {
                findRecursion(userDeptId);
            }
        }
        return userDeptId;
    }

    /**
     * 用户ID生成 token
     *
     * @param userId
     * @return
     */
    public String userIdLogin(Long userId) {
        if (ObjectUtil.isEmpty(userId)) {
            throw new CustomException("未携带用户id");
        }
        // 通过用户id获取用户
        SysUser user = userMapper.selectUserById(userId);
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("未找到用户");
        }
        // 用户验证
        Authentication authentication = null;
        try {
            UserIdAuthenticationToken authenticationToken = new UserIdAuthenticationToken(userId);
            // AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用 PhoneUserDetailsService.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                // 异步记录日志
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_FAIL, MessageUtils.message("user.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_FAIL, e.getMessage()));
                throw new CustomException(e.getMessage());
            }
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));

        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        // 生成token
        return tokenService.createToken(loginUser);
    }

    /**
     * 用户手机号生成 token
     *
     * @param phone
     * @return
     */
    public String phoneLogin(String phone) {
        if (ObjectUtil.isEmpty(phone)) {
            throw new CustomException("未携带用户id");
        }
        // 通过用户id获取用户
        SysUser user = userMapper.getUserByTelephone(phone);
        if (ObjectUtil.isEmpty(user)) {
            throw new CustomException("该手机号未注册");
        }
        // 用户验证
        Authentication authentication = null;
        try {
            PhoneAuthenticationToken authenticationToken = new PhoneAuthenticationToken(phone);
            // AuthenticationContextHolder.setContext(authenticationToken);
            // 该方法会去调用 PhoneUserDetailsService.loadUserByUsername
            authentication = authenticationManager.authenticate(authenticationToken);
        } catch (Exception e) {
            if (e instanceof BadCredentialsException) {
                // 异步记录日志
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_FAIL, MessageUtils.message("user.not.match")));
                throw new UserPasswordNotMatchException();
            } else {
                AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_FAIL, e.getMessage()));
                throw new CustomException(e.getMessage());
            }
        }
        AsyncManager.me().execute(AsyncFactory.recordLogininfor(user.getUserName(), Constants.LOGIN_SUCCESS, MessageUtils.message("user.login.success")));

        LoginUser loginUser = (LoginUser) authentication.getPrincipal();
        // 生成token
        return tokenService.createToken(loginUser);
    }
}
