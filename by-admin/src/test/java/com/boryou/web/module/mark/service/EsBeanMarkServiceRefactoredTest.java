package com.boryou.web.module.mark.service;

import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;

/**
 * EsBeanMarkService 重构后测试类
 * 测试重新封装后的字段更新方法
 */
@SpringBootTest
public class EsBeanMarkServiceRefactoredTest {

    @Resource
    private EsBeanMarkService esBeanMarkService;

    @Test
    public void testRefactoredFieldUpdate() {
        // 创建测试用户
        SysUser user = new SysUser();
        user.setUserId(5L);
        user.setUserName("refactoredTestUser");
        user.setDeptId(500L);

        System.out.println("=== 测试重构后的字段更新方法 ===");

        // 创建初始数据
        EsBeanMarkVO initialVO = new EsBeanMarkVO();
        initialVO.setArticleId("test-refactored-001");
        initialVO.setTitle("重构测试标题");
        initialVO.setAuthor("重构测试作者");
        initialVO.setType(1);
        initialVO.setFansNum(100);
        initialVO.setReadNum(200);
        initialVO.setLikeNum(50);
        initialVO.setIsOriginal(false);
        initialVO.setSiteMeta(Arrays.asList("重构标签1", "重构标签2"));
        initialVO.setPublishTime(new Date());

        // 第一次保存（新增）
        System.out.println("--- 新增数据 ---");
        boolean addResult = esBeanMarkService.saveOrUpdateEsBeanMark(initialVO, user);
        System.out.println("新增结果: " + addResult);

        // 查询初始数据
        EsBeanMarkVO queryVO = new EsBeanMarkVO();
        queryVO.setArticleId("test-refactored-001");
        EsBeanMarkVO initialResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (initialResult != null) {
            System.out.println("初始数据:");
            System.out.println("- 标题: " + initialResult.getTitle());
            System.out.println("- 作者: " + initialResult.getAuthor());
            System.out.println("- 类型: " + initialResult.getType());
            System.out.println("- 粉丝数: " + initialResult.getFansNum());
            System.out.println("- 是否原创: " + initialResult.getIsOriginal());
            System.out.println("- 站点标签: " + initialResult.getSiteMeta());
        }

        // 测试字符串字段更新
        System.out.println("\n--- 测试字符串字段更新 ---");
        EsBeanMarkVO stringUpdateVO = new EsBeanMarkVO();
        stringUpdateVO.setArticleId("test-refactored-001");
        stringUpdateVO.setTitle("重构后的新标题");  // 会更新
        stringUpdateVO.setAuthor("重构测试作者");   // 不会更新（相同）
        stringUpdateVO.setSummary("新增的摘要内容"); // 会更新（原来为空）
        stringUpdateVO.setDomain("example.com");   // 会更新（原来为空）

        boolean stringUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(stringUpdateVO, user);
        System.out.println("字符串字段更新结果: " + stringUpdateResult);

        // 验证字符串字段更新
        EsBeanMarkVO stringResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (stringResult != null) {
            System.out.println("字符串更新后:");
            System.out.println("- 标题: " + stringResult.getTitle() + " (应该是: 重构后的新标题)");
            System.out.println("- 作者: " + stringResult.getAuthor() + " (应该是: 重构测试作者)");
            System.out.println("- 摘要: " + stringResult.getSummary() + " (应该是: 新增的摘要内容)");
            System.out.println("- 域名: " + stringResult.getDomain() + " (应该是: example.com)");
        }

        // 测试数值字段更新
        System.out.println("\n--- 测试数值字段更新 ---");
        EsBeanMarkVO numericUpdateVO = new EsBeanMarkVO();
        numericUpdateVO.setArticleId("test-refactored-001");
        numericUpdateVO.setType(2);             // 会更新
        numericUpdateVO.setFansNum(500);        // 会更新
        numericUpdateVO.setReadNum(200);        // 不会更新（相同）
        numericUpdateVO.setLikeNum(100);        // 会更新
        numericUpdateVO.setAuthorSex(1);        // 会更新（原来为空）
        numericUpdateVO.setAccountLevel(2);     // 会更新（原来为空）

        boolean numericUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(numericUpdateVO, user);
        System.out.println("数值字段更新结果: " + numericUpdateResult);

        // 验证数值字段更新
        EsBeanMarkVO numericResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (numericResult != null) {
            System.out.println("数值更新后:");
            System.out.println("- 类型: " + numericResult.getType() + " (应该是: 2)");
            System.out.println("- 粉丝数: " + numericResult.getFansNum() + " (应该是: 500)");
            System.out.println("- 阅读数: " + numericResult.getReadNum() + " (应该是: 200)");
            System.out.println("- 点赞数: " + numericResult.getLikeNum() + " (应该是: 100)");
            System.out.println("- 作者性别: " + numericResult.getAuthorSex() + " (应该是: 1)");
            System.out.println("- 账号级别: " + numericResult.getAccountLevel() + " (应该是: 2)");
        }

        // 测试布尔字段更新
        System.out.println("\n--- 测试布尔字段更新 ---");
        EsBeanMarkVO booleanUpdateVO = new EsBeanMarkVO();
        booleanUpdateVO.setArticleId("test-refactored-001");
        booleanUpdateVO.setIsOriginal(true);    // 会更新
        booleanUpdateVO.setIsSpam(false);       // 会更新（原来为空）

        boolean booleanUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(booleanUpdateVO, user);
        System.out.println("布尔字段更新结果: " + booleanUpdateResult);

        // 验证布尔字段更新
        EsBeanMarkVO booleanResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (booleanResult != null) {
            System.out.println("布尔更新后:");
            System.out.println("- 是否原创: " + booleanResult.getIsOriginal() + " (应该是: true)");
            System.out.println("- 是否垃圾: " + booleanResult.getIsSpam() + " (应该是: false)");
        }

        // 测试列表字段更新
        System.out.println("\n--- 测试列表字段更新 ---");
        EsBeanMarkVO listUpdateVO = new EsBeanMarkVO();
        listUpdateVO.setArticleId("test-refactored-001");
        listUpdateVO.setSiteMeta(Arrays.asList("新重构标签1", "新重构标签2", "新重构标签3"));  // 会更新
        listUpdateVO.setContentMeta(Arrays.asList("内容标签1", "内容标签2"));              // 会更新（原来为空）
        listUpdateVO.setPicUrl(Arrays.asList("pic1.jpg", "pic2.jpg"));                // 会更新（原来为空）
        listUpdateVO.setContentForm(Arrays.asList(1, 2, 3));                         // 会更新（原来为空）

        boolean listUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(listUpdateVO, user);
        System.out.println("列表字段更新结果: " + listUpdateResult);

        // 验证列表字段更新
        EsBeanMarkVO listResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (listResult != null) {
            System.out.println("列表更新后:");
            System.out.println("- 站点标签: " + listResult.getSiteMeta());
            System.out.println("- 内容标签: " + listResult.getContentMeta());
            System.out.println("- 图片链接: " + listResult.getPicUrl());
            System.out.println("- 内容形式: " + listResult.getContentForm());
        }

        // 测试日期字段更新
        System.out.println("\n--- 测试日期字段更新 ---");
        Date newPublishTime = new Date(System.currentTimeMillis() + 86400000); // 明天
        EsBeanMarkVO dateUpdateVO = new EsBeanMarkVO();
        dateUpdateVO.setArticleId("test-refactored-001");
        dateUpdateVO.setPublishTime(newPublishTime);  // 会更新

        boolean dateUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(dateUpdateVO, user);
        System.out.println("日期字段更新结果: " + dateUpdateResult);

        // 验证日期字段更新
        EsBeanMarkVO dateResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (dateResult != null) {
            System.out.println("日期更新后:");
            System.out.println("- 发布时间: " + dateResult.getPublishTime());
        }

        // 测试无变化更新
        System.out.println("\n--- 测试无变化更新 ---");
        EsBeanMarkVO noChangeVO = new EsBeanMarkVO();
        noChangeVO.setArticleId("test-refactored-001");
        noChangeVO.setTitle("重构后的新标题");     // 相同，不会更新
        noChangeVO.setType(2);                 // 相同，不会更新
        noChangeVO.setIsOriginal(true);        // 相同，不会更新

        boolean noChangeResult = esBeanMarkService.saveOrUpdateEsBeanMark(noChangeVO, user);
        System.out.println("无变化更新结果: " + noChangeResult + " (应该是: true)");

        // 最终验证
        EsBeanMarkVO finalResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (finalResult != null) {
            System.out.println("\n=== 最终验证 ===");
            assert "重构后的新标题".equals(finalResult.getTitle());
            assert "重构测试作者".equals(finalResult.getAuthor());
            assert "新增的摘要内容".equals(finalResult.getSummary());
            assert "example.com".equals(finalResult.getDomain());
            assert Integer.valueOf(2).equals(finalResult.getType());
            assert Integer.valueOf(500).equals(finalResult.getFansNum());
            assert Integer.valueOf(200).equals(finalResult.getReadNum());
            assert Integer.valueOf(100).equals(finalResult.getLikeNum());
            assert Integer.valueOf(1).equals(finalResult.getAuthorSex());
            assert Integer.valueOf(2).equals(finalResult.getAccountLevel());
            assert Boolean.TRUE.equals(finalResult.getIsOriginal());
            assert Boolean.FALSE.equals(finalResult.getIsSpam());
            assert finalResult.getSiteMeta().contains("新重构标签1");
            assert finalResult.getContentMeta().contains("内容标签1");
            assert finalResult.getPicUrl().contains("pic1.jpg");
            assert finalResult.getContentForm().contains(1);

            System.out.println("所有断言通过！重构后的方法工作正常。");
        }
    }

    @Test
    public void testFieldTypeCompatibility() {
        System.out.println("=== 测试字段类型兼容性 ===");

        // 创建测试用户
        SysUser user = new SysUser();
        user.setUserId(6L);
        user.setUserName("typeTestUser");
        user.setDeptId(600L);

        // 测试各种字段类型
        EsBeanMarkVO vo = new EsBeanMarkVO();
        vo.setArticleId("test-type-001");
        
        // 字符串类型
        vo.setTitle("类型测试标题");
        vo.setAuthor("类型测试作者");
        vo.setUrl("http://example.com");
        
        // 数值类型
        vo.setType(3);
        vo.setFansNum(1000);
        vo.setAuthorSex(0);
        
        // 布尔类型
        vo.setIsOriginal(true);
        vo.setIsSpam(false);
        
        // 列表类型
        vo.setSiteMeta(Arrays.asList("类型测试标签"));
        vo.setContentForm(Arrays.asList(1, 2));
        
        // 日期类型
        vo.setPublishTime(new Date());

        boolean result = esBeanMarkService.saveOrUpdateEsBeanMark(vo, user);
        System.out.println("字段类型兼容性测试结果: " + result);
        
        if (result) {
            System.out.println("所有字段类型都兼容，重构成功！");
        }
    }
}
