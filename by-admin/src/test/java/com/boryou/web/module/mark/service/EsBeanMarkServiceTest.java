package com.boryou.web.module.mark.service;

import com.boryou.web.controller.common.entity.EsBeanMark;
import com.boryou.web.module.mark.domain.EsBeanMarkEntity;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;

/**
 * EsBeanMarkService 测试类
 * 测试字段合并功能
 */
@SpringBootTest
public class EsBeanMarkServiceTest {

    @Resource
    private EsBeanMarkService esBeanMarkService;

    @Test
    public void testMergeFrom() {
        // 创建源实体（新数据）
        EsBeanMarkEntity src = new EsBeanMarkEntity();
        src.setArticleId("test-article-001");
        src.setTitle("新标题");
        src.setAuthor("新作者");
        src.setType(1);
        src.setSiteMeta(Arrays.asList("标签1", "标签2"));
        src.setFansNum(1000);
        src.setIsOriginal(true);

        // 创建目标实体（已存在的数据）
        EsBeanMarkEntity target = new EsBeanMarkEntity();
        target.setArticleId("test-article-001");
        target.setTitle("旧标题");
        target.setAuthor("旧作者");
        target.setType(2);
        target.setSiteMeta(Arrays.asList("旧标签"));
        target.setFansNum(500);
        target.setIsOriginal(false);
        target.setCreateTime(new Date());

        System.out.println("合并前:");
        System.out.println("Target - 标题: " + target.getTitle());
        System.out.println("Target - 作者: " + target.getAuthor());
        System.out.println("Target - 类型: " + target.getType());
        System.out.println("Target - 粉丝数: " + target.getFansNum());
        System.out.println("Target - 是否原创: " + target.getIsOriginal());
        System.out.println("Target - 站点标签: " + target.getSiteMeta());

        // 执行字段合并
        esBeanMarkService.mergeFrom(src, target);

        System.out.println("\n合并后:");
        System.out.println("Target - 标题: " + target.getTitle());
        System.out.println("Target - 作者: " + target.getAuthor());
        System.out.println("Target - 类型: " + target.getType());
        System.out.println("Target - 粉丝数: " + target.getFansNum());
        System.out.println("Target - 是否原创: " + target.getIsOriginal());
        System.out.println("Target - 站点标签: " + target.getSiteMeta());
        System.out.println("Target - 创建时间: " + target.getCreateTime());

        // 验证字段是否正确合并
        assert "新标题".equals(target.getTitle());
        assert "新作者".equals(target.getAuthor());
        assert Integer.valueOf(1).equals(target.getType());
        assert Integer.valueOf(1000).equals(target.getFansNum());
        assert Boolean.TRUE.equals(target.getIsOriginal());
        assert target.getSiteMeta().contains("标签1");
        assert target.getSiteMeta().contains("标签2");
        assert target.getCreateTime() != null; // 创建时间应该保持不变
    }

    @Test
    public void testSaveOrUpdateEsBeanMark() {
        // 创建测试数据
        EsBeanMarkVO vo = new EsBeanMarkVO();
        vo.setArticleId("test-article-002");
        vo.setTitle("测试标题");
        vo.setAuthor("测试作者");
        vo.setType(1);
        vo.setFansNum(500);

        // 第一次保存（新增）
        boolean result1 = esBeanMarkService.saveOrUpdateEsBeanMark(vo);
        System.out.println("第一次保存结果: " + result1);

        // 修改数据
        vo.setTitle("修改后的标题");
        vo.setFansNum(1000);

        // 第二次保存（更新，会使用 mergeFrom 比较字段）
        boolean result2 = esBeanMarkService.saveOrUpdateEsBeanMark(vo);
        System.out.println("第二次保存结果: " + result2);

        // 查询验证
        EsBeanMarkVO queryVO = new EsBeanMarkVO();
        queryVO.setArticleId("test-article-002");
        EsBeanMarkVO result = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        
        if (result != null) {
            System.out.println("查询结果 - 标题: " + result.getTitle());
            System.out.println("查询结果 - 粉丝数: " + result.getFansNum());
            
            assert "修改后的标题".equals(result.getTitle());
            assert Integer.valueOf(1000).equals(result.getFansNum());
        }
    }

    @Test
    public void testSaveFromEsBeanMark() {
        // 创建 EsBeanMark 对象
        EsBeanMark esBeanMark = new EsBeanMark();
        esBeanMark.setArticleId("test-article-003");
        esBeanMark.setTitle("ES数据标题");
        esBeanMark.setAuthor("ES数据作者");
        esBeanMark.setType(2);
        esBeanMark.setContentMeta(Arrays.asList("内容标签1", "内容标签2"));
        esBeanMark.setReadNum(2000);

        // 保存
        boolean result = esBeanMarkService.saveFromEsBeanMark(esBeanMark);
        System.out.println("从EsBeanMark保存结果: " + result);

        // 查询验证
        EsBeanMarkVO queryVO = new EsBeanMarkVO();
        queryVO.setArticleId("test-article-003");
        EsBeanMarkVO queryResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        
        if (queryResult != null) {
            System.out.println("查询结果 - 标题: " + queryResult.getTitle());
            System.out.println("查询结果 - 阅读数: " + queryResult.getReadNum());
            System.out.println("查询结果 - 内容标签: " + queryResult.getContentMeta());
        }
    }
}
