package com.boryou.web.module.mark.service;

import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;

/**
 * EsBeanMarkService 封装方法测试类
 * 测试封装后的字段比较和设置方法
 */
@SpringBootTest
public class EsBeanMarkServiceEncapsulatedTest {

    @Resource
    private EsBeanMarkService esBeanMarkService;

    @Test
    public void testEncapsulatedFieldUpdate() {
        // 创建测试用户
        SysUser user = new SysUser();
        user.setUserId(3L);
        user.setUserName("encapsulatedTestUser");
        user.setDeptId(300L);

        System.out.println("=== 测试封装后的字段更新方法 ===");

        // 创建初始数据
        EsBeanMarkVO initialVO = new EsBeanMarkVO();
        initialVO.setArticleId("test-encapsulated-001");
        initialVO.setTitle("初始标题");
        initialVO.setAuthor("初始作者");
        initialVO.setType(1);
        initialVO.setFansNum(100);
        initialVO.setReadNum(200);
        initialVO.setLikeNum(50);
        initialVO.setIsOriginal(false);
        initialVO.setSiteMeta(Arrays.asList("初始标签1", "初始标签2"));
        initialVO.setContentMeta(Arrays.asList("初始内容标签"));
        initialVO.setPublishTime(new Date());

        // 第一次保存（新增）
        System.out.println("--- 新增数据 ---");
        boolean addResult = esBeanMarkService.saveOrUpdateEsBeanMark(initialVO, user);
        System.out.println("新增结果: " + addResult);

        // 查询初始数据
        EsBeanMarkVO queryVO = new EsBeanMarkVO();
        queryVO.setArticleId("test-encapsulated-001");
        EsBeanMarkVO initialResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (initialResult != null) {
            System.out.println("初始数据:");
            System.out.println("- 标题: " + initialResult.getTitle());
            System.out.println("- 作者: " + initialResult.getAuthor());
            System.out.println("- 粉丝数: " + initialResult.getFansNum());
            System.out.println("- 站点标签: " + initialResult.getSiteMeta());
        }

        // 测试字符串字段更新
        System.out.println("\n--- 测试字符串字段更新 ---");
        EsBeanMarkVO stringUpdateVO = new EsBeanMarkVO();
        stringUpdateVO.setArticleId("test-encapsulated-001");
        stringUpdateVO.setTitle("更新后的标题");  // 会更新
        stringUpdateVO.setAuthor("初始作者");     // 不会更新（相同）
        stringUpdateVO.setSummary("新增摘要");   // 会更新（原来为空）

        boolean stringUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(stringUpdateVO, user);
        System.out.println("字符串字段更新结果: " + stringUpdateResult);

        // 验证字符串字段更新
        EsBeanMarkVO stringResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (stringResult != null) {
            System.out.println("字符串更新后:");
            System.out.println("- 标题: " + stringResult.getTitle() + " (应该是: 更新后的标题)");
            System.out.println("- 作者: " + stringResult.getAuthor() + " (应该是: 初始作者)");
            System.out.println("- 摘要: " + stringResult.getSummary() + " (应该是: 新增摘要)");
        }

        // 测试数值字段更新
        System.out.println("\n--- 测试数值字段更新 ---");
        EsBeanMarkVO numericUpdateVO = new EsBeanMarkVO();
        numericUpdateVO.setArticleId("test-encapsulated-001");
        numericUpdateVO.setFansNum(500);        // 会更新
        numericUpdateVO.setReadNum(200);        // 不会更新（相同）
        numericUpdateVO.setLikeNum(100);        // 会更新
        numericUpdateVO.setType(2);             // 会更新

        boolean numericUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(numericUpdateVO, user);
        System.out.println("数值字段更新结果: " + numericUpdateResult);

        // 验证数值字段更新
        EsBeanMarkVO numericResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (numericResult != null) {
            System.out.println("数值更新后:");
            System.out.println("- 粉丝数: " + numericResult.getFansNum() + " (应该是: 500)");
            System.out.println("- 阅读数: " + numericResult.getReadNum() + " (应该是: 200)");
            System.out.println("- 点赞数: " + numericResult.getLikeNum() + " (应该是: 100)");
            System.out.println("- 类型: " + numericResult.getType() + " (应该是: 2)");
        }

        // 测试布尔字段更新
        System.out.println("\n--- 测试布尔字段更新 ---");
        EsBeanMarkVO booleanUpdateVO = new EsBeanMarkVO();
        booleanUpdateVO.setArticleId("test-encapsulated-001");
        booleanUpdateVO.setIsOriginal(true);    // 会更新
        booleanUpdateVO.setIsSpam(false);       // 会更新（原来为空）

        boolean booleanUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(booleanUpdateVO, user);
        System.out.println("布尔字段更新结果: " + booleanUpdateResult);

        // 验证布尔字段更新
        EsBeanMarkVO booleanResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (booleanResult != null) {
            System.out.println("布尔更新后:");
            System.out.println("- 是否原创: " + booleanResult.getIsOriginal() + " (应该是: true)");
            System.out.println("- 是否垃圾: " + booleanResult.getIsSpam() + " (应该是: false)");
        }

        // 测试列表字段更新
        System.out.println("\n--- 测试列表字段更新 ---");
        EsBeanMarkVO listUpdateVO = new EsBeanMarkVO();
        listUpdateVO.setArticleId("test-encapsulated-001");
        listUpdateVO.setSiteMeta(Arrays.asList("新标签1", "新标签2", "新标签3"));  // 会更新
        listUpdateVO.setContentMeta(Arrays.asList("初始内容标签"));              // 不会更新（相同）
        listUpdateVO.setPicUrl(Arrays.asList("图片1.jpg", "图片2.jpg"));        // 会更新（原来为空）

        boolean listUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(listUpdateVO, user);
        System.out.println("列表字段更新结果: " + listUpdateResult);

        // 验证列表字段更新
        EsBeanMarkVO listResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (listResult != null) {
            System.out.println("列表更新后:");
            System.out.println("- 站点标签: " + listResult.getSiteMeta() + " (应该是: [新标签1, 新标签2, 新标签3])");
            System.out.println("- 内容标签: " + listResult.getContentMeta() + " (应该是: [初始内容标签])");
            System.out.println("- 图片链接: " + listResult.getPicUrl() + " (应该是: [图片1.jpg, 图片2.jpg])");
        }

        // 测试无变化更新
        System.out.println("\n--- 测试无变化更新 ---");
        EsBeanMarkVO noChangeVO = new EsBeanMarkVO();
        noChangeVO.setArticleId("test-encapsulated-001");
        noChangeVO.setTitle("更新后的标题");     // 相同，不会更新
        noChangeVO.setFansNum(500);           // 相同，不会更新
        noChangeVO.setIsOriginal(true);       // 相同，不会更新

        boolean noChangeResult = esBeanMarkService.saveOrUpdateEsBeanMark(noChangeVO, user);
        System.out.println("无变化更新结果: " + noChangeResult + " (应该是: true)");

        // 最终验证
        EsBeanMarkVO finalResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (finalResult != null) {
            System.out.println("\n=== 最终验证 ===");
            assert "更新后的标题".equals(finalResult.getTitle());
            assert "初始作者".equals(finalResult.getAuthor());
            assert "新增摘要".equals(finalResult.getSummary());
            assert Integer.valueOf(500).equals(finalResult.getFansNum());
            assert Integer.valueOf(200).equals(finalResult.getReadNum());
            assert Integer.valueOf(100).equals(finalResult.getLikeNum());
            assert Integer.valueOf(2).equals(finalResult.getType());
            assert Boolean.TRUE.equals(finalResult.getIsOriginal());
            assert Boolean.FALSE.equals(finalResult.getIsSpam());
            assert finalResult.getSiteMeta().contains("新标签1");
            assert finalResult.getContentMeta().contains("初始内容标签");
            assert finalResult.getPicUrl().contains("图片1.jpg");

            System.out.println("所有断言通过！封装方法工作正常。");
        }
    }

    @Test
    public void testFieldUpdatePerformance() {
        System.out.println("=== 测试字段更新性能 ===");

        // 创建测试用户
        SysUser user = new SysUser();
        user.setUserId(4L);
        user.setUserName("performanceTestUser");
        user.setDeptId(400L);

        // 创建初始数据
        EsBeanMarkVO initialVO = new EsBeanMarkVO();
        initialVO.setArticleId("test-performance-001");
        initialVO.setTitle("性能测试标题");
        initialVO.setAuthor("性能测试作者");
        initialVO.setType(1);
        initialVO.setFansNum(1000);

        // 新增
        esBeanMarkService.saveOrUpdateEsBeanMark(initialVO, user);

        // 测试多次更新相同数据（应该不会执行SQL更新）
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 10; i++) {
            EsBeanMarkVO sameVO = new EsBeanMarkVO();
            sameVO.setArticleId("test-performance-001");
            sameVO.setTitle("性能测试标题");    // 相同值
            sameVO.setAuthor("性能测试作者");   // 相同值
            sameVO.setType(1);               // 相同值
            sameVO.setFansNum(1000);         // 相同值

            esBeanMarkService.saveOrUpdateEsBeanMark(sameVO, user);
        }
        long endTime = System.currentTimeMillis();

        System.out.println("10次相同数据更新耗时: " + (endTime - startTime) + "ms");
        System.out.println("由于使用了字段比较，相同数据不会执行SQL更新，性能较好。");
    }
}
