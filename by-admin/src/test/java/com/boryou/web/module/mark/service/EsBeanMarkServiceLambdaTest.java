package com.boryou.web.module.mark.service;

import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;

/**
 * EsBeanMarkService Lambda Update 测试类
 * 测试使用 lambdaUpdate().set() 方法的字段更新功能
 */
@SpringBootTest
public class EsBeanMarkServiceLambdaTest {

    @Resource
    private EsBeanMarkService esBeanMarkService;

    @Test
    public void testSaveOrUpdateWithLambdaSet() {
        // 创建测试用户
        SysUser user = new SysUser();
        user.setUserId(1L);
        user.setUserName("testUser");
        user.setDeptId(100L);

        // 创建测试数据
        EsBeanMarkVO vo = new EsBeanMarkVO();
        vo.setArticleId("test-lambda-001");
        vo.setTitle("初始标题");
        vo.setAuthor("初始作者");
        vo.setType(1);
        vo.setFansNum(500);
        vo.setSiteMeta(Arrays.asList("标签1", "标签2"));
        vo.setIsOriginal(false);

        System.out.println("=== 第一次保存（新增）===");
        boolean result1 = esBeanMarkService.saveOrUpdateEsBeanMark(vo, user);
        System.out.println("新增结果: " + result1);

        // 查询验证
        EsBeanMarkVO queryVO = new EsBeanMarkVO();
        queryVO.setArticleId("test-lambda-001");
        EsBeanMarkVO queryResult1 = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (queryResult1 != null) {
            System.out.println("新增后查询 - 标题: " + queryResult1.getTitle());
            System.out.println("新增后查询 - 作者: " + queryResult1.getAuthor());
            System.out.println("新增后查询 - 粉丝数: " + queryResult1.getFansNum());
            System.out.println("新增后查询 - 是否原创: " + queryResult1.getIsOriginal());
        }

        System.out.println("\n=== 第二次保存（更新部分字段）===");
        // 修改部分数据
        vo.setTitle("修改后的标题");  // 这个字段会更新
        vo.setFansNum(1000);        // 这个字段会更新
        vo.setAuthor("初始作者");    // 这个字段不会更新（值相同）
        vo.setIsOriginal(true);     // 这个字段会更新

        boolean result2 = esBeanMarkService.saveOrUpdateEsBeanMark(vo, user);
        System.out.println("更新结果: " + result2);

        // 查询验证
        EsBeanMarkVO queryResult2 = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (queryResult2 != null) {
            System.out.println("更新后查询 - 标题: " + queryResult2.getTitle());
            System.out.println("更新后查询 - 作者: " + queryResult2.getAuthor());
            System.out.println("更新后查询 - 粉丝数: " + queryResult2.getFansNum());
            System.out.println("更新后查询 - 是否原创: " + queryResult2.getIsOriginal());
        }

        System.out.println("\n=== 第三次保存（所有字段都相同）===");
        // 所有字段都不变
        boolean result3 = esBeanMarkService.saveOrUpdateEsBeanMark(vo, user);
        System.out.println("无变化更新结果: " + result3);

        System.out.println("\n=== 第四次保存（测试列表字段更新）===");
        // 修改列表字段
        vo.setSiteMeta(Arrays.asList("新标签1", "新标签2", "新标签3"));
        vo.setContentMeta(Arrays.asList("内容标签1", "内容标签2"));

        boolean result4 = esBeanMarkService.saveOrUpdateEsBeanMark(vo, user);
        System.out.println("列表字段更新结果: " + result4);

        // 查询验证
        EsBeanMarkVO queryResult4 = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (queryResult4 != null) {
            System.out.println("列表更新后查询 - 站点标签: " + queryResult4.getSiteMeta());
            System.out.println("列表更新后查询 - 内容标签: " + queryResult4.getContentMeta());
        }

        // 验证断言
        assert "修改后的标题".equals(queryResult4.getTitle());
        assert Integer.valueOf(1000).equals(queryResult4.getFansNum());
        assert Boolean.TRUE.equals(queryResult4.getIsOriginal());
        assert queryResult4.getSiteMeta().contains("新标签1");
        assert queryResult4.getContentMeta().contains("内容标签1");

        System.out.println("\n=== 测试完成，所有断言通过 ===");
    }

    @Test
    public void testUpdateOnlyChangedFields() {
        // 创建测试用户
        SysUser user = new SysUser();
        user.setUserId(2L);
        user.setUserName("testUser2");
        user.setDeptId(200L);

        // 创建初始数据
        EsBeanMarkVO initialVO = new EsBeanMarkVO();
        initialVO.setArticleId("test-lambda-002");
        initialVO.setTitle("原始标题");
        initialVO.setAuthor("原始作者");
        initialVO.setType(1);
        initialVO.setFansNum(100);
        initialVO.setReadNum(200);
        initialVO.setLikeNum(50);
        initialVO.setIsOriginal(false);
        initialVO.setPublishTime(new Date());

        // 新增
        esBeanMarkService.saveOrUpdateEsBeanMark(initialVO, user);

        // 只修改部分字段
        EsBeanMarkVO updateVO = new EsBeanMarkVO();
        updateVO.setArticleId("test-lambda-002");
        updateVO.setTitle("原始标题");        // 相同，不会更新
        updateVO.setAuthor("新作者");        // 不同，会更新
        updateVO.setType(1);                // 相同，不会更新
        updateVO.setFansNum(500);           // 不同，会更新
        updateVO.setReadNum(200);           // 相同，不会更新
        updateVO.setLikeNum(null);          // null，不会更新
        updateVO.setIsOriginal(true);       // 不同，会更新
        // publishTime 不设置，不会更新

        System.out.println("=== 测试只更新变化的字段 ===");
        boolean updateResult = esBeanMarkService.saveOrUpdateEsBeanMark(updateVO, user);
        System.out.println("选择性更新结果: " + updateResult);

        // 查询验证
        EsBeanMarkVO queryVO = new EsBeanMarkVO();
        queryVO.setArticleId("test-lambda-002");
        EsBeanMarkVO finalResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);

        if (finalResult != null) {
            System.out.println("最终结果:");
            System.out.println("- 标题: " + finalResult.getTitle() + " (应该是: 原始标题)");
            System.out.println("- 作者: " + finalResult.getAuthor() + " (应该是: 新作者)");
            System.out.println("- 类型: " + finalResult.getType() + " (应该是: 1)");
            System.out.println("- 粉丝数: " + finalResult.getFansNum() + " (应该是: 500)");
            System.out.println("- 阅读数: " + finalResult.getReadNum() + " (应该是: 200)");
            System.out.println("- 点赞数: " + finalResult.getLikeNum() + " (应该是: 50)");
            System.out.println("- 是否原创: " + finalResult.getIsOriginal() + " (应该是: true)");
            System.out.println("- 发布时间: " + finalResult.getPublishTime() + " (应该保持原值)");

            // 验证只有变化的字段被更新
            assert "原始标题".equals(finalResult.getTitle());
            assert "新作者".equals(finalResult.getAuthor());
            assert Integer.valueOf(1).equals(finalResult.getType());
            assert Integer.valueOf(500).equals(finalResult.getFansNum());
            assert Integer.valueOf(200).equals(finalResult.getReadNum());
            assert Integer.valueOf(50).equals(finalResult.getLikeNum());
            assert Boolean.TRUE.equals(finalResult.getIsOriginal());
            assert finalResult.getPublishTime() != null;
        }

        System.out.println("=== 选择性更新测试完成 ===");
    }
}
