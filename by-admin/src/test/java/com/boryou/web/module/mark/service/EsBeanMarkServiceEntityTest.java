package com.boryou.web.module.mark.service;

import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;

import javax.annotation.Resource;
import java.util.Arrays;
import java.util.Date;

/**
 * EsBeanMarkService 实体类封装测试类
 * 测试使用实体类封装后的字段更新方法
 */
@SpringBootTest
public class EsBeanMarkServiceEntityTest {

    @Resource
    private EsBeanMarkService esBeanMarkService;

    @Test
    public void testEntityBasedFieldUpdate() {
        // 创建测试用户
        SysUser user = new SysUser();
        user.setUserId(7L);
        user.setUserName("entityTestUser");
        user.setDeptId(700L);

        System.out.println("=== 测试实体类封装的字段更新方法 ===");

        // 创建初始数据
        EsBeanMarkVO initialVO = new EsBeanMarkVO();
        initialVO.setArticleId("test-entity-001");
        initialVO.setTitle("实体封装测试标题");
        initialVO.setAuthor("实体封装测试作者");
        initialVO.setType(1);
        initialVO.setFansNum(100);
        initialVO.setReadNum(200);
        initialVO.setLikeNum(50);
        initialVO.setIsOriginal(false);
        initialVO.setSiteMeta(Arrays.asList("实体标签1", "实体标签2"));
        initialVO.setPublishTime(new Date());

        // 第一次保存（新增）
        System.out.println("--- 新增数据 ---");
        boolean addResult = esBeanMarkService.saveOrUpdateEsBeanMark(initialVO, user);
        System.out.println("新增结果: " + addResult);

        // 查询初始数据
        EsBeanMarkVO queryVO = new EsBeanMarkVO();
        queryVO.setArticleId("test-entity-001");
        EsBeanMarkVO initialResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (initialResult != null) {
            System.out.println("初始数据:");
            System.out.println("- 标题: " + initialResult.getTitle());
            System.out.println("- 作者: " + initialResult.getAuthor());
            System.out.println("- 类型: " + initialResult.getType());
            System.out.println("- 粉丝数: " + initialResult.getFansNum());
            System.out.println("- 是否原创: " + initialResult.getIsOriginal());
            System.out.println("- 站点标签: " + initialResult.getSiteMeta());
        }

        // 测试链式调用的字段更新
        System.out.println("\n--- 测试链式调用字段更新 ---");
        EsBeanMarkVO chainUpdateVO = new EsBeanMarkVO();
        chainUpdateVO.setArticleId("test-entity-001");
        
        // 同时更新多种类型的字段
        chainUpdateVO.setTitle("实体封装后的新标题");      // 字符串字段
        chainUpdateVO.setAuthor("实体封装测试作者");       // 字符串字段（相同，不会更新）
        chainUpdateVO.setSummary("新增的摘要内容");       // 字符串字段（新增）
        chainUpdateVO.setType(2);                       // 数值字段
        chainUpdateVO.setFansNum(500);                  // 数值字段
        chainUpdateVO.setReadNum(200);                  // 数值字段（相同，不会更新）
        chainUpdateVO.setIsOriginal(true);              // 布尔字段
        chainUpdateVO.setIsSpam(false);                 // 布尔字段（新增）
        chainUpdateVO.setSiteMeta(Arrays.asList("新实体标签1", "新实体标签2", "新实体标签3")); // 列表字段
        chainUpdateVO.setContentMeta(Arrays.asList("内容标签1", "内容标签2")); // 列表字段（新增）
        chainUpdateVO.setPublishTime(new Date(System.currentTimeMillis() + 3600000)); // 日期字段

        boolean chainUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(chainUpdateVO, user);
        System.out.println("链式调用更新结果: " + chainUpdateResult);

        // 验证链式调用更新结果
        EsBeanMarkVO chainResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (chainResult != null) {
            System.out.println("链式更新后:");
            System.out.println("- 标题: " + chainResult.getTitle() + " (应该是: 实体封装后的新标题)");
            System.out.println("- 作者: " + chainResult.getAuthor() + " (应该是: 实体封装测试作者)");
            System.out.println("- 摘要: " + chainResult.getSummary() + " (应该是: 新增的摘要内容)");
            System.out.println("- 类型: " + chainResult.getType() + " (应该是: 2)");
            System.out.println("- 粉丝数: " + chainResult.getFansNum() + " (应该是: 500)");
            System.out.println("- 阅读数: " + chainResult.getReadNum() + " (应该是: 200)");
            System.out.println("- 是否原创: " + chainResult.getIsOriginal() + " (应该是: true)");
            System.out.println("- 是否垃圾: " + chainResult.getIsSpam() + " (应该是: false)");
            System.out.println("- 站点标签: " + chainResult.getSiteMeta());
            System.out.println("- 内容标签: " + chainResult.getContentMeta());
            System.out.println("- 发布时间: " + chainResult.getPublishTime());
        }

        // 测试部分字段更新
        System.out.println("\n--- 测试部分字段更新 ---");
        EsBeanMarkVO partialUpdateVO = new EsBeanMarkVO();
        partialUpdateVO.setArticleId("test-entity-001");
        partialUpdateVO.setAuthorSex(1);                // 只更新作者性别
        partialUpdateVO.setAccountLevel(3);             // 只更新账号级别
        partialUpdateVO.setDomain("newdomain.com");     // 只更新域名

        boolean partialUpdateResult = esBeanMarkService.saveOrUpdateEsBeanMark(partialUpdateVO, user);
        System.out.println("部分字段更新结果: " + partialUpdateResult);

        // 验证部分字段更新
        EsBeanMarkVO partialResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (partialResult != null) {
            System.out.println("部分更新后:");
            System.out.println("- 标题: " + partialResult.getTitle() + " (应该保持: 实体封装后的新标题)");
            System.out.println("- 作者性别: " + partialResult.getAuthorSex() + " (应该是: 1)");
            System.out.println("- 账号级别: " + partialResult.getAccountLevel() + " (应该是: 3)");
            System.out.println("- 域名: " + partialResult.getDomain() + " (应该是: newdomain.com)");
            System.out.println("- 粉丝数: " + partialResult.getFansNum() + " (应该保持: 500)");
        }

        // 测试无变化更新
        System.out.println("\n--- 测试无变化更新 ---");
        EsBeanMarkVO noChangeVO = new EsBeanMarkVO();
        noChangeVO.setArticleId("test-entity-001");
        noChangeVO.setTitle("实体封装后的新标题");     // 相同，不会更新
        noChangeVO.setType(2);                     // 相同，不会更新
        noChangeVO.setFansNum(500);                // 相同，不会更新
        noChangeVO.setIsOriginal(true);            // 相同，不会更新

        boolean noChangeResult = esBeanMarkService.saveOrUpdateEsBeanMark(noChangeVO, user);
        System.out.println("无变化更新结果: " + noChangeResult + " (应该是: true)");

        // 最终验证
        EsBeanMarkVO finalResult = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        if (finalResult != null) {
            System.out.println("\n=== 最终验证 ===");
            assert "实体封装后的新标题".equals(finalResult.getTitle());
            assert "实体封装测试作者".equals(finalResult.getAuthor());
            assert "新增的摘要内容".equals(finalResult.getSummary());
            assert "newdomain.com".equals(finalResult.getDomain());
            assert Integer.valueOf(2).equals(finalResult.getType());
            assert Integer.valueOf(500).equals(finalResult.getFansNum());
            assert Integer.valueOf(200).equals(finalResult.getReadNum());
            assert Integer.valueOf(1).equals(finalResult.getAuthorSex());
            assert Integer.valueOf(3).equals(finalResult.getAccountLevel());
            assert Boolean.TRUE.equals(finalResult.getIsOriginal());
            assert Boolean.FALSE.equals(finalResult.getIsSpam());
            assert finalResult.getSiteMeta().contains("新实体标签1");
            assert finalResult.getContentMeta().contains("内容标签1");

            System.out.println("所有断言通过！实体类封装方法工作正常。");
        }
    }

    @Test
    public void testEntityPerformanceComparison() {
        System.out.println("=== 测试实体类封装性能对比 ===");

        // 创建测试用户
        SysUser user = new SysUser();
        user.setUserId(8L);
        user.setUserName("performanceTestUser");
        user.setDeptId(800L);

        // 创建初始数据
        EsBeanMarkVO initialVO = new EsBeanMarkVO();
        initialVO.setArticleId("test-performance-entity-001");
        initialVO.setTitle("性能测试标题");
        initialVO.setAuthor("性能测试作者");
        initialVO.setType(1);
        initialVO.setFansNum(1000);
        initialVO.setSiteMeta(Arrays.asList("性能标签1", "性能标签2"));

        // 新增
        esBeanMarkService.saveOrUpdateEsBeanMark(initialVO, user);

        // 测试多次相同数据更新（应该不会执行SQL更新）
        long startTime = System.currentTimeMillis();
        for (int i = 0; i < 20; i++) {
            EsBeanMarkVO sameVO = new EsBeanMarkVO();
            sameVO.setArticleId("test-performance-entity-001");
            sameVO.setTitle("性能测试标题");    // 相同值
            sameVO.setAuthor("性能测试作者");   // 相同值
            sameVO.setType(1);               // 相同值
            sameVO.setFansNum(1000);         // 相同值

            esBeanMarkService.saveOrUpdateEsBeanMark(sameVO, user);
        }
        long endTime = System.currentTimeMillis();

        System.out.println("20次相同数据更新耗时: " + (endTime - startTime) + "ms");

        // 测试多次不同数据更新
        startTime = System.currentTimeMillis();
        for (int i = 0; i < 10; i++) {
            EsBeanMarkVO diffVO = new EsBeanMarkVO();
            diffVO.setArticleId("test-performance-entity-001");
            diffVO.setFansNum(1000 + i);     // 不同值，会更新

            esBeanMarkService.saveOrUpdateEsBeanMark(diffVO, user);
        }
        endTime = System.currentTimeMillis();

        System.out.println("10次不同数据更新耗时: " + (endTime - startTime) + "ms");
        System.out.println("实体类封装提供了良好的性能和代码可读性。");
    }

    @Test
    public void testEntityFlexibility() {
        System.out.println("=== 测试实体类封装的灵活性 ===");

        // 创建测试用户
        SysUser user = new SysUser();
        user.setUserId(9L);
        user.setUserName("flexibilityTestUser");
        user.setDeptId(900L);

        // 测试只更新单个字段
        EsBeanMarkVO singleFieldVO = new EsBeanMarkVO();
        singleFieldVO.setArticleId("test-flexibility-001");
        singleFieldVO.setTitle("灵活性测试");

        boolean singleResult = esBeanMarkService.saveOrUpdateEsBeanMark(singleFieldVO, user);
        System.out.println("单字段新增结果: " + singleResult);

        // 测试只更新另一个字段
        EsBeanMarkVO anotherFieldVO = new EsBeanMarkVO();
        anotherFieldVO.setArticleId("test-flexibility-001");
        anotherFieldVO.setAuthor("灵活性测试作者");

        boolean anotherResult = esBeanMarkService.saveOrUpdateEsBeanMark(anotherFieldVO, user);
        System.out.println("另一字段更新结果: " + anotherResult);

        // 验证结果
        EsBeanMarkVO queryVO = new EsBeanMarkVO();
        queryVO.setArticleId("test-flexibility-001");
        EsBeanMarkVO result = esBeanMarkService.getEsBeanMarkByArticleId(queryVO);
        
        if (result != null) {
            System.out.println("灵活性测试结果:");
            System.out.println("- 标题: " + result.getTitle());
            System.out.println("- 作者: " + result.getAuthor());
            
            assert "灵活性测试".equals(result.getTitle());
            assert "灵活性测试作者".equals(result.getAuthor());
            
            System.out.println("灵活性测试通过！");
        }
    }
}
