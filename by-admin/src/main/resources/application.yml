# 项目相关配置
app:
  # 名称【注意修改 com.boryou.common.constant.Constants 的 PROJECT_PREFIX！】
  name: yq-boryou
  # 版本
  version: 1.0.0
  # 版权年份
  copyrightYear: 2024
  # 验证码类型 math 数组计算 char 字符验证
  captchaType: math

# 滑块验证码
aj:
  captcha:
    # 缓存类型
    cache-type: redis
    # blockPuzzle 滑块 clickWord 文字点选  default默认两者都实例化
    type: blockPuzzle
    # 右下角显示字
    water-mark: ""
    # 校验滑动拼图允许误差偏移量(默认5像素)
    slip-offset: 5
    # aes加密坐标开启或者禁用(true|false)
    aes-status: true
    # 滑动干扰项(0/1/2)
    interference-options: 2
    # 背景图片路径
    jigsaw: classpath:images/jigsaw

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: dev
  activiti:
    database-schema-update: false
    # 自动部署验证设置:true-开启（默认）、false-关闭  生成表
    check-process-definitions: false
    process-definition-location-prefix: classpath:/bpmn/
    history-level: full
    # 检测身份信息表是否存在
    db-identity-used: false
    #添加这个配置控制台就不会一直十几秒就查询了act_ru_timer_job表问题
    #在流程引擎启动就激活AsyncExecutor,异步 true  false 关闭  （切记关闭）
    async-executor-activate: false
# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*

# MyBatis Plus配置
mybatis-plus:
  # 搜索指定包别名
  typeAliasesPackage: com.boryou.**.domain
  # 配置mapper的扫描，找到所有的mapper.xml映射文件
  mapperLocations: classpath*:mapper/**/*Mapper.xml
  # 加载全局的配置文件
  configLocation: classpath:mybatis/mybatis-config.xml
