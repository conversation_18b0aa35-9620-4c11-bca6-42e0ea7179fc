<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.mapper.CloudWordMapper">

    <resultMap id="CloudWordMap" type="com.boryou.web.domain.vo.CloudWordVO">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="word" column="word" jdbcType="VARCHAR"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="planId" column="plan_id" jdbcType="BIGINT"/>
        <result property="state" column="state" jdbcType="CHAR"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="selectCloudWordVO">
        select id,
        word,
        user_id,
        plan_id,
        state,
        create_time,
        create_by,
        update_time,
        update_by,
        CASE WHEN plan_id IS NULL THEN 1 ELSE 2 END AS isPlan
        from by_cloud_word
    </sql>
    <insert id="insertCloudWord" keyColumn="id" keyProperty="id" parameterType="com.boryou.web.domain.vo.CloudWordVO"
            useGeneratedKeys="true">
        insert into by_cloud_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="word != null">word,</if>
            <if test="userId != null">user_id,</if>
            <if test="planId != null">plan_id,</if>
            <if test="state != null">state,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="word != null">#{word,jdbcType=VARCHAR},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="planId != null">#{planId,jdbcType=BIGINT},</if>
            <if test="state != null">#{state,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <update id="updateWordStateByIds" parameterType="com.boryou.web.domain.vo.CloudWordVO">
        update by_cloud_word
        <set>
            <if test="state != null">
                state = #{state,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
        </set>
        where user_id = #{userId} AND find_in_set(id, #{ids})
    </update>
    <select id="selectHotWords" resultType="com.boryou.web.domain.vo.CloudWordVO" resultMap="CloudWordMap">
        <include refid="selectCloudWordVO"/>
        <where>
            <if test="userId != null and userId != ''">
                AND user_id = #{userId}
            </if>
            <if test="planId != null and planId != ''">
                AND plan_id = #{planId}
            </if>
            <if test="word != null and word != ''">
                AND word = #{word}
            </if>
            <if test="state != null and state != ''">
                AND state = #{state}
            </if>
            <if test="isPlan != null and isPlan == 2">AND plan_id is not null</if>
            <if test="isPlan != null and isPlan == 1">AND plan_id is null</if>
        </where>
        order by update_time desc
    </select>

</mapper>
