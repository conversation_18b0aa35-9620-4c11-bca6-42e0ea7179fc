<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.mapper.SearchWordMapper">

    <resultMap id="BaseResultMap" type="com.boryou.web.domain.SearchWord">
        <id property="id" column="id" jdbcType="BIGINT"/>
        <result property="word" column="word" jdbcType="VARCHAR"/>
        <result property="num" column="num" jdbcType="BIGINT"/>
        <result property="userId" column="user_id" jdbcType="BIGINT"/>
        <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
        <result property="createBy" column="create_by" jdbcType="VARCHAR"/>
        <result property="updateTime" column="update_time" jdbcType="TIMESTAMP"/>
        <result property="updateBy" column="update_by" jdbcType="VARCHAR"/>
    </resultMap>

    <sql id="selectHotWordVO">
        select id,
        word,
        num,
        user_id,
        create_time,
        create_by,
        update_time,
        update_by
        from by_search_word
    </sql>

    <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
        <include refid="selectHotWordVO"/>
        where id = #{id,jdbcType=BIGINT}
    </select>
    <select id="selectHotWordByUserIdCount" resultType="searchWord" resultMap="BaseResultMap">
        <include refid="selectHotWordVO"/>
        where user_id = #{userId} order by num desc limit #{count}
    </select>
    <select id="selectHotWordByUserIdWord" resultType="searchWord" resultMap="BaseResultMap">
        <include refid="selectHotWordVO"/>
        where user_id = #{userId} and word = #{word}
    </select>

    <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
        delete from by_search_word
        where id = #{id,jdbcType=BIGINT}
    </delete>
    <delete id="deleteWord">
        delete from by_search_word
        where user_id = #{userId,jdbcType=BIGINT} and word = #{word,jdbcType=VARCHAR}
    </delete>
    <insert id="insert" keyColumn="id" keyProperty="id" parameterType="com.boryou.web.domain.SearchWord"
            useGeneratedKeys="true">
        insert into by_search_word
        ( id,word,num
        ,user_id,create_time,create_by
        ,update_time,update_by)
        values (#{id,jdbcType=BIGINT},#{word,jdbcType=VARCHAR},#{num,jdbcType=BIGINT}
        ,#{userId,jdbcType=BIGINT},#{createTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=VARCHAR}
        ,#{updateTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR})
    </insert>
    <insert id="insertSelective" keyColumn="id" keyProperty="id" parameterType="com.boryou.web.domain.SearchWord"
            useGeneratedKeys="true">
        insert into by_search_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="word != null">word,</if>
            <if test="num != null">num,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="word != null">#{word,jdbcType=VARCHAR},</if>
            <if test="num != null">#{num,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    <insert id="addWord">
        insert into by_search_word
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,</if>
            <if test="word != null">word,</if>
            <if test="num != null">num,</if>
            <if test="userId != null">user_id,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null">create_by,</if>
            <if test="updateTime != null">update_time,</if>
            <if test="updateBy != null">update_by,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="word != null">#{word,jdbcType=VARCHAR},</if>
            <if test="num != null">#{num,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
        </trim>
    </insert>

    <update id="updateWordById" parameterType="com.boryou.web.domain.SearchWord">
        update by_search_word
        <set>
            <if test="word != null">
                word = #{word,jdbcType=VARCHAR},
            </if>
            <if test="num != null">
                num = #{num,jdbcType=BIGINT},
            </if>
            <if test="userId != null">
                user_id = #{userId,jdbcType=BIGINT},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="createBy != null">
                create_by = #{createBy,jdbcType=VARCHAR},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.boryou.web.domain.SearchWord">
        update by_search_word
        set
        word = #{word,jdbcType=VARCHAR},
        num = #{num,jdbcType=BIGINT},
        user_id = #{userId,jdbcType=BIGINT},
        create_time = #{createTime,jdbcType=TIMESTAMP},
        create_by = #{createBy,jdbcType=VARCHAR},
        update_time = #{updateTime,jdbcType=TIMESTAMP},
        update_by = #{updateBy,jdbcType=VARCHAR}
        where id = #{id,jdbcType=BIGINT}
    </update>

</mapper>
