<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.loginlog.mapper.ByViewRecordMapper">

    <resultMap type="ByViewRecord" id="ByViewRecordResult">
        <result property="id" column="id"/>
        <result property="phone" column="phone"/>
        <result property="createTime" column="create_time"/>
        <result property="userId" column="user_id"/>
    </resultMap>

    <sql id="selectByViewRecordVo">
        select id, phone, create_time, user_id from by_view_record
    </sql>

    <select id="selectByViewRecordList" parameterType="ByViewRecord" resultMap="ByViewRecordResult">
        <include refid="selectByViewRecordVo"/>
        <where>
            <if test="phone != null  and phone != ''">
                and phone = #{phone}
            </if>
            <if test="ids != null">
                and id in
                <foreach item="id" collection="ids" open="(" separator="," close=")">
                    #{id}
                </foreach>
            </if>

        </where>
    </select>

    <select id="selectByViewRecordById" parameterType="Long"
            resultMap="ByViewRecordResult">
        <include refid="selectByViewRecordVo"/>
        where id = #{id}
    </select>

    <insert id="insertByViewRecord" parameterType="ByViewRecord" useGeneratedKeys="true"
            keyProperty="id">
        insert into by_view_record
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="phone != null">phone,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="userId != null">user_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="phone != null">#{phone},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="userId != null">#{userId},
            </if>
        </trim>
    </insert>

    <update id="updateByViewRecord" parameterType="ByViewRecord">
        update by_view_record
        <trim prefix="SET" suffixOverrides=",">
            <if test="phone != null">phone =
                #{phone},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="userId != null">user_id =
                #{userId},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteByViewRecordById" parameterType="Long">
        delete from by_view_record where id = #{id}
    </delete>

    <delete id="deleteByViewRecordByIds" parameterType="String">
        delete from by_view_record where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
