<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.search.mapper.FilterInfoMapper">
    <resultMap id="filterInfoMap" type="com.boryou.web.module.search.entity.FilterInfo">
        <id property="id" column="id"/>
        <result property="planId" column="plan_id"/>
        <result property="filterTime" column="filter_time"/>
        <result property="indexId" column="index_id"/>
        <result property="type" column="type"/>
        <result property="typeName" column="type_name"/>
        <result property="publishTime" column="publish_time"/>
        <result property="title" column="title"/>
        <result property="text" column="text"/>
        <result property="url" column="url"/>
        <result property="host" column="host"/>
        <result property="author" column="author"/>
        <result property="authorId" column="author_id"/>
        <result property="accountLevel" column="account_level"/>
        <result property="siteAreaCodeName" column="site_area_code_name"/>
        <result property="emotionFlag" column="emotion_flag"/>
        <result property="isOriginal" column="is_original"/>
        <result property="hitWords" column="hit_words"/>
        <result property="md5" column="md5"/>
        <result property="isRead" column="is_read"/>
        <result property="planName" column="plan_name"/>
    </resultMap>


    <select id="selectFilterInfo" resultMap="filterInfoMap"
            parameterType="com.boryou.web.module.search.entity.FilterInfo">
        select f.id,f.plan_id,f.filter_time,f.index_id,f.type,type_name,f.publish_time,title,text,url,host,f.md5
        ,author,author_id,account_level,site_area_code_name,emotion_flag,is_original,hit_words
        ,CASE WHEN r.id IS NOT NULL THEN 1 ELSE 0 END AS is_read,p.plan_name
        from by_filter_info f left join by_read_data r on f.index_id = r.index_id
        left join by_plan p on f.plan_id = p.plan_id
        <where>
            <if test="id != null">AND f.id = #{id,jdbcType=BIGINT}</if>
            <if test="planId != null">AND f.plan_id = #{planId,jdbcType=BIGINT}</if>
            <if test="planIds != null ">AND find_in_set(f.plan_id, #{planIds,jdbcType=VARCHAR})</if>
            <if test="type != null">AND f.type = #{type,jdbcType=BIGINT}</if>
            <if test="typeId != null">AND p.type_id = #{typeId,jdbcType=BIGINT}</if>
            <if test="emotionFlag != null">AND emotion_flag = #{emotionFlag,jdbcType=BIGINT}</if>
            <if test="isRead != null and isRead == 1">AND r.id is not null</if>
            <if test="isRead != null and isRead == 0">AND r.id is null</if>
            <if test="text != null">AND (title like concat('%', #{text,jdbcType=VARCHAR}, '%') OR text like concat('%',
                #{text,jdbcType=VARCHAR}, '%'))
            </if>
        </where>
    </select>


    <select id="selectFilterInfoById" resultMap="filterInfoMap" parameterType="java.lang.Long">
        select id,plan_id,filter_time,index_id,type,type_name,publish_time,title,text,url,host
        ,author,author_id,account_level,site_area_code_name,emotion_flag,is_original,hit_words,f.md5
        from by_filter_info where id = #{id}
    </select>
    <select id="selectFilterInfoByIndexIdAndPlanId" resultMap="filterInfoMap" parameterType="java.lang.Long">
        select id,plan_id,filter_time,index_id,type,type_name,publish_time,title,text,url,host
        ,author,author_id,account_level,site_area_code_name,emotion_flag,is_original,hit_words
        from by_filter_info where index_id = #{indexId} AND plan_id = #{planId}
    </select>

    <insert id="insertFilterInfo" keyColumn="id" keyProperty="id"
            parameterType="com.boryou.web.module.search.entity.FilterInfo" useGeneratedKeys="true">
        insert into by_filter_info
        ( id,plan_id,filter_time,index_id,type,type_name,publish_time,title,text,url,host
        ,author,author_id,account_level,site_area_code_name,emotion_flag,is_original,hit_words,md5)
        values
        (#{id,jdbcType=BIGINT},#{planId,jdbcType=BIGINT},#{filterTime,jdbcType=TIMESTAMP},#{indexId,jdbcType=BIGINT}
        ,#{type,jdbcType=BIGINT},#{typeName,jdbcType=VARCHAR},#{publishTime,jdbcType=TIMESTAMP}
        ,#{title,jdbcType=VARCHAR},#{text,jdbcType=VARCHAR},#{url,jdbcType=VARCHAR},#{host,jdbcType=VARCHAR}
        ,#{author,jdbcType=VARCHAR},#{authorId,jdbcType=VARCHAR},#{accountLevel,jdbcType=BIGINT}
        ,#{siteAreaCodeName,jdbcType=VARCHAR},#{emotionFlag,jdbcType=VARCHAR},#{isOriginal,jdbcType=VARCHAR}
        ,#{hitWords,jdbcType=VARCHAR},#{md5,jdbcType=VARCHAR})
    </insert>


    <delete id="deleteFilterInfoByIds" parameterType="String">
        delete from by_filter_info where find_in_set(id, #{ids})
    </delete>


    <select id="selectFilterInfoBySize" parameterType="com.boryou.web.module.search.entity.FilterInfo"
            resultType="String">
        select f.id
        from by_filter_info f left join by_read_data r on f.index_id = r.index_id
        left join by_plan p on f.plan_id = p.plan_id
        <where>
            <if test="id != null">AND f.id = #{id,jdbcType=BIGINT}</if>
            <if test="planId != null">AND f.plan_id = #{planId,jdbcType=BIGINT}</if>
            <if test="type != null">AND f.type = #{type,jdbcType=BIGINT}</if>
            <if test="typeId != null">AND f.type_id = #{typeId,jdbcType=BIGINT}</if>
            <if test="emotionFlag != null">AND emotion_flag = #{emotionFlag,jdbcType=BIGINT}</if>
            <if test="isRead != null and isRead == 1">AND r.id is not null</if>
            <if test="isRead != null and isRead == 0">AND r.id is null</if>
            <if test="text != null">AND text like concat('%', #{text,jdbcType=VARCHAR}, '%')</if>
        </where>
        <if test="size != null">limit #{pageNum,jdbcType=BIGINT},#{size,jdbcType=BIGINT}</if>
    </select>

</mapper>
