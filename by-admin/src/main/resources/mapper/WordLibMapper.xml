<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.search.mapper.WordLibMapper">
    <resultMap id="wordMap" type="com.boryou.web.module.search.entity.WordLib">
        <id property="id" column="id"/>
        <result property="name" column="name"/>
        <result property="word" column="word"/>
        <result property="userId" column="user_id"/>
        <result property="typeId" column="type_id"/>
        <result property="state" column="state"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
    </resultMap>

    <resultMap id="wordLibMap" type="com.boryou.web.module.search.entity.WordLibType">
        <id property="id" column="id"/>
        <result property="type" column="type"/>
        <result property="userId" column="user_id"/>
        <result property="state" column="state"/>
    </resultMap>

    <select id="selectWordLibTree" resultType="com.boryou.web.module.search.entity.vo.WordLibVO"
            parameterType="com.boryou.web.module.search.entity.vo.WordLibVO">
        select l.id,t.type,l.name,l.word,l.type_id typeId,l.create_time createTime,l.create_by createBy from
        by_word_lib_type t left join by_word_lib l on t.id = l.type_id
        <where>
            <if test="id != null">AND l.id = #{id,jdbcType=BIGINT}</if>
            <if test="userId != null">AND l.user_id = #{userId,jdbcType=BIGINT}</if>
            <if test="type != null">AND t.type = #{type,jdbcType=VARCHAR}</if>
            <if test="state != null">AND l.state = #{state,jdbcType=VARCHAR}</if>
            <if test="name != null">AND l.name like concat('%', #{name,jdbcType=VARCHAR}, '%')</if>
        </where>
    </select>

    <select id="selectWordLib" resultMap="wordLibMap" parameterType="com.boryou.web.module.search.entity.WordLibType">
        select id,type,user_id,state,create_by from by_word_lib_type
        <where>
            <if test="id != null">AND id = #{id,jdbcType=BIGINT}</if>
            <if test="userId != null">AND user_id = #{userId,jdbcType=BIGINT}</if>
            <if test="type != null">AND type = #{type,jdbcType=VARCHAR}</if>
            <if test="state != null">AND state = #{state,jdbcType=VARCHAR}</if>
        </where>
    </select>

    <select id="selectWord" resultMap="wordMap" parameterType="com.boryou.web.module.search.entity.WordLib">
        select id,name,word,user_id,state,type_id,create_time,create_by from by_word_lib
        <where>
            <if test="userId != null">AND user_id = #{userId,jdbcType=BIGINT}</if>
            <if test="typeId != null">AND type_id = #{typeId,jdbcType=BIGINT}</if>
            <if test="name != null">AND name like concat('%', #{name,jdbcType=VARCHAR}, '%')</if>
            <if test="word != null">AND word like concat('%', #{word,jdbcType=VARCHAR}, '%')</if>
            <if test="state != null">AND state = #{state,jdbcType=VARCHAR}</if>
        </where>
    </select>

    <select id="checkWord" resultMap="wordMap" parameterType="com.boryou.web.module.search.entity.WordLib">
        select id,name,word from by_word_lib
        <where>
            <if test="userId != null">AND user_id = #{userId,jdbcType=BIGINT}</if>
            <if test="typeId != null">AND type_id = #{typeId,jdbcType=BIGINT}</if>
            <if test="name != null">AND name = #{name,jdbcType=VARCHAR}</if>
        </where>
    </select>

    <select id="selectWordById" resultMap="wordMap" parameterType="java.lang.Long">
        select id,name,word,user_id,state,type_id from by_word_lib where id = #{id}
    </select>

    <insert id="insertWordLib" keyColumn="id" keyProperty="id"
            parameterType="com.boryou.web.module.search.entity.WordLibType" useGeneratedKeys="true">
        insert into by_word_lib_type
        ( id,type,user_id,state,create_time,create_by
        ,update_time,update_by)
        values (#{id,jdbcType=BIGINT},#{type,jdbcType=VARCHAR},#{userId,jdbcType=BIGINT},#{state,jdbcType=BIGINT}
        ,#{createTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=VARCHAR}
        ,#{updateTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR})
    </insert>

    <insert id="insertWord" keyColumn="id" keyProperty="id" parameterType="com.boryou.web.module.search.entity.WordLib"
            useGeneratedKeys="true">
        insert into by_word_lib
        ( id,type_id,name,word
        ,user_id,state
        ,create_time,create_by
        ,update_time,update_by)
        values (#{id,jdbcType=BIGINT},#{typeId,jdbcType=BIGINT},#{name,jdbcType=VARCHAR},#{word,jdbcType=VARCHAR}
        ,#{userId,jdbcType=BIGINT},#{state,jdbcType=BIGINT}
        ,#{createTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=VARCHAR}
        ,#{updateTime,jdbcType=TIMESTAMP},#{updateBy,jdbcType=VARCHAR})
    </insert>

    <update id="updateWordLibById" parameterType="com.boryou.web.module.search.entity.WordLibType">
        update by_word_lib_type
        <set>
            <if test="type != null">
                type = #{type,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <update id="updateWordById" parameterType="com.boryou.web.module.search.entity.WordLib">
        update by_word_lib
        <set>
            <if test="typeId != null">
                type_id = #{typeId,jdbcType=BIGINT},
            </if>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="word != null">
                word = #{word,jdbcType=VARCHAR},
            </if>
            <if test="state != null">
                state = #{state,jdbcType=BIGINT},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateBy != null">
                update_by = #{updateBy,jdbcType=VARCHAR},
            </if>
        </set>
        where id = #{id,jdbcType=BIGINT}
    </update>

    <delete id="deleteWordLibByIds" parameterType="String">
        delete from by_word_lib_type where find_in_set(id, #{ids})
    </delete>

    <delete id="deleteWordByIds" parameterType="String">
        delete from by_word_lib where find_in_set(id, #{ids})
    </delete>
</mapper>
