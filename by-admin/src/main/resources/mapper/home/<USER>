<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.home.mapper.LawyereMapper">

    <select id="selectLawyerList" resultType="com.boryou.web.module.home.entity.Lawyer">
        select id,name,media_type source,media_dict media_dict, user_id userId from by_lawyer where del_flag='0'
    </select>

    <select id="getAreaCodeList" resultType="java.lang.String">
        select distinct area_code from sys_dept where area_code is not null
    </select>

    <select id="getAllDeptIdList" resultType="long">
        select distinct dept_id from sys_dept where dept_id is not null and del_flag = 0 and status = 0
    </select>

    <select id="getAllArea" resultType="com.boryou.web.module.area.entity.Area">
        select id,code,name areaName,parent_code parentId,level from by_regions
    </select>
</mapper>
