<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.webservice.mapper.JhOrgMapper">

    <resultMap id="BaseResultMap" type="com.boryou.web.module.webservice.domain.JhOrg">
            <id property="id" column="id" jdbcType="BIGINT"/>
            <id property="originalCode" column="original_code" jdbcType="VARCHAR"/>
            <result property="orgUnifyCredit" column="org_unify_credit" jdbcType="VARCHAR"/>
            <result property="orgCode" column="org_code" jdbcType="VARCHAR"/>
            <result property="orgName" column="org_name" jdbcType="VARCHAR"/>
            <result property="orgFatherCode" column="org_father_code" jdbcType="VARCHAR"/>
            <result property="orgLeaevl" column="org_leaevl" jdbcType="VARCHAR"/>
            <result property="orgDivisionCode" column="org_division_code" jdbcType="VARCHAR"/>
            <result property="orgDivisionName" column="org_division_name" jdbcType="VARCHAR"/>
            <result property="orgPhone" column="org_phone" jdbcType="VARCHAR"/>
            <result property="orgType" column="org_type" jdbcType="VARCHAR"/>
            <result property="orgManageType" column="org_manage_type" jdbcType="VARCHAR"/>
            <result property="orgUnitClass" column="org_unit_class" jdbcType="VARCHAR"/>
            <result property="orgIsornoCenter" column="org_isorno_center" jdbcType="VARCHAR"/>
            <result property="orgIsornoVircenter" column="org_isorno_vircenter" jdbcType="VARCHAR"/>
            <result property="orgIsornoNetworkre" column="org_isorno_networkre" jdbcType="VARCHAR"/>
            <result property="port" column="port" jdbcType="VARCHAR"/>
            <result property="orgHeadName" column="org_head_name" jdbcType="VARCHAR"/>
            <result property="orgHeadPhone" column="org_head_phone" jdbcType="VARCHAR"/>
            <result property="orgAddr" column="org_addr" jdbcType="VARCHAR"/>
            <result property="orgRemark" column="org_remark" jdbcType="VARCHAR"/>
            <result property="modifiedTime" column="modified_time" jdbcType="TIMESTAMP"/>
            <result property="createTime" column="create_time" jdbcType="TIMESTAMP"/>
    </resultMap>

    <sql id="Base_Column_List">
        id,original_code,org_unify_credit,
        org_code,org_name,org_father_code,
        org_leaevl,org_division_code,org_division_name,
        org_phone,org_type,org_manage_type,
        org_unit_class,org_isorno_center,org_isorno_vircenter,
        org_isorno_networkre,port,org_head_name,
        org_head_phone,org_addr,org_remark,
        modified_time,create_time
    </sql>
</mapper>
