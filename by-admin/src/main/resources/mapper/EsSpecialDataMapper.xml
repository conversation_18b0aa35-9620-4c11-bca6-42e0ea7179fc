<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.search.mapper.EsSpecialDataMapper">
    <resultMap id="esSpecialDataMap" type="com.boryou.web.module.search.entity.EsSpecialData">
        <id property="id" column="id"/>
        <result property="indexId" column="index_id"/>
        <result property="md5" column="md5"/>
        <result property="createBy" column="create_by"/>
        <result property="emotionFlag" column="emotion_flag"/>
        <result property="userLike" column="user_like"/>
        <result property="original" column="original"/>
        <result property="trash" column="trash"/>
        <result property="deal" column="deal"/>
        <result property="follow" column="follow"/>
        <result property="warned" column="warned"/>
    </resultMap>
    <resultMap id="likeDataMap" type="com.boryou.web.module.search.entity.EsLikeData">
        <id property="id" column="id"/>
        <result property="indexId" column="index_id"/>
        <result property="userId" column="user_id"/>
        <result property="type" column="type"/>
        <result property="typeName" column="type_name"/>
        <result property="publishTime" column="publish_time"/>
        <result property="title" column="title"/>
        <result property="text" column="text"/>
        <result property="url" column="url"/>
        <result property="host" column="host"/>
        <result property="author" column="author"/>
        <result property="authorId" column="author_id"/>
        <result property="accountLevel" column="account_level"/>
        <result property="siteAreaCodeName" column="site_area_code_name"/>
        <result property="emotionFlag" column="emotion_flag"/>
        <result property="isOriginal" column="is_original"/>
        <result property="hitWords" column="hit_words"/>
        <result property="md5" column="md5"/>
        <result property="isRead" column="is_read"/>
    </resultMap>

    <select id="selectEsLikeData" resultMap="likeDataMap"
            parameterType="com.boryou.web.module.search.entity.EsLikeData">
        select f.id,f.user_id,f.index_id,f.type,type_name,f.publish_time,title,text,url,host,f.md5
        ,author,author_id,account_level,site_area_code_name,emotion_flag,is_original,hit_words
        ,CASE WHEN r.id IS NOT NULL THEN 1 ELSE 0 END AS is_read
        from by_user_like_info f left join by_read_data r on f.index_id = r.index_id
        <where>
            <if test="id != null">AND f.id = #{id,jdbcType=BIGINT}</if>
            <if test="userId != null">AND f.user_id = #{userId,jdbcType=BIGINT}</if>
            <if test="type != null">AND f.type = #{type,jdbcType=BIGINT}</if>
            <if test="emotionFlag != null">AND emotion_flag = #{emotionFlag,jdbcType=BIGINT}</if>
            <if test="isRead != null and isRead == 1">AND r.id is not null</if>
            <if test="isRead != null and isRead == 0">AND r.id is null</if>
            <if test="title != null">AND title like concat('%', #{title,jdbcType=VARCHAR}, '%')</if>
        </where>
    </select>


    <insert id="insertEsLikeData" keyColumn="id" keyProperty="id"
            parameterType="com.boryou.web.module.search.entity.EsLikeData" useGeneratedKeys="true">
        insert into by_user_like_info
        ( id,user_id,index_id,type,type_name,publish_time,title,text,url,host
        ,author,author_id,account_level,site_area_code_name,emotion_flag,is_original,hit_words,md5)
        values (#{id,jdbcType=BIGINT},#{userId,jdbcType=BIGINT},#{indexId,jdbcType=BIGINT}
        ,#{type,jdbcType=BIGINT},#{typeName,jdbcType=VARCHAR},#{publishTime,jdbcType=TIMESTAMP}
        ,#{title,jdbcType=VARCHAR},#{text,jdbcType=VARCHAR},#{url,jdbcType=VARCHAR},#{host,jdbcType=VARCHAR}
        ,#{author,jdbcType=VARCHAR},#{authorId,jdbcType=VARCHAR},#{accountLevel,jdbcType=BIGINT}
        ,#{siteAreaCodeName,jdbcType=VARCHAR},#{emotionFlag,jdbcType=VARCHAR},#{isOriginal,jdbcType=VARCHAR}
        ,#{hitWords,jdbcType=VARCHAR},#{md5,jdbcType=VARCHAR})
    </insert>

    <select id="getEsSpecialDataPage" resultType="com.boryou.web.module.search.entity.vo.EsSpecialDataVO">
        select s.index_id indexId, s.create_time createTime,d.md5,
        d.title, d.text, d.type, d.host hostName,d.author, d.url,d.emotion_flag emotionFlag, d.is_original isOriginal,
        d.publish_time publishTime
        ,CASE WHEN r.id IS NOT NULL THEN 1 ELSE 0 END AS isRead from by_special_data s
        left join by_read_data r on s.index_id = r.index_id
        left join sys_user u on s.create_by = u.user_name
        left join by_index_data_details d on s.index_id = d.index_id
        <where>
            d.index_id is not null
            <if test="deptId != null">
                AND u.dept_id = #{deptId,jdbcType=BIGINT}
            </if>
            <!--<if test="createBy != null">AND s.create_by = #{createBy,jdbcType=VARCHAR}</if>-->
            <if test="createBy != null">AND find_in_set(s.create_by,#{createBy})</if>
            <if test="isRead != null and isRead == 1">AND r.id is not null</if>
            <if test="isRead != null and isRead == 0">AND r.id is null</if>
            <if test="emotionFlag != null and emotionFlag != ''">AND d.emotion_flag = #{emotionFlag,jdbcType=BIGINT}
            </if>
            <if test="type != null and type != ''">AND d.type = #{type,jdbcType=VARCHAR}</if>
            <if test="content != null and content != ''">AND d.text like concat('%', #{content,jdbcType=VARCHAR}, '%')
            </if>
            <if test="startTime != null and startTime != ''">
                AND d.publish_time &gt;= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null and endTime != ''">
                AND d.publish_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
            <if test="specialType != null and specialType == 1">AND s.warned > 0</if>
            <if test="specialType != null and specialType == 2">AND s.deal > 0</if>
            <if test="specialType != null and specialType == 3">AND s.follow > 0</if>
            <!--<if test="warned > 0">AND s.warned > #{warned,jdbcType=INTEGER}</if>-->
        </where>
        order by d.publish_time desc
    </select>


    <insert id="insertIndexData" keyColumn="id" keyProperty="id"
            parameterType="com.boryou.web.module.search.entity.IndexData" useGeneratedKeys="true">
        insert into by_index_data_details
        (index_id,type,publish_time,title,text,url,host
        ,author,account_level,content_area_code,emotion_flag,is_original,md5)
        values (#{indexId,jdbcType=BIGINT},#{type,jdbcType=BIGINT},#{publishTime,jdbcType=TIMESTAMP}
        ,#{title,jdbcType=VARCHAR},#{text,jdbcType=VARCHAR},#{url,jdbcType=VARCHAR},#{host,jdbcType=VARCHAR}
        ,#{author,jdbcType=VARCHAR},#{accountLevel,jdbcType=BIGINT}
        ,#{contentAreaCode,jdbcType=VARCHAR},#{emotionFlag,jdbcType=VARCHAR},#{isOriginal,jdbcType=VARCHAR}
        ,#{md5,jdbcType=VARCHAR})
    </insert>

    <select id="selectIndexDataById" resultType="com.boryou.web.module.search.entity.IndexData">
        select index_id indexId from by_index_data_details where index_id = #{indexId,jdbcType=BIGINT}
    </select>

    <update id="updateIndexDataEmotionFlag" parameterType="com.boryou.web.module.search.entity.IndexData">
        update by_index_data_details set emotion_flag = #{emotionFlag,jdbcType=VARCHAR} where index_id =
        #{indexId,jdbcType=BIGINT}
    </update>
</mapper>
