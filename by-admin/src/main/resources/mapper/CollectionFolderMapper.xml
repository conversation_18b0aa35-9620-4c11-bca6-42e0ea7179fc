<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.collection.mapper.CollectionFolderMapper">


    <select id="folderList" resultType="com.boryou.web.module.collection.entity.CollectionFolder">
        SELECT f.*, (SELECT count(1) FROM by_collection WHERE user_id = #{userId} AND folder_id = f.id) as count
        FROM
        by_collection_folder f
        WHERE
        user_id = #{userId} and is_group = '0'
        order by create_time asc
    </select>
</mapper>
