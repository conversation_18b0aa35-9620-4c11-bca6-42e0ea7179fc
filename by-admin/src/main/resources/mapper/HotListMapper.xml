<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.mapper.HotListMapper">

    <resultMap type="com.boryou.web.domain.Hot" id="HotListResult">
        <result property="id" column="id"/>
        <result property="title" column="title"/>
        <result property="url" column="url"/>
        <result property="indexNum" column="indexNum"/>
        <result property="type" column="type"/>
        <result property="indexStatus" column="indexStatus"/>
        <result property="newInfo" column="newInfo"/>
        <result property="sort" column="sort"/>
        <result property="areaCode" column="areaCode"/>
        <result property="updateTime" column="updateTime"/>
    </resultMap>

    <sql id="selectTHotListVo">
        select id,
        title,
        url,
        indexNum,
        type,
        indexStatus,
        newInfo,
        sort,
        areaCode,
        updateTime
        from t_hot_list t
    </sql>

    <select id="selectHotList" resultMap="HotListResult">
        <include refid="selectTHotListVo"/>
        inner join (
        select min(id) mid from t_hot_list group by title
        ) c on t.id=c.mid
        where type in
        <foreach item="type" collection="list" open="(" separator="," close=")">
            #{type}
        </foreach>
        <if test="userAreaName!=null and userAreaName!=''">and areaCode = #{userAreaName}</if>
        order by indexNum desc limit #{count}
    </select>

    <select id="selectHotAllList" resultMap="HotListResult">
        select id,
        title,
        url,
        indexNum,
        type,
        indexStatus,
        newInfo,
        sort,
        areaCode,
        updateTime
        from by_system.t_hot_list t
        inner join (
        select min(d.id) mid from by_system.t_hot_list d group by d.type,d.title
        ) c on t.id=c.mid
        where 1=1
        <if test="list!=null and list!=''">
            and type in
            <foreach item="type" collection="list" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <!--       <if test="beginTime != null and params.beginTime != ''">&lt;!&ndash; 开始时间检索 &ndash;&gt;
                   AND date_format(u.create_time,'%y%m%d') &gt;= date_format(#{params.beginTime},'%y%m%d')
               </if>
               <if test="params.endTime != null and params.endTime != ''">&lt;!&ndash; 结束时间检索 &ndash;&gt;
                   AND date_format(u.create_time,'%y%m%d') &lt;= date_format(#{params.endTime},'%y%m%d')
               </if>-->
        order by indexNum desc
    </select>

    <select id="selectHotBackList" resultMap="HotListResult">
        select id,
        title,
        url,
        indexNum,
        type,
        indexStatus,
        newInfo,
        sort,
        areaCode,
        updateTime
        from by_system.t_hot_list_back t
        inner join (
        select min(d.id) mid from by_system.t_hot_list_back d group by d.title,d.url
        ) c on t.id=c.mid
        where 1=1
        <if test="list!=null and list!=''">
            and type in
            <foreach item="type" collection="list" open="(" separator="," close=")">
                #{type}
            </foreach>
        </if>
        <if test="startTime != null and startTime != '' and startTime != null and startTime != ''">
            AND updateTime between STR_TO_DATE(#{startTime},'%Y-%m-%d %H:%i:%s') and STR_TO_DATE(#{endTime},'%Y-%m-%d
            %H:%i:%s')
        </if>
        order by updateTime desc
    </select>
    <select id="selectHotTypeList" resultType="com.boryou.web.domain.Hot">
        select id,
        title,
        url,
        indexNum,
        type,
        indexStatus,
        newInfo,
        sort,
        areaCode,
        updateTime
        from by_system.t_hot_list t
        inner join (
        select min(d.id) mid from by_system.t_hot_list d group by d.type,d.title
        ) c on t.id=c.mid
        where 1=1
        and type = #{type}
        order by indexNum desc
        limit #{limit}
    </select>

    <select id="selectOutAnnouncementList" resultType="com.boryou.web.domain.ApidemicAnnouncement">
        select * from by_epidemic_announcement
        <where>
            <if test="time != null and time != ''">
                and update_time &gt;= #{time}
            </if>
        </where>
    </select>

    <select id="selectOutKeywordIndexList" resultType="com.boryou.web.domain.KeywordIndex">
        select * from by_keyword_index
        <where>
            <if test="time != null and time != ''">
                and create_time &gt;= #{time}
            </if>
        </where>
    </select>
</mapper>
