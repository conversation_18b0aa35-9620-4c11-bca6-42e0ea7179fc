<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.message.mapper.MessageTemplateMapper">

    <resultMap type="MessageTemplate" id="MessageTemplateResult">
        <result property="id" column="id"/>
        <result property="templateName" column="template_name"/>
        <result property="templateContent" column="template_content"/>
        <result property="status" column="status"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="templateScript" column="template_script"/>
        <result property="templateCode" column="template_code"/>
    </resultMap>

    <sql id="selectMessageTemplateVo">
        select id, template_name, template_content, status, create_by, create_time,template_script from
        by_message_template
    </sql>

    <select id="selectMessageTemplateList" parameterType="MessageTemplate" resultMap="MessageTemplateResult">
        <include refid="selectMessageTemplateVo"/>
        <where>
            <if test="templateName != null  and templateName != ''">
                and template_name like concat('%', #{templateName}, '%')
            </if>
            <if test="templateContent != null  and templateContent != ''">
                and template_content = #{templateContent}
            </if>
            <if test="status != null  and status != ''">
                and status = #{status}
            </if>

        </where>
    </select>

    <select id="selectMessageTemplateById" parameterType="Long"
            resultMap="MessageTemplateResult">
        <include refid="selectMessageTemplateVo"/>
        where id = #{id}
    </select>
    <select id="getDeptMessage" parameterType="Long" resultMap="MessageTemplateResult">
        select id, template_name, template_content, status, create_by, create_time,template_script,template_code
        from by_message_template where id=(select mssage_template_id from by_dept_message where dept_id=#{deptId} )
    </select>

    <select id="getDeptTemplatesByIds" resultType="com.boryou.web.module.message.domain.vo.DeptMessage">
        select dept_id deptId,mssage_template_id templateId from by_dept_message
        where dept_id in
        <foreach item="id" collection="deptIds" open="(" separator="," close=")">
            #{id}
        </foreach>
    </select>


    <insert id="insertMessageTemplate" parameterType="MessageTemplate" useGeneratedKeys="true"
            keyProperty="id">
        insert into by_message_template
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="templateName != null">template_name,
            </if>
            <if test="templateContent != null">template_content,
            </if>
            <if test="status != null">status,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="templateName != null">#{templateName},
            </if>
            <if test="templateContent != null">#{templateContent},
            </if>
            <if test="status != null">#{status},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
        </trim>
    </insert>

    <update id="updateMessageTemplate" parameterType="MessageTemplate">
        update by_message_template
        <trim prefix="SET" suffixOverrides=",">
            <if test="templateName != null">template_name =
                #{templateName},
            </if>
            <if test="templateContent != null">template_content =
                #{templateContent},
            </if>
            <if test="status != null">status =
                #{status},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
        </trim>
        where id = #{id}
    </update>
    <update id="saveDeptMessage">
        insert into by_dept_message(dept_id,mssage_template_id) values(#{deptId},#{templateId})
    </update>

    <delete id="deleteMessageTemplateById" parameterType="Long">
        delete from by_message_template where id = #{id}
    </delete>

    <delete id="deleteMessageTemplateByIds" parameterType="String">
        delete from by_message_template where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
    <delete id="deleteDeptMessage">
        delete from by_dept_message where dept_id=#{deptId}
    </delete>
</mapper>
