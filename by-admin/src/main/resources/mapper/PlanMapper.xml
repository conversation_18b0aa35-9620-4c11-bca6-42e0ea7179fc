<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.mapper.PlanMapper">

    <resultMap type="Plan" id="PlanResult">
        <result property="planId" column="plan_id"/>
        <result property="planName" column="plan_name"/>
        <result property="kw1" column="kw1"/>
        <result property="kw2" column="kw2"/>
        <result property="kw3" column="kw3"/>
        <result property="excludeWord" column="exclude_word"/>
        <result property="area" column="area"/>
        <result property="searchMode" column="search_mode"/>
        <result property="searchPosition" column="search_position"/>
        <result property="typeId" column="type_id"/>
        <result property="userId" column="user_id"/>
        <result property="effectStartTime" column="effect_start_time"/>
        <result property="effectEndTime" column="effect_end_time"/>
        <result property="sort" column="sort"/>
        <result property="delFlag" column="del_flag"/>
        <result property="enableFlag" column="enable_flag"/>
        <result property="createTime" column="create_time"/>
        <result property="createBy" column="create_by"/>
        <result property="updateTime" column="update_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="highMonitorWord" column="high_monitor_word"/>
        <result property="highExcludeWord" column="high_exclude_word"/>
        <result property="highArea" column="high_area"/>
        <result property="historyFlag" column="history_flag"/>
        <result property="hotKw" column="hot_kw"/>
    </resultMap>

    <resultMap id="PlanPlanMainResult" type="planVO" extends="PlanResult">
        <collection property="planMainList" notNullColumn="id"
                    javaType="java.util.List" resultMap="PlanMainResult"/>
    </resultMap>

    <resultMap type="PlanMain" id="PlanMainResult">
        <result property="id" column="id"/>
        <result property="planId" column="plan_id"/>
        <result property="mainType" column="main_type"/>
        <result property="userId" column="user_id"/>
    </resultMap>
    <sql id="selectByPlanVo">
        select plan_id,
        plan_name,
        kw1,
        kw2,
        kw3,
        exclude_word,
        area,
        search_mode,
        search_position,
        type_id,
        user_id,
        effect_start_time,
        effect_end_time,
        sort,
        del_flag,
        enable_flag,
        create_time,
        create_by,
        update_time,
        update_by,
        high_monitor_word,
        high_exclude_word,
        high_area,
        history_flag,
        hot_kw
        from by_plan
    </sql>
    <sql id="selectPlanMainVo">
        select id,
        plan_id,
        main_type,
        user_id,
        create_by,
        create_time,
        update_by,
        update_time
        from by_plan_main
    </sql>
    <sql id="selectPlanAndPlanMainVo">
        select a.plan_id,
        a.plan_name,
        a.kw1,
        a.kw2,
        a.kw3,
        a.exclude_word,
        a.area,
        a.search_mode,
        a.search_position,
        a.type_id,
        a.user_id,
        a.effect_start_time,
        a.effect_end_time,
        a.sort,
        a.del_flag,
        a.enable_flag,
        a.create_time,
        a.create_by,
        a.update_time,
        a.update_by,
        a.high_monitor_word,
        a.high_exclude_word,
        a.high_area,
        a.history_flag,
        b.id,
        b.plan_id,
        b.main_type,
        b.user_id
        <!--   ,
          b.create_by,
          b.create_time,
          b.update_by,
          b.update_time-->
        from by_plan a
        left join by_plan_main b on b.plan_id = a.plan_id
    </sql>

    <select id="selectPlanAndPlanMain" parameterType="plan" resultMap="PlanPlanMainResult">
        <include refid="selectPlanAndPlanMainVo"/>
        <where>
            <if test="userId != null  and userId != ''">
                and a.user_id = #{userId}
            </if>
        </where>
    </select>

    <select id="selectByPlanList" parameterType="Plan" resultMap="PlanResult">
        <include refid="selectByPlanVo"/>
        <where>
            <if test="planName != null  and planName != ''">
                and plan_name like concat('%', #{planName}, '%')
            </if>
            <if test="kw1 != null  and kw1 != ''">
                and kw1 = #{kw1}
            </if>
            <if test="kw2 != null  and kw2 != ''">
                and kw2 = #{kw2}
            </if>
            <if test="kw3 != null  and kw3 != ''">
                and kw3 = #{kw3}
            </if>
            <if test="excludeWord != null  and excludeWord != ''">
                and exclude_word = #{excludeWord}
            </if>
            <if test="area != null  and area != ''">
                and area = #{area}
            </if>
            <if test="searchMode != null ">
                and search_mode = #{searchMode}
            </if>
            <if test="searchPosition != null ">
                and search_position = #{searchPosition}
            </if>
            <if test="typeId != null ">
                and type_id = #{typeId}
            </if>
            <if test="userId != null  and userId != ''">
                and user_id = #{userId}
            </if>
            <if test="effectStartTime != null ">
                and effect_start_time = #{effectStartTime}
            </if>
            <if test="effectEndTime != null ">
                and effect_end_time = #{effectEndTime}
            </if>
            <if test="sort != null ">
                and sort = #{sort}
            </if>
            <if test="enableFlag != null  and enableFlag != ''">
                and enable_flag = #{enableFlag}
            </if>
            <if test="highMonitorWord != null  and highMonitorWord != ''">
                and high_monitor_word = #{highMonitorWord}
            </if>
            <if test="highExcludeWord != null  and highExcludeWord != ''">
                and hish_exclude_word = #{highExcludeWord}
            </if>
            <if test="highArea != null  and highArea != ''">
                and high_area = #{highArea}
            </if>
            <if test="historyFlag != null ">
                and history_flag = #{historyFlag}
            </if>
            <if test="mainPoint != null ">
                and main_point = #{mainPoint}
            </if>
        </where>
    </select>

    <select id="selectByPlanById" parameterType="Long" resultMap="PlanResult">
        <include refid="selectByPlanVo"/>
        where plan_id = #{planId}
    </select>

    <select id="selectPlanMainByPlanId" parameterType="Long" resultMap="PlanMainResult">
        <include refid="selectPlanMainVo"/>
        where plan_id = #{planId}
    </select>

    <insert id="insertByPlan" parameterType="Plan">
        insert into by_plan
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="planId != null">plan_id,</if>
            <if test="planName != null and planName != ''">plan_name,</if>
            <if test="kw1 != null and kw1 != ''">kw1,</if>
            <if test="kw2 != null">kw2,</if>
            <if test="kw3 != null">kw3,</if>
            <if test="excludeWord != null">exclude_word,</if>
            <if test="area != null">area,</if>
            <if test="searchMode != null">search_mode,</if>
            <if test="searchPosition != null">search_position,</if>
            <if test="typeId != null">type_id,</if>
            <if test="userId != null">user_id,</if>
            <if test="effectStartTime != null">effect_start_time,</if>
            <if test="effectEndTime != null">effect_end_time,</if>
            <if test="sort != null">sort,</if>
            <if test="delFlag != null and delFlag != ''">del_flag,</if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag,</if>
            <if test="createTime != null">create_time,</if>
            <if test="createBy != null and createBy != ''">create_by,</if>
            <if test="updateTime != null">update_time,
            </if>
            <if test="updateBy != null and updateBy != ''">update_by,
            </if>
            <if test="highMonitorWord != null">high_monitor_word,
            </if>
            <if test="highExcludeWord != null">hish_exclude_word,
            </if>
            <if test="highArea != null">high_area,
            </if>
            <if test="historyFlag != null">historyFlag,</if>
            <if test="hotKw != null">hot_kw,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="planId != null">#{planId},
            </if>
            <if test="planName != null and planName != ''">#{planName},
            </if>
            <if test="kw1 != null and kw1 != ''">#{kw1},
            </if>
            <if test="kw2 != null">#{kw2},
            </if>
            <if test="kw3 != null">#{kw3},
            </if>
            <if test="excludeWord != null">#{excludeWord},
            </if>
            <if test="area != null">#{area},
            </if>
            <if test="searchMode != null">#{searchMode},
            </if>
            <if test="searchPosition != null">#{searchPosition},
            </if>
            <if test="typeId != null">#{typeId},
            </if>
            <if test="userId != null">#{userId},
            </if>
            <if test="effectStartTime != null">#{effectStartTime},
            </if>
            <if test="effectEndTime != null">#{effectEndTime},
            </if>
            <if test="sort != null">#{sort},
            </if>
            <if test="delFlag != null and delFlag != ''">#{delFlag},
            </if>
            <if test="enableFlag != null and enableFlag != ''">#{enableFlag},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="createBy != null and createBy != ''">#{createBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},
            </if>
            <if test="highMonitorWord != null">#{highMonitorWord},
            </if>
            <if test="highExcludeWord != null">#{highExcludeWord},
            </if>
            <if test="highArea != null">#{highArea},
            </if>
            <if test="historyFlag != null">#{historyFlag},
            </if>
            <if test="mainPoint != null">#{mainPoint},
            </if>
            <if test="hotKw != null">#{hotKw},
            </if>
        </trim>
    </insert>

    <update id="updateByPlan" parameterType="Plan">
        update by_plan
        <trim prefix="SET" suffixOverrides=",">
            <if test="planName != null and planName != ''">plan_name =
                #{planName},
            </if>
            <if test="kw1 != null and kw1 != ''">kw1 =
                #{kw1},
            </if>
            <if test="kw2 != null">kw2 =
                #{kw2},
            </if>
            <if test="kw3 != null">kw3 =
                #{kw3},
            </if>
            <if test="excludeWord != null">exclude_word =
                #{excludeWord},
            </if>
            <if test="area != null">area =
                #{area},
            </if>
            <if test="searchMode != null">search_mode =
                #{searchMode},
            </if>
            <if test="searchPosition != null">search_position =
                #{searchPosition},
            </if>
            <if test="typeId != null">type_id =
                #{typeId},
            </if>
            <if test="userId != null">user_id =
                #{userId},
            </if>
            <if test="effectStartTime != null">effect_start_time =
                #{effectStartTime},
            </if>
            <if test="effectEndTime != null">effect_end_time =
                #{effectEndTime},
            </if>
            <if test="sort != null">sort =
                #{sort},
            </if>
            <if test="delFlag != null and delFlag != ''">del_flag =
                #{delFlag},
            </if>
            <if test="enableFlag != null and enableFlag != ''">enable_flag =
                #{enableFlag},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="createBy != null and createBy != ''">create_by =
                #{createBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
            <if test="updateBy != null and updateBy != ''">update_by =
                #{updateBy},
            </if>
            <if test="highMonitorWord != null">high_monitor_word =
                #{highMonitorWord},
            </if>
            <if test="highExcludeWord != null">hish_exclude_word =
                #{highExcludeWord},
            </if>
            <if test="highArea != null">high_area =
                #{highArea},
            </if>
            <if test="historyFlag != null">history_flag =
                #{historyFlag},
            </if>
            <if test="mainPoint != null">main_point =
                #{mainPoint},
            </if>
            <if test="hotKw != null">hot_kw =
                #{hotKw},
            </if>
        </trim>
        where plan_id = #{planId}
    </update>

    <delete id="deleteByPlanById" parameterType="Long">
        delete
        from by_plan
        where plan_id = #{planId}
    </delete>

    <delete id="deleteByPlanByIds" parameterType="String">
        delete from by_plan where plan_id in
        <foreach item="planId" collection="array" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </delete>

    <delete id="deletePlanMainByPlanIds" parameterType="String">
        delete from by_plan_main where plan_id in
        <foreach item="planId" collection="array" open="(" separator="," close=")">
            #{planId}
        </foreach>
    </delete>

    <delete id="deletePlanMainByPlanId" parameterType="Long">
        delete
        from by_plan_main
        where plan_id = #{planId}
    </delete>

    <insert id="batchPlanMain">
        insert into by_plan_main
        ( id, plan_id, main_type, user_id, create_by, create_time, update_by, update_time) values
        <foreach item="item" index="index" collection="list" separator=",">
            ( #{item.id}, #{item.planId}, #{item.mainType}, #{item.userId}, #{item.createBy},
            #{item.createTime}, #{item.updateBy}, #{item.updateTime})
        </foreach>
    </insert>

    <update id="changeSortById">
        update by_plan as t1 join by_plan as t2
        on (t1.plan_id = #{planId1} and t2.plan_id = #{planId2})
        set t1.sort = t2.sort,
        t2.sort = t1.sort;
    </update>

    <select id="selectByMainTypeList" parameterType="PlanMainBO" resultMap="PlanResult">
        <include refid="selectPlanAndPlanMainVo"/>
        <where>
            <if test="mainType != null  and mainType != ''">
                and b.main_type =#{mainType}
            </if>
            <if test="userIds != null">
                and b.user_id in
                <foreach collection="userIds" index="index" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            and del_flag='0'
        </where>
        order by a.create_time desc
    </select>

    <select id="selectByMainTypeListCount" parameterType="PlanMainBO" resultType="java.lang.Integer">
        select count(1)
        from by_plan a
        left join by_plan_main b on b.plan_id = a.plan_id
        <where>
            <if test="mainType != null  and mainType != ''">
                and b.main_type =#{mainType}
            </if>
            <if test="userIds != null">
                and b.user_id in
                <foreach collection="userIds" index="index" item="item" separator="," close=")" open="(">
                    #{item}
                </foreach>
            </if>
            and del_flag='0'
        </where>
    </select>
    <select id="manageList" resultType="com.boryou.web.domain.Plan">
        SELECT u.dept_id, d.dept_name, pt.type_name, p.*
        FROM by_plan p
        LEFT JOIN sys_user u ON p.user_id = u.user_id
        LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        LEFT JOIN by_plan_type pt ON pt.type_id = p.type_id
        WHERE
        <if test="query.historyFlag != null and query.historyFlag != ''">
            p.history_flag = #{query.historyFlag} AND
        </if>
        <if test="query.typeId != null and query.typeId != ''">
            p.type_id = #{query.typeId} AND
        </if>
        <if test="query.planName != null and query.planName != ''">
            p.plan_name LIKE concat('%', #{query.planName}, '%') AND
        </if>
        <if test="query.deptId != null and query.deptId != ''">
            u.dept_id = #{query.deptId} AND
        </if>
        <choose>
            <when test="query.deptIds != null and !query.deptIds.isEmpty()">
                u.dept_id IN
                <foreach collection="query.deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
                AND
            </when>
            <otherwise>
                <if test="query.userId != null and query.userId != ''">
                    u.user_id = #{query.userId} AND
                </if>
            </otherwise>
        </choose>
        p.del_flag = 0
        order by p.create_time desc
    </select>
    <select id="getGreaterSortPlan" resultType="com.boryou.web.domain.Plan">
        select plan_id,sort from by_plan where user_id=#{userId} and type_id=#{typeId} and sort&gt;#{sort} order by sort
        asc limit 1
    </select>
    <select id="getLessSortPlan" resultType="com.boryou.web.domain.Plan">
        select plan_id,sort from by_plan where user_id=#{userId} and type_id=#{typeId} and sort&lt; #{sort} order by
        sort asc limit 1
    </select>

    <!--重排序-->
    <update id="updatePlanResetSort">
        update by_plan set sort=sort+1 where sort > #{sort} and type_id=#{typeId} and user_id=#{userId}
    </update>

</mapper>
