<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//ibatis.apache.org//DTD Mapper 3.0//EN"
        "http://ibatis.apache.org/dtd/ibatis-3-mapper.dtd">

<mapper namespace="com.boryou.web.module.source.mapper.SourceSettingMapper">

    <!-- 根据筛选条件获得信源设置列表 -->
    <select id="getSourceSettingList" resultType="com.boryou.web.module.source.entity.SourceSetting">
        select * from t_source_setting
        where 1=1
        <if test="userId!=null and userId!=''">
            and userId=#{userId}
        </if>
        <if test="name!=null and name!=''">
            and name like CONCAT('%','${name}','%')
        </if>
        <if test="state!=null and state!='' and state!=-1">
            and state=#{state}
        </if>
        <if test="id!=null and id!=''">
            and id=#{id}
        </if>
        <if test="plateId!=null and plateId!=''">
            and plateId=#{plateId}
        </if>
        <if test="sourceType!=null and sourceType!=''">
            and sourceType=#{sourceType}
        </if>
        <if test="settingType!=null and settingType!=''">
            and settingType=#{settingType}
        </if>
        <if test="settingHost!=null and settingHost!=''">
            and settingHost like CONCAT('%','${settingHost}','%')
        </if>
        <if test="keywords!=null and keywords!=''">
            and (name like CONCAT('%','${keywords}','%') or settingHost like CONCAT('%','${keywords}','%'))
        </if>
        order by createTime desc
    </select>

    <!-- 根据筛选条件获得信源设置数量 -->
    <select id="getSourceSettingListCount" parameterType="Map" resultType="int">
        select count(*) from t_source_setting
        where 1=1
        <if test="userId!=null and userId!=''">
            and userId=#{userId}
        </if>
        <if test="name!=null and name!=''">
            and name like CONCAT('%','${name}','%')
        </if>
        <if test="state!=null and state!='' and state!=-1">
            and state=#{state}
        </if>
        <if test="id!=null and id!=''">
            and id=#{id}
        </if>
        <if test="plateId!=null and plateId!=''">
            and plateId=#{plateId}
        </if>
        <if test="sourceType!=null and sourceType!=''">
            and sourceType=#{sourceType}
        </if>
        <if test="settingType!=null and settingType!=''">
            and settingType=#{settingType}
        </if>
    </select>


    <!-- 根据筛选条件获得信源设置 -->
    <select id="getSourceSetting" parameterType="Map" resultType="com.boryou.web.module.source.entity.SourceSetting">
        select * from t_source_setting
        where 1=1
        <if test="userId!=null and userId!=''">
            and userId=#{userId}
        </if>
        <if test="name!=null and name!=''">
            and name like CONCAT('%','${name}','%')
        </if>
        <if test="state!=null and state!='' and state!=-1">
            and state=#{state}
        </if>
        <if test="id!=null and id!=''">
            and id=#{id}
        </if>
        <if test="plateId!=null and plateId!=''">
            and plateId=#{plateId}
        </if>
        <if test="sourceType!=null and sourceType!=''">
            and sourceType=#{sourceType}
        </if>
        <if test="settingType!=null and settingType!=''">
            and settingType=#{settingType}
        </if>
        order by createTime desc
    </select>

    <!-- 添加信源设置 -->
    <insert id="addSourceSetting">
        insert into
        t_source_setting(id,name,sourceType,settingType,settingHost,userId,state,createTime,updateTime,plateId)
        values(#{id},#{name},#{sourceType},#{settingType},#{settingHost},#{userId},#{state},#{createTime},#{updateTime},#{plateId})
    </insert>

    <!-- 更新信源设置 -->
    <update id="updateSourceSetting">
        update t_source_setting
        set name=#{name},sourceType=#{sourceType},settingType=#{settingType},settingHost=#{settingHost},
        userId=#{userId},state=#{state},createTime=#{createTime},updateTime=#{updateTime},plateId=#{plateId}
        where id = #{id}
    </update>

    <!-- 更新信源设置状态 -->
    <update id="updateSourceSettingState" parameterType="Map">
        update t_source_setting
        set state=#{state},updateTime=#{updateTime}
        where FIND_IN_SET(id,#{ids})
    </update>

    <!-- 根据主键id删除信源设置 -->
    <delete id="delSourceSetting" parameterType="String">
        delete from t_source_setting where id=#{id}
    </delete>

    <!-- 根据板块id删除信源设置 -->
    <delete id="deleteSourceSetting">
        delete from t_source_setting where plateId=#{plateId}
        <if test="settingType!=null and settingType!=''">
            and settingType=#{settingType}
        </if>
        <if test="sourceType!=null and sourceType!=''">
            and sourceType=#{sourceType}
        </if>
    </delete>
</mapper>
