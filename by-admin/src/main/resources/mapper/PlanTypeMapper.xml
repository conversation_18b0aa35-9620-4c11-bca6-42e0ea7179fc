<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.mapper.PlanTypeMapper">

    <resultMap type="PlanType" id="PlanTypeResult">
        <result property="typeId" column="type_id"/>
        <result property="typeName" column="type_name"/>
        <result property="userId" column="user_id"/>
        <result property="sort" column="sort"/>
        <result property="createBy" column="create_by"/>
        <result property="createTime" column="create_time"/>
        <result property="updateBy" column="update_by"/>
        <result property="updateTime" column="update_time"/>
    </resultMap>

    <sql id="selectPlanTypeVo">
        select type_id,
        type_name,
        user_id,
        sort,
        create_by,
        create_time,
        update_by,
        update_time
        from by_plan_type
    </sql>

    <select id="selectPlanTypeList" parameterType="PlanType" resultMap="PlanTypeResult">
        <include refid="selectPlanTypeVo"/>
        <where>
            <if test="typeName != null  and typeName != ''">
                and type_name like concat('%', #{typeName}, '%')
            </if>
            <if test="userId != null ">
                and user_id = #{userId}
            </if>
            <if test="sort != null ">
                and sort = #{sort}
            </if>
        </where>
    </select>

    <select id="selectPlanTypeById" parameterType="Long"
            resultMap="PlanTypeResult">
        <include refid="selectPlanTypeVo"/>
        where type_id = #{typeId}
    </select>
    <select id="manageList" resultType="com.boryou.web.domain.PlanType">
        SELECT u.dept_id, d.dept_name, p.*
        FROM by_plan_type p
        LEFT JOIN sys_user u ON p.user_id = u.user_id
        LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
        WHERE
        <if test="typeName != null and typeName != ''">
            p.type_name LIKE concat('%', #{typeName}, '%') AND
        </if>
        <choose>
            <when test="deptIds != null and !deptIds.isEmpty()">
                u.dept_id IN
                <foreach collection="deptIds" item="deptId" open="(" separator="," close=")">
                    #{deptId}
                </foreach>
            </when>
            <otherwise>
                <if test="userId != null and userId != ''">
                    u.user_id = #{userId}
                </if>
            </otherwise>
        </choose>
        order by p.create_time desc
    </select>

    <insert id="insertPlanType" parameterType="PlanType">
        insert into by_plan_type
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="typeId != null">type_id,
            </if>
            <if test="typeName != null">type_name,
            </if>
            <if test="userId != null">user_id,
            </if>
            <if test="sort != null">sort,
            </if>
            <if test="createBy != null">create_by,
            </if>
            <if test="createTime != null">create_time,
            </if>
            <if test="updateBy != null and updateBy != ''">update_by,
            </if>
            <if test="updateTime != null">update_time,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="typeId != null">#{typeId},
            </if>
            <if test="typeName != null">#{typeName},
            </if>
            <if test="userId != null">#{userId},
            </if>
            <if test="sort != null">#{sort},
            </if>
            <if test="createBy != null">#{createBy},
            </if>
            <if test="createTime != null">#{createTime},
            </if>
            <if test="updateBy != null and updateBy != ''">#{updateBy},
            </if>
            <if test="updateTime != null">#{updateTime},
            </if>
        </trim>
    </insert>

    <update id="updatePlanType" parameterType="PlanType">
        update by_plan_type
        <trim prefix="SET" suffixOverrides=",">
            <if test="typeName != null">type_name =
                #{typeName},
            </if>
            <if test="userId != null">user_id =
                #{userId},
            </if>
            <if test="sort != null">sort =
                #{sort},
            </if>
            <if test="createBy != null">create_by =
                #{createBy},
            </if>
            <if test="createTime != null">create_time =
                #{createTime},
            </if>
            <if test="updateBy != null and updateBy != ''">update_by =
                #{updateBy},
            </if>
            <if test="updateTime != null">update_time =
                #{updateTime},
            </if>
        </trim>
        where type_id = #{typeId}
    </update>

    <delete id="deletePlanTypeById" parameterType="Long">
        delete
        from by_plan_type
        where type_id = #{typeId}
    </delete>

    <delete id="deletePlanTypeByIds" parameterType="String">
        delete from by_plan_type where type_id in
        <foreach item="typeId" collection="array" open="(" separator="," close=")">
            #{typeId}
        </foreach>
    </delete>

    <update id="changeSortById">
        update by_plan_type as t1 join by_plan_type as t2
        on (t1.type_id = #{fid1} and t2.type_id = #{fid2})
        set t1.sort = t2.sort,
        t2.sort=t1.sort;
    </update>

</mapper>
