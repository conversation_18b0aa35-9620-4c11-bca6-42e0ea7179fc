<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.module.submit.mapper.SubmitMapper">

    <resultMap type="com.boryou.web.module.submit.entity.Submit" id="SubmitResult">
        <result property="id" column="id"/>
        <result property="courtName" column="court_name"/>
        <result property="riskLevel" column="risk_level"/>
        <result property="source" column="source"/>
        <result property="summary" column="summary"/>
        <result property="url" column="url"/>
        <result property="phone" column="phone"/>
        <result property="docIndexId" column="doc_index_id"/>
        <result property="contactIds" column="contact_ids"/>
        <result property="deadline" column="deadline"/>
        <result property="deadlineNum" column="deadline_num"/>
        <result property="suggest" column="suggest"/>
        <result property="processText" column="process_text"/>
        <result property="processStatus" column="process_status"/>
        <result property="userId" column="user_id"/>
        <result property="processTime" column="process_time"/>
    </resultMap>

    <sql id="selectSubmitVo">
        select id, court_name, risk_level, source, summary, url, phone, doc_index_id, contact_ids, deadline,
        deadline_num, suggest, process_text, process_status,user_id,process_time from by_submit
    </sql>

    <select id="selectSubmitList" parameterType="com.boryou.web.module.submit.entity.Submit" resultMap="SubmitResult">
        <include refid="selectSubmitVo"/>
        <where>
            <if test="courtName != null  and courtName != ''">
                and court_name like concat('%', #{courtName}, '%')
            </if>
            <if test="riskLevel != null  and riskLevel != ''">
                and risk_level = #{riskLevel}
            </if>
            <if test="source != null  and source != ''">
                and source = #{source}
            </if>
            <if test="summary != null  and summary != ''">
                and summary = #{summary}
            </if>
            <if test="url != null  and url != ''">
                and url = #{url}
            </if>
            <if test="phone != null  and phone != ''">
                and phone = #{phone}
            </if>
            <if test="docIndexId != null ">
                and doc_index_id = #{docIndexId}
            </if>
            <if test="contactIds != null  and contactIds != ''">
                and contact_ids = #{contactIds}
            </if>
            <if test="deadline != null ">
                and deadline = #{deadline}
            </if>
            <if test="deadlineNum != null  and deadlineNum != ''">
                and deadline_num = #{deadlineNum}
            </if>
            <if test="suggest != null  and suggest != ''">
                and suggest = #{suggest}
            </if>
            <if test="processText != null  and processText != ''">
                and process_text = #{processText}
            </if>
            <if test="processStatus != null  and processStatus != ''">
                and process_status = #{processStatus}
            </if>
            <if test="id != null  and id != ''">
                and id = #{id}
            </if>
        </where>
    </select>
    <select id="selectSubmitListBySpecialData" parameterType="com.boryou.web.module.search.entity.vo.EsSpecialDataVO"
            resultType="com.boryou.web.module.search.entity.vo.EsSpecialDataVO">
        select s.id,s.phone, s.doc_index_id indexId, CASE WHEN s.process_status = 1 THEN "已处置" WHEN s.process_status
        = 2 THEN "已过期" ELSE "流转中" END AS processStatus, s.process_time createTime, s.deadline deadLine,
        u.user_name userName ,CASE WHEN r.id IS NOT NULL THEN 1 ELSE 0 END AS isRead, d.md5,
        d.title, d.text, d.type, d.host hostName,d.author, d.url,d.emotion_flag emotionFlag, d.is_original isOriginal,
        d.publish_time publishTime
        from by_submit s left join by_read_data r on s.doc_index_id = r.index_id
        left join sys_user u on s.user_id = u.user_id
        left join by_index_data_details d on s.doc_index_id = d.index_id
        <where>
            d.index_id is not null
            <if test="deptId != null">
                AND u.dept_id = #{deptId,jdbcType=BIGINT}
            </if>
            <if test="createBy != null">AND find_in_set(s.user_id,#{createBy})</if>
            <if test="processStatus != null  and processStatus != ''">
                and s.process_status = #{processStatus}
            </if>
            <if test="isRead != null and isRead == 1">AND r.id is not null</if>
            <if test="isRead != null and isRead == 0">AND r.id is null</if>
            <if test="emotionFlag != null and emotionFlag != ''">AND d.emotion_flag = #{emotionFlag,jdbcType=BIGINT}
            </if>
            <if test="type != null and type != ''">AND d.type = #{type,jdbcType=VARCHAR}</if>
            <if test="content != null and content != ''">AND d.text like concat('%', #{content,jdbcType=VARCHAR}, '%')
            </if>
            <if test="startTime != null and startTime != ''">
                AND d.publish_time &gt;= #{startTime,jdbcType=TIMESTAMP}
            </if>
            <if test="endTime != null and endTime != ''">
                AND d.publish_time &lt;= #{endTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        order by d.publish_time desc
    </select>


    <select id="selectSubmitById" parameterType="Long"
            resultMap="SubmitResult">
        <include refid="selectSubmitVo"/>
        where id = #{id}
    </select>

    <insert id="insertSubmit" parameterType="com.boryou.web.module.submit.entity.Submit" useGeneratedKeys="true"
            keyProperty="id">
        insert into by_submit
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="courtName != null">court_name,
            </if>
            <if test="riskLevel != null">risk_level,
            </if>
            <if test="source != null">source,
            </if>
            <if test="summary != null">summary,
            </if>
            <if test="url != null">url,
            </if>
            <if test="phone != null">phone,
            </if>
            <if test="docIndexId != null">doc_index_id,
            </if>
            <if test="contactIds != null">contact_ids,
            </if>
            <if test="deadline != null">deadline,
            </if>
            <if test="deadlineNum != null">deadline_num,
            </if>
            <if test="suggest != null">suggest,
            </if>
            <if test="processText != null">process_text,
            </if>
            <if test="processStatus != null">process_status,
            </if>
            <if test="userName != null">user_name,
            </if>
            <if test="userId != null">user_id
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="courtName != null">#{courtName},
            </if>
            <if test="riskLevel != null">#{riskLevel},
            </if>
            <if test="source != null">#{source},
            </if>
            <if test="summary != null">#{summary},
            </if>
            <if test="url != null">#{url},
            </if>
            <if test="phone != null">#{phone},
            </if>
            <if test="docIndexId != null">#{docIndexId},
            </if>
            <if test="contactIds != null">#{contactIds},
            </if>
            <if test="deadline != null">#{deadline},
            </if>
            <if test="deadlineNum != null">#{deadlineNum},
            </if>
            <if test="suggest != null">#{suggest},
            </if>
            <if test="processText != null">#{processText},
            </if>
            <if test="processStatus != null">#{processStatus},
            </if>
            <if test="userName != null">#{userName},
            </if>
            <if test="userId != null">#{userId}
            </if>
        </trim>
    </insert>

    <update id="updateSubmit" parameterType="com.boryou.web.module.submit.entity.Submit">
        update by_submit
        <trim prefix="SET" suffixOverrides=",">
            <if test="courtName != null">court_name =
                #{courtName},
            </if>
            <if test="riskLevel != null">risk_level =
                #{riskLevel},
            </if>
            <if test="source != null">source =
                #{source},
            </if>
            <if test="summary != null">summary =
                #{summary},
            </if>
            <if test="url != null">url =
                #{url},
            </if>
            <if test="phone != null">phone =
                #{phone},
            </if>
            <if test="docIndexId != null">doc_index_id =
                #{docIndexId},
            </if>
            <if test="contactIds != null">contact_ids =
                #{contactIds},
            </if>
            <if test="deadline != null">deadline =
                #{deadline},
            </if>
            <if test="deadlineNum != null">deadline_num =
                #{deadlineNum},
            </if>
            <if test="suggest != null">suggest =
                #{suggest},
            </if>
            <if test="processText != null">process_text =
                #{processText},
            </if>
            <if test="processStatus != null">process_status =
                #{processStatus},
            </if>
            <if test="processTime != null">process_time =
                #{processTime},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteSubmitById" parameterType="Long">
        delete from by_submit where id = #{id}
    </delete>

    <delete id="deleteSubmitByIds" parameterType="String">
        delete from by_submit where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
