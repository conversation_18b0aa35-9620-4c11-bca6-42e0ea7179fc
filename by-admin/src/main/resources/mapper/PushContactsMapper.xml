<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.boryou.web.mapper.PushContactsMapper">

    <resultMap type="PushContacts" id="ByPushContactsResult">
        <result property="id" column="id"/>
        <result property="username" column="username"/>
        <result property="phone" column="phone"/>
    </resultMap>

    <sql id="selectByPushContactsVo">
        select id, username, phone from by_push_contacts
    </sql>

    <select id="selectByPushContactsList" parameterType="PushContacts" resultMap="ByPushContactsResult">
        <include refid="selectByPushContactsVo"/>
        <where>
            <if test="username != null  and username != ''">
                and username like concat('%', #{username}, '%')
            </if>
            <if test="phone != null  and phone != ''">
                and phone = #{phone}
            </if>
            and del_flag = 0;
        </where>
    </select>

    <insert id="insertByPushContacts" parameterType="PushContacts">
        insert into by_push_contacts
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">id,
            </if>
            <if test="username != null">username,
            </if>
            <if test="phone != null">phone,
            </if>
            <if test="delFlag != null">del_flag,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id},
            </if>
            <if test="username != null">#{username},
            </if>
            <if test="phone != null">#{phone},
            </if>
            <if test="delFlag != null">#{delFlag},
            </if>
        </trim>
    </insert>

    <update id="updateByPushContacts" parameterType="PushContacts">
        update by_push_contacts
        <trim prefix="SET" suffixOverrides=",">
            <if test="username != null">username =
                #{username},
            </if>
            <if test="phone != null">phone = #{phone},
            </if>
        </trim>
        where id = #{id}
    </update>

    <delete id="deleteByPushContactsByIds" parameterType="String">
        delete from by_push_contacts where id in
        <foreach item="id" collection="array" open="(" separator="," close=")">
            #{id}
        </foreach>
    </delete>
</mapper>
