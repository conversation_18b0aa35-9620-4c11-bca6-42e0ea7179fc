package com.boryou.web.task;

import cn.hutool.core.util.ObjectUtil;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.module.submit.entity.Submit;
import com.boryou.web.module.submit.service.ISubmitService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.annotation.Configuration;
import org.springframework.scheduling.annotation.EnableScheduling;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.Collection;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024-07-29 09:13
 */
@EnableScheduling
@Configuration
@Slf4j
@RequiredArgsConstructor
public class SubmitTask {
    private final RedisCache redisTemplate;

    private final ISubmitService submitService;
    @Resource(name = "scheduledExecutorService")
    ScheduledExecutorService scheduledExecutorService;

    @PostConstruct
    public void recover() {
        Collection<String> submitList = redisTemplate.keys(RedisConstant.submit_task + "ids:");
        log.info("本次恢复短信舆情处置任务:{}个", submitList.size());
        for (String submitKey : submitList) {
            Long submitId = redisTemplate.getCacheObject(submitKey);
            Submit submits = submitService.selectSubmitById(submitId);
            if (ObjectUtil.isNotEmpty(submits)) {
                if ("0".equals(submits.getProcessStatus())) {
                    suspendProcess(submitId, submits);
                } else {
                    redisTemplate.deleteObject(RedisConstant.submit_task + "ids:" + submitId);
                }
            }
        }
    }

    public void suspendProcess(Long submitId, Submit info) {
        // 在截止时间挂起任务
        scheduledExecutorService.schedule(() -> {
            try {
                Submit submits = submitService.selectSubmitById(submitId);
                if (ObjectUtil.isNotEmpty(submits)) {
                    String processStatus = submits.getProcessStatus();
                    //获取最新的报送信息（主要是相关人的处理状态信息），如果此处不是最新的状态，会导致
                    if ("0".equals(processStatus)) {
                        Submit submit = new Submit();
                        submit.setId(submitId);
                        submit.setProcessStatus("2");
                        submit.setProcessTime(null);
                        submitService.updateSubmit(submit);
                    }
                    redisTemplate.deleteObject(RedisConstant.submit_task + "ids:" + submitId);
                    log.info("任务{}{}已结束", submits.getId(), submits.getSuggest());
                }
            } catch (Exception e) {
                log.error("挂起流程失败", e);
            }
        }, info.getDeadline().getTime() - System.currentTimeMillis(), TimeUnit.MILLISECONDS);
    }

    public void addTask(Long id, Submit bean) {
        redisTemplate.setCacheObject(RedisConstant.submit_task + "ids:", id);
        suspendProcess(id, bean);
    }
}

