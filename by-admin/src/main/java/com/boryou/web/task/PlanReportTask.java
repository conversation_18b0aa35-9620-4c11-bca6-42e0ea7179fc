package com.boryou.web.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.enums.EmotionEnum;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.controller.common.util.ConvertHandler;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.PlanSimpleReport;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.domain.vo.GraphModelVO;
import com.boryou.web.domain.vo.PlanSimpleReportVO;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.module.mail.service.MailService;
import com.boryou.web.module.report.entity.Report;
import com.boryou.web.module.report.entity.ReportTemplate;
import com.boryou.web.module.report.entity.vo.ReportVO;
import com.boryou.web.module.report.mapper.ReportMapper;
import com.boryou.web.module.report.service.PlanSimpleReportService;
import com.boryou.web.module.report.service.ReportService;
import com.boryou.web.service.ElasticsearchService;
import com.boryou.web.service.PlanService;
import com.boryou.web.service.SearchAnalyseService;
import com.boryou.web.util.ReportFieldUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.text.DecimalFormat;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Random;
import java.util.stream.Collectors;

/**
 * 方案报告任务
 *
 * <AUTHOR>
 */
@Component("PlanReport")
@RequiredArgsConstructor
public class PlanReportTask {

    private final PlanSimpleReportService planSimpleReportService;

    private final SearchAnalyseService searchAnalyseService;

    private final PlanService planService;

    private final ReportService reportService;
    private final ReportMapper reportMapper;

    private final ConvertHandler convertHandler;

    private final MailService mailService;

    @Resource(name = "threadPoolTaskExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Resource
    ElasticsearchService elasticsearchService;

    @Value("${warnHost}")
    private String warnHost = "http://*************:18342";

    /**
     * 方案报告生成：每5分钟监测接下来5分钟内是否有报告需要生成，按照方案ID、报告类型和报告时间生成报告数据
     * 每份报告都需要一个线程来生成，以免过于阻塞
     * 报告数据保存在舆情报告表中--此类数据有模版和报告类别
     */
//    @Scheduled(cron = "0 */5 * * * ? ")
    public void createReport() {
        DateTime date = DateUtil.date();
        int weekDay = DateUtil.dayOfWeek(date);
        int monthDay = DateUtil.dayOfMonth(date);
        String startTime = DateUtil.format(date, "HH:mm");
        String endTime = DateUtil.format(DateUtil.offsetMinute(date, 4), "HH:mm");

        PlanSimpleReportVO vo = new PlanSimpleReportVO();
        vo.setStatus(1);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);

        vo.setReportDate(String.valueOf(weekDay));
        //日报
        vo.setReportType(1);
        List<PlanSimpleReport> list = planSimpleReportService.getList(vo);
        for (PlanSimpleReport planSimpleReport : list) {
            String eTime = DateUtil.format(date, DatePattern.NORM_DATE_PATTERN) + " " + planSimpleReport.getReportTime() + ":00";
            String sTime = DateUtil.format(DateUtil.offsetDay(DateUtil.parse(endTime), -1), DatePattern.NORM_DATETIME_PATTERN);
            threadPoolTaskExecutor.submit(() -> getIndexDataToCreateReport(date, sTime, eTime, planSimpleReport));
        }

        //周报
        vo.setReportType(2);
        list = planSimpleReportService.getList(vo);
        for (PlanSimpleReport planSimpleReport : list) {
            String eTime = DateUtil.format(date, DatePattern.NORM_DATE_PATTERN) + " " + planSimpleReport.getReportTime() + ":00";
            String sTime = DateUtil.format(DateUtil.offsetDay(DateUtil.parse(endTime), -7), DatePattern.NORM_DATETIME_PATTERN);
            threadPoolTaskExecutor.submit(() -> getIndexDataToCreateReport(date, sTime, eTime, planSimpleReport));
        }

        //月报
        vo.setReportDate(String.valueOf(monthDay));
        vo.setReportType(3);
        list = planSimpleReportService.getList(vo);
        for (PlanSimpleReport planSimpleReport : list) {
            String eTime = DateUtil.format(date, DatePattern.NORM_DATE_PATTERN) + " " + planSimpleReport.getReportTime() + ":00";
            String sTime = DateUtil.format(DateUtil.offsetMonth(DateUtil.parse(endTime), -1), DatePattern.NORM_DATETIME_PATTERN);
            threadPoolTaskExecutor.submit(() -> getIndexDataToCreateReport(date, sTime, eTime, planSimpleReport));
        }
    }

    /**
     * 方案报告信息发送：每分钟监测之前5分钟内是否已经有报告生成，给对应邮箱发送信息提醒
     * 即报告最多提前5分钟生成，最多推迟5分钟发送
     */
//    @Scheduled(cron = "0 * * * * ? ")
    public void sendReportMessage() {
        DateTime date = DateUtil.date();
        int weekDay = DateUtil.dayOfWeek(date);
        int monthDay = DateUtil.dayOfMonth(date);

        PlanSimpleReportVO vo = new PlanSimpleReportVO();
        vo.setSendStatus(1);
        vo.setReportDate(String.valueOf(weekDay));
        String startTime = DateUtil.format(DateUtil.offsetMinute(date, -4), "HH:mm");
        String endTime = DateUtil.format(date, "HH:mm");
        //日报
        vo.setReportType(1);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        List<PlanSimpleReport> list = planSimpleReportService.getList(vo);
        sendReport(date, vo, list);
        //周报
        vo.setReportType(2);
        vo.setStartTime(startTime);
        vo.setEndTime(endTime);
        list = planSimpleReportService.getList(vo);
        sendReport(date, vo, list);

        //月报
        vo.setReportDate(String.valueOf(monthDay));
        vo.setReportType(3);
        list = planSimpleReportService.getList(vo);
        sendReport(date, vo, list);
    }

    private void getIndexDataToCreateReport(DateTime date, String sTime, String eTime, PlanSimpleReport planSimpleReport) {
        ReportTemplate reportTemplate = reportService.selectTemplateById(planSimpleReport.getTempId());
        Plan plan = planService.selectPlanById(planSimpleReport.getPlanId());
        Report report = new Report();
        if (reportTemplate != null) {
            List<String> params = ReportFieldUtil.setTemplateParamSort(reportTemplate);
            JSONArray array = JSONUtil.parseArray(params);
            report.setInputComponents(array.toString());
        }
        report.setReportType(String.valueOf(planSimpleReport.getReportType()));

        report.setTempId(planSimpleReport.getTempId());
        report.setTitle(plan.getPlanName());
        Report report1 = reportMapper.selectIssue(report);
        if (report1 != null) {
            Integer issue = Integer.parseInt(report1.getIssue()) + 1;
            report.setIssue(String.valueOf(issue));
        } else {
            report.setIssue("1");
        }
        report.setReportId(IdUtil.getSnowflakeNextId());
        report.setUserId(plan.getUserId());
        report.setCreateBy(plan.getCreateBy());
        report.setCreateTime(date);

        SearchVO vo = new SearchVO();
        vo.setStartTime(sTime);
        vo.setEndTime(eTime);
        vo.setPageSize(10);
        EsSearchBO bo = convertHandler.copyPropertiesFromPlan(vo, plan);
        PageResult<EsBean> search = EsSearchUtil.searchEsBeanList(bo);
        Map<Integer, List<String>> hostMap
                = search.stream().filter(s -> s != null && !s.getHost().isEmpty()).collect(Collectors.groupingBy(EsBean::getType, Collectors.mapping(EsBean::getHost, Collectors.toList())));
        List<AccountInfoVO> accountInfoVOList = elasticsearchService.getNameByHosts(hostMap);
        Map<String, String> hostAndNameMap = accountInfoVOList.stream().distinct().collect(Collectors.toMap(AccountInfoVO::getDomain, AccountInfoVO::getSector, (oldValue, newValue) -> oldValue));
        for (EsBean bean : search) {
            //媒体类型
            bean.setTypeName(MediaTypeEnum.getDesc(String.valueOf(bean.getType())));
            //站点名称
            if (hostAndNameMap.containsKey(bean.getHost())) {
                //双微显示作者
                if (bean.getType() == MediaTypeEnum.WECHAT.getValue() || bean.getType() == MediaTypeEnum.WEIBO.getValue()) {
                    bean.setHost(bean.getAuthor());
                } else {
                    bean.setHost(hostAndNameMap.get(bean.getHost()));
                }
            }
        }
        //媒体类型分析
        JSONArray mediaTypes = searchAnalyseService.mediaTypeAnalyse(bo);
        //情感分析
        JSONObject emotions = searchAnalyseService.getEmotionAnalyse(bo);
        //站点报道量排行
        List<Object> mediaActive = searchAnalyseService.mediaActiveMap(bo);
        //字符云
        JSONArray wordArray = searchAnalyseService.wordAnalyse(bo, plan.getPlanId());
        //走势图
        GraphModelVO graphModelVO = searchAnalyseService.timeType(bo);

        //根据以上6个维度信息生成报告数据
        DecimalFormat df2 = new DecimalFormat("0.00");
        StringBuilder intro = new StringBuilder("本报告就加入的素材进行分析，共有" + search.getTotal() + "篇相关内容。其中");
        for (int i = 0; i < mediaTypes.size(); i++) {
            JSONObject object = (JSONObject) mediaTypes.get(i);
            intro.append(object.getStr("name")).append("共有").append(object.getStr("value")).append("篇，占比").append(object.getStr("percent"));
        }
        if (mediaTypes.size() >= 3) {
            intro.append("可以看出").append(((JSONObject) mediaTypes.get(0)).getStr("name")).append("的比重最大，占比达到信息总量的").append(((JSONObject) mediaTypes.get(0)).getStr("percent")).append("其次主要集中").append(((JSONObject) mediaTypes.get(1)).getStr("name")).append("、").append(((JSONObject) mediaTypes.get(2)).getStr("name")).append("等几大站点。");
        }
        intro.append("详细报告请继续浏览。");
        report.setReportIntro(intro.toString());

        report.setSuggest("对于舆情信息中具有潜在危害的事件及情况应给予关注并积极处理，防止不良影响产生及扩散。此外，密切关注此前敏感预警事件的发展情况，及时制定有效应对措施。鉴于监测结果中负面舆情有在发生，应即时做好预防和处理工作，阻止事态继续发展。");

        DecimalFormat df1 = new DecimalFormat("0.0");
        String overview = "监测主题相关信息内容" + search.getTotal() + "条。其中";
        JSONArray emoDatas = emotions.getJSONArray("data");
        JSONObject emoParams = emotions.getJSONObject("prarms");
        JSONObject emoDesc = emotions.getJSONObject("desc");
        if (emoParams.containsKey("negative")) {
            int size = emoParams.getInt("negative", 0);
            overview += "敏感" + size + "条，敏感占比" + emoDesc.getStr("negative");
        }
        if (emoParams.containsKey("positive")) {
            if (emoParams.containsKey("negative")) {
                overview += "，";
            }
            int size = emoParams.getInt("positive", 0);
            overview += "非敏感" + size + "条，非敏感占比" + emoDesc.getStr("positive");
        }
        if (emoParams.containsKey("neutral")) {
            if (emoParams.containsKey("negative") || emoParams.containsKey("positive")) {
                overview += "，";
            }
            int size = emoParams.getInt("neutral", 0);
            overview += "中性" + size + "条，中性占比" + emoDesc.getStr("neutral");
        }
        overview += "。";
        report.setOverview(overview);

        JSONArray array = new JSONArray();
        JSONObject object;
        for (Object mediaType : mediaTypes) {
            JSONObject typeObject = (JSONObject) mediaType;
            object = JSONUtil.createObj();
            object.putOnce("name", typeObject.getStr("name"));
            object.putOnce("value", typeObject.getStr("value"));
            object.putOnce("percent", typeObject.getStr("percent"));
            array.set(object);
        }
        report.setMediaStatistics(array.toString());

        array = new JSONArray();
        for (Object emoData : emoDatas) {
            JSONObject emoObject = (JSONObject) emoData;
            object = JSONUtil.createObj();
            object.putOnce("name", emoObject.getStr("name"));
            object.putOnce("value", emoObject.getStr("value"));
            object.putOnce("percent", df2.format((double) emoObject.getInt("value", 0) * 100 / emoParams.getInt("total", 1)) + "%");
            array.set(object);
        }
        report.setEmotionAnalysis(array.toString());

        //对hostCollect的条目进行排序
        object = JSONUtil.createObj();
        List<String> x = new ArrayList<>();
        List<Integer> y = new ArrayList<>();
        for (Object o : mediaActive) {
            JSONObject mediaObject = (JSONObject) o;
            x.add(mediaObject.getStr("name"));
            y.add(mediaObject.getInt("value"));
        }
        object.putOnce("xList", x);
        object.putOnce("yList", y);
        report.setMediaDetails(object.toString());

        report.setCharCloud(wordArray.toString());

        array = new JSONArray();
        for (int i = 0; i < search.size() && i <= 5; i++) {
            EsBean bean = search.get(i);
            object = JSONUtil.createObj();
            String title = bean.getTitle();
            if (title == null) {
                title = "";
            } else if (title.length() > 20) {
                title = title.substring(0, 20) + "...";
            }
            object.putOnce("title", title);
            object.putOnce("sourceAndTime", bean.getTypeName() + (bean.getHost() == null ? "" : bean.getHost()) + " " + DateUtil.format(bean.getPublishTime(), DatePattern.NORM_DATETIME_PATTERN));
            object.putOnce("emotion", EmotionEnum.fromValue(bean.getEmotionFlag()).getName());
            array.add(object);
        }
        report.setMainInfo(array.toString());

        array = new JSONArray();
        for (int i = 0; i < search.size() && i <= 5; i++) {
            EsBean bean = search.get(i);
            object = JSONUtil.createObj();
            object.putOnce("title", bean.getTitle());
            object.putOnce("text", bean.getText());
            if (bean.getType() == 3 || bean.getType() == 5) {
                object.putOnce("source", bean.getTypeName());
            } else {
                object.putOnce("source", bean.getHost());
            }
            object.putOnce("time", DateUtil.format(bean.getPublishTime(), DatePattern.NORM_DATETIME_PATTERN));
            object.putOnce("author", bean.getAuthor());
            object.putOnce("emotion", EmotionEnum.fromValue(bean.getEmotionFlag()).getName());
            object.putOnce("url", bean.getUrl());
            array.add(object);
        }
        report.setInfoIntro(array.toString());

        JSONObject jsonObject = JSONUtil.parseObj(graphModelVO);
        report.setMediaTrendChart(jsonObject.toString());

        reportMapper.insert(report);

        planSimpleReport.setSendStatus(1);
        planSimpleReportService.update(planSimpleReport);
    }

    private void sendReport(DateTime date, PlanSimpleReportVO vo, List<PlanSimpleReport> list) {
        for (PlanSimpleReport planSimpleReport : list) {
            //查询报告  查询5分钟内 某用户某方案的月报
            Plan plan = planService.selectPlanById(planSimpleReport.getPlanId());
            ReportVO report = new ReportVO();
            report.setTempId(planSimpleReport.getTempId());
            report.setStartTime(DateUtil.offsetMinute(date, -5));
            report.setUserId(plan.getUserId());
            report.setTitle(plan.getPlanName());
            report.setReportType(String.valueOf(vo.getReportType()));
            List<ReportVO> reportVOS = reportService.selectReport(report);
            if (CollUtil.isNotEmpty(reportVOS)) {
                report = reportVOS.get(0);
                //发送邮件
                JSONArray array = JSONUtil.parseArray(planSimpleReport.getReceiverEmails());
                for (Object o : array) {
                    String receiver = ((JSONObject) o).getStr("email");
                    sendEmail(report, receiver);
                }
            }
            planSimpleReport.setSendStatus(0);
            planSimpleReportService.update(planSimpleReport);
        }
    }

    private void sendEmail(ReportVO report, String receiver) {
        String result = "发送失败，请稍后再试！";
        //验证邮箱
        if (System.getProperty("os.name").toLowerCase().contains("windows")) {
            //开发环境使用测试环境打开报告
            warnHost = "http://*************:18342";
        }
        String reportUrl = warnHost + "/boryou/outReport?reportId=" + report.getReportId();
        StringBuilder content = new StringBuilder();
        content.append("<b style='font-size:25px;'>您有一份舆情报告待查看：《" + report.getTitle() + "》</b><br>");
        content.append("<b style='font-size:20px;color:#5369a0;'>【点击下方查看】</b>");
        content.append("<a  target='_blank' href='" + reportUrl + "' style='text-decoration: none;'>");
//        content.append("<div style='background: url(http://" + SYSTEM_IP + "/" + SYSTEM_NAME + "/resources/img/seniorReport/bodyCot.svg) no-repeat center center;\n" +
//                "    height: 216px;\n" +
//                "    padding-top: 50px;\n" +
//                "    box-sizing: border-box;\n" +
//                "    text-align: center;color:red;width: 166px;margin-top: 20px;border: 1px solid #bdbdbd;'>");
        content.append("<h3>" + report.getTitle() + "</h3>");
        content.append("<span style='border: 1px solid #f45757;color:red;padding: 5px 5px;border-radius: 2px;'>");
        content.append(" 点击预览报告 ");
        content.append("</span>");
        content.append("</div>");
        content.append("</a>");
        content.append("<br><br><br>");
        content.append("-- 此邮件由舆情系统发出，请勿直接回复 --");

        Random r = new Random();
        int num = r.nextInt(1);
//        Email mail = new RegisterEmail(receiver, "【您有一份舆情报告】- " + title, content.toString(), num);
//        boolean sendOK = MailSender.sendHtmlMail(mail);

        boolean b = mailService.sendMail(num, receiver, report.getTitle(), content.toString());
        if (b) {
            result = "发送成功！";
        }
    }

}
