package com.boryou.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.crypto.digest.MD5;
import cn.hutool.http.HtmlUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.constant.BC;
import com.boryou.web.controller.common.entity.BoryouBean;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.entity.vo.BoryouBeanVO;
import com.boryou.web.controller.common.enums.SortTypeEnum;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.domain.vo.DetailVO;
import com.boryou.web.domain.vo.InfoVO;
import com.boryou.web.domain.vo.WordVO;
import com.boryou.web.module.area.entity.Area;
import com.boryou.web.module.area.service.AreaService;
import com.boryou.web.module.collection.entity.Collection;
import com.boryou.web.module.collection.service.CollectionService;
import com.boryou.web.module.material.entity.Material;
import com.boryou.web.module.material.service.MaterialService;
import com.boryou.web.module.search.entity.EsSpecialData;
import com.boryou.web.module.search.service.EsSpecialDataService;
import com.boryou.web.util.BoryouBeanUtil;
import com.boryou.web.util.HighlightUtil;
import com.hankcs.hanlp.HanLP;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import javax.annotation.Resource;
import java.io.StringReader;
import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.stream.Collectors;

@RequiredArgsConstructor
@Service
@Slf4j
public class InfoService {
    @Resource
    PlanService planService;
    @Resource
    private ElasticsearchService elasticsearchService;
    @Resource
    private AreaService areaService;
    @Resource
    EsSpecialDataService esSpecialDataService;
    private final RedisCache redisTemplate;

    private final HotService hotService;
    private final MaterialService materialService;
    private final CollectionService collectionService;

    public List<BoryouBean> getSimilarInfo(InfoVO infoVO) {
        String md5 = infoVO.getMd5();
        try {
            // 根据索引id、时间获取索引数据
            if (StrUtil.isBlankIfStr(md5)) {
                return Collections.emptyList();
            }
            Integer pageNum = infoVO.getPageNum();
            Integer pageSize = infoVO.getPageSize();
            if (pageNum == null || pageNum <= 0) {
                pageNum = 1;
            }
            if (pageSize == null || pageSize <= 0) {
                pageSize = 10;
            }
            int from = (pageNum - 1) * pageSize;
            int size = pageSize;
            EsSearchBO esSearchBO = new EsSearchBO();
            esSearchBO.setMd5(md5);
            esSearchBO.setPageNum(from);
            esSearchBO.setPageSize(size);
            if (StrUtil.isNotEmpty(infoVO.getSort())) {
                esSearchBO.setSortType(SortTypeEnum.getEnumValue(SortTypeEnum.TIME_ASC));
            }
            List<EsBean> esBeanList = EsSearchUtil.getSimilarDataByMD5(esSearchBO);
            List<BoryouBean> boryouBeanList = new ArrayList<>();
            for (EsBean bean : esBeanList) {
                BoryouBeanVO boryouBean = bean.convertEsBeanToBoryouBean();
                String time1 = boryouBean.getTime();
                String format = DateUtil.parse(time1).toString("yyyyMMddHHmmss");
                boryouBean.setTime(format);
                if (StrUtil.isEmpty(boryouBean.getTitle())) {
                    String text = boryouBean.getText();
                    if (text.length() > 60) {
                        text = text.substring(0, 59);
                    }
                    boryouBean.setTitle(text);
                }
                boryouBeanList.add(boryouBean);
            }
            List<BoryouBean> list = BoryouBeanUtil.removeImgTag(boryouBeanList);

            // 根据标题、来源、时间过滤
            Iterator<BoryouBean> iterator = list.iterator();
            HashSet<Object> set = new HashSet<>();
            MD5 md51 = MD5.create();
            while (iterator.hasNext()) {
                BoryouBean next = iterator.next();
                String s = next.getTitle() + next.getTime() + next.getHost();
                if (!set.add(md51.digestHex(s))) {
                    iterator.remove();
                }
            }
            // 此处做修改--如果类型为3微博 就截取内容的前20字作为标题
            for (BoryouBean boryouBean : list) {
                if (boryouBean.getTitle() == null || boryouBean.getTitle() == "") {
                    if (boryouBean.getText().length() > 20) {
                        boryouBean.setTitle(boryouBean.getText().substring(0, 20));
                    } else {
                        boryouBean.setTitle(boryouBean.getText());
                    }
                }
            }
            // 按照时间排序，为了在页面上显示传播路径时不矛盾
            // list.sort((b1, b2) -> (int) (Long.parseLong(b1.getTime()) - Long.parseLong(b2.getTime())));

            List<String> hosts = new ArrayList<>();
            for (BoryouBean boryouBean : list) {
                getDomainMap(boryouBean, hosts);
            }
//            long start=System.currentTimeMillis();
            Map<Integer, List<String>> hostMap
                    = list.stream().filter(s -> s != null && !s.getHost().isEmpty()).collect(Collectors.groupingBy(BoryouBean::getType, Collectors.mapping(BoryouBean::getHost, Collectors.toList())));
            List<AccountInfoVO> domainMap = elasticsearchService.getNameByHosts(hostMap);
            Map<String, String> hostAndNameMap = new HashMap<>();
            for (AccountInfoVO accountInfoVO : domainMap) {
                hostAndNameMap.putIfAbsent(accountInfoVO.getDomain(), accountInfoVO.getSector());
            }
//            log.info("host耗时:{}秒",(System.currentTimeMillis()-start)/1000);
            for (BoryouBean bean : list) {
                // 转换时间格式
                String time1 = bean.getTime();
                bean.setTime(DateUtil.parse(time1).toString("yyyy-MM-dd HH:mm:ss"));
//                String url = bean.getUrl();
//                String host = new URL(url).getHost();
                String host = bean.getHost();
                bean.setHostName(hostAndNameMap.getOrDefault(host, host));
            }
            return list;
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("获取相似信息失败");
        }
    }

    private void getDomainMap(BoryouBean bean, List<String> hosts) {
        if (bean == null) {
            return;
        }
        int type = bean.getType();
        if (type == 8 || type == 9) {
            // 音视频截取标题前半部分
            String[] hostSplit = bean.getTitle().replace("<em>", "").replace("</em>", "").split("--");
            if (hostSplit.length == 0) {
                return;
            }
            hosts.add(hostSplit[0]);
        } else if (type == 17) {
            String host = bean.getHost();
            hosts.add(host);
        } else {
            String host;
            try {
                host = new URL(bean.getUrl()).getHost();
            } catch (MalformedURLException e) {
                return;
            }
            hosts.add(host);
        }
    }

    private String getDomain(BoryouBean bean) {
        if (bean == null) {
            return null;
        }
        int type = bean.getType();
        if (type == 8 || type == 9) {
            // 音视频截取标题前半部分
            String[] hostSplit = bean.getTitle().replace("<em>", "").replace("</em>", "").split("--");
            if (hostSplit.length == 0) {
                return null;
            }
            return hostSplit[0];
        } else if (type == 17) {
            return bean.getHost();
        } else {
            String host;
            try {
                host = new URL(bean.getUrl()).getHost();
            } catch (MalformedURLException e) {
                return null;
            }
            return host;
        }
    }

    public List<WordVO> getKeyWordsAnalyse(String text) {
        if (StrUtil.isBlankIfStr(text)) {
            // throw new CustomException("文章不能为空");
            return Collections.emptyList();
        }
        Map<String, Integer> wordsFren = new HashMap<>();
        try {
            List<WordVO> wordVOS = new ArrayList<>();
            if (StringUtils.isNotEmpty(text)) {
                List<String> filterCloudWord = hotService.filterCloudWordList();
                text = HtmlUtil.cleanHtmlTag(text);
                Lexeme lexeme;
                IKSegmenter ikSegmenter = new IKSegmenter(new StringReader(text), true);
                while ((lexeme = ikSegmenter.next()) != null) {
                    String lexemeText = lexeme.getLexemeText();
                    if (lexemeText.length() > 1) {
                        if (filterCloudWord.contains(lexemeText)) {
                            continue;
                        }
                        if (wordsFren.containsKey(lexemeText)) {
                            wordsFren.put(lexemeText, wordsFren.get(lexemeText) + 1);
                        } else {
                            wordsFren.put(lexemeText, 1);
                        }
                    }
                }
                wordsFren.keySet().forEach(x -> {
                    WordVO wordVO = new WordVO();
                    wordVO.setName(x);
                    wordVO.setValue(wordsFren.get(x));
                    wordVOS.add(wordVO);
                });
            }
            List<WordVO> collect = wordVOS.stream().sorted((o1, o2) -> Math.toIntExact(o2.getValue() - o1.getValue())).collect(Collectors.toList());
            if (collect.size() > 21) {
                collect = collect.subList(0, 20);
            }
            return collect;
        } catch (Exception e) {
            // throw new CustomException("获取词云错误");
            log.error("获取词云错误: {}", e.getMessage(), e);
            return Collections.emptyList();
        }
    }

    public List<WordVO> getKeyWordsAnalyse1(String text) {
        if (StrUtil.isBlankIfStr(text)) {
            throw new CustomException("文章不能为空");
        }
        try {
            List<WordVO> wordList = new ArrayList<>();
            List<String> keyWords = HanLP.extractKeyword(text, 20);
            int appearNum = 0;
            for (String str : keyWords) {
                appearNum = appearNumber(str, text);
                if (appearNum == 0) {
                    // 一次没出现不返回
                    continue;
                }
                if (!BC.hot_words.contains(str)/*TextUtil.isNumeric(str) || TextUtil.isAlphabetic(str)|| TextUtil.isSpecialCharacters(str)*/) {
                    continue;
                }
                WordVO wordVO = new WordVO();
                wordVO.setName(str);
                wordVO.setValue(appearNum);
                wordList.add(wordVO);
            }
            return wordList;
        } catch (Exception e) {
            throw new CustomException("获取词云错误");
        }
    }

    /**
     * 统计关键词在文章中出现的次数
     *
     * @param keyWord 关键词
     * @param text    文章内容
     * @return int 出现的次数
     * <AUTHOR>
     * @date 2017-5-8 下午4:40:35
     */
    private int appearNumber(String keyWord, String text) {
        int count = 0;
        int index = 0;
        while ((index = text.indexOf(keyWord, index)) != -1) {
            index = index + keyWord.length();
            count++;
        }
        return count;
    }

    public DetailVO oneInfo(InfoVO infoVO) {
        Long id = infoVO.getId();
        if (id == null) {
            throw new CustomException("id不能为空");
        }
        try {
            EsSearchBO esSearchBO = new EsSearchBO();
            esSearchBO.setId(String.valueOf(id));
            String time = infoVO.getTime();
            esSearchBO.setStartTime(time);
            esSearchBO.setEndTime(time);
            BoryouBeanVO bean = EsSearchUtil.searchByIdTime(esSearchBO);
            if (bean == null) {
                return null;
            }
            // 此处做修改--如果类型为3微博 就截取内容的前20字作为标题
            if (bean.getType() == 3) {
                // if (bean.getText().length() > 20) {
                //     bean.setTitle(bean.getText().substring(0, 20));
                // } else {
                //     bean.setTitle(bean.getText());
                // }

                String text = bean.getText();
                if (text.contains("//@")) {
                    bean.setText(markWeiboForwardInfo(text, text.indexOf("//@")));
                } else if (text.contains("// @")) {
                    bean.setText(markWeiboForwardInfo(text, text.indexOf("// @")));
                } else if (text.contains("@") && text.contains(":") && text.indexOf("@") < text.indexOf(":")) {
                    int lastIndex = text.indexOf(":");
                    text = text.substring(0, lastIndex);
                    int beforeIndex = text.lastIndexOf("@");
                    if (lastIndex - beforeIndex < 15) {
                        bean.setText(markWeiboForwardInfo(bean.getText(), beforeIndex));
                    }
                }
            }
            if (bean.getType() == 5 && !StrUtil.isEmptyIfStr(bean.getText())) {
                bean.setText(HtmlUtil.removeHtmlTag(bean.getText(), "img", "section", "p"));
            }

            // String domain = getDomain(bean);

            String host = bean.getHost();
            int type = bean.getType();

            Map<Integer, List<String>> hostMap = new HashMap<>();
            if (CharSequenceUtil.isNotBlank(host)) {
                hostMap.put(type, CollUtil.newArrayList(host));
            }

            List<AccountInfoVO> domainMap = elasticsearchService.getNameByHosts(hostMap);

            Map<String, String> hostAndNameMap = new HashMap<>();
            for (AccountInfoVO accountInfoVO : domainMap) {
                hostAndNameMap.putIfAbsent(accountInfoVO.getDomain(), accountInfoVO.getSector());
            }

            bean.setHostName(hostAndNameMap.getOrDefault(host, host));
            // AccountInfoVO nameByHost = elasticsearchService.getNameByHost(hostName);

            List<WordVO> keyWordsAnalyse = this.getKeyWordsAnalyse(bean.getText());
            List<String> textAddress = bean.getTextAddress();
            List<String> textAddressChange = new ArrayList<>();
            if (CollUtil.isNotEmpty(textAddress)) {
                Map<String, String> areaMap = new HashMap<>();
                List<Area> areaList = areaService.list(new LambdaQueryWrapper<Area>().in(Area::getId, textAddress));
                if (CollUtil.isNotEmpty(areaList)) {
                    areaMap = areaList.stream().collect(Collectors.toMap(item -> String.valueOf(item.getId()), Area::getAreaName, (k1, k2) -> k1));
                }
                for (String address : textAddress) {
                    if (areaMap.containsKey(address)) {
                        String areaName = areaMap.get(address);
                        textAddressChange.add(String.valueOf(areaName));
                    }
                }
            }
            String address = CharSequenceUtil.join(" ", textAddressChange);
            bean.setAddress(address);
            if (!StrUtil.isBlankIfStr(address)) {
                String text = HighlightUtil.highlighter(bean.getText(), address, true);
                text = text.replaceAll("<em>", "<span>").replaceAll("</em>", "</span>");
                bean.setText(text);
            }

            String keyWords = infoVO.getKeyWords();
            if (!StrUtil.isBlankIfStr(keyWords)) {
                String title = HighlightUtil.highlighter(bean.getTitle(), keyWords, true);
                String text = HighlightUtil.highlighter(bean.getText(), keyWords, true);
                if (!StrUtil.isBlankIfStr(title)) {
                    bean.setTitle(title);
                }
                if (!StrUtil.isBlankIfStr(text)) {
                    bean.setText(text);
                }
            }
//            JSONObject jsonObject = redisTemplate.getCacheObject(RedisConstant.info_status_prefix + id);
//            if (jsonObject == null) {
//                jsonObject = UrlAccessUtil.getUrlState(bean.getUrl());
//            }

//            if (!jsonObject.isEmpty() && !jsonObject.containsKey("err")) {
            // 阅读数（微信）
            bean.setReadNum(bean.getReadNum());
            // 点赞数
            bean.setGoodNum(bean.getCommentGoodsNum());
            // 评论数
            bean.setCommentNum(bean.getCommentNum());
            // 转发数
            bean.setReprintNum(bean.getReprintNum());
            // 收藏数（短视频）
            bean.setCollect(0);
            // 在看（微信）
            bean.setPoorNum(0);
//            }

            List<String> indexList = new ArrayList<>();
            List<String> md5List = new ArrayList<>();
            md5List.add(bean.getMD5());
            List<EsSpecialData> esSpecialDataList = esSpecialDataService.getEsSpecialDatasByList(indexList, md5List);
            if (CollUtil.isNotEmpty(esSpecialDataList) && esSpecialDataList.get(0).getMd5().equals(bean.getMD5())) {
                // 情感
                if (esSpecialDataList.get(0).getEmotionFlag() != bean.getEmotional()) {
                    bean.setEmotional(esSpecialDataList.get(0).getEmotionFlag());
                }
                // 噪音
                if (esSpecialDataList.get(0).getTrash() > 0) {
                    bean.setIsSpam(esSpecialDataList.get(0).getTrash() == 2);
                }
                // 处置
                bean.setDeal(esSpecialDataList.get(0).getDeal());
                // 关注
                bean.setFollow(esSpecialDataList.get(0).getFollow());
                // 预警
                bean.setWarned(esSpecialDataList.get(0).getWarned());
            }


            DetailVO detailVO = new DetailVO();
            detailVO.setDetail(bean);
            detailVO.setWords(keyWordsAnalyse);

            Long userIdL = SecurityUtils.getUserIdL();
            Material material = materialService.getOne(Wrappers.<Material>lambdaQuery().eq(Material::getUserId, userIdL).eq(Material::getContentId, bean.getId()));
            com.boryou.web.module.collection.entity.Collection collection = collectionService.getOne(Wrappers.<com.boryou.web.module.collection.entity.Collection>lambdaQuery().eq(com.boryou.web.module.collection.entity.Collection::getUserId, userIdL).eq(Collection::getContentId, bean.getId()));
            detailVO.setMaterialStatus(material != null);
            detailVO.setCollectionStatus(collection != null);

            return detailVO;
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("查询失败");
        }
    }

    private static String markWeiboForwardInfo(String text, int index) {
        return "<i style=\"background-color: khaki;\">" + text.substring(0, index) + "</i>" + text.substring(index);
    }

    private String getKeyWords(String keyWords, Long plateId) {
        try {
            // keyWords = new String(keyWords.getBytes("ISO8859_1"), "UTF-8");
            if (!keyWords.isEmpty()) {
                return keyWords;
            }
            // 取舆情配置ID
            if (plateId != null) {
                Plan plan = planService.selectPlanById(plateId);
                if (plan != null) {
                    Integer searchMode = plan.getSearchMode();
                    if (searchMode == 0) {
                        String kw1 = plan.getKw1();
                        String kw2 = plan.getKw2();
                        String kw3 = plan.getKw3();
                        return getRegexWord(kw1, kw2, kw3);
                    } else if (searchMode == 1) {
                        String highMonitorWord = plan.getHighMonitorWord();
                        highMonitorWord = highMonitorWord.replace("|", " ").replace("(", "").replace(")", "").replace("+", " ");
                        return highMonitorWord;
                    }
                }
            } else {
                // 取预警规则id
                // String ruleId = ParamUtil.getString(getRequest(), "ruleId", "");
                // if (ruleId != null && ruleId.length() > 0 && !"-2".equals(ruleId)) {
                //    WarningRule rule = ruleService.getWarningRuleById(ruleId);
                //    return getRegexWord(rule.getRegexArea(), rule.getRegexEvent(), rule.getRegexPerson());
                //}
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return keyWords;
    }

    /**
     * 将不同规则的词组成一个字符串
     *
     * @param regex 规则数组
     * @return
     * <AUTHOR>
     * @date 2016-11-26 上午10:09:33
     */
    private String getRegexWord(String... regex) {
        StringBuffer sb = new StringBuffer();
        if (regex != null) {
            for (String reg : regex) {
                if (reg != null) {
                    if (sb.length() > 0) {
                        sb.append(" ");
                    }
                    sb.append(reg);
                }
            }
        }
        return sb.toString();
    }

    public String keywordsDetail(InfoVO infoVO) {
        DetailVO detailVO = this.oneInfo(infoVO);

        if (detailVO != null) {
            List<String> collect = detailVO.getWords().stream().map(WordVO::getName).collect(Collectors.toList());
            String title = detailVO.getDetail().getTitle();
            collect.addAll(HanLP.extractKeyword(title, 10));
            return collect.stream().collect(Collectors.joining(" "));
        }
        return null;
    }

}
