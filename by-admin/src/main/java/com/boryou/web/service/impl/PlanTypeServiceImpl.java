package com.boryou.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.PlanType;
import com.boryou.web.domain.vo.PlanTypeVO;
import com.boryou.web.domain.vo.SortVO;
import com.boryou.web.enums.SortTypeEnum;
import com.boryou.web.mapper.PlanTypeMapper;
import com.boryou.web.service.PlanService;
import com.boryou.web.service.PlanTypeService;
import com.boryou.web.util.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

/**
 * 方案文件夹Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Service
@AllArgsConstructor
public class PlanTypeServiceImpl extends ServiceImpl<PlanTypeMapper, PlanType> implements PlanTypeService {

    private final PlanService planService;
    private final SysDeptMapper deptMapper;
    private final PlanTypeMapper planTypeMapper;

    @Override
    public List<PlanType> planTypeList(String typeName) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        //-1为其他方案 -2为重点方案 固定写死
        return this.list(new LambdaQueryWrapper<PlanType>()
                .like(StrUtil.isNotBlank(typeName), PlanType::getTypeName, typeName)
                .and(x -> x.eq(PlanType::getUserId, userId).or().eq(PlanType::getTypeId, -1)
                        .or().eq(PlanType::getTypeId, -2))
                .orderByDesc(PlanType::getSort));
    }

    @Override
    public boolean addPlanType(PlanTypeVO planTypeVO) {
        String typeName = planTypeVO.getTypeName();
        if (StrUtil.isBlankIfStr(typeName)) {
            throw new CustomException("名称不能为空");
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        judgeTypeName(userId, typeName);

        // 获取sort最大值, 若type为null, 增加type is null 条件
        Long sort = this.getObj(new LambdaQueryWrapper<PlanType>().eq(PlanType::getUserId, userId)
                .orderByDesc(PlanType::getSort)
                .select(PlanType::getSort), item -> {
            if (item == null) {
                return null;
            } else {
                String string = StrUtil.toString(item);
                return NumberUtil.parseLong(string, null);
            }
        });
        if (sort == null) {
            sort = 100L;
        } else {
            sort += 1;
        }
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        PlanType planType = new PlanType();
        planType.setTypeId(IdUtil.getSnowflakeNextId());
        planType.setTypeName(typeName);
        planType.setUserId(userId);
        planType.setSort(sort);
        planType.setCreateBy(userName);
        planType.setCreateTime(date);
        planType.setUpdateBy(userName);
        planType.setUpdateTime(date);
        return this.save(planType);
    }

    private void judgeTypeName(Long userId, String typeName) {
        int count = this.count(new LambdaQueryWrapper<PlanType>().eq(PlanType::getUserId, userId)
                .eq(PlanType::getTypeName, typeName)
                .select(PlanType::getSort));
        if (count > 0) {
            throw new CustomException("名称已存在");
        }
    }

    @Override
    public boolean updatePlanType(PlanTypeVO planTypeVO) {
        Long typeId = planTypeVO.getTypeId();
        String typeName = planTypeVO.getTypeName();
        if (typeId == null) {
            throw new CustomException("id不能为空");
        }
        PlanType planTypeOld = this.getById(typeId);
        String typeNameOld = planTypeOld.getTypeName();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        if (!StrUtil.isBlankIfStr(typeName) || !CharSequenceUtil.equals(typeNameOld, typeName)) {
            judgeTypeName(userId, typeName);
        }
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        PlanType planType = new PlanType();
        planType.setTypeId(typeId);
        planType.setTypeName(typeName);
        planType.setUpdateBy(userName);
        planType.setUpdateTime(date);
        return this.updateById(planType);
    }

    @Override
    public boolean delPlanType(PlanTypeVO planTypeVO) {
        Long typeId = planTypeVO.getTypeId();
        if (typeId == null) {
            throw new CustomException("id不能为空");
        }
        if (typeId == -1) {
            throw new CustomException("【其他方案】不可删除");
        }
        int count = planService.count(new LambdaQueryWrapper<Plan>()
                .eq(Plan::getTypeId, typeId)
                .eq(Plan::getDelFlag, 0)
        );
        if (count > 0) {
            throw new CustomException("此方案分类下存在方案，无法删除");
        }
        return this.removeById(typeId);
    }

    @Override
    public boolean movePlanType(SortVO sortVO) {
        Long typeId = sortVO.getTypeId();
        Long planId = sortVO.getPlanId();
        String sortType = sortVO.getSortType();
        PlanType planType = this.getById(typeId);

        Long sort = planType.getSort();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        LambdaQueryWrapper<PlanType> qw = new LambdaQueryWrapper<>();
        qw.eq(PlanType::getUserId, userId);

        Long changeSortTypeId;
        switch (SortTypeEnum.getByType(sortType)) {
            // 上移
            case UP:
                qw.gt(PlanType::getSort, sort);
                qw.orderByAsc(PlanType::getSort);
//				qw.last("limit 1");
                List<PlanType> upTypes = this.list(qw);
                if (CollUtil.isEmpty(upTypes)) {
                    throw new CustomException("已经在最顶部啦");
                }
                changeSortTypeId = getChangeSortTypeId(upTypes);
                if (changeSortTypeId == null) {
                    throw new CustomException("已经在最顶部啦");
                }
                baseMapper.changeSortById(typeId, changeSortTypeId);
                break;
            // 下移
            case DOWN:
                qw.lt(PlanType::getSort, sort);
                qw.orderByDesc(PlanType::getSort);
//				qw.last("limit 1");
                List<PlanType> downType = this.list(qw);
                if (downType == null) {
                    throw new CustomException("已经在最底部啦");
                }
                changeSortTypeId = getChangeSortTypeId(downType);
                if (changeSortTypeId == null) {
                    throw new CustomException("已经在最底部啦");
                }
                baseMapper.changeSortById(typeId, changeSortTypeId);
                break;
            // 置顶
            case TOP:
                qw.orderByDesc(PlanType::getSort);
                qw.last("limit 1");
                PlanType topType = this.getOne(qw);
                if (topType.getTypeId().equals(typeId)) {
                    throw new CustomException("已经在最顶部啦");
                }
                Long topSort = topType.getSort();
                PlanType planType1 = new PlanType();
                planType1.setTypeId(typeId);
                planType1.setSort(topSort + 1);
                this.updateById(planType1);
                break;
            // 置底
            case BOTTOM:
                qw.orderByAsc(PlanType::getSort);
                qw.last("limit 1");
//                baseMapper.selectOne(qw);
                PlanType bottomType = baseMapper.selectOne(qw);
                if (bottomType.getTypeId().equals(typeId)) {
                    throw new CustomException("已经在最底部啦");
                }
                Long bottomSort = bottomType.getSort();
                PlanType planType2 = new PlanType();
                planType2.setTypeId(typeId);
                planType2.setSort(bottomSort - 1);
                baseMapper.updateById(planType2);
                break;
            default:
                throw new CustomException("未知排序操作");
        }
        return true;
    }

    private Long getChangeSortTypeId(List<PlanType> upTypes) {
        Long upTypeId;
        for (PlanType upType : upTypes) {
            upTypeId = upType.getTypeId();
            LambdaQueryWrapper<Plan> qw = new LambdaQueryWrapper<>();
            qw.eq(Plan::getTypeId, upTypeId)
                    .eq(Plan::getDelFlag, 0)
                    .eq(Plan::getEnableFlag, 1);
            List<Plan> planList = planService.list(qw);
            if (CollUtil.isNotEmpty(planList)) {
                return upTypeId;
            }
        }
        return null;
    }

    @Override
    public List<PlanType> manageList(String typeName, Long userId, Integer pageNum, Integer pageSize) {

        // 默认情况，查询本级和下级的组织id
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        // 是否只查询自己创建的方案
        List<Long> deptIds = new ArrayList<>();
        if (userId == null) {
            deptIds = deptMapper.selectManageDept(deptId);
        }
        PageUtil.startPage(pageNum, pageSize);
        return planTypeMapper.manageList(typeName, deptIds, userId);
    }
}
