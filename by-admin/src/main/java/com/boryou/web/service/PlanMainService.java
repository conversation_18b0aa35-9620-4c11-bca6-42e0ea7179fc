package com.boryou.web.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.PlanMain;
import com.boryou.web.domain.vo.PlanMainVO;
import com.boryou.web.domain.vo.PlanVO;
import com.boryou.web.mapper.PlanMainMapper;
import com.boryou.web.mapper.PlanMapper;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;

/**
 * 方案Service接口
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
public class PlanMainService extends ServiceImpl<PlanMainMapper, PlanMain> {

}
