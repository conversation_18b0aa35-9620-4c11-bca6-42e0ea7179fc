package com.boryou.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.domain.PushContacts;
import com.boryou.web.domain.vo.PushContactsVO;

import java.util.List;
import java.util.Map;


/**
 * 【请填写功能名称】Service接口
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
public interface IPushContactsService extends IService<PushContacts> {

    List<PushContacts> selectByPushContactsList(PushContactsVO pushContacts);

    int insertByPushContacts(PushContacts pushContacts);

    int updateByPushContacts(PushContacts pushContacts);

    boolean deleteByPushContactsByIds(List<String> ids);

    boolean changeContacts(PushContactsVO pushContacts);

    Map<Long, PushContacts> selectPushContactsMapByIds(List<Long> ids);

    Map<Long, PushContacts> selectAllPushContactsMapByIds(List<Long> ids);
}
