package com.boryou.web.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.ObjUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.domain.entity.SysUserLimit;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.mapper.SysUserMapper;
import com.boryou.system.service.SysUserLimitService;
import com.boryou.web.constant.ESConstant;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.util.ConvertHandler;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.PlanMain;
import com.boryou.web.domain.PlanType;
import com.boryou.web.domain.bo.PlanMainBO;
import com.boryou.web.domain.dto.PlanManageQueryDTO;
import com.boryou.web.domain.vo.*;
import com.boryou.web.domain.vo.plan.PlanEsSearchBO;
import com.boryou.web.enums.SortTypeEnum;
import com.boryou.web.mapper.PlanMapper;
import com.boryou.web.service.PlanMainService;
import com.boryou.web.service.PlanService;
import com.boryou.web.service.PlanTypeService;
import com.boryou.web.service.SearchService;
import com.boryou.web.util.PageUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 方案Service接口
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
@Slf4j
public class PlanServiceImpl extends ServiceImpl<PlanMapper, Plan> implements PlanService {
    private RedisCache redisTemplate;
    @Resource
    private PlanMainService planMainService;
    @Resource
    private PlanTypeService planTypeService;
    @Resource
    private PlanMapper planMapper;
    @Autowired
    private PlanService planService;
    @Resource
    private SysDeptMapper deptMapper;
    @Resource
    private SysUserMapper sysUserMapper;
    @Resource
    ConvertHandler convertHandler;
    @Resource
    private SearchService searchService;
    @Resource
    private SysUserLimitService userLimitService;
    @Resource(name = "threadPoolTaskExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 查询方案列表
     *
     * @param plan 方案
     * @return 方案
     */
    @Override
    public List<Plan> selectByPlanList(Plan plan) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        Long userIdGet = plan.getUserId();
        Plan planSql = new Plan();
        if (userIdGet == null) {
            planSql.setUserId(userId);
        } else {
            planSql.setUserId(userIdGet);
        }
        return baseMapper.selectPlanAndPlanMain(planSql);
    }

    @Override
    public Plan planOne(PlanVO planVO) {
        Long planId = planVO.getPlanId();
        if (planId == null) {
            throw new CustomException("planId不能为空");
        }
        return this.selectPlanById(planId);
    }

    @Override
    public Plan selectPlanById(Long planId) {
        if (planId == null) {
            return null;
        }
        return baseMapper.selectById(planId);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addPlan(PlanVO planVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        Long typeId = planVO.getTypeId();
        String planName = planVO.getPlanName();
        if (typeId == null) {
            throw new CustomException("文件夹不能为空");
        }
        // 判断方案数和关键词数
        judgePlanNumAndKeywordNum(userId, planVO, "add");
        // 判断方案名称
        judgePlanName(planName, typeId, userId);
        // 获取sort最大值, 若type为null, 增加type is null 条件
        LambdaQueryWrapper<Plan> qw = new LambdaQueryWrapper<>();
        qw.eq(Plan::getTypeId, typeId);
        qw.eq(Plan::getUserId, userId)
                .orderByDesc(Plan::getSort)
                .select(Plan::getSort);
        Long sort = this.getObj(qw, item -> {
            if (item == null) {
                return null;
            } else {
                String string = StrUtil.toString(item);
                return NumberUtil.parseLong(string, null);
            }
        });
        if (sort == null) {
            sort = 100L;
        } else {
            sort += 1;
        }
        planVO.setSort(sort);
        planVO.setUserId(userId);
        planVO.setSearchPosition(0);
        planVO.setDelFlag(0);
        planVO.setEnableFlag(1);
        planVO.setHistoryFlag(0);
        Plan plan = buildPlanByVO(planVO);

        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        plan.setCreateBy(userName);
        plan.setCreateTime(date);
        plan.setUpdateBy(userName);
        plan.setUpdateTime(date);

        long snowflakeNextId = IdUtil.getSnowflakeNextId();
        plan.setPlanId(snowflakeNextId);
        boolean save = this.save(plan);
        if ("1".equals(planVO.getMainType())) {
            // 如果是重点案件，就直接插入关联表
            PlanMain planMain = new PlanMain();
            planMain.setId(IdUtil.getSnowflakeNextId());
            planMain.setPlanId(plan.getPlanId());
            planMain.setMainType("1");
            planMain.setUserId(userId);
            boolean save1 = planMainService.save(planMain);
        }
        return save;
    }

    private void judgePlanNumAndKeywordNum(Long userId, PlanVO planVO, String changeType) {
        SysUserLimit sysUserLimit = userLimitService.getOne(
                new LambdaQueryWrapper<SysUserLimit>().eq(SysUserLimit::getUserId, userId)
                        .eq(SysUserLimit::getDelFlag, 0));
        if (sysUserLimit == null) {
            return;
        }
        if (CharSequenceUtil.equals(changeType, "add")) {
            judgePlanNum(sysUserLimit);
        }
        judgeKeywordNum(planVO, sysUserLimit);
    }

    private void judgeKeywordNum(PlanVO planVO, SysUserLimit sysUserLimit) {
        Long keywordNum = sysUserLimit.getKeywordNum();
        if (keywordNum == null) {
            return;
        }
        Integer searchMode = planVO.getSearchMode();
        if (searchMode == null) {
            return;
        }
        long total = 0L;
        if (searchMode == 0) {
            // 普通
            String kw1 = planVO.getKw1();
            String kw2 = planVO.getKw2();
            String kw3 = planVO.getKw3();
            String excludeWord = planVO.getExcludeWord();
            String keyword = kw1 + kw2 + kw3 + excludeWord;
            keyword = keyword.replace(" ", "");
            total = keyword.length();
        } else if (searchMode == 1) {
            // 高级
            String highMonitorWord = planVO.getHighMonitorWord();
            String highExcludeWord = planVO.getHighExcludeWord();
            String keyword = highMonitorWord + highExcludeWord;
            keyword = keyword.replace("+", "")
                    .replace("|", "")
                    .replace("(", "")
                    .replace(")", "")
                    .replace(" ", "");
            total = keyword.length();
        }
        if (total > keywordNum) {
            throw new CustomException("方案的关键字数最多为" + keywordNum + "个");
        }
    }

    private void judgePlanNum(SysUserLimit sysUserLimit) {
        Long planNum = sysUserLimit.getPlanNum();
        if (planNum == null) {
            return;
        }
        Long userId = sysUserLimit.getUserId();
        int count = this.count(new LambdaQueryWrapper<Plan>().eq(Plan::getUserId, userId)
                .eq(Plan::getDelFlag, 0)
                .eq(Plan::getEnableFlag, 1));
        // 只有新增需要校验方案数 总数加1
        long total = count + 1L;
        if (total > planNum) {
            throw new CustomException("方案最多可以创建" + planNum + "个");
        }
    }

    private Long getSort(Long typeId, Long userId) {
        // sort值为方案和文件夹同步的
        long sort;
        // 获取sort最大值, 若type为null, 需要获取所有方案和文件夹中的最大值
        LambdaQueryWrapper<Plan> qw = new LambdaQueryWrapper<>();
        if (typeId == null) {
            qw.isNull(Plan::getTypeId);
        } else {
            qw.eq(Plan::getTypeId, typeId);
        }
        Long planMaxSort = this.getObj(qw.eq(Plan::getUserId, userId)
                .orderByDesc(Plan::getSort)
                .select(Plan::getSort), item -> {
            if (item == null) {
                return null;
            } else {
                String string = StrUtil.toString(item);
                return NumberUtil.parseLong(string, null);
            }
        });
        Long planTypeMaxSort = planTypeService.getObj(new LambdaQueryWrapper<PlanType>()
                .eq(PlanType::getUserId, userId)
                .orderByDesc(PlanType::getSort)
                .select(PlanType::getSort), Long.class::cast);
        if (planMaxSort == null && planTypeMaxSort == null) {
            sort = 100L;
        } else if (planMaxSort == null) {
            sort = planTypeMaxSort;
        } else if (planTypeMaxSort == null) {
            sort = planMaxSort;
        } else {
            sort = Math.max(planMaxSort, planTypeMaxSort);
        }
        return sort;
    }

    private void judgePlanName(String planName, Long typeId, Long userId) {
        if (StrUtil.isBlankIfStr(planName)) {
            throw new CustomException("方案名称不能为空");
        }
        LambdaQueryWrapper<Plan> qw = new LambdaQueryWrapper<>();
        qw.eq(typeId != null, Plan::getTypeId, typeId)
                .eq(Plan::getPlanName, planName)
                .eq(Plan::getUserId, userId)
                .eq(Plan::getDelFlag, 0)
                .eq(Plan::getEnableFlag, 1);
        if (this.count(qw) > 0) {
            throw new CustomException("方案名称重复");
        }
    }

    private Plan buildPlanByVO(PlanVO planVO) {
        String planName = planVO.getPlanName();
        String kw1 = planVO.getKw1();
        String kw2 = planVO.getKw2();
        String kw3 = planVO.getKw3();
        String excludeWord = planVO.getExcludeWord();
        String area = planVO.getArea();
        Integer searchMode = planVO.getSearchMode();
        Integer searchPosition = planVO.getSearchPosition();
        Long typeId = planVO.getTypeId();
        Long userId = planVO.getUserId();
        Date effectStartTime = planVO.getEffectStartTime();
        Date effectEndTime = planVO.getEffectEndTime();
        Long sort = planVO.getSort();
        Integer delFlag = planVO.getDelFlag();
        Integer enableFlag = planVO.getEnableFlag();
        String highMonitorWord = planVO.getHighMonitorWord();
        String highExcludeWord = planVO.getHighExcludeWord();
        String highArea = planVO.getHighArea();
        Integer historyFlag = planVO.getHistoryFlag();

        Plan plan = new Plan();
        plan.setPlanName(planName);
        plan.setArea(area);
        plan.setSearchMode(searchMode);
        plan.setSearchPosition(searchPosition);
        plan.setTypeId(typeId);
        plan.setUserId(userId);
        plan.setEffectStartTime(effectStartTime);
        plan.setEffectEndTime(effectEndTime);
        plan.setSort(sort);
        plan.setDelFlag(delFlag);
        plan.setEnableFlag(enableFlag);
        plan.setHighMonitorWord(highMonitorWord);
        plan.setHighExcludeWord(highExcludeWord);
        plan.setHighArea(highArea);
        plan.setHistoryFlag(historyFlag);
        plan.setHotKw(planVO.getHotKw());
        if (StringUtils.isNotNull(kw1)) {
            plan.setKw1(kw1.replaceAll("\\s+", " "));
        }
        if (StringUtils.isNotNull(kw2)) {
            plan.setKw2(kw2.replaceAll("\\s+", " "));
        }
        if (StringUtils.isNotNull(kw3)) {
            plan.setKw3(kw3.replaceAll("\\s+", " "));
        }
        if (StringUtils.isNotNull(excludeWord)) {
            plan.setExcludeWord(excludeWord.replaceAll("\\s+", " "));
        }
        return plan;
    }

    @Override
    public boolean updatePlan(PlanVO planVO) {
        // 传进来值有不为空更新先更新方案
        Long planId = planVO.getPlanId();
        if (planId == null) {
            throw new CustomException("id不能为空");
        }
        Plan planOld = this.getById(planId);
        if (planOld == null) {
            throw new CustomException("方案不存在");
        }

        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        Long typeId = planVO.getTypeId();
        String planVOName = planVO.getPlanName();
        String planOldName = planOld.getPlanName();
        // 判断关键词数
        judgePlanNumAndKeywordNum(userId, planVO, "update");
        if (!StrUtil.isBlankIfStr(planVOName) && !CharSequenceUtil.equals(planVOName, planOldName)) {
            // 修改的方案名称不为空或者更原来的方案名称不同时需要判断方案名称
            judgePlanName(planVOName, typeId, userId);
        }
        if (null != planVO.getHistoryFlag() && planVO.getHistoryFlag() == 1) {
            // 加入历史方案,记录用户现在确认的搜索开始和结束时间
            addHistory(planVO);
        }
        Plan plan = buildPlanByVO(planVO);
        plan.setPlanId(planId);
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        plan.setUpdateBy(userName);
        plan.setUpdateTime(date);
        return this.updateById(plan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlanMain(PlanMainVO planMainVO) {
        Long planId = planMainVO.getPlanId();
        if (planId == null) {
            throw new CustomException("id不能为空");
        }
        Integer delFlag = planMainVO.getDelFlag();
        if (delFlag == null) {
            throw new CustomException("操作方式不能为空");
        }
        String mainType = planMainVO.getMainType();
        if (StrUtil.isBlankIfStr(mainType)) {
            throw new CustomException("操作类型不能为空");
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        LambdaQueryWrapper<PlanMain> qw = new LambdaQueryWrapper<>();
        qw.eq(PlanMain::getMainType, mainType)
                .eq(PlanMain::getUserId, userId)
                .eq(PlanMain::getPlanId, planId);
        if (delFlag == 0) {
            // 加入重点关注
            int count = planMainService.count(qw);
            if (count > 0) {
                // 已加入直接返回
                return true;
            }
            PlanMain planMain = new PlanMain();
            planMain.setId(IdUtil.getSnowflakeNextId());
            planMain.setPlanId(planId);
            planMain.setMainType(mainType);
            planMain.setUserId(userId);
            try {
                // 先保存重点案件
                boolean save = planMainService.save(planMain);
                // 再更新重点案件的文件夹为重点案件文件夹---所有用户公用这个文件夹
                LambdaUpdateWrapper<Plan> updateWrapper = new LambdaUpdateWrapper<>();
                updateWrapper.eq(Plan::getPlanId, planId);
                updateWrapper.set(Plan::getTypeId, "-2");
                planService.update(updateWrapper);
                return save;
            } catch (Exception e) {
                TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
                throw new CustomException("已经加入过了");
            }
        }
        if (delFlag == 1) {
            // 取消加入直接删除
            // PlanMain one = planMainService.getOne(qw);
            List<PlanMain> planMains = planMainService.list(qw);
            if (CollUtil.isEmpty(planMains)) {
                // 已删除直接返回
                return true;
            }
            List<Long> ids = planMains.stream().map(PlanMain::getId).collect(Collectors.toList());
            // Long id = one.getId();
            // return planMainService.removeById(id);
            return planMainService.removeByIds(ids);
        }
        return true;
    }

    @Override
    public List<PlanTreeVO> planTree(PlanVO planVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        Integer historyFlag = planVO.getHistoryFlag();
        String planNameGet = planVO.getPlanName();
        String mainType = planVO.getMainType();
//
        // 查询用户所有方案
        LambdaQueryWrapper<Plan> planLambdaQueryWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<Plan> eq = planLambdaQueryWrapper.eq(Plan::getUserId, userId)
                .eq(planVO.getHistoryFlag() != null, Plan::getHistoryFlag, historyFlag)
                .like(!StrUtil.isBlankIfStr(planNameGet), Plan::getPlanName, planNameGet)
                .eq(Plan::getDelFlag, 0)
                .eq(Plan::getEnableFlag, 1);
        // 查询当前用户所有重点案件，用来做排除重点和非重点案件用的
        List<PlanMain> list = planMainService.list(Wrappers.<PlanMain>lambdaQuery().eq(PlanMain::getMainType, "1").eq(PlanMain::getUserId, userId));
        List<Long> mainList = list.stream().map(PlanMain::getPlanId).collect(Collectors.toList());
        mainList.add(-999L);// 加入一个默认重点，用于无重点关注时查询到所有方案的bug
        LambdaQueryWrapper<Plan> newFilter;
        if (StrUtil.isEmpty(mainType)) {
            // 如果是舆情监测，只查非重点的
            newFilter = eq.notIn(Plan::getPlanId, mainList);
        } else if ("1".equals(mainType)) {
            // 重点关注，只查重点关注的
            newFilter = eq.in(Plan::getPlanId, mainList);
        } else {
            throw new CustomException("参数异常");
        }
        List<Plan> planList = this.list(newFilter.orderByDesc(Plan::getSort));
        // 查询用户所有文件夹（包括公用的：重点方案-2，其他方案-1）
        List<PlanType> planTypeList = planTypeService.list(new LambdaQueryWrapper<PlanType>()
                .eq(PlanType::getUserId, userId).or().in(PlanType::getTypeId, -1, -2)
                .orderByDesc(PlanType::getSort));
        if (CollUtil.isEmpty(planList) && CollUtil.isEmpty(planTypeList)) {
            return Collections.emptyList();
        }

        // 查询所有重点
        Map<Long, List<PlanMain>> planMainMap = new HashMap<>();
        if (CollUtil.isNotEmpty(planList)) {
            List<Long> planIdList = planList.stream().map(Plan::getPlanId).collect(Collectors.toList());
            List<PlanMain> planMainList = planMainService.list(new LambdaQueryWrapper<PlanMain>()
                    .in(PlanMain::getPlanId, planIdList));

            if (CollUtil.isNotEmpty(planMainList)) {
                planMainMap = planMainList.stream().collect(Collectors.groupingBy(PlanMain::getPlanId));
            }
        }

        List<PlanTreeVO> planTreeList = new ArrayList<>();
        Map<Long, List<Plan>> planTypeMap = planList.stream()
                .collect(Collectors.groupingBy(Plan::getTypeId));

        if (CollUtil.isEmpty(planTypeList)) {
            return planTreeList;
        }
        for (PlanType planType : planTypeList) {
            Long typeId = planType.getTypeId();
            String typeName = planType.getTypeName();
            PlanTreeVO planTreeVO = new PlanTreeVO();
            planTreeVO.setId(typeId);
            planTreeVO.setName(typeName);
            if (!planTypeMap.containsKey(typeId)) {
                // planTreeList.add(planTreeVO);
                continue;
            }
            List<Plan> planListType = planTypeMap.get(typeId);
            List<PlanTreeVO> planTreeChildList = new ArrayList<>();
            for (Plan plan : planListType) {
                Long planId = plan.getPlanId();
                String planName = plan.getPlanName();
                PlanTreeVO planTreeChild = new PlanTreeVO();
                planTreeChild.setId(planId);
                planTreeChild.setName(planName);
                planTreeChild.setExpire(plan.getExpire());
                planTreeChild.setHistoryFlag(plan.getHistoryFlag());
                // 历史方案中不显示追踪图标
                if (planTreeChild.getHistoryFlag() == 0) {
                    planTreeChild.setTracking(plan.getTracking() == null ? 0 : plan.getTracking());
                } else {
                    planTreeChild.setTracking(0);
                }
                if (planMainMap.containsKey(planId)) {
                    List<PlanMain> planMains = planMainMap.get(planId);
                    planTreeChild.setPlanMain(planMains);
                }
                planTreeChildList.add(planTreeChild);
            }
            planTreeVO.setChildren(planTreeChildList);
            planTreeList.add(planTreeVO);
        }
        return planTreeList;
    }

    @Override
    public boolean sortPlan(SortVO sortVO) {
        String sortType = sortVO.getSortType();
        Long planId = sortVO.getPlanId();
        Long typeId = sortVO.getTypeId();
        Plan plan = this.getById(planId);
        Long sort = plan.getSort();
        LambdaQueryWrapper<Plan> qw = new LambdaQueryWrapper<>();
        qw.eq(Plan::getTypeId, typeId)
                .eq(Plan::getDelFlag, 0)
                .eq(Plan::getEnableFlag, 1);

        switch (SortTypeEnum.getByType(sortType)) {
            // 上移
            case UP:
                qw.gt(Plan::getSort, sort);
                qw.orderByAsc(Plan::getSort);
                qw.last("limit 1");
                Plan upPlan = this.getOne(qw);
                if (upPlan == null) {
                    throw new CustomException("已经在最顶部啦");
                }
                baseMapper.changeSortById(planId, upPlan.getPlanId());
                break;
            // 下移
            case DOWN:
                qw.lt(Plan::getSort, sort);
                qw.orderByDesc(Plan::getSort);
                qw.last("limit 1");
                Plan downPlan = this.getOne(qw);
                if (downPlan == null) {
                    throw new CustomException("已经在最底部啦");
                }
                baseMapper.changeSortById(planId, downPlan.getPlanId());
                break;
            // 置顶
            case TOP:
                qw.orderByDesc(Plan::getSort);
                qw.last("limit 1");
                Plan topPlan = this.getOne(qw);
                if (topPlan.getPlanId().equals(plan.getPlanId())) {
                    throw new CustomException("已经在最顶部啦");
                }
                Long topSort = topPlan.getSort();
                plan.setSort(topSort + 1);
                baseMapper.updateById(plan);
                break;
            // 置底
            case BOTTOM:
                qw.orderByAsc(Plan::getSort);
                qw.last("limit 1");
                Plan bottomPlan = this.getOne(qw);
                if (bottomPlan.getPlanId().equals(plan.getPlanId())) {
                    throw new CustomException("已经在最底部啦");
                }
                Long bottomSort = bottomPlan.getSort();
                plan.setSort(bottomSort - 1);
                baseMapper.updateById(plan);
                break;
            default:
                throw new CustomException("未知排序操作");
        }
        return true;
    }

    /**
     * 存储方案最终的开始结束时间。
     * 需要这一步的原因是因为加入历史方案需要记录用户当时确认的开始结束时间
     *
     * @param plan
     * @return void
     * <AUTHOR>
     * @date 2024/6/21 14:26
     **/
    public void addHistory(Plan plan) {
        if (null == plan.getPlanId()) {
            throw new CustomException("方案id为空，不允许该操作");
        }
        if (StringUtils.isEmpty(plan.getPlanName())) {
            throw new CustomException("方案名称不能为空");
        }
        int count = this.count(Wrappers.<Plan>lambdaQuery()
                .eq(Plan::getPlanName, plan.getPlanName())
                .eq(Plan::getUserId, SecurityUtils.getUserId())
                .eq(Plan::getDelFlag, 0)
                .eq(Plan::getHistoryFlag, 1)
        );
        if (count > 0) {
            throw new CustomException("历史方案中存在重复名称，请修改方案名称");
        }

        String startTime = "";
        String endTime = "";
        Integer timeIndex = null;
        JSONObject searchCriteria = searchService.getSearchCriteria(plan.getPlanId());
        if (searchCriteria != null) {
            if (StrUtil.isNotEmpty(searchCriteria.getStr("startTime")) && StrUtil.isNotEmpty(searchCriteria.getStr("endTime"))) {
                startTime = searchCriteria.getStr("startTime");
                endTime = searchCriteria.getStr("endTime");
                plan.setEffectStartTime(DateUtil.parseDateTime(startTime));
                plan.setEffectEndTime(DateUtil.parseDateTime(endTime));
                this.updateById(plan);
                return;
            } else {
                timeIndex = searchCriteria.getInt("timeIndex");
            }
        }
        if (timeIndex == null) {
            timeIndex = 0;
        }
        SearchVO searchVO = new SearchVO();
        searchVO.setTimeIndex(timeIndex);
        EsSearchBO bo = new EsSearchBO();
        searchVO.getSearchTimeRange(bo);
        startTime = bo.getStartTime();
        endTime = bo.getEndTime();
        plan.setEffectStartTime(DateUtil.parseDateTime(startTime));
        plan.setEffectEndTime(DateUtil.parseDateTime(endTime));
        this.updateById(plan);
    }

    @Override
    public void scanPlan() {
        List<Plan> planList = this.list(new LambdaQueryWrapper<Plan>()
                .eq(Plan::getHistoryFlag, "1")
                .eq(Plan::getDelFlag, 0)
                .eq(Plan::getEnableFlag, 1).eq(Plan::getExpire, "0")
        );
        // 1判断时间是否成为历史，如果是，并且要走es扫描数据加节点，然后更新redids标记状态为已过期
        for (Plan plan : planList) {
            Date effectStartTime = plan.getEffectStartTime();
            Date effectEndTime = plan.getEffectEndTime();
            if (effectStartTime == null || effectEndTime == null) {
                continue;
            }
            Date now = DateUtil.date();
            int compareResult = effectEndTime.compareTo(now);
            if (compareResult < 0) {
                Long planId = plan.getPlanId();
                long start = System.currentTimeMillis();
                log.info("方案:{},名称{},开始扫描", plan.getPlanId(), plan.getPlanName());
                // 结束时间小于当前时间，说明已过期,,开始加入es新节点
                SearchVO searchVO = new SearchVO();
                EsSearchBO esSearchBO1 = convertHandler.copyPropertiesFromPlan(searchVO, plan);
                esSearchBO1.setStartTime(DateUtil.formatDateTime(effectStartTime));
                esSearchBO1.setEndTime(DateUtil.formatDateTime(effectEndTime));
                PlanEsSearchBO esSearchBO = new PlanEsSearchBO();
                esSearchBO.setPlanId(plan.getPlanId());
                esSearchBO1.setIndexs(Collections.singletonList(ESConstant.NETXMAN_YQ_HISPLAN));
                esSearchBO.setEsSearchBO(esSearchBO1);
                // 因为es插入时间可能过长，为了防止服务重启导致之前的任务没有完成，下次重复插入数据
                updatePlanExpire(planId, -1);
                threadPoolTaskExecutor.submit(() -> {
                    AjaxResult ajaxResult = saveHistoryPlan(esSearchBO);
                    if (null != ajaxResult && ajaxResult.getCode() == 200) {
                        // es节点处理完成，更新es历史节点是否可查
//                    redisTemplate.setCacheObject(RedisConstant.plan_history + plan.getPlanId() , flag);
                        updatePlanExpire(plan.getPlanId(), 1);
                        log.info("方案:{},名称{},存储es新节点完成，耗时:{}秒", plan.getPlanId(), plan.getPlanName(), (System.currentTimeMillis() - start) / 1000);
                    } else {
                        updatePlanExpire(plan.getPlanId(), 0);
                        log.info("方案节点存储结果:{}", ajaxResult.toString());
                    }
                });
            }
        }
//        log.info("方案扫描结束");
    }

    private void updatePlanExpire(Long planId, int expire) {
        Plan entity = new Plan();
        entity.setPlanId(planId);
        entity.setExpire(expire);
        this.updateById(entity);
    }

    public AjaxResult saveHistoryPlan(PlanEsSearchBO esSearchBO) {
        HttpRequest postRequest = EsSearchUtil.createPostRequest(EsSearchUtil.PLAN_SAVE_HISTORY);
        postRequest.body(JSONUtil.toJsonStr(esSearchBO));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        return JSONUtil.toBean(body, AjaxResult.class);
    }

    @Override
    public boolean movePlan(PlanVO planVO) {
        // 传进来值有不为空更新先更新方案
        Long planId = planVO.getPlanId();
        if (planId == null) {
            throw new CustomException("id不能为空");
        }
        Plan planOld = this.getById(planId);
        if (planOld == null) {
            throw new CustomException("方案不存在");
        }
        Plan plan = new Plan();
        plan.setPlanId(planId);
        plan.setTypeId(planVO.getTypeId());
        return this.updateById(plan);
    }

    @Override
    public List<Plan> manageList(PlanManageQueryDTO query) {

        // deptId为空时，也就是默认情况，查询本级和下级的组织id
        if (StringUtils.isEmpty(query.getDeptId())) {
            Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
            // 是否只查询自己创建的方案
            if (!query.getIsOwner()) {
                List<Long> deptIds = deptMapper.selectManageDept(deptId);
                query.setDeptIds(deptIds);
            } else {
                query.setUserId(SecurityUtils.getUserId());
            }
        }

        PageUtil.startPage(query.getPageNum(), query.getPageSize());
        // PageHelper.startPage(query.getPageNum(), query.getPageSize());
        List<Plan> list = planMapper.manageList(query);

        // 设置方案是否可删除
        list.forEach(x -> x.setOptionFlag(x.getUserId().equals(Long.parseLong(SecurityUtils.getUserId()))));
        return list;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean copyPlan(Long planId) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        Plan plan = this.getById(planId);

        if (plan.getUserId().equals(userId)) {
            throw new CustomException("不能复制自己的方案");
        } else {
            // 复制方案类型
            PlanType planType = planTypeService.getById(plan.getTypeId());
            int count = planTypeService.count(Wrappers.<PlanType>lambdaQuery()
                    .eq(PlanType::getTypeName, planType.getTypeName())
                    .eq(PlanType::getUserId, userId)
            );
            if (count == 0) {
                planType.setTypeId(null);
                planType.setUserId(userId);
                planType.setCreateBy(SecurityUtils.getUsername());
                planType.setUpdateBy(SecurityUtils.getUsername());
                planType.setCreateTime(new Date());
                planType.setUpdateTime(new Date());
                PlanType one = planTypeService.getOne(Wrappers.<PlanType>lambdaQuery()
                        .eq(PlanType::getUserId, userId)
                        .orderByDesc(PlanType::getCreateTime)
                        .last("limit 1")
                );
                planType.setSort(one.getSort() + 1);
                planTypeService.save(planType);
            }

            // 复制方案
            int count1 = this.count(Wrappers.<Plan>lambdaQuery()
                    .eq(Plan::getPlanName, plan.getPlanName())
                    .eq(Plan::getUserId, userId)
                    .eq(Plan::getDelFlag, 0)
            );
            if (count1 > 0) {
                throw new CustomException("当前方案名称重复，请勿重复引用");
            }
            plan.setPlanId(null);
            plan.setTypeId(planType.getTypeId());
            plan.setUserId(userId);
            plan.setCreateBy(SecurityUtils.getUsername());
            plan.setUpdateBy(SecurityUtils.getUsername());
            plan.setCreateTime(new Date());
            plan.setUpdateTime(new Date());
            plan.setDelFlag(0);
            Plan latest = this.getOne(Wrappers.<Plan>lambdaQuery()
                    .eq(Plan::getUserId, userId)
                    .orderByDesc(Plan::getCreateTime)
                    .last("limit 1")
            );
            plan.setSort(latest.getSort() + 1);
            return this.save(plan);

        }
    }

    @Override
    public List<Plan> selectByMainTypeList(PlanMainBO plan) {
        // Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        // if (deptId == null) {
        //    return Collections.emptyList();
        //}
        // List<SysUser> sysUsers = sysUserMapper.selectUserListByDeptIdNew(deptId);
        List<Long> userIds = new ArrayList<>();
        // if (CollUtil.isNotEmpty(sysUsers)) {
        //    userIds = sysUsers.stream().map(item -> item.getUserId()).collect(Collectors.toList());
        //}
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        userIds.add(userId);
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        plan.setUserIds(userIds);
        return planMapper.selectByMainTypeList(plan);
    }

    @Override
    public List<Plan> selectByMainTypeListV2(PlanMainBO plan) {
        // Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        // if (deptId == null) {
        //    return Collections.emptyList();
        //}
        // List<SysUser> sysUsers = sysUserMapper.selectUserListByDeptIdNew(deptId);
        // List<Long> userIds = new ArrayList<>();
        // if (CollUtil.isNotEmpty(sysUsers)) {
        //    userIds = sysUsers.stream().map(item -> item.getUserId()).collect(Collectors.toList());
        //}
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        // userIds.add(userId);
        // if (CollUtil.isEmpty(userIds)) {
        //    return Collections.emptyList();
        //}
        // plan.setUserIds(userIds);
        // planMapper.selectByMainTypeList(plan);


        Integer historyFlag = 0;
        String mainType = "1";
        LambdaQueryWrapper<Plan> planLambdaQueryWrapper = new LambdaQueryWrapper<>();
        LambdaQueryWrapper<Plan> eq = planLambdaQueryWrapper.eq(Plan::getUserId, userId)
                .eq(Plan::getHistoryFlag, historyFlag)
                .eq(Plan::getDelFlag, 0)
                .eq(Plan::getEnableFlag, 1);
        List<PlanMain> list = planMainService.list(Wrappers.<PlanMain>lambdaQuery().eq(PlanMain::getMainType, "1").eq(PlanMain::getUserId, userId));
        List<Long> mainList = list.stream().map(PlanMain::getPlanId).collect(Collectors.toList());
        mainList.add(-999L);// 加入一个默认重点，用于无重点关注时查询到所有方案的bug
        LambdaQueryWrapper<Plan> newFilter;
        if (StrUtil.isEmpty(mainType)) {
            // 如果是舆情监测，只查非重点的
            newFilter = eq.notIn(!mainList.isEmpty(), Plan::getPlanId, mainList);
        } else if ("1".equals(mainType)) {
            // 重点关注，只查重点关注的
            newFilter = eq.in(!mainList.isEmpty(), Plan::getPlanId, mainList);
        } else {
            throw new CustomException("参数异常");
        }
        List<Plan> list1 = this.list(newFilter.orderByDesc(Plan::getSort));
        return list1;
    }

    @Override
    public int selectByMainTypeListCount(PlanMainBO plan) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        if (deptId == null) {
            return 0;
        }
        List<SysUser> sysUsers = sysUserMapper.selectUserListByDeptIdNew(deptId);
        List<Long> userIds = new ArrayList<>();
        if (CollUtil.isNotEmpty(sysUsers)) {
            userIds = sysUsers.stream().map(item -> item.getUserId()).collect(Collectors.toList());
        }
        if (CollUtil.isEmpty(userIds)) {
            return 0;
        }
        plan.setUserIds(userIds);
        return planMapper.selectByMainTypeListCount(plan);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean updatePlanInfo(PlanVO plan) {
        String position = plan.getPosition();// 目标方案的位置  1是上面  0是下面
        Plan plan2 = this.selectPlanById(Long.parseLong(plan.getOriginPlanId()));  // 当前方案id
        Plan plan1 = this.selectPlanById(plan.getPlanId());// 目标方案id位置
        if (ObjUtil.isAllEmpty(plan1, plan2)) {
            throw new CustomException("方案不存在");
        }
        // 重排序-新写法
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        Integer historyFlag = plan.getHistoryFlag();
        String planNameGet = plan.getPlanName();
        // 查询用户所有方案
        List<Plan> planList = this.list(new LambdaQueryWrapper<Plan>().eq(Plan::getUserId, userId)
                .eq(plan.getHistoryFlag() != null, Plan::getHistoryFlag, historyFlag)
                .like(!StrUtil.isBlankIfStr(planNameGet), Plan::getPlanName, planNameGet)
                .eq(Plan::getDelFlag, 0).eq(Plan::getTypeId, plan.getTypeId())
                .eq(Plan::getEnableFlag, 1)
                .orderByDesc(Plan::getSort));
        List<Plan> newPlan = new ArrayList<>();
        Plan planIdd = new Plan();// 记录上面一个方案的id
        for (Plan plan3 : planList) {
            planIdd = plan3;
            if (plan3.getPlanId().equals(plan1.getPlanId())) {
                if ("1".equals(position)) {
                    // 如果是上面
                    break;
                }
            }
        }
        boolean flag = false;
        Long planINext = 0L;// 记录下面一个方案的id
        for (Plan plan3 : planList) {
            if (plan3.getPlanId().equals(plan1.getPlanId())) {
                if ("0".equals(position)) {
                    // 如果是下面
                    flag = true;
                    planINext = plan3.getPlanId();
                    continue;
                }
            }
            if (flag) {
                break;
            }
        }
        // 移除调当前方案
        Plan currentPlan = new Plan();
        List<Plan> newPlans = new ArrayList<>();
        for (Plan plan3 : planList) {
            if (plan3.getPlanId().equals(plan2.getPlanId())) {
                currentPlan = plan3;
                continue;
            }
            newPlans.add(plan3);
        }
        // 1.移除当前方案
        // 判断是上面还是下面   如果是添加上面
        for (Plan plan3 : newPlans) {
            if ("0".equals(position)) {
                newPlan.add(plan3);
                if (plan3.getPlanId().equals(planINext)) {
                    newPlan.add(currentPlan);// 如果当前方案是
                }
            } else {
                // 如果是上面
                if (plan3.getPlanId().equals(planIdd.getPlanId())) {
                    newPlan.add(currentPlan);
                }
                newPlan.add(plan3);
            }
        }
        int j = 0;
        for (int i = newPlan.size(); i > 0; i--) {
            Plan plan3 = newPlan.get(j);
            Plan entity = new Plan();
            entity.setSort(Long.parseLong(i + ""));
            entity.setPlanId(plan3.getPlanId());
            planMapper.updateById(entity);
//            System.out.println("---" + plan3.getPlanName());
            j++;
        }
        // 先更新当前方案的sort为目标方案的位置
//        Plan entity = new Plan();
//        entity.setPlanId(Long.parseLong(plan.getOriginPlanId()));
//        entity.setSort(plan1.getSort());
//        this.updateById(entity);
//
//        Plan entity1 = new Plan();
//        entity1.setPlanId(plan.getPlanId());
//        entity1.setSort(plan2.getSort());
//        this.updateById(entity1);
        return true;
    }


    @Override
    public List<Plan> selectAllPlan() {
        // Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        // if (deptId == null) {
        //    return Collections.emptyList();
        //}
        // List<Long> userIds = sysUserMapper.selectAllUserIdByDeptId(deptId);
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        List<Long> userIds = new ArrayList<>();
        userIds.add(userId);
        if (CollUtil.isEmpty(userIds)) {
            return Collections.emptyList();
        }
        return this.list(new LambdaQueryWrapper<Plan>().in(Plan::getUserId, userIds)
                .eq(Plan::getDelFlag, 0)
                .eq(Plan::getEnableFlag, 1));
    }

    @Override
    public String planTracking(String id) {
        Plan one = this.getById(id);
        int count = this.count(Wrappers.<Plan>lambdaQuery()
                .eq(Plan::getTrackingId, id)
                .eq(Plan::getUserId, SecurityUtils.getUserId())
                .eq(Plan::getDelFlag, 0)
        );

        if (count > 0) {
            throw new CustomException("该事件已被追踪");
        }
        if (one != null && one.getHistoryFlag() == 1) {
            one.setPlanId(null);
            one.setPlanName(one.getPlanName());
            one.setHistoryFlag(0);
            one.setExpire(0);
            one.setCreateTime(new Date());
            one.setUpdateTime(new Date());
            one.setTracking(1);
            one.setTrackingId(Long.valueOf(id));
            one.setCreateBy(SecurityUtils.getUsername());
            one.setUpdateBy(SecurityUtils.getUsername());
            boolean save = this.save(one);
            if (save) {
                return one.getPlanId().toString();
            } else {
                throw new CustomException("操作失败");
            }
        } else {
            throw new CustomException("当前方案不存在或不是历史方案");
        }
    }

    @Override
    public List<Long> allOpenPlanId() {
        return this.listObjs(new LambdaQueryWrapper<Plan>().select(Plan::getPlanId)
                .eq(Plan::getDelFlag, 0)
                .eq(Plan::getEnableFlag, 1)
                .eq(Plan::getExpire, 0)
                .eq(Plan::getHistoryFlag, 0), id -> {
            // 进行一系列转换操作 ，最终返回我们所需要的类型V ， 这里只做简单演示。
            return Long.valueOf(id.toString());
        });

    }

}
