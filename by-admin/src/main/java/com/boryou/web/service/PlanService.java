package com.boryou.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.bo.PlanMainBO;
import com.boryou.web.domain.dto.PlanManageQueryDTO;
import com.boryou.web.domain.vo.PlanMainVO;
import com.boryou.web.domain.vo.PlanTreeVO;
import com.boryou.web.domain.vo.PlanVO;
import com.boryou.web.domain.vo.SortVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24 15:02
 */
public interface PlanService extends IService<Plan> {

    List<Plan> selectByPlanList(Plan plan);

    Plan planOne(PlanVO planVO);

    List<PlanTreeVO> planTree(PlanVO planVO);

    boolean addPlan(PlanVO plan);

    boolean updatePlan(PlanVO plan);

    boolean movePlan(PlanVO plan);

    boolean updatePlanMain(PlanMainVO planMainVO);

    Plan selectPlanById(Long plateId);

    boolean sortPlan(SortVO sortVO);

    void scanPlan();

    /**
     * 方案管理-方案列表
     *
     * @return
     */
    List<Plan> manageList(PlanManageQueryDTO query);

    /**
     * 复制方案
     *
     * @param planId
     * @return
     */
    boolean copyPlan(Long planId);

    List<Plan> selectByMainTypeList(PlanMainBO plan);

    List<Plan> selectByMainTypeListV2(PlanMainBO plan);

    int selectByMainTypeListCount(PlanMainBO plan1);

    boolean updatePlanInfo(PlanVO plan);

    List<Plan> selectAllPlan();

    /**
     * 历史方案-事件追踪
     *
     * @param id
     * @return
     */
    String planTracking(String id);

    List<Long> allOpenPlanId();
}
