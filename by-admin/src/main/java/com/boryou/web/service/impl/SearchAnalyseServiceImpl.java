package com.boryou.web.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.PageResult;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.domain.entity.SysDictData;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.exception.CustomException;
import com.boryou.framework.web.service.TokenService;
import com.boryou.system.service.ISysDictDataService;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.Word;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.entity.bo.EsSearchTimeBO;
import com.boryou.web.controller.common.entity.vo.TimeFlowVO;
import com.boryou.web.controller.common.enums.EmotionEnum;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.controller.common.enums.SortTypeEnum;
import com.boryou.web.controller.common.util.ConvertHandler;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.Hot;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.*;
import com.boryou.web.mapper.HotListMapper;
import com.boryou.web.module.area.entity.Area;
import com.boryou.web.module.area.service.AreaService;
import com.boryou.web.module.home.entity.vo.MediaLevelVO;
import com.boryou.web.module.report.service.ReportService;
import com.boryou.web.service.*;
import com.boryou.web.util.AssertX;
import com.boryou.web.util.TextUtil;
import com.boryou.web.util.TimeUtil;
import com.boryou.web.util.poi.POIWordUtil;
import com.boryou.web.util.poi.custom.CustomXWPFParagraph;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Service;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.*;
import java.math.BigInteger;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.text.DecimalFormat;
import java.time.LocalDateTime;
import java.time.Period;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

import static com.boryou.web.controller.common.enums.SortTypeEnum.getEnumValue;
import static com.boryou.web.module.report.service.impl.ReportServiceImpl.addFirstGradeTitle;

/**
 * <AUTHOR>
 */
@Service
@Data
@Slf4j
public class SearchAnalyseServiceImpl implements SearchAnalyseService {

    private final ElasticsearchService elasticsearchService;
    private final AreaService areaService;
    private final HotService hotService;

    private final ISysDictDataService dictDataService;

    private final SimilarityService similarityService;
    private final HotListMapper hotListMapper;
    private final SearchService searchService;
    private final PlanService planService;
    private final ReportService reportService;

    private final ConvertHandler convertHandler;
    private final RedisCache redisCache;

    private final TokenService tokenService;


    @Override
    public JSONObject getEmotionAnalyse(EsSearchBO bo) {
        Map<String, Integer> map = EsSearchUtil.emotionAnalysisForOriginal(bo);
        JSONArray array = new JSONArray();
        JSONObject object;
        JSONObject object1 = JSONUtil.createObj();
        if (map.get(EmotionEnum.NEUTRAL.getValue().toString()) != null) {
            object = JSONUtil.createObj();
            object.putOnce("name", EmotionEnum.NEUTRAL.getName());
            object.putOnce("value", map.get(EmotionEnum.NEUTRAL.getValue().toString()));
            object1.putOnce("neutral", map.get(EmotionEnum.NEUTRAL.getValue().toString()));
            array.set(object);
        }
        if (map.get(EmotionEnum.NEGATIVE.getValue().toString()) != null) {
            object = JSONUtil.createObj();
            object.putOnce("name", EmotionEnum.NEGATIVE.getName());
            object.putOnce("value", map.get(EmotionEnum.NEGATIVE.getValue().toString()));
            object1.putOnce("negative", map.get(EmotionEnum.NEGATIVE.getValue().toString()));
            array.set(object);
        }
        if (map.get(EmotionEnum.POSITIVE.getValue().toString()) != null) {
            object = JSONUtil.createObj();
            object.putOnce("name", EmotionEnum.POSITIVE.getName());
            object.putOnce("value", map.get(EmotionEnum.POSITIVE.getValue().toString()));
            object1.putOnce("positive", map.get(EmotionEnum.POSITIVE.getValue().toString()));
            array.set(object);
        }
        JSONObject desc = JSONUtil.createObj();
        desc.putOnce("startTime", bo.getStartTime());
        desc.putOnce("endTime", bo.getEndTime());
        if (map.get("all") != null) {
            object1.putOnce("total", map.get("all"));
            desc.putOnce("total", map.get("all"));
            DecimalFormat df = new DecimalFormat("0.00");
            if (map.get("all") > 0) {
                desc.putOnce("neutral", df.format((double) map.get(EmotionEnum.NEUTRAL.getValue().toString()) * 100 / map.get("all")) + "%");
                desc.putOnce("negative", df.format((double) map.get(EmotionEnum.NEGATIVE.getValue().toString()) * 100 / map.get("all")) + "%");
                desc.putOnce("positive", df.format((double) map.get(EmotionEnum.POSITIVE.getValue().toString()) * 100 / map.get("all")) + "%");
            } else {
                desc.putOnce("neutral", "0%");
                desc.putOnce("negative", "0%");
                desc.putOnce("positive", "0%");
            }
        }
        JSONObject object2 = JSONUtil.createObj();
        object2.putOnce("data", array);
        object2.putOnce("prarms", object1);
        object2.putOnce("desc", desc);
        return object2;
    }

    @Override
    public JSONArray mediaTypeAnalyse(EsSearchBO bo) {
        Map<String, Integer> map = EsSearchUtil.mediaTypeMapForOriginal(bo);
        JSONArray array = new JSONArray();
        JSONObject object;
        DecimalFormat df = new DecimalFormat("0.00");
        List<Map.Entry<String, Integer>> sortedEntries = map.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .collect(Collectors.toList());
        SysDictData dictData = new SysDictData();
        dictData.setDictType("sys_media_type");
        dictData.setStatus("0");
        List<String> dictList = dictDataService.selectDictDataList(dictData).stream().map(SysDictData::getDictValue).collect(Collectors.toList());
        //打补丁  把字典type全部补齐
        Map<String, Boolean> keyExistsMap = new HashMap<>();
        // 标记不完整列表中已存在的 key
        for (Map.Entry<String, Integer> entry : sortedEntries) {
            keyExistsMap.put(entry.getKey(), true);
        }
        List<Map.Entry<String, Integer>> incompleteEntries = new ArrayList<>(sortedEntries);
        // 添加不存在的 key
        for (String key : dictList) {
            if (!keyExistsMap.containsKey(key)) {
                incompleteEntries.add(new HashMap.SimpleEntry<>(key, 0));
            }
        }
        for (Map.Entry<String, Integer> entry : incompleteEntries) {
            if (!entry.getKey().equals("all")) {
                object = JSONUtil.createObj();
                object.putOnce("name", MediaTypeEnum.getDesc(entry.getKey()));
                object.putOnce("value", entry.getValue());
                object.putOnce("percent", df.format((double) entry.getValue() * 100 / map.get("all")) + "%");
                object.putOnce("type", entry.getKey());
                array.set(object);
            }
        }
        return array;
    }

    @Override
    public JSONArray wordAnalyse(EsSearchBO bo, Long planId) {
        PageResult<EsBean> esBeanList = EsSearchUtil.searchEsBeanList(bo);
        Map<String, Integer> wordsFren = new HashMap<>();
        List<Word> res = new ArrayList<>();
        List<String> filterCloudWord = hotService.filterCloudWordList();
        Lexeme lexeme;
        IKSegmenter ikSegmenter;
        for (EsBean indexResultBean : esBeanList) {
            if (StringUtils.isNotEmpty(indexResultBean.getText())) {
                indexResultBean.setText(HtmlUtil.cleanHtmlTag(indexResultBean.getText()));
                ikSegmenter = new IKSegmenter(new StringReader(indexResultBean.getText()), true);
                try {
                    while ((lexeme = ikSegmenter.next()) != null) {
                        String lexemeText = lexeme.getLexemeText();
                        if (lexemeText.length() > 1) {
                            if (filterCloudWord.contains(lexemeText) /*|| !BC.hot_words.contains(lexemeText)*/|| TextUtil.isNumeric(lexemeText) || TextUtil.isAlphabetic(lexemeText)|| TextUtil.isSpecialCharacters(lexemeText)) {
                                continue;
                            }
                            if (wordsFren.containsKey(lexemeText)) {
                                wordsFren.put(lexemeText, wordsFren.get(lexemeText) + 1);
                            } else {
                                wordsFren.put(lexemeText, 1);
                            }
                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        wordsFren.keySet().forEach(x -> {
            res.add(new Word(x, wordsFren.get(x)));
        });
        List<Word> collect = res.stream().sorted((o1, o2) -> Math.toIntExact(o2.getNum() - o1.getNum())).collect(Collectors.toList());
        if (collect.size() > 51) {
            collect = collect.subList(0, 50);
        }
        List<String> filterWords = new ArrayList<>();
        if (planId != null) {
            CloudWordVO vo = new CloudWordVO();
            vo.setState("1");
            vo.setPlanId(planId);
            List<CloudWordVO> vos = hotService.cloudWordList(vo);
            if (CollUtil.isNotEmpty(vos)) {
                filterWords = vos.stream().map(CloudWordVO::getWord).collect(Collectors.toList());
            }
        }
        JSONArray array = new JSONArray();
        JSONObject object;
        for (Word word : collect) {
            if (filterWords.contains(word.getWord())) {
                continue;
            }
            object = JSONUtil.createObj();
            object.putOnce("name", word.getWord());
            object.putOnce("value", word.getNum());
            array.add(object);
        }
        return array;
    }

    @Override
    public JSONArray mediaActiveMap(EsSearchBO bo) {
        Map<String, Integer> map = EsSearchUtil.mediaActiveMap(bo);
        JSONArray array = new JSONArray();
        if (CollUtil.isEmpty(map)) {
            return array;
        }
        List<String> host = CollUtil.newArrayList(map.keySet());
        host.removeIf(""::equals);
        Map<String, String> hostAndNameMap = elasticsearchService.getDomainMap(host);

        Map<String, Integer> mergeHostMap = new HashMap<>();
        Map<String, String> mergeRealHostMap = new HashMap<>();
        for (Map.Entry<String, Integer> entry : map.entrySet()) {
            if (hostAndNameMap.containsKey(entry.getKey())) {
                String name = hostAndNameMap.get(entry.getKey());
                if (mergeHostMap.containsKey(name)) {
                    mergeHostMap.put(name, mergeHostMap.get(name) + map.get(entry.getKey()));
                } else {
                    mergeHostMap.put(name, map.get(entry.getKey()));
                }
                if (mergeRealHostMap.containsKey(name)) {
                    mergeRealHostMap.put(name, mergeRealHostMap.get(name) + "," + entry.getKey());
                } else {
                    mergeRealHostMap.put(name, entry.getKey());
                }
            } else {
                mergeHostMap.put(entry.getKey(), map.get(entry.getKey()));
                mergeRealHostMap.put(entry.getKey(), entry.getKey());
            }
        }

        JSONObject object;
        List<Map.Entry<String, Integer>> sortedEntries = mergeHostMap.entrySet().stream()
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .collect(Collectors.toList());
        for (Map.Entry<String, Integer> entry : sortedEntries) {
            object = JSONUtil.createObj();
            object.putOnce("name", entry.getKey());
            object.putOnce("value", entry.getValue());
            object.putOnce("host", mergeRealHostMap.get(entry.getKey()));
            if (!"".equals(entry.getKey()) && array.size() < 20) {
                array.add(object);
            }
        }
        return array;
    }

    @Override
    public JSONArray areaMap(EsSearchBO bo) {
        //地域统计图不再处理多个精准地域
        if (StrUtil.isNotEmpty(bo.getContentAreaCode()) && bo.getContentAreaCode().contains(",")) {
            bo.setContentAreaCode(null);
        }
        Map<String, Integer> map = EsSearchUtil.areaMap(bo);
        JSONArray array = new JSONArray();
        JSONObject object = JSONUtil.createObj();
        if (StrUtil.isNotEmpty(bo.getContentAreaCode())&& !CharSequenceUtil.equals(bo.getContentAreaCode(), "100000")) {
            Area byId = areaService.getById(Integer.parseInt(bo.getContentAreaCode()));
            if (null==byId){
                object.putOnce("code", "100000");
                object.putOnce("name", "中国");
            }else{
                object.putOnce("code", bo.getContentAreaCode());
                object.putOnce("name", byId.getAreaName());
            }
        } else {
            object.putOnce("code", "100000");
            object.putOnce("name", "中国");
        }
        if (CollUtil.isEmpty(map)) {
            object.putOnce("xList", new ArrayList<>());
            object.putOnce("yList", new ArrayList<>());
            object.putOnce("zList", new ArrayList<>());
            object.putOnce("max", "");
            array.add(object);
            return array;
        }
        List<Map.Entry<String, Integer>> sortedEntries = map.entrySet().stream().filter(m -> !m.getKey().equals("999999"))
                .sorted(Map.Entry.<String, Integer>comparingByValue().reversed())
                .collect(Collectors.toList());
        if (sortedEntries.size() > 10) {
            sortedEntries = sortedEntries.subList(0, 10);
        }
        List<String> x = new ArrayList<>();
        List<Integer> y = new ArrayList<>();
        List<JSONObject> z = new ArrayList<>();
        JSONObject object1;
        for (Map.Entry<String, Integer> entry : sortedEntries) {
            Area area = areaService.getById(Integer.parseInt(entry.getKey()));
            if (null==area){
                area=new Area();
                area.setAreaName("中国");//实际上并达不到初始显示中国的效果
                area.setCode("100000");
            }
            x.add(area.getAreaName());
            y.add(entry.getValue());
            object1 = JSONUtil.createObj();
            object1.putOnce("name", area.getAreaName());
            if (area.getCode() != null && area.getCode().length() > 6) {
                object1.putOnce("code", area.getCode().substring(area.getCode().length() - 6));
            } else {
                object1.putOnce("code", area.getCode());
            }
            object1.putOnce("data", entry.getValue());
            List<Double> lngAndLat = new ArrayList<>();
            lngAndLat.add(area.getLng());
            lngAndLat.add(area.getLat());
            object1.putOnce("value", lngAndLat);
            z.add(object1);
        }
        object.putOnce("xList", x);
        object.putOnce("yList", y);
        object.putOnce("zList", z);
        if (CollUtil.isNotEmpty(x)) {
            object.putOnce("max", x.get(0));
        }
        array.add(object);

        return array;
    }

    @Override
    public GraphModelVO timeType(EsSearchBO bo) {
        EsSearchTimeBO esSearchTimeBO = BeanUtil.copyProperties(bo, EsSearchTimeBO.class);
        esSearchTimeBO.setStatisticType("type");

        String type = esSearchTimeBO.getType();
        List<String> typeList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        if (StrUtil.isBlankIfStr(type)) {
            for (MediaTypeEnum value : MediaTypeEnum.values()) {
                String valueStr = StrUtil.toString(value.getValue());
                String desc = value.getDesc();
                map.put(valueStr, desc);
                typeList.add(valueStr);
            }
            esSearchTimeBO.setType(CollUtil.join(typeList, ","));
        } else {
            for (MediaTypeEnum value : MediaTypeEnum.values()) {
                String valueStr = StrUtil.toString(value.getValue());
                String desc = value.getDesc();
                map.put(valueStr, desc);
            }
            typeList = CharSequenceUtil.split(type, ",");
        }

        Map.Entry<String, List<TimeRoundFlowVO>> stringListEntry = buildRound(esSearchTimeBO);
        String key = stringListEntry.getKey();
        List<TimeRoundFlowVO> timeRoundFlow = stringListEntry.getValue();

        esSearchTimeBO.setTimeRoundFlow(timeRoundFlow);
        List<TimeFlowVO> time = EsSearchUtil.time(esSearchTimeBO);

        return getGraphModelVO(time, typeList, map, true, key);
    }

    public static Map.Entry<String, List<TimeRoundFlowVO>> buildRound(EsSearchTimeBO esSearchTimeBO) {
        String startTime = esSearchTimeBO.getStartTime().trim();
        String endTime = esSearchTimeBO.getEndTime().trim();
        if (StrUtil.isBlankIfStr(startTime) || StrUtil.isBlankIfStr(endTime)) {
            throw new CustomException("时间不能为空");
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDateTime = LocalDateTime.parse(startTime, formatter);
        LocalDateTime endDateTime = LocalDateTime.parse(endTime, formatter);

        //long secondsBetween = ChronoUnit.SECONDS.between(startDateTime, endDateTime);

        long minutesBetween = ChronoUnit.MINUTES.between(startDateTime, endDateTime);
        if (minutesBetween <= 400) {
            List<TimeRoundFlowVO> timeRoundFlowMinute = TimeUtil.timeRange(startTime, endTime, DateField.MINUTE, 1, 0);
            return MapUtil.entry("yyyy-MM-dd HH:mm", timeRoundFlowMinute);
        }
        long hoursBetween = ChronoUnit.HOURS.between(startDateTime, endDateTime);
        if (hoursBetween <= 160) {
            List<TimeRoundFlowVO> timeRoundFlowHour = TimeUtil.timeRange(startTime, endTime, DateField.HOUR, 1, 0);
            return MapUtil.entry("yyyy-MM-dd HH", timeRoundFlowHour);
        }
        long daysBetween = ChronoUnit.DAYS.between(startDateTime, endDateTime);
        if (daysBetween <= 90) {//三个月按天来
            List<TimeRoundFlowVO> timeRoundFlowDay = TimeUtil.timeRange(startTime, endTime, DateField.HOUR, 24, 0);
            return MapUtil.entry("yyyy-MM-dd", timeRoundFlowDay);
        }
        Period period = Period.between(startDateTime.toLocalDate(), endDateTime.toLocalDate());
        long monthsBetween = period.toTotalMonths();
        if (monthsBetween > 2) {
            List<TimeRoundFlowVO> timeRoundFlowMouth = TimeUtil.timeRange(startTime, endTime, DateField.MONTH, 1, 0);
            return MapUtil.entry("yyyy-MM", timeRoundFlowMouth);
        }
        //long yearsBetween = period.getYears();
        List<TimeRoundFlowVO> timeRoundFlowYear = TimeUtil.timeRange(startTime, endTime, DateField.YEAR, 1, 0);
        return MapUtil.entry("yyyy", timeRoundFlowYear);
    }

    public static GraphModelVO getGraphModelVO(List<TimeFlowVO> time, List<String> typeList,
                                               Map<String, String> map,
                                               Boolean needTotal,
                                               String parse) {
        if (CollUtil.isEmpty(time)) {
            return new GraphModelVO();
        }
        List<String> xs = new ArrayList<>();
        Map<String, List<String>> countMap = new HashMap<>();
        Map<String, List<String>> totalMap = new HashMap<>();
        for (TimeFlowVO node : time) {
            totalMap.computeIfAbsent("total", k -> new ArrayList<>()).add(node.getCount());
            String key = node.getKey();
            String s = CharSequenceUtil.splitTrim(key, "|").get(0);
            String format = DateUtil.format(DateUtil.parse(s, "yyyy-MM-dd HH:mm:ss"), parse);
            xs.add(format);
            if (node.getChildren() != null) {
                Map<String, String> childMap = node.getChildren().stream().collect(Collectors.toMap(k -> k.getKey(), v -> v.getCount()));
                for (String string : typeList) {
                    if (childMap.containsKey(string)) {
                        String count = childMap.get(string);
                        countMap.computeIfAbsent(string, k -> new ArrayList<>()).add(count);
                    } else {
                        countMap.computeIfAbsent(string, k -> new ArrayList<>()).add("0");
                    }
                }

            }
        }

        GraphModelVO graphModelVO = new GraphModelVO();
        List<SeriesVO<String>> seriesList = new ArrayList<>();
        if (Boolean.TRUE.equals(needTotal)) {
            SeriesVO<String> seriesVOTotal = new SeriesVO<>();
            seriesVOTotal.setName("总数");
            if (totalMap.containsKey("total")) {
                List<String> total = totalMap.get("total");
                seriesVOTotal.setData(total);
            } else {
                seriesVOTotal.setData(Collections.singletonList("0"));
            }
            seriesList.add(seriesVOTotal);
        }

        for (String valueStr : typeList) {
            String desc = map.get(valueStr);
            SeriesVO<String> seriesVO = new SeriesVO<>();
            seriesVO.setName(desc);
            if (countMap.containsKey(valueStr)) {
                List<String> count = countMap.get(valueStr);
                seriesVO.setData(count);
            } else {
                seriesVO.setData(Collections.singletonList("0"));
            }
            seriesList.add(seriesVO);
        }

        graphModelVO.setSeriesList(seriesList);
        graphModelVO.setXs(xs);
        return graphModelVO;
    }

    @Override
    public GraphModelVO timeEmotion(EsSearchBO bo) {
        EsSearchTimeBO esSearchTimeBO = BeanUtil.copyProperties(bo, EsSearchTimeBO.class);
        esSearchTimeBO.setStatisticType("emotionFlag");
        String emotionFlag = esSearchTimeBO.getEmotionFlag();

        List<String> typeList = new ArrayList<>();
        Map<String, String> map = new HashMap<>();
        if (StrUtil.isBlankIfStr(emotionFlag)) {
            for (EmotionEnum value : EmotionEnum.values()) {
                String valueStr = StrUtil.toString(value.getValue());
                String desc = value.getName();
                map.put(valueStr, desc);
                typeList.add(valueStr);
            }
            esSearchTimeBO.setEmotionFlag(CollUtil.join(typeList, ","));
        } else {
            for (EmotionEnum value : EmotionEnum.values()) {
                String valueStr = StrUtil.toString(value.getValue());
                String desc = value.getName();
                map.put(valueStr, desc);
            }
            typeList = CharSequenceUtil.split(emotionFlag, ",");
        }
        Map.Entry<String, List<TimeRoundFlowVO>> stringListEntry = buildRound(esSearchTimeBO);
        String key = stringListEntry.getKey();
        List<TimeRoundFlowVO> timeRoundFlow = stringListEntry.getValue();
        esSearchTimeBO.setTimeRoundFlow(timeRoundFlow);
        List<TimeFlowVO> time = EsSearchUtil.time(esSearchTimeBO);
        return getGraphModelVO(time, typeList, map, false, key);
    }

    @Override
    public JSONObject mediaLevel(EsSearchBO bo) {
        List<BaseSimpleVO> datas = new ArrayList<>();
        if (StrUtil.isEmpty(bo.getAccountGrade())) {
            bo.setAccountGrade("0,2,4,6,8,10,12");
        }
        JSONObject postBodyJSONObject = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.MEDIA_RANK_STATIS);
        JSONArray o = postBodyJSONObject.getJSONArray("mediaRankData");
        for (Object object : o) {
            JSONObject obj = (JSONObject) object;
            for (Map.Entry<String, Object> stringObjectEntry : obj) {
                BaseSimpleVO simpleVO = new BaseSimpleVO();
                String s = dictDataService.selectDictLabel("account_level", stringObjectEntry.getKey());
                simpleVO.setName(s);
                simpleVO.setCount(Integer.parseInt(String.valueOf(stringObjectEntry.getValue())));
                simpleVO.setType(Integer.parseInt(stringObjectEntry.getKey()));
                simpleVO.setHostDown("");
                datas.add(simpleVO);
            }
        }
        postBodyJSONObject.set("mediaRankData", datas);
        return postBodyJSONObject;
    }

    @Override
    public JSONObject mediaCentral(EsSearchBO bo) {
        JSONObject res = new JSONObject();
//        if (StrUtil.isEmpty(bo.getAccountGrade()) || "0".equals(bo.getAccountGrade())) {
//            throw new CustomException("accountGrade参数异常");
//        }
        if (StrUtil.isEmpty(bo.getAccountGrade())) {
            bo.setAccountGrade("0,2,4,6,8,10,12");
        }
//        Long infoCount = EsSearchUtil.getInfoCount(bo);
        bo.setAggName("author");
        bo.setAggSize(35);
//        bo.setType(String.valueOf(MediaTypeEnum.NEWS.getValue()));//媒体网站查  不知道当时怎么说的了，为什么媒体网站查1
//        bo.setType(MediaTypeEnum.WEIBO.getValue() + "," + MediaTypeEnum.WECHAT.getValue() + "," + MediaTypeEnum.CLIENT.getValue() + "," + MediaTypeEnum.VIDEO.getValue());
//        JSONObject postBodyJSONObject = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.TYPE_TOTAL_COUNT);    //查询author作者
        JSONObject postBodyJSONObject = new JSONObject();
        ArrayList<String> allHost = new ArrayList<>();
//        ArrayList<String> hostList = CollUtil.newArrayList(postBodyJSONObject.keySet());
//        if (CollUtil.isNotEmpty(hostList)) {
//            allHost.addAll(hostList);
//        }
        bo.setAggName("host");
//        bo.setType(MediaTypeEnum.WEIBO.getValue() + "," + MediaTypeEnum.WECHAT.getValue() + "," + MediaTypeEnum.CLIENT.getValue() + "," + MediaTypeEnum.VIDEO.getValue());
        JSONObject postBodyJSONObject1 = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.TYPE_TOTAL_COUNT);//查询host网站数量
        ArrayList<String> hostList1 = CollUtil.newArrayList(postBodyJSONObject1.keySet());
        if (CollUtil.isNotEmpty(hostList1)) {
            allHost.addAll(hostList1);
        }
        List<BaseSimpleVO> accountRankList = new ArrayList<>();
        Map<String, String> hostAndNameMap = elasticsearchService.getDomainMap(allHost);
        //媒体网站
        for (Map.Entry<String, Object> stringObjectEntry : postBodyJSONObject1) {
            String key = hostAndNameMap.get(stringObjectEntry.getKey());
            if (key == null) {
                accountRankList.add(new BaseSimpleVO(stringObjectEntry.getKey(), Integer.parseInt(String.valueOf(stringObjectEntry.getValue()))
                        , stringObjectEntry.getKey()));
                continue;
            }
            if (!accountRankList.contains(key)) {
                accountRankList.add(new BaseSimpleVO(key, Integer.parseInt(String.valueOf(stringObjectEntry.getValue()))
                        , stringObjectEntry.getKey()));
            } else {
                accountRankList.add(new BaseSimpleVO(key, Integer.parseInt(String.valueOf(stringObjectEntry.getValue())) + findValueByKey(accountRankList, key)
                        , stringObjectEntry.getKey()));
            }
        }
//        res.set("accountRank", accountRankList);
        //媒体作者
        List<BaseSimpleVO> siteRankList = new ArrayList<>();
        for (Map.Entry<String, Object> stringObjectEntry : postBodyJSONObject) {
            String key = hostAndNameMap.get(stringObjectEntry.getKey());
            if (key == null) {
                siteRankList.add(new BaseSimpleVO(stringObjectEntry.getKey(), Integer.parseInt(String.valueOf(stringObjectEntry.getValue()))
                        , stringObjectEntry.getKey()));
                continue;//存在host带端口情况cms.ahgyss.cn:80，大多查不出来，此处先跳过
            }
            if (!siteRankList.contains(key)) {
                siteRankList.add(new BaseSimpleVO(key, Integer.parseInt(String.valueOf(stringObjectEntry.getValue())), stringObjectEntry.getKey()));
            } else {
                siteRankList.add(new BaseSimpleVO(key, Integer.parseInt(String.valueOf(stringObjectEntry.getValue())) + findValueByKey(siteRankList, key)
                        , stringObjectEntry.getKey()));
            }
        }
        int total = accountRankList.size();
        if (total < siteRankList.size()) {
            total = siteRankList.size();
        }
        List<MediaLevelVO> medias = new ArrayList<>();
        for (int i = 0; i < total; i++) {
            MediaLevelVO mediaLevelVO = new MediaLevelVO();//媒体网站
            if (CollUtil.isNotEmpty(accountRankList) && i < accountRankList.size()) {
                BaseSimpleVO baseSimpleVO = accountRankList.get(i);
                if (null != baseSimpleVO) {
                    mediaLevelVO.setMediaName(baseSimpleVO.getName());
                    mediaLevelVO.setMediaNum(baseSimpleVO.getCount());
                    mediaLevelVO.setHostDown(baseSimpleVO.getHostDown());
                }
            }
            if (CollUtil.isNotEmpty(siteRankList) && i < siteRankList.size()) {//媒体账号
                BaseSimpleVO baseSimpleVO1 = siteRankList.get(i);
                if (null != baseSimpleVO1) {
                    mediaLevelVO.setSiteName(baseSimpleVO1.getName());
                    mediaLevelVO.setSiteNum(baseSimpleVO1.getCount());
                }
            }
            medias.add(mediaLevelVO);
        }
//        res.set("rankDatas", medias);
//        res.set("leveCount", accountRankList.size() + siteRankList.size());
        // 可能会出现不同的siteName对应着同一个mediaName的情况，合并去除siteNum少的那个
//        List<MediaLevelVO> rankDatas = entries.getBeanList("rankDatas", MediaLevelVO.class);

        List<MediaLevelVO> res1 = new ArrayList<>();
        Map<String, List<MediaLevelVO>> collect = medias.stream()
                .filter(x-> com.boryou.common.utils.StringUtils.isNotEmpty(x.getMediaName()))
                .collect(Collectors.groupingBy(MediaLevelVO::getMediaName));
        List<MediaLevelVO> finalRes = res1;
        collect.forEach((k, v)->{
            if (v.size() > 1){
                MediaLevelVO item = v.stream().max(Comparator.comparing(MediaLevelVO::getMediaNum)).get();
                item.setMediaNum(v.stream().mapToInt(MediaLevelVO::getMediaNum).sum());
                finalRes.add(item);
            } else {
                finalRes.addAll(v);
            }
        });
        res1.sort(Comparator.comparing(MediaLevelVO::getMediaNum).reversed());
        if (res1.size()>20){
            res1=res1.subList(0,20);
        }
        res.set("rankDatas", res1);
//        res.set("leveCount", MediaLevelVO.countNonNullRecords(res1));
        res.set("leveCount",0);
        return res;
    }

    public Integer findValueByKey(List<BaseSimpleVO> list, String targetKey) {
        for (BaseSimpleVO item : list) {
            if (item.getName().equals(targetKey)) {
                return item.getCount();
            }
        }
        return null;
    }

    public String[] getTimeRange(Long planId) {
        String startTime = "";
        String endTime = "";
        Integer timeIndex = null;
        if (null != planId) {
            JSONObject searchCriteria = searchService.getSearchCriteria(planId);
            if (searchCriteria != null) {
                if (StrUtil.isNotEmpty(searchCriteria.getStr("startTime")) && StrUtil.isNotEmpty(searchCriteria.getStr("endTime"))) {
                    startTime = searchCriteria.getStr("startTime");
                    endTime = searchCriteria.getStr("endTime");
                    return new String[]{startTime, endTime};
                } else {
                    timeIndex = searchCriteria.getInt("timeIndex");
                }
            }
        }
        if (timeIndex == null) {
            timeIndex = 0;
        }
        SearchVO searchVO = new SearchVO();
        searchVO.setTimeIndex(timeIndex);
        EsSearchBO bo = new EsSearchBO();
        searchVO.getSearchTimeRange(bo);
        startTime = bo.getStartTime();
        endTime = bo.getEndTime();
        return new String[]{startTime, endTime};
    }

    @Override
    public LinkedHashMap hotsearch(SearchVO searchVO) {
        LinkedHashMap datas = new LinkedHashMap();
        Plan plan = planService.selectPlanById(searchVO.getPlanId());
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
//        String searchKeyWord = SearchServiceImpl.getSearchKeyWord(bo);
        String searchKeyWord = plan.getHotKw();
        if (searchKeyWord == null) {
            searchKeyWord = "";
        }
        List<String> keyword = Arrays.stream(searchKeyWord.split(" ")).distinct().filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        List<Hot> hotList = new ArrayList<>();
        String[] timeRange = {bo.getStartTime(), bo.getEndTime()};
        //AREA_BELLWETHER百度地域风向标,  WB_REALTIME微博实时榜， WB_NEWS微博要闻，DOUYIN_HOT抖音热点榜，DOUYIN_VIDEO抖音视频榜，TOUTIAO_HOT今日头条热点榜
        List<Hot> hots = hotListMapper.selectHotBackList(Arrays.asList("WB_REALTIME", "WB_NEWS", "DOUYIN_VIDEO", "DOUYIN_HOT", "TOUTIAO_HOT", "AREA_BELLWETHER", "REALTIME"), timeRange[0], timeRange[1]);
        Map<String, List<Hot>> collect = hots.stream().collect(Collectors.groupingBy(Hot::getType));
        Set<String> strings = collect.keySet();
        for (String string : strings) {
            List<Hot> hots1 = collect.get(string);
            List<String> collect1 = hots1.stream().map(Hot::getTitle).collect(Collectors.toList());
            for (int i = 0; i < hots1.size(); i++) {
                Hot hot = hots1.get(i);
                hot.setSort(Long.valueOf(String.valueOf(i + 1)));
                String type = hot.getType();
                if ("WB_REALTIME".equals(type) || "WB_NEWS".equals(type)) {
                    hot.setType("微博");
                } else if ("DOUYIN_VIDEO".equals(type) || "DOUYIN_HOT".equals(type)) {
                    hot.setType("抖音");
                } else if ("TOUTIAO_HOT".equals(type)) {
                    hot.setType("头条");
                } else if ("REALTIME".equals(type)) {
                    hot.setType("百度");//丁组磊采集的百度榜单，不是百度的链接，而是百度榜单中链接里面的原文url。这个REALTIME他说可以当作百度的榜单
                }
            }
            for (String s : keyword) {
                List<String> similarityList = similarityService.getSimilarityTextList(s, collect1, 0.5);
                for (String b : similarityList) {
                    Hot hot = getHot(hots1, b);
                    if (null != hot) {
                        hot.setIndexNumStr(hot.getIndexNum() == 0 ? "采集中" : String.valueOf(hot.getIndexNum()));
                        hotList.add(hot);
                    }
                }
            }
        }
        if (CollUtil.isNotEmpty(hotList) && hotList.size() > 10) {
            hotList = hotList.subList(0, 10);
        }
        List<Hot> collect2 = hotList.stream().distinct().sorted((o1, o2) -> Math.toIntExact(o2.getIndexNum() - o1.getIndexNum())).collect(Collectors.toList());
        Map<String, List<Hot>> collect1 = hotList.stream().distinct().collect(Collectors.groupingBy(Hot::getType));
        Set<String> strings1 = collect1.keySet();
        JSONArray arr = new JSONArray();
        LinkedHashMap fullPlat = new LinkedHashMap();
        fullPlat.put("typeName", "全平台");
        fullPlat.put("data", collect2);
        arr.add(fullPlat);
        for (String s : strings1) {
            List<Hot> hots1 = collect1.get(s);
            hots1 = hots1.stream().sorted((o1, o2) -> Math.toIntExact(o2.getIndexNum() - o1.getIndexNum())).collect(Collectors.toList());
            for (Hot hot : hots1) {
                hot.setIndexNumStr(hot.getIndexNum() == 0 ? "采集中" : String.valueOf(hot.getIndexNum()));
            }
            LinkedHashMap data = new LinkedHashMap();
            data.put("typeName", s);
            data.put("data", hots1);
            arr.add(data);
        }
//        List<Hot> hotss = new ArrayList<>();
//        boolean flag=false;
//        for (String keyword1 : keyword) {
//            try{
//            List<Hot> wordResults = HttpUtils.getList("http://192.168.10.153:5001/hotTopic?key=" + keyword1, Hot.class, "list");
//                int numToTake = Math.max(1, wordResults.size());
//                for (int i = 0; i < numToTake; i++) {
//                    if (hotss.size() >= 10) {
//                        flag=true;
//                        break;  // 如果结果超过 10 个，跳出循环
//                    }
//                    if (i < wordResults.size()) {
//                        hotss.add(wordResults.get(i));
//                    }
//                }
//            }catch (Exception e){
//                e.printStackTrace();
//            }
//            if (flag) {
//                break;
//            }
//        }
        JSONArray arr1 = new JSONArray();
//        searchVO.getSearchTimeRange(bo);
//        Map<String, List<Hot>> collect3 = hotss.stream().collect(Collectors.groupingBy(s -> s.getType()));
//        Set<String> strings2 = collect3.keySet();
//        for (String s : strings2) {
//            if ("抖音".equals(s)){
//                continue;
//            }
//            List<Hot> hots1 = collect3.get(s);
//            hots1 = hots1.stream().sorted((o1, o2) -> o2.getIndexNum() > o1.getIndexNum()?1:0).collect(Collectors.toList());
//
//            for (int i = 0; i < hots1.size(); i++) {
//                Hot hot = hots1.get(i);
//                EsSearchBO bo1 = new EsSearchBO();
//                bo1.setKeyWord1(hot.getTitle());
//                bo1.setAccurate(true);
//                bo1.setSearchPosition("1,2");
//                bo1.setPageSize(1);
//                bo1.setPageNum(1);
//                bo1.setSortType(1);
//                bo1.setType(String.valueOf(MediaTypeEnum.WEIBO.getValue()));
//                bo1.setStartTime(bo.getStartTime());
//                bo1.setEndTime(bo.getEndTime());
//                JSONObject search = EsSearchUtil.getPostBodyJSONObject(bo1, EsSearchUtil.SEARCHX);
//                Integer total = search.getInt("total");
//                List<EsBean> list = new ArrayList<>(JSONUtil.toList(search.getJSONArray("records"), EsBean.class));
////                PageResult<EsBean> similarityData=new PageResult<>();
//                EsBean esBean=new EsBean();
//                if (CollUtil.isNotEmpty(list)){
////                    EsSearchBO bo2 = new EsSearchBO();
//                   esBean = list.get(0);
////                    bo2.setId(esBean.getId());
//////                    Date publishTime = esBean.getPublishTime();
////                    bo2.setStartTime(bo.getStartTime());
////                    bo2.setEndTime(bo.getEndTime());
////                     similarityData = searchService.getSimilarityData(bo2);
////                     log.info("一共:{}个，当前:{}结束",hots1.size(),(i+1));
//                }
////                hot.setDocNum(similarityData.getTotal());
////                hot.setDocId(esBean.getId());
//                hot.setDocNum(total);
//                if (StrUtil.isNotEmpty(esBean.getId())){
//                    hot.setDocId(esBean.getId());
//                }else{
//                    hot.setDocId(null);
//                }
//            }
//            JSONObject data = new JSONObject();
//            data.put("typeName", s);
//            data.put("data", hots1);
//            arr1.add(data);
//        }
        //话题-只有微博    抖音，头条那些都拿不到
        datas.put("topic", arr1);
        //热搜 -丁组磊榜单（微博，头条，抖音，百度）
        datas.put("hotWords", arr);
        if (StrUtil.isNotEmpty(searchKeyWord)) {
            datas.put("hotKw", true);
        } else {
            datas.put("hotKw", false);
        }
        return datas;
    }

    @Override
    public void createReport(HttpServletResponse response, SearchVO searchVO) {
        Plan plan = planService.selectPlanById(searchVO.getPlanId());
        if (plan == null) {
            return;
        }
        EsSearchBO bo = convertHandler.copyPropertiesFromPlan(searchVO, plan);
        //媒体类型分析
        long runTime = System.currentTimeMillis();
        JSONArray mediaTypes = mediaTypeAnalyse(bo);
        runTime = printRunTime(runTime, "mediaTypeAnalyse");
        //情感分析
        JSONObject emotions = getEmotionAnalyse(bo);
        runTime = printRunTime(runTime, "getEmotionAnalyse");
        //情感分析top10
        EsSearchBO emotionTopSearchBO = convertHandler.getEsSearchBOForAnalyse(searchVO);
        emotionTopSearchBO.setEmotionFlag("1");
        emotionTopSearchBO.setSortType(7);
        emotionTopSearchBO.setPageSize(10);
        List<EsBean> emotionTop = EsSearchUtil.searchEsBeanList(emotionTopSearchBO);
        runTime = printRunTime(runTime, "emotionTopSearch");
        //摘要
//        EsSearchBO summaryBO = convertHandler.getEsSearchBOForAnalyse(searchVO);
//        summaryBO.setPageSize(3);
//        PageResult<BoryouBean> search = EsSearchUtil.search(summaryBO, EsSearchUtil.SEARCH);
//        String join = search.stream().map(BoryouBean::getText).collect(Collectors.joining("。"));
//        String summary = DoubaoUtil.summary(join);
        String summary = searchVO.getData().getStr("summary");

        runTime = printRunTime(runTime, "getEsSearchBOForAnalyse");
        //重要媒体
        EsSearchBO centralBO = convertHandler.getEsSearchBOForAnalyse(searchVO);
        centralBO.setAccountGrade("2,4,6,8");
        JSONObject central = mediaCentral(centralBO);
        runTime = printRunTime(runTime, "mediaCentral");
        //站点报道量排行
        List<Object> mediaActive = mediaActiveMap(bo);
        runTime = printRunTime(runTime, "mediaActiveMap");
        //首发
        List<EsBean> firstRelease = searchService.getFirstRelease(searchVO);
        runTime = printRunTime(runTime, "getFirstRelease");
        //事件脉络
        bo.setSortType(getEnumValue(SortTypeEnum.TIME_ASC));
        if (plan.getHistoryFlag() == 1) {
            bo.setStartTime(DateUtil.formatDateTime(plan.getEffectStartTime()));
            bo.setEndTime(DateUtil.formatDateTime(plan.getEffectEndTime()));
        } else {
            bo.setEndTime(DateUtil.formatDateTime(new Date()));
            bo.setStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -30)));
            bo.setIndexs(Arrays.asList("netxman_yq_week", "netxman_yq_month"));
        }
        JSONObject jsonObject = searchService.eventContext(bo);
        List<EsBean> eventContext = jsonObject.getBeanList("records", EsBean.class);
        runTime = printRunTime(runTime, "eventContext");
        //相关热搜
        LinkedHashMap hotsearch = hotsearch(searchVO);
        runTime = printRunTime(runTime, "hotsearch");
        //信息来源走势图 - 前端 - 后端
        List<SeriesVO<String>> seriesList = timeType(bo).getSeriesList();
        runTime = printRunTime(runTime, "seriesList");
        //媒体级别分布图 - 前端 - 后端
        centralBO.setAccountGrade("");
        JSONObject object1 = mediaCentral(centralBO);
        List<Object> mediaCentral = object1.getJSONArray("rankDatas");
        if (mediaCentral.size() > 10) {
            mediaCentral = mediaCentral.subList(0, 10);
        }
        JSONObject mediaLevel = mediaLevel(bo);
        runTime = printRunTime(runTime, "mediaLevel");
        //字符云
        List<Object> wordArray = wordAnalyse(bo, searchVO.getPlanId());
        runTime = printRunTime(runTime, "wordAnalyse");
        //地域统计图-地域名称排名列表
        JSONArray areaNames = ((JSONObject) areaMap(bo).get(0)).getJSONArray("xList");
        runTime = printRunTime(runTime, "areaMap");
        //媒体观点
        JSONArray mediaOpinion = mediaOpinion(searchVO);
        runTime = printRunTime(runTime, "mediaOpinion");
        //网民观点
        List<EsBean> netizenOpinion = netizenOpinion(searchVO);
        runTime = printRunTime(runTime, "netizenOpinion");

        POIWordUtil wordUtil = new POIWordUtil();
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);
        paragraph.addText(plan.getPlanName(), 32, true, false);
        //简介
        addFirstGradeTitle(wordUtil, "事件简介", true, "arial", null);
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.CENTER);
        JSONObject desc = emotions.getJSONObject("desc");
        StringBuilder title = new StringBuilder("    在" + desc.getStr("startTime") + "至" + desc.getStr("endTime") + "，"
                + plan.getPlanName() + "方案中涉及信息总量：" + desc.getStr("total") + "，其中");
        if (firstRelease.size() > 0) {
            for (Object mediaType : mediaTypes) {
                JSONObject object = (JSONObject) mediaType;
                String name = object.getStr("name");
                Integer value = object.getInt("value");
                title.append(name).append(value).append("，");
            }
        } else {
            title.append("暂未发现相关数据。");
        }
        for (SeriesVO<String> stringSeriesVO : seriesList) {
            if (stringSeriesVO.getName().equals("总数")) {
                List<String> data = stringSeriesVO.getData();
                int max = 0;
                for (String datum : data) {
                    Integer num = Integer.valueOf(datum);
                    if (num > max) {
                        max = num;
                    }
                }
                title.append("波峰值：").append(max).append("条，");
            }
        }

        title.append("非敏感占比").append(desc.getStr("positive")).append("，");
        title.append("敏感占比").append(desc.getStr("negative")).append("，");
        title.append("中性占比").append(desc.getStr("neutral"));
        if (mediaActive.size() > 0) {
            JSONObject media = (JSONObject) mediaActive.get(0);
            title.append("；最大舆论场").append(media.getStr("name")).append("，活跃度").append(media.getStr("value"));
        }
        if (areaNames.size() > 0) {
            title.append("；提及地域占比最大为").append(areaNames.get(0));
        }
        title.append("。");
        paragraph.addText(title.toString(), 22, false, false, "arial", "000000");
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "事件摘要", true, "arial", null);
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
        paragraph.addText("    " + summary, 22, false, false, "arial", "000000");

        List<Object> jsonArray = central.getJSONArray("rankDatas");
        if (jsonArray.size() > 5) {
            jsonArray = jsonArray.subList(0, 5);
        }
        title = new StringBuilder("参与此事件的重要媒体：");
        for (Object o : jsonArray) {
            JSONObject object = (JSONObject) o;
            title.append(" ").append(object.getStr("mediaName")).append(" ").append(object.getInt("mediaNum")).append(" ");
        }
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
        paragraph.addText(title.toString(), 22, false, false, "arial", "000000");

        title = new StringBuilder("站点报道量排行：");
        if (mediaActive.size() > 5) {
            mediaActive = mediaActive.subList(0, 5);
        }
        for (Object o : mediaActive) {
            JSONObject object = (JSONObject) o;
            title.append(" ").append(object.getStr("name")).append(" ").append(object.getInt("value")).append(" ");
        }
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
        paragraph.addText(title.toString(), 22, false, false, "arial", "000000");
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "相关热文", true, "arial", null);
        getFirstRelease(wordUtil, firstRelease);
        wordUtil.createParagraph();

//        addFirstGradeTitle(wordUtil, "事件脉络", true, "arial", null);
//        int num = 0;
//        for (int i = 0; i < eventContext.size(); i++) {
//            if (StrUtil.isEmpty(eventContext.get(i).getTitle())) {
//                continue;
//            }
//            paragraph = wordUtil.createParagraph();
//            paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
//            String title1 = (num + 1) + ". " + HtmlUtil.cleanHtmlTag(eventContext.get(i).getTitle()).trim();
//            paragraph.addHyperlink(title1, 22, eventContext.get(i).getUrl());
//            num++;
//            String data = DateUtil.format(DateUtil.parse(DateUtil.format(eventContext.get(i).getPublishTime(), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
//            paragraph = wordUtil.createParagraph();
//            paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
//            paragraph.addText("  " + data + "  " + "媒体：" + MediaTypeEnum.getDesc(String.valueOf(eventContext.get(i).getType())) + "  " + "相似文章数量：" + eventContext.get(i).getSimilarCount(), 22, false, false, "arial", "000000");
//        }

        wordUtil.createParagraph();

//        addFirstGradeTitle(wordUtil, "相关热搜", true, "arial", null);
//        hotsearch(wordUtil, hotsearch);
//        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "信息来源走势图", true, "arial", null);
        addEchartsPicture(wordUtil, searchVO.getData().getStr("lineImg1"));
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "媒体级别分布图", true, "arial", null);
        title = new StringBuilder("媒体总数量：");
        title.append(mediaLevel.getStr("mediaTotalNum")).append("家    ");
        title.append("媒体总发文数量：").append(mediaLevel.getStr("mediaPublishTotal")).append("条");
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
        paragraph.addText(title.toString(), 22, false, false, "arial", "000000");
        addEchartsPicture(wordUtil, searchVO.getData().getStr("barMediaImg"));
        title = new StringBuilder("媒体数量：").append(object1.getStr("leveCount")).append("家");
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
        paragraph.addText(title.toString(), 22, false, false, "arial", "000000");
        getMediaLevel(wordUtil, mediaCentral);
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "敏感走势图", true, "arial", null);
        addEchartsPicture(wordUtil, searchVO.getData().getStr("lineImg2"));
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "敏感占比图", true, "arial", null);
        addEchartsPicture(wordUtil, searchVO.getData().getStr("pieImg1"));
        addFirstGradeTitle(wordUtil, "敏感信息TOP10", true, "arial", null);
        getNegativeTop(wordUtil, emotionTop);
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "信息来源占比", true, "arial", null);
        addEchartsPicture(wordUtil, searchVO.getData().getStr("pieImg2"));
        addFirstGradeTitle(wordUtil, "来源占比信息", true, "arial", null);
        getMediaType(wordUtil, mediaTypes);
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "关键词云", true, "arial", null);
        addEchartsPicture(wordUtil, searchVO.getData().getStr("cloudImg"));
        addFirstGradeTitle(wordUtil, "热门词频", true, "arial", null);
        if (wordArray.size() > 10) {
            wordArray = wordArray.subList(0, 10);
        }
        getWordChart(wordUtil, wordArray);
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "媒体活跃度", true, "arial", null);
        addEchartsPicture(wordUtil, searchVO.getData().getStr("barImg"));

        addFirstGradeTitle(wordUtil, "地域分布图", true, "arial", null);
        addEchartsPicture(wordUtil, searchVO.getData().getStr("mapImg"));
        addFirstGradeTitle(wordUtil, "地域分布TOP10", true, "arial", null);
        addEchartsPicture(wordUtil, searchVO.getData().getStr("barAreaImg"));

        addFirstGradeTitle(wordUtil, "媒体观点", true, "arial", null);
        addEchartsPicture(wordUtil, searchVO.getData().getStr("barImg2"));
        wordUtil.createParagraph();
        for (int i = 0; i < mediaOpinion.size(); i++) {
            JSONObject object = (JSONObject) mediaOpinion.get(i);
            String text = object.getStr("value") + "% 的媒体关注“" + object.getStr("name") + "”";
            if (i < mediaOpinion.size() - 1) {
                text += "；";
            } else {
                text += "。";
            }
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
            paragraph.addText(text, 22, false, false, "arial", "000000");
            wordUtil.createParagraph();
        }

        addFirstGradeTitle(wordUtil, "网民观点", true, "arial", null);
        getNetizenOpinion(wordUtil, netizenOpinion);
        FileUtil.mkdir("reportFile/");
        File file = FileUtil.file("reportFile/report.docx");
        wordUtil.saveDocument(file);

        InputStream in = FileUtil.getInputStream(file);
        try {
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(plan.getPlanName() + ".pdf", Constants.UTF8));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setContentType("application/pdf; charset=UTF-8");
        reportService.changeFileTypeToDownload(in, response, "");
    }

    @Override
    public void updateReport(HttpServletResponse response, SearchVO searchVO) {
//        Plan plan = planService.selectPlanById(searchVO.getPlanId());
//        if (plan == null) {
//            return;
//        }
        JSONObject data = searchVO.getData();
        POIWordUtil wordUtil = new POIWordUtil();
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);
        paragraph.addText(data.getStr("title"), 32, true, false);
        //简介
        addFirstGradeTitle(wordUtil, "事件简介", true, "arial", null);
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.CENTER);
        String desc = data.getStr("desc");
        paragraph.addText("    " + desc, 22, false, false, "arial", "000000");
        wordUtil.createParagraph();
        addFirstGradeTitle(wordUtil, "事件摘要", true, "arial", null);
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
        String summary = data.getStr("summary");
        paragraph.addText("    " + summary, 22, false, false, "arial", "000000");

        List<Object> jsonArray = data.getJSONArray("rankDatas");
        if (jsonArray.size() > 5) {
            jsonArray = jsonArray.subList(0, 5);
        }
        StringBuilder title = new StringBuilder("参与此事件的重要媒体：");
        for (Object o : jsonArray) {
            JSONObject object = (JSONObject) o;
            title.append(" ").append(object.getStr("mediaName")).append(" ").append(object.getInt("mediaNum")).append(" ");
        }
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
        paragraph.addText(title.toString(), 22, false, false, "arial", "000000");

        List<Object> mediaActive = data.getJSONArray("mediaActive");
        title = new StringBuilder("站点报道量排行：");
        if (mediaActive.size() > 5) {
            mediaActive = mediaActive.subList(0, 5);
        }
        for (Object o : mediaActive) {
            JSONObject object = (JSONObject) o;
            title.append(" ").append(object.getStr("name")).append(" ").append(object.getInt("value")).append(" ");
        }
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
        paragraph.addText(title.toString(), 22, false, false, "arial", "000000");
        wordUtil.createParagraph();
        List<EsBean> firstRelease = data.getBeanList("firstRelease", EsBean.class);
        addFirstGradeTitle(wordUtil, "相关热文", true, "arial", null);
        getFirstRelease(wordUtil, firstRelease);
        wordUtil.createParagraph();

//        addFirstGradeTitle(wordUtil, "事件脉络", true, "arial", null);
//        List<EsBean> eventContext = data.getBeanList("eventContext", EsBean.class);
//        int num = 0;
//        for (int i = 0; i < eventContext.size(); i++) {
//            if (StrUtil.isEmpty(eventContext.get(i).getTitle())) {
//                continue;
//            }
//            paragraph = wordUtil.createParagraph();
//            paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
//            String title1 = (num + 1) + ". " + HtmlUtil.cleanHtmlTag(eventContext.get(i).getTitle()).trim();
//            paragraph.addHyperlink(title1, 22, eventContext.get(i).getUrl());
//            num++;
//            String date = DateUtil.format(DateUtil.parse(DateUtil.format(eventContext.get(i).getPublishTime(), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN);
//            paragraph = wordUtil.createParagraph();
//            paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
//            paragraph.addText("  " + date + "  " + "媒体：" + MediaTypeEnum.getDesc(String.valueOf(eventContext.get(i).getType())) + "  " + "相似文章数量：" + eventContext.get(i).getSimilarCount(), 22, false, false, "arial", "000000");
//        }

        wordUtil.createParagraph();
//        JSONArray hotWords = data.getJSONArray("hotWords");
//        LinkedHashMap<String, JSONArray> hotsearch = new LinkedHashMap<>();
//        JSONObject hotWordsObj = new JSONObject();
//        hotWordsObj.putOnce("typeName", "全平台");
//        hotWordsObj.putOnce("data", hotWords);
//        hotWords = new JSONArray();
//        hotWords.add(hotWordsObj);
//        hotsearch.put("hotWords", hotWords);
//        addFirstGradeTitle(wordUtil, "相关热搜", true, "arial", null);
//        hotsearch(wordUtil, hotsearch);
//        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "信息来源走势图", true, "arial", null);
        addEchartsPicture(wordUtil, data.getStr("lineImg1"));
        wordUtil.createParagraph();
//
        addFirstGradeTitle(wordUtil, "媒体级别分布图", true, "arial", null);
//        title = new StringBuilder("媒体总数量：");
//        title.append(mediaLevel.getStr("mediaTotalNum")).append("家    ");
//        title.append("媒体总发文数量：").append(mediaLevel.getStr("mediaPublishTotal")).append("条");
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
        paragraph.addText(data.getStr("mediaTotalNum"), 22, false, false, "arial", "000000");
        addEchartsPicture(wordUtil, data.getStr("barMediaImg"));
//        title = new StringBuilder("媒体数量：").append(object1.getStr("leveCount")).append("家");
        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
        paragraph.addText(data.getStr("leveCount"), 22, false, false, "arial", "000000");
        JSONArray mediaCentral = data.getJSONArray("mediaCentral");
        getMediaLevel(wordUtil, mediaCentral);
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "敏感走势图", true, "arial", null);
        addEchartsPicture(wordUtil, data.getStr("lineImg2"));
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "敏感占比图", true, "arial", null);
        addEchartsPicture(wordUtil, data.getStr("pieImg1"));
        addFirstGradeTitle(wordUtil, "敏感信息TOP10", true, "arial", null);
        List<EsBean> emotionTop = data.getBeanList("emotionTop", EsBean.class);
        getNegativeTop(wordUtil, emotionTop);
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "信息来源占比", true, "arial", null);
        addEchartsPicture(wordUtil, data.getStr("pieImg2"));
        addFirstGradeTitle(wordUtil, "来源占比信息", true, "arial", null);
        JSONArray mediaTypes = data.getJSONArray("mediaTypes");
        getMediaType(wordUtil, mediaTypes);
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "关键词云", true, "arial", null);
        addEchartsPicture(wordUtil, data.getStr("cloudImg"));
        addFirstGradeTitle(wordUtil, "热门词频", true, "arial", null);
        List<Object> wordArray = data.getJSONArray("wordArray");
        if (wordArray.size() > 10) {
            wordArray = wordArray.subList(0, 10);
        }
        getWordChart(wordUtil, wordArray);
        wordUtil.createParagraph();

        addFirstGradeTitle(wordUtil, "媒体活跃度", true, "arial", null);
        addEchartsPicture(wordUtil, data.getStr("barImg"));

        addFirstGradeTitle(wordUtil, "地域分布图", true, "arial", null);
        addEchartsPicture(wordUtil, data.getStr("mapImg"));
        addFirstGradeTitle(wordUtil, "地域分布TOP10", true, "arial", null);
        addEchartsPicture(wordUtil, data.getStr("barAreaImg"));

        addFirstGradeTitle(wordUtil, "媒体观点", true, "arial", null);
        addEchartsPicture(wordUtil, data.getStr("barImg2"));
        wordUtil.createParagraph();
//        JSONArray mediaOpinion = data.getJSONArray("mediaOpinion");
//        for (int i = 0; i < mediaOpinion.size(); i++) {
//            JSONObject object = (JSONObject) mediaOpinion.get(i);
//            String text = object.getStr("value") + "% 的媒体关注“" + object.getStr("name") + "”";
//            if (i < mediaOpinion.size() - 1) {
//                text += "；";
//            } else {
//                text += "。";
//            }
        String mediaOpinion = data.getStr("mediaOpinion");
        if (mediaOpinion.contains("\n")) {
            String[] split = mediaOpinion.split("\\n");
            for (int i = 0; i < split.length; i++) {
                String s = split[i];
                paragraph = wordUtil.createParagraph();
                paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
                paragraph.addText(s, 22, false, false, "arial", "000000");
                if (i < split.length - 1) {
                    wordUtil.createParagraph();
                }
            }
        } else {
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
            paragraph.addText(data.getStr("mediaOpinion"), 22, false, false, "arial", "000000");
        }
        wordUtil.createParagraph();
//        }

        List<EsBean> netizenOpinion = data.getJSONArray("netizenOpinion").toList(EsBean.class);
        addFirstGradeTitle(wordUtil, "网民观点", true, "arial", null);
        getNetizenOpinion(wordUtil, netizenOpinion);
        FileUtil.mkdir("reportFile/");
        File file = FileUtil.file("reportFile/report.docx");
        wordUtil.saveDocument(file);

        InputStream in = FileUtil.getInputStream(file);
        // Word
        try {
            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(data.getStr("title") + ".docx", Constants.UTF8));
        } catch (UnsupportedEncodingException e) {
            e.printStackTrace();
        }
        response.setContentType("application/docx; charset=UTF-8");
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            IOUtils.copy(in, out);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.flush();
                    out.close();
                    if (in != null) {
                        in.close();
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        //PDF
//        try {
//            response.setHeader("Content-disposition", "attachment;filename*=utf-8''" + URLEncoder.encode(data.getStr("title") + ".pdf", Constants.UTF8));
//        } catch (UnsupportedEncodingException e) {
//            e.printStackTrace();
//        }
//        response.setContentType("application/pdf; charset=UTF-8");
//        reportService.changeFileTypeToDownload(in, response, "");
    }

    @Override
    public JSONArray mediaOpinion(SearchVO searchVO) {
        //媒体观点统计  媒体账号为新闻和客户端  按相同发文数量降序 最多5条
        searchVO.setType("1,6");
        searchVO.setSort(7);
        searchVO.setPageNum(1);
        PageResult<EsBean> pageResult = new PageResult<>();
        Plan plan = planService.selectPlanById(searchVO.getPlanId());
        if (plan != null) {
            EsSearchBO bo = convertHandler.copyPropertiesFromPlan(searchVO, plan);
            pageResult = searchService.search(bo);
        }
        List<EsBean> esBeans = pageResult.stream().filter(bean -> !bean.getTitle().contains("转发了")).collect(Collectors.toList());
        if (esBeans.size() > 5) {
            esBeans = esBeans.subList(0, 5);
        }
        int allNum = esBeans.stream().mapToInt(EsBean::getSimilarCount).sum();
        JSONArray array = new JSONArray();
        JSONObject object;
        for (EsBean bean : esBeans) {
            object = JSONUtil.createObj();
            object.putOnce("name", HtmlUtil.cleanHtmlTag(bean.getTitle()));
            DecimalFormat df = new DecimalFormat("0.00");
            object.putOnce("value", df.format((double) bean.getSimilarCount() * 100 / allNum));
            if (array.size() < 5) {
                array.add(object);
            }
        }
        return array;
    }

    @Override
    public List<EsBean> netizenOpinion(SearchVO searchVO) {
        //网民观点 微博意见领袖
        searchVO.setType("3");
        searchVO.setPageNum(1);
        searchVO.setSort(12);
        Plan plan = planService.selectPlanById(searchVO.getPlanId());
        if (plan != null) {
            EsSearchBO bo = convertHandler.copyPropertiesFromPlan(searchVO, plan);
            return EsSearchUtil.searchEsBeanList(bo);
//            return EsSearchUtil.netizenOpinion(bo);
        }
        return new ArrayList<>();
    }


    public Hot getHot(List<Hot> hot, String text) {
        for (Hot hot1 : hot) {
            if (hot1.getTitle().equals(text)) {
                return hot1;
            }
        }
        return null;
    }

    /**
     * 添加图片，设置段落居中
     *
     * <AUTHOR>
     * @date 2017-06-12 上午10:58:12
     */
    private void addEchartsPicture(POIWordUtil wordUtil, String echartsPicture) {
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);
        paragraph.addEchartsPicture(echartsPicture);
    }

    private XWPFTable createTable(POIWordUtil wordUtil, int rows, int cols) {
        //创建表格
        XWPFTable table = wordUtil.createTable(rows, cols);
        CTTblPr tblPr = table.getCTTbl().getTblPr();
        tblPr.getTblW().setType(STTblWidth.DXA);
        tblPr.getTblW().setW(new BigInteger("8100"));
        CTString styleStr = tblPr.addNewTblStyle();
        styleStr.setVal("StyledTable");
//        表格边框
        CTTblBorders borders = table.getCTTbl().getTblPr().addNewTblBorders();
        CTBorder hBorder = borders.addNewInsideH();
        hBorder.setVal(STBorder.SINGLE);
        hBorder.setSz(new BigInteger("1"));
        hBorder.setColor("c8c8c8");
        CTBorder vBorder = borders.addNewInsideV();
        vBorder.setVal(STBorder.SINGLE);
        vBorder.setSz(new BigInteger("1"));
        vBorder.setColor("c8c8c8");
        POIWordUtil.tableBorderColor(borders, "c8c8c8", "c8c8c8", "c8c8c8", "c8c8c8");
        table.getCTTbl().getTblPr().addNewJc().setVal(STJc.CENTER); // 表格水平居中
        return table;
    }

    private Integer createRowCell(POIWordUtil wordUtil, XWPFTableRow titleRow, int col, String text, int width) {
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();  //列中使用段落
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText(text, 20, false, false, null, null);
        CTTcPr tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        CTTblWidth cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(width));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());
        return col;
    }

    private void getFirstRelease(POIWordUtil wordUtil, List<EsBean> firstRelease) {
        //计算行数
        int size = firstRelease.size();
        int rows = size + 1; //行数
        int cols = 5;   //列数
        XWPFTable table = createTable(wordUtil, rows, cols);

        int row = 0;
        int col = 0;

        //头部
        XWPFTableRow titleRow = table.getRow(row);  //当前行
        titleRow.setHeight(600);
//        col = createFirstRowCell(wordUtil, titleRow, col, "类型", 600);
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();  //列中使用段落
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("类型", 20, false, false, null, null);
        CTTcPr tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        CTTblWidth cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(600));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("来源", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(1000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("作者", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(1600));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("时间", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(1600));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("标题", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(3300));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        //表格数据
        for (int i = 0; i < firstRelease.size(); i++) {
            EsBean bean = firstRelease.get(i);
            row = ++row;//新起一行
            col = 0;    //列从1开始
            XWPFTableRow thisRow = table.getRow(row);  //当前行
            thisRow.setHeight(300);

            //类型
            int index = i + 1;
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getTypeName(), 20, false, false, null, null);
            CTTcPr ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            CTTblWidth ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(600));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            //来源
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getHost(), 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(1000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            //作者
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getAuthor(), 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(1600));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            //时间
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            String data = DateUtil.format(DateUtil.parse(DateUtil.format(bean.getPublishTime(), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN), DatePattern.CHINESE_DATE_PATTERN);
            paragraph.addText(data, 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(1600));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            //标题
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            String title = HtmlUtil.cleanHtmlTag(bean.getTitle()).trim();
            if (title.equals("转发微博")) {
                title = HtmlUtil.cleanHtmlTag(bean.getText()).trim();
            }
            if (title.length() > 16) {
                title = title.substring(0, 16);
            }
            String encodedString = bean.getUrl();
            try {
                if (!encodedString.startsWith("https")) {
                    encodedString = URLEncoder.encode(bean.getUrl(), StandardCharsets.UTF_8.toString());
                }
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            paragraph.addHyperlink(title, 20, encodedString);
//            paragraph.addText(title, 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(3300));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());
        }
        table.getCTTbl().getTblPr().addNewJc().setVal(STJc.CENTER); // 表格水平居中
    }

    private void hotsearch(POIWordUtil wordUtil, LinkedHashMap hotsearch) {
        JSONArray hotWords = (JSONArray) hotsearch.get("hotWords");
        for (Object hotWord : hotWords) {
            JSONObject object = (JSONObject) hotWord;
            if (!object.getStr("typeName").equals("全平台")) {
                continue;
            }
            JSONArray datas = object.getJSONArray("data");
            //计算行数
            int size = datas.size();
            int rows = size + 1; //行数
            int cols = 4;   //列数

            XWPFTable table = createTable(wordUtil, rows, cols);

            int row = 0;
            int col = 0;

            //标题
            XWPFTableRow titleRow = table.getRow(row);  //当前行
            titleRow.setHeight(600);
            CustomXWPFParagraph paragraph = wordUtil.createParagraph();  //列中使用段落
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText("标题", 20, false, false, null, null);
            CTTcPr tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
            CTTblWidth cellw = tcpr.addNewTcW();
            cellw.setType(STTblWidth.DXA);
            cellw.setW(BigInteger.valueOf(3600));
            titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
            titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText("当前排名", 20, false, false, null, null);
            tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
            cellw = tcpr.addNewTcW();
            cellw.setType(STTblWidth.DXA);
            cellw.setW(BigInteger.valueOf(1500));
            titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
            titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText("热度", 20, false, false, null, null);
            tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
            cellw = tcpr.addNewTcW();
            cellw.setType(STTblWidth.DXA);
            cellw.setW(BigInteger.valueOf(1500));
            titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
            titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText("平台", 20, false, false, null, null);
            tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
            cellw = tcpr.addNewTcW();
            cellw.setType(STTblWidth.DXA);
            cellw.setW(BigInteger.valueOf(1500));
            titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
            titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            //表格数据
            for (int i = 0; i < datas.size(); i++) {
                JSONObject bean = (JSONObject) datas.get(i);
                row = ++row;//新起一行
                col = 0;    //列从1开始
                XWPFTableRow thisRow = table.getRow(row);  //当前行
                thisRow.setHeight(300);

                //类型
                int index = i + 1;
                paragraph = wordUtil.createParagraph();
                paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
                paragraph.addText(bean.getStr("title"), 20, false, false, null, null);
                CTTcPr ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
                CTTblWidth ctTblWidth = ctTcPr.addNewTcW();
                ctTblWidth.setW(BigInteger.valueOf(3600));
                thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
                thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
                thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

                //来源
                paragraph = wordUtil.createParagraph();
                paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
                paragraph.addText(bean.getStr("sort"), 20, false, false, null, null);
                ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
                ctTblWidth = ctTcPr.addNewTcW();
                ctTblWidth.setW(BigInteger.valueOf(1500));
                thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
                thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
                thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

                //作者
                paragraph = wordUtil.createParagraph();
                paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
                paragraph.addText(bean.getStr("indexNum"), 20, false, false, null, null);
                ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
                ctTblWidth = ctTcPr.addNewTcW();
                ctTblWidth.setW(BigInteger.valueOf(1500));
                thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
                thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
                thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

                //时间
                paragraph = wordUtil.createParagraph();
                paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
                paragraph.addText(bean.getStr("type"), 20, false, false, null, null);
                ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
                ctTblWidth = ctTcPr.addNewTcW();
                ctTblWidth.setW(BigInteger.valueOf(1500));
                thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
                thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
                thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
                POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());
            }
            table.getCTTbl().getTblPr().addNewJc().setVal(STJc.CENTER); // 表格水平居中
        }

    }

    private void getMediaLevel(POIWordUtil wordUtil, List<Object> jsonArray) {
        //计算行数
        int size = jsonArray.size();
        int rows = size + 1; //行数
        int cols = 3;   //列数

        XWPFTable table = createTable(wordUtil, rows, cols);

        int row = 0;
        int col = 0;

        //排名
        XWPFTableRow titleRow = table.getRow(row);  //当前行
        titleRow.setHeight(600);
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();  //列中使用段落
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("排名", 20, false, false, null, null);
        CTTcPr tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        CTTblWidth cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
//        cellw.setW(BigInteger.valueOf(1100));
        cellw.setW(BigInteger.valueOf(1500));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("媒体网站", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
//        cellw.setW(BigInteger.valueOf(2000));
        cellw.setW(BigInteger.valueOf(4000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("信息量", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
//        cellw.setW(BigInteger.valueOf(1500));
        cellw.setW(BigInteger.valueOf(2600));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());


        //表格数据
        for (int i = 0; i < jsonArray.size(); i++) {
            JSONObject bean = (JSONObject) jsonArray.get(i);
            row = ++row;//新起一行
            col = 0;    //列从1开始
            XWPFTableRow thisRow = table.getRow(row);  //当前行
            thisRow.setHeight(300);

            int index = i + 1;
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(String.valueOf(index), 20, false, false, null, null);
            CTTcPr ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            CTTblWidth ctTblWidth = ctTcPr.addNewTcW();
//            ctTblWidth.setW(BigInteger.valueOf(1100));
            ctTblWidth.setW(BigInteger.valueOf(1500));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

//            paragraph = wordUtil.createParagraph();
//            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
//            paragraph.addText(bean.getStr("siteName"), 20, false, false, null, null);
//            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
//            ctTblWidth = ctTcPr.addNewTcW();
//            ctTblWidth.setW(BigInteger.valueOf(2000));
//            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
//            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
//            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
//            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());
//
//            paragraph = wordUtil.createParagraph();
//            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
//            paragraph.addText(bean.getStr("siteNum"), 20, false, false, null, null);
//            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
//            ctTblWidth = ctTcPr.addNewTcW();
//            ctTblWidth.setW(BigInteger.valueOf(1500));
//            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
//            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
//            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
//            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getStr("mediaName"), 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
//            ctTblWidth.setW(BigInteger.valueOf(2000));
            ctTblWidth.setW(BigInteger.valueOf(4000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getStr("mediaNum"), 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
//            ctTblWidth.setW(BigInteger.valueOf(1500));
            ctTblWidth.setW(BigInteger.valueOf(2600));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());
        }
        table.getCTTbl().getTblPr().addNewJc().setVal(STJc.CENTER); // 表格水平居中
    }

    private void getNegativeTop(POIWordUtil wordUtil, List<EsBean> emotionTop) {
        //计算行数
        int size = emotionTop.size();
        int rows = size + 1; //行数
        int cols = 4;   //列数

        XWPFTable table = createTable(wordUtil, rows, cols);

        int row = 0;
        int col = 0;

        //排名
        XWPFTableRow titleRow = table.getRow(row);  //当前行
        titleRow.setHeight(600);
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();  //列中使用段落
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("排名", 20, false, false, null, null);
        CTTcPr tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        CTTblWidth cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(1000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("标题", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(3600));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("发布时间", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(2000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("相似文章", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(1500));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        //表格数据
        for (int i = 0; i < emotionTop.size(); i++) {
            EsBean bean = emotionTop.get(i);
            row = ++row;//新起一行
            col = 0;    //列从1开始
            XWPFTableRow thisRow = table.getRow(row);  //当前行
            thisRow.setHeight(300);

            int index = i + 1;
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(String.valueOf(index), 20, false, false, null, null);
            CTTcPr ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            CTTblWidth ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(1000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            String title = HtmlUtil.cleanHtmlTag(bean.getTitle()).trim();
            if (title.equals("转发微博")) {
                title = HtmlUtil.cleanHtmlTag(bean.getText()).trim();
            }
            if (title.length() > 20) {
                title = title.substring(0, 20);
            }
            String encodedString = bean.getUrl();
            try {
                if (!encodedString.startsWith("https")) {
                    encodedString = URLEncoder.encode(bean.getUrl(), StandardCharsets.UTF_8.toString());
                }
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            paragraph.addHyperlink(title, 20, encodedString);
//            paragraph.addText(title, 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(3600));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            String data = DateUtil.format(DateUtil.parse(DateUtil.format(bean.getPublishTime(), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN), DatePattern.CHINESE_DATE_PATTERN);
            paragraph.addText(data, 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(2000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(String.valueOf(bean.getSimilarCount()), 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(1500));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        }
        table.getCTTbl().getTblPr().addNewJc().setVal(STJc.CENTER); // 表格水平居中
    }

    private void getMediaType(POIWordUtil wordUtil, JSONArray mediaTypes) {
        //计算行数
        int size = mediaTypes.size();
        int rows = size + 1; //行数
        int cols = 3;   //列数

        XWPFTable table = createTable(wordUtil, rows, cols);

        int row = 0;
        int col = 0;

        //排名
        XWPFTableRow titleRow = table.getRow(row);  //当前行
        titleRow.setHeight(600);
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();  //列中使用段落
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("来源", 20, false, false, null, null);
        CTTcPr tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        CTTblWidth cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(2100));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("信息量", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(3000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("占比", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(3000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        //表格数据
        for (int i = 0; i < mediaTypes.size(); i++) {
            JSONObject bean = (JSONObject) mediaTypes.get(i);
            row = ++row;//新起一行
            col = 0;    //列从1开始
            XWPFTableRow thisRow = table.getRow(row);  //当前行
            thisRow.setHeight(300);

            int index = i + 1;
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getStr("name"), 20, false, false, null, null);
            CTTcPr ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            CTTblWidth ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(2100));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getStr("value"), 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(3000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getStr("percent"), 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(3000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        }
        table.getCTTbl().getTblPr().addNewJc().setVal(STJc.CENTER); // 表格水平居中
    }

    private void getWordChart(POIWordUtil wordUtil, List<Object> wordArray) {
        //计算行数
        int size = wordArray.size();
        int rows = size + 1; //行数
        int cols = 3;   //列数

        XWPFTable table = createTable(wordUtil, rows, cols);

        int row = 0;
        int col = 0;

        //排名
        XWPFTableRow titleRow = table.getRow(row);  //当前行
        titleRow.setHeight(600);
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();  //列中使用段落
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("排名", 20, false, false, null, null);
        CTTcPr tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        CTTblWidth cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(2100));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("热词", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(3000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("提及量", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(3000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        //表格数据
        for (int i = 0; i < wordArray.size(); i++) {
            JSONObject bean = (JSONObject) wordArray.get(i);
            row = ++row;//新起一行
            col = 0;    //列从1开始
            XWPFTableRow thisRow = table.getRow(row);  //当前行
            thisRow.setHeight(300);

            int index = i + 1;
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(String.valueOf(index), 20, false, false, null, null);
            CTTcPr ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            CTTblWidth ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(2100));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getStr("name"), 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(3000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getStr("value"), 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(3000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        }
        table.getCTTbl().getTblPr().addNewJc().setVal(STJc.CENTER); // 表格水平居中
    }

    private void getNetizenOpinion(POIWordUtil wordUtil, List<EsBean> netizenOpinion) {
        //计算行数
        int size = netizenOpinion.size();
        int rows = size + 1; //行数
        int cols = 4;   //列数

        XWPFTable table = createTable(wordUtil, rows, cols);

        int row = 0;
        int col = 0;

        //排名
        XWPFTableRow titleRow = table.getRow(row);  //当前行
        titleRow.setHeight(600);
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();  //列中使用段落
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("排名", 20, false, false, null, null);
        CTTcPr tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        CTTblWidth cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(2100));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("作者", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(2000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("标题", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(2000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("发布时间", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(2000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("92cddc");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());


        //表格数据
        for (int i = 0; i < netizenOpinion.size(); i++) {
            EsBean bean = netizenOpinion.get(i);
            row = ++row;//新起一行
            col = 0;    //列从1开始
            XWPFTableRow thisRow = table.getRow(row);  //当前行
            thisRow.setHeight(300);

            int index = i + 1;
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(String.valueOf(index), 20, false, false, null, null);
            CTTcPr ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            CTTblWidth ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(2100));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(bean.getAuthor(), 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(2000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            String title = HtmlUtil.cleanHtmlTag(bean.getTitle()).trim();
            if (title.equals("转发微博")) {
                title = HtmlUtil.cleanHtmlTag(bean.getText()).trim();
            }
            if (title.length() > 20) {
                title = title.substring(0, 20);
            }
            String encodedString = bean.getUrl();
            try {
                if (!encodedString.startsWith("https")) {
                    encodedString = URLEncoder.encode(bean.getUrl(), StandardCharsets.UTF_8.toString());
                }
            } catch (UnsupportedEncodingException e) {
                e.printStackTrace();
            }
            paragraph.addHyperlink(title, 20, encodedString);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(2000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            String data = DateUtil.format(DateUtil.parse(DateUtil.format(bean.getPublishTime(), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN), DatePattern.CHINESE_DATE_PATTERN);
            paragraph.addText(data, 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(2000));
            thisRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("b7dee8");
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());
        }
        table.getCTTbl().getTblPr().addNewJc().setVal(STJc.CENTER); // 表格水平居中
    }

    private long printRunTime(long time, String name) {
        long runTime = System.currentTimeMillis();
        System.out.println(name + "查询用时：" + (runTime - time) / 1000);
        return runTime;
    }


    @Override
    public void saveFilter(JSONObject searchVO, HttpServletRequest request) {
        String planId = searchVO.getStr("planId");
        AssertX.strNotNull(planId);
//        LoginUser token = tokenService.getLoginUser(request);
//        String phone = redisCache.getCacheObject(RedisConstant.system_prefix + "login:" + token); //TODO 预留获取当前访客的手机号功能
        redisCache.setCacheObject(RedisConstant.system_prefix + "analyse:" + planId, searchVO, 30, TimeUnit.DAYS);
    }

    @Override
    public JSONObject getAnalyseCondition(HttpServletRequest request, String planId) {
        AssertX.strNotNull(planId);
//        LoginUser token = tokenService.getLoginUser(request);
//        String phone = redisCache.getCacheObject(RedisConstant.system_prefix + "login:" + token); //TODO 预留获取当前访客的手机号功能
        return redisCache.getCacheObject(RedisConstant.system_prefix + "analyse:" +/*phone+":"+ token.getToken()+"_"+*/planId);
    }
}
