package com.boryou.web.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.boryou.common.utils.IdUtil;
import com.boryou.web.domain.pg.PgDemo;
import com.boryou.web.mapper.pg.PgDemoMapper;
import com.boryou.web.service.PgDemoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
public class PgDemoServiceImpl implements PgDemoService {

    @Resource
    private PgDemoMapper pgDemoMapper;

    @Override
    public int insertOne(PgDemo pgDemo) {
        Long id = IdUtil.nextLong();
        pgDemo.setId(id);

        LocalDateTime now = LocalDateTime.now();
        pgDemo.setCreateTime(now);
        pgDemo.setIsDel(false);

        return pgDemoMapper.insert(pgDemo);
    }

    @Override
    public List<PgDemo> getList() {
        List<PgDemo> pgDemoList = pgDemoMapper.selectList(new LambdaQueryWrapper<>());
        return pgDemoList;
    }

}
