package com.boryou.web.service;

import cn.hutool.db.PageResult;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.domain.Hot;
import com.boryou.web.domain.msg.MessageVO;
import com.boryou.web.domain.vo.EsBeanVO;
import com.boryou.web.domain.vo.HomeDownVO;
import com.boryou.web.domain.vo.SearchVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface SearchService {

    PageResult<EsBean> search(EsSearchBO bo);

    Integer similarCount(SearchVO searchVO);

    Integer realTimeInfoCount(EsSearchBO bo);

    JSONObject getSearchCriteria(Long planId);

    JSONObject getSearchCriteria(Long userId, Long planId);

    void saveSearchCriteria(JSONObject searchVO);

    boolean sendMsg1(MessageVO messageVO);

    boolean sendMsg(MessageVO messageVO);

    boolean sendWarnMsgDefault(String phone, String p1, String p2, String p3);

    EsSearchBO buildEsSearchBO(HomeDownVO homeDownVO);

    EsBeanVO docDetail(EsSearchBO infoVO);

    JSONArray mediaTypeCount(EsSearchBO bo);

    List<Hot> getSimilarHot(SearchVO searchVO);

    PageResult<EsBean> searchSimilarity(EsSearchBO bo);

    PageResult<EsBean> getSimilarityData(EsSearchBO bo);

    JSONArray accountLevelCount(EsSearchBO bo);

    Integer getRealTimeInfoCount(SearchVO searchVO);

    /**
     * 事件脉络
     *
     * @param searchVO
     * @return
     */
    JSONObject eventContext(EsSearchBO searchVO);

    /**
     * 首发
     */
    List<EsBean> getFirstRelease(SearchVO query);

    /**
     * 相关热文
     *
     * @param query
     * @return
     */
    List<EsBean> relatedHotArticle(SearchVO query);
}
