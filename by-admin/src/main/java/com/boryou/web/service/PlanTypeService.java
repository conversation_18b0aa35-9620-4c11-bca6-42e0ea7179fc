package com.boryou.web.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.domain.PlanType;
import com.boryou.web.domain.vo.PlanTypeVO;
import com.boryou.web.domain.vo.SortVO;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/24 14:00
 */
public interface PlanTypeService extends IService<PlanType> {

    /**
     * 方案类型列表
     *
     * @return
     */
    List<PlanType> planTypeList(String typeName);

    /**
     * 新增方案类型
     *
     * @param planTypeVO
     * @return
     */
    boolean addPlanType(PlanTypeVO planTypeVO);

    /**
     * 更新方案类型
     *
     * @param planTypeVO
     * @return
     */
    boolean updatePlanType(PlanTypeVO planTypeVO);

    /**
     * 删除方案类型
     *
     * @param planTypeVO
     * @return
     */
    boolean delPlanType(PlanTypeVO planTypeVO);

    /**
     * 移动方案类型
     *
     * @param sortVO
     * @return
     */
    boolean movePlanType(SortVO sortVO);

    /**
     * 方案管理-方案分类列表
     *
     * @param typeName
     * @param userId
     * @param pageNum
     * @param pageSize
     * @return
     */
    List<PlanType> manageList(String typeName, Long userId, Integer pageNum, Integer pageSize);
}
