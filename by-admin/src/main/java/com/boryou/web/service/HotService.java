package com.boryou.web.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.domain.*;
import com.boryou.web.domain.vo.CloudWordVO;
import com.boryou.web.domain.vo.HotVO;
import com.boryou.web.mapper.CloudWordMapper;
import com.boryou.web.mapper.CourtNameMapper;
import com.boryou.web.mapper.HotListMapper;
import com.boryou.web.mapper.SearchWordMapper;
import com.boryou.web.mapper.secondary.OutHotListMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 热榜Service接口
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HotService {
    @Resource
    private HotListMapper hotListMapper;
    @Resource
    private OutHotListMapper outHotListMapper;
    @Resource
    private SearchWordMapper searchWordMapper;
    @Resource
    private CloudWordMapper cloudWordMapper;
    @Resource
    private CourtNameMapper courtNameMapper;
    private final RedisCache redisTemplate;

    public List<Hot> selectHotList(HotVO hotVO) {
        Integer type = hotVO.getType();
        List<String> typeList = new ArrayList<>();
        switch (type) {
            case 1:
                typeList.add("100903");
                break;
            case 2:
                typeList.add("100902");
                break;
            case 3:
                typeList.add("100905");
                break;
            case 4:
                typeList.add("100901");
                typeList.add("100902");
                typeList.add("100903");
                typeList.add("100904");
                typeList.add("100905");
                typeList.add("100906");
                typeList.add("100910");
                typeList.add("100911");
                typeList.add("100912");
                typeList.add("100913");
                typeList.add("100914");
                typeList.add("100915");
                typeList.add("100917");
                typeList.add("100918");
                typeList.add("100919");
                typeList.add("100920");
                typeList.add("100921");
                typeList.add("100922");
                typeList.add("100923");
                typeList.add("100924");
                break;
            default:
                break;
        }
        if (CollUtil.isEmpty(typeList)) {
            return Collections.emptyList();
        }
        String userAreaName = hotVO.getUserAreaName();
        Integer count = hotVO.getCount();
        if (count == null || count < 0) {
            count = 0;
        }
        if (count > 30) {
            count = 30;
        }
        return hotListMapper.selectHotList(typeList, userAreaName, count);
    }

    public List<String> selectHotWord(HotVO hotVO) {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        Integer count = hotVO.getCount();
        if (count == null || count <= 0) {
            count = 0;
        }
        if (count > 30) {
            count = 30;
        }
        List<SearchWord> searchWords = searchWordMapper.selectHotWordByUserIdCount(userId, count);
        return searchWords.stream().map(SearchWord::getWord).collect(Collectors.toList());
    }

    @Transactional(rollbackFor = Exception.class)
    public int addWord(String word) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        SearchWord searchWord = searchWordMapper.selectHotWordByUserIdWord(userId, word);
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        if (BeanUtil.isEmpty(searchWord)) {
            //新增
            searchWord = new SearchWord();
            searchWord.setId(IdUtil.getSnowflakeNextId());
            searchWord.setWord(word);
            searchWord.setNum(1L);
            searchWord.setUserId(userId);
            searchWord.setCreateTime(date);
            searchWord.setCreateBy(userName);
            searchWord.setUpdateTime(date);
            searchWord.setUpdateBy(userName);
            return searchWordMapper.addWord(searchWord);
        } else {
            //更新
            SearchWord searchWordUpdate = new SearchWord();
            Long num = searchWord.getNum();
            Long id = searchWord.getId();
            searchWordUpdate.setId(id);
            searchWordUpdate.setNum(num + 1);
            searchWordUpdate.setUpdateTime(date);
            searchWordUpdate.setUpdateBy(userName);
            return searchWordMapper.updateWordById(searchWordUpdate);
        }
    }

    @Transactional(rollbackFor = Exception.class)
    public int addCloudWord(CloudWordVO wordVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        CloudWordVO check = new CloudWordVO();
        check.setWord(wordVO.getWord());
        if (wordVO.getPlanId() == null) {
            //新增时没有planId  只判断数据概览
            check.setIsPlan(1);
        } else {
            check.setPlanId(wordVO.getPlanId());
        }
        check.setUserId(userId);
        List<CloudWordVO> searchWord = cloudWordMapper.selectHotWords(check);
        if (CollUtil.isNotEmpty(searchWord)) {
            return updateWordStateByIds(searchWord.get(0).getId().toString(), "1");
        }
        //新增
        wordVO.setUserId(userId);
        wordVO.setId(IdUtil.getSnowflakeNextId());
        wordVO.setState("1");
        wordVO.setCreateTime(date);
        wordVO.setCreateBy(userName);
        wordVO.setUpdateTime(date);
        wordVO.setUpdateBy(userName);
        return cloudWordMapper.insertCloudWord(wordVO);
    }

    @Transactional(rollbackFor = Exception.class)
    public int updateWordStateByIds(String ids, String state) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        CloudWordVO wordUpdate = new CloudWordVO();
        wordUpdate.setIds(ids);
        wordUpdate.setUserId(userId);
        wordUpdate.setState(state);
        wordUpdate.setUpdateTime(date);
        wordUpdate.setUpdateBy(userName);
        return cloudWordMapper.updateWordStateByIds(wordUpdate);
    }

    public List<CloudWordVO> cloudWordList(CloudWordVO vo) {
        try {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            if (user != null) {
                vo.setUserId(user.getUserId());
            }
        } catch (CustomException e) {
//            log.error("cloudWordList首页缓存获取用户信息异常");
        }
        if (vo.getIsPlan() != null && vo.getIsPlan() == 1) {
            vo.setPlanId(null);
        }
        return cloudWordMapper.selectHotWords(vo);
    }

    public List<String> filterCloudWordList() {
        CloudWordVO vo = new CloudWordVO();
        vo.setState("1");
        List<CloudWordVO> voList = cloudWordList(vo);
        if (CollUtil.isEmpty(voList)) {
            return new ArrayList<>();
        }
        return voList.stream().map(CloudWordVO::getWord).collect(Collectors.toList());
    }

    public List<String> selectCourtNames() {
        List<String> courtNames = redisTemplate.getCacheList(RedisConstant.court_prefix);
        if (CollUtil.isEmpty(courtNames)) {
            courtNames = courtNameMapper.selectCourtNames();
            redisTemplate.setCacheList(RedisConstant.court_prefix, courtNames);
        }
        return courtNames;
    }

    public Map<String, Object> listForZhzl(Integer count) {
        //AREA_BELLWETHER百度地域风向标,  WB_REALTIME微博实时榜， WB_NEWS微博要闻，DOUYIN_HOT抖音热点榜，DOUYIN_VIDEO抖音视频榜，TOUTIAO_HOT今日头条热点榜
        List<Hot> weibo = hotListMapper.selectHotTypeList("WB_REALTIME", count);
        List<Hot> douyin = hotListMapper.selectHotTypeList("DOUYIN_HOT", count);

        HotVO hotVO = new HotVO();
        hotVO.setType(4);
        hotVO.setCount(count);
        List<Hot> legal = this.selectHotList(hotVO);

        Map<String, Object> res = new HashMap<>();
        res.put("weibo", weibo);
        res.put("douyin", douyin);
        res.put("legal", legal);
        return res;
    }

    public List<OutHot> selectOutHotList(List<String> typeList, List<String> keyword, String time) {
        return outHotListMapper.selectHotList(typeList, keyword, time);
    }

    public List<ApidemicAnnouncement> selectOutAnnouncementList(String time) {
        return hotListMapper.selectOutAnnouncementList(time);
    }

    public List<KeywordIndex> selectOutKeywordIndexList(String time) {
        return hotListMapper.selectOutKeywordIndexList(time);
    }

}
