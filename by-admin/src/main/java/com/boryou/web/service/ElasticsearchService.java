package com.boryou.web.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONObject;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.aggregations.*;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import co.elastic.clients.elasticsearch.core.search.HitsMetadata;
import co.elastic.clients.elasticsearch.core.search.TrackHits;
import co.elastic.clients.json.JsonData;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.RedisUtil;
import com.boryou.web.constant.ElasticsearchIndex;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.domain.AccountInfo;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.module.source.vo.SourceVO;
import com.boryou.web.task.CommonDataTask;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ElasticsearchService {
    @Resource
    private ElasticsearchClient esClient;
    @Resource
    private RedisUtil redisUtil;
    @Resource
    private AccountInfoService accountInfoService;

    public List<AccountInfoVO> getNameByHosts(List<String> hostList) {
        if (CollUtil.isEmpty(hostList)) {
            return Collections.emptyList();
        }
        long start = System.currentTimeMillis();
        final String hostAgg = "hostAgg";
        final String topAgg = "topAgg";
        List<AccountInfoVO> results = new ArrayList<>();
        try {
            List<FieldValue> list = hostList.stream()
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
            Query terms = QueryBuilders.terms(t -> t.field("domain").terms(q -> q.value(list)));
            TrackHits.Builder trackHits = new TrackHits.Builder();
            trackHits.enabled(true);
            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO) // 确保这里是你的ES索引名
                    .query(terms)
                    .aggregations(hostAgg, a -> a.terms(t -> t.field("domain").size(hostList.size()))
                            .aggregations(topAgg, top -> top.topHits(t -> t.size(1))))
                    .size(0)
                    .trackTotalHits(trackHits.build())
                    .build();
            log.warn("searchRequest: {}", searchRequest);
            long end = System.currentTimeMillis();
            double passedTime = (end - start) / 1000.0;
            if (passedTime > 5) {
                log.warn("项目:博约舆情,{},查询耗时{}秒", searchRequest, passedTime);
            }
            SearchResponse<AccountInfoVO> response = esClient.search(searchRequest, AccountInfoVO.class);
            if (response != null) {
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate1 = aggregations.get(hostAgg);
                List<StringTermsBucket> array1 = ((StringTermsAggregate) aggregate1._get()).buckets().array();
                for (StringTermsBucket bucket : array1) {
                    HitsMetadata<JsonData> hits = ((TopHitsAggregate) bucket.aggregations().get(topAgg)._get()).hits();
                    for (Hit<JsonData> hit : hits.hits()) {
                        if (hit.source() != null) {
                            results.add(hit.source().to(AccountInfoVO.class));
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error("getNameByHosts失败, id: {}", hostList);
            throw new CustomException("'查询失败'");
        }
        return results;
    }

    public Map<String, String> getDomainMap(List<String> hostList) {
        Map<String, String> hostAndNameMap = new HashMap<>();
        List<AccountInfoVO> accountInfoVOList = getNameByHosts(hostList);
        if (CollUtil.isNotEmpty(accountInfoVOList)) {
            hostAndNameMap = accountInfoVOList.stream().filter(item -> CharSequenceUtil.isNotBlank(item.getSector()) && CharSequenceUtil.isNotBlank(item.getDomain()))
                    .distinct().collect(Collectors.toMap(AccountInfoVO::getDomain, AccountInfoVO::getSector, (oldValue, newValue) -> oldValue));
        }
        return hostAndNameMap;
    }

    public List<AccountInfoVO> getNameByHosts(Map<Integer, List<String>> map) {
        if (CollUtil.isEmpty(map)) {
            return Collections.emptyList();
        }
        List<Integer> uniqueHostMediaType = CommonDataTask.UNIQUE_HOST_MEDIA_TYPE_LIST.stream().map(MediaTypeEnum::getValue).collect(Collectors.toList());
        List<AccountInfoVO> results = new ArrayList<>();
        Set<Map.Entry<Integer, List<String>>> entries = map.entrySet();
        for (Map.Entry<Integer, List<String>> entry : entries) {
            if (uniqueHostMediaType.contains(entry.getKey())) {
                List<String> collect = entry.getValue();
                //查ES
                List<FieldValue> list = collect.stream()
                        .map(FieldValue::of)
                        .collect(Collectors.toList());
                BoolQuery.Builder bool = QueryBuilders.bool();
                Query terms = QueryBuilders.terms(t -> t.field("domain").terms(q -> q.value(list)));
                bool.filter(terms);
                Query typeTerm = QueryBuilders.term(t -> t.field("type").value(entry.getKey()));
                bool.filter(typeTerm);
                SearchRequest searchRequest = new SearchRequest.Builder()
                        .index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO)
                        .query(bool.build()._toQuery())
                        .size(collect.size()).build();
                try {
                    SearchResponse<AccountInfoVO> response = esClient.search(searchRequest, AccountInfoVO.class);
                    List<Hit<AccountInfoVO>> hits = response.hits().hits();
                    for (Hit<AccountInfoVO> hit : hits) {
                        results.add(hit.source());
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }

            } else {
                //查缓存
                List<String> list = entry.getValue();
                for (String host : list) {
                    String key = CommonDataTask.concatAccountInfoKey(String.valueOf(entry.getKey()), host);
                    String strValue = redisUtil.get(key);
                    if (CharSequenceUtil.isNotEmpty(strValue)) {
                        AccountInfoVO accountInfoVO = new AccountInfoVO();
                        accountInfoVO.setDomain(host);
                        accountInfoVO.setSector(strValue);
                        results.add(accountInfoVO);
                    } else {
                        LambdaQueryWrapper<AccountInfo> lambdaQueryWrapper = new LambdaQueryWrapper<>();
                        lambdaQueryWrapper.eq(AccountInfo::getHost, host).eq(AccountInfo::getType, entry.getKey());
                        AccountInfo one = accountInfoService.getOne(lambdaQueryWrapper);
                        if (one != null) {
                            AccountInfoVO accountInfoVO = new AccountInfoVO();
                            accountInfoVO.setDomain(host);
                            accountInfoVO.setSector(one.getName());
                            results.add(accountInfoVO);
                        }

                    }
                }
            }
        }
        return results;
    }

    //用于其他系统缓存
    public List<AccountInfoVO> getAllAccountByType(Integer value, AccountInfo one) {
        String aggKey = "hostAgg";
        BoolQuery.Builder bool = QueryBuilders.bool();
        Query termQuery = QueryBuilders.term(t -> t.field("type").value(value));
        bool.must(termQuery);
        if (one != null) {
            Date updateTime = one.getUpdateTime();
            Query rangeQuery = QueryBuilders.range(r -> r.field("updateTime").from(DateUtil.format(updateTime, DatePattern.NORM_DATETIME_PATTERN)));
            bool.must(rangeQuery);
        }
        Aggregation aggregation = AggregationBuilders.terms(t -> t.field("domain").size(50000));
        SearchRequest searchRequest = new SearchRequest.Builder()
                .index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO)
                .query(bool.build()._toQuery())
                .aggregations(aggKey, aggregation)
                .size(0)
                .build();

        try {
            SearchResponse<Long> response = esClient.search(searchRequest, Long.class);
            if (response != null) {
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate = aggregations.get(aggKey);
                StringTermsAggregate stringTermsAggregate = (StringTermsAggregate) aggregate._get();
                List<StringTermsBucket> array = stringTermsAggregate.buckets().array();
                List<String> hostList = new ArrayList<>();
                for (StringTermsBucket stringTermsBucket : array) {
                    String host = stringTermsBucket.key().stringValue();
                    hostList.add(host);
                }
                return getNameByHostsFromEs(hostList, value);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return Collections.emptyList();
    }

    public List<AccountInfoVO> getNameByHostsFromEs(List<String> hostList, Integer type) {
        if (CollUtil.isEmpty(hostList)) {
            return Collections.emptyList();
        }
        final String hostAgg = "hostAgg";
        final String topAgg = "topAgg";
        List<AccountInfoVO> results = new ArrayList<>();
        try {
            List<FieldValue> list = hostList.stream()
                    .map(FieldValue::of)
                    .collect(Collectors.toList());
            BoolQuery.Builder bool = QueryBuilders.bool();
            Query terms = QueryBuilders.terms(t -> t.field("domain").terms(q -> q.value(list)));
            bool.filter(terms);
            if (type != null) {
                Query typeTerm = QueryBuilders.term(t -> t.field("type").value(type));
                bool.filter(typeTerm);
            }
            TrackHits.Builder trackHits = new TrackHits.Builder();
            trackHits.enabled(true);
            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO) // 确保这里是你的ES索引名
                    .query(bool.build()._toQuery())
                    .aggregations(hostAgg, a -> a.terms(t -> t.field("domain").size(hostList.size()))
                            .aggregations(topAgg, top -> top.topHits(t -> t.size(1))))
                    .size(0)
                    .trackTotalHits(trackHits.build())
                    .build();
            SearchResponse<AccountInfoVO> response = esClient.search(searchRequest, AccountInfoVO.class);
            if (response != null) {
                Map<String, Aggregate> aggregations = response.aggregations();
                Aggregate aggregate1 = aggregations.get(hostAgg);
                List<StringTermsBucket> array1 = ((StringTermsAggregate) aggregate1._get()).buckets().array();
                for (StringTermsBucket bucket : array1) {
                    HitsMetadata<JsonData> hits = ((TopHitsAggregate) bucket.aggregations().get(topAgg)._get()).hits();
                    for (Hit<JsonData> hit : hits.hits()) {
                        if (hit.source() != null) {
                            results.add(hit.source().to(AccountInfoVO.class));
                        }
                    }
                }
            }
        } catch (IOException e) {
            log.error("getNameByHosts失败, id: {}", hostList);
            throw new CustomException("'查询失败'");
        }
        return results;
    }


    public AccountInfoVO getNameByHost(String host) {
        if (CharSequenceUtil.isBlank(host)) {
            return null;
        }
        try {
            Query term = QueryBuilders.term(t -> t.field("domain").value(FieldValue.of(host)));
            TrackHits.Builder trackHits = new TrackHits.Builder();
            trackHits.enabled(true);
            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO) // 确保这里是你的ES索引名
                    .query(term)
                    .size(1)
                    .trackTotalHits(trackHits.build())
                    .build();
            log.debug("getNameByHost的searchRequest: {}", searchRequest);
            SearchResponse<AccountInfoVO> response = esClient.search(searchRequest, AccountInfoVO.class);
            if (response != null) {
                for (Hit<AccountInfoVO> hit : response.hits().hits()) {
                    AccountInfoVO source = hit.source();
                    if (source != null) {
                        return source;
                    }
                }
            }
        } catch (IOException e) {
            log.error("getNameByHost失败, id: {}", host);
            throw new CustomException("'查询失败'");
        }
        return null;
    }

    public PageResult<AccountInfoVO> searchSourceByType(JSONObject object) {
        SourceVO sourceVO = new SourceVO();
        sourceVO.setDomain(object.getStr("domain"));
        sourceVO.setNickname(object.getStr("nickname"));
        sourceVO.setType(object.getInt("type"));
        sourceVO.setPageNum(object.getInt("pageIndex"));
        sourceVO.setPageSize(object.getInt("pageSize"));
        return searchSourceByType(sourceVO);
    }

    public PageResult<AccountInfoVO> searchSourceByType(SourceVO accountInfoVO) {
        PageResult<AccountInfoVO> accountInfoVOS = new PageResult<>();
        if (ObjectUtil.isEmpty(accountInfoVO)) {
            return accountInfoVOS;
        }
        try {
            BoolQuery.Builder query = QueryBuilders.bool();
            ArrayList<Query> list = new ArrayList<>();
            if (StrUtil.isNotEmpty(accountInfoVO.getDomain())) {
                list.add(QueryBuilders.terms(t -> t.field("domain").
                        terms(q -> q.value(StrUtil.splitTrim(accountInfoVO.getDomain(), ",").stream().map(FieldValue::of).collect(Collectors.toList())))));
            }
            if (StrUtil.isNotEmpty(accountInfoVO.getNickname())) {
                if (accountInfoVO.getPrecision() == 0) {
                    list.add(QueryBuilders.matchPhrase(s -> s.field("nickname").query("*" + accountInfoVO.getNickname() + "*")));
                } else {
                    list.add(QueryBuilders.term(s -> s.field("nickname").value(accountInfoVO.getNickname())));
                }
            }
            list.add(QueryBuilders.term(s -> s.field("type").value(accountInfoVO.getType())));
            list.add(QueryBuilders.exists(s -> s.field("domain")));
            query.must(list);

            TrackHits.Builder trackHits = new TrackHits.Builder();
            trackHits.enabled(true);
            SearchRequest searchRequest = new SearchRequest.Builder()
                    .index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO) // 确保这里是你的ES索引名
                    .query(query.build()._toQuery())
                    .from(((accountInfoVO.getPageNum() - 1)) * accountInfoVO.getPageSize())
                    .size(accountInfoVO.getPageSize())
                    .trackTotalHits(trackHits.build())
                    .build();
            log.warn("searchRequest: {}", searchRequest);
            SearchResponse<AccountInfoVO> response = esClient.search(searchRequest, AccountInfoVO.class);
            if (response != null) {
                List<AccountInfoVO> collect = response.hits().hits().stream().map(Hit::source).collect(Collectors.toList());
                accountInfoVOS.setTotal((int) Objects.requireNonNull(response.hits().total()).value());
                accountInfoVOS.addAll(collect);
                return accountInfoVOS;
            }
        } catch (IOException e) {
            throw new CustomException("'查询失败'");
        }
        return accountInfoVOS;
    }

}
