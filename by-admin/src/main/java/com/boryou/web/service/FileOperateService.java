package com.boryou.web.service;

import cn.hutool.core.text.CharSequenceUtil;
import com.boryou.common.exception.CustomException;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.hwpf.extractor.WordExtractor;
import org.apache.poi.openxml4j.util.ZipSecureFile;
import org.apache.poi.xwpf.usermodel.XWPFDocument;
import org.apache.poi.xwpf.usermodel.XWPFParagraph;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import java.io.InputStream;
import java.util.List;

@Service
@Slf4j
public class FileOperateService {

    public String importDoc(MultipartFile file) {
        String text = "";
        String name = "";
        try {
            // 获取文件后缀名
            String fileName = file.getOriginalFilename();
            if (CharSequenceUtil.isBlank(fileName)) {
                throw new CustomException("文件名不能为空");
            }
            if (fileName.lastIndexOf(".") != -1 && fileName.lastIndexOf(".") != 0) {
                name = fileName.substring(fileName.lastIndexOf(".") + 1);
            }
            InputStream inputStream = file.getInputStream();

            // 非utf-8编码，进行转码
            ZipSecureFile.setMinInflateRatio(-1.0d);
            if (name.contains("docx")) {
                text = this.getContentDocx(inputStream);
                if (CharSequenceUtil.isBlank(text)) {
                    text = this.getContentDoc(inputStream);
                }
            } else if (name.contains("doc")) {
                text = this.getContentDoc(inputStream);
                if (CharSequenceUtil.isBlank(text)) {
                    text = this.getContentDocx(inputStream);
                }
            }
            inputStream.close();
            log.warn("文件名为: {}, 转换文本为: {}", fileName, text);
        } catch (Exception e) {
            log.error("上传失败: {}", e.getMessage(), e);
            throw new CustomException("上传失败");
        }
        return text;
    }

    /**
     * 获取正文文件内容，docx方法
     *
     * @param file
     * @return
     */
    public String getContentDocx(InputStream is) {
        StringBuilder content = new StringBuilder();
        try (XWPFDocument xwpf = new XWPFDocument(is)) {
            // 2007版本的word
            // 2007版本，仅支持docx文件处理
            List<XWPFParagraph> paragraphs = xwpf.getParagraphs();
            if (!paragraphs.isEmpty()) {
                for (XWPFParagraph paragraph : paragraphs) {
                    if (!paragraph.getParagraphText().startsWith("    ")) {
                        content.append(paragraph.getParagraphText().trim()).append("\n\t");
                    } else {
                        content.append(paragraph.getParagraphText());
                    }
                }
            }
        } catch (Exception e) {
            log.error("docx解析正文异常:{}", e.getMessage(), e);
        }
        return content.toString();
    }

    /**
     * 获取正文文件内容，doc方法
     *
     * @param path
     * @return
     */
    public String getContentDoc(InputStream is) {
        StringBuilder content = new StringBuilder();
        try (WordExtractor extractor = new WordExtractor(is)) {
            // 2003版本的word
            // 2003版本 仅doc格式文件可处理，docx文件不可处理
            String[] paragraphText = extractor.getParagraphText();   // 获取段落，段落缩进无法获取，可以在前添加空格填充
            if (paragraphText != null) {
                for (String paragraph : paragraphText) {
                    if (!paragraph.startsWith("    ")) {
                        content.append(paragraph.trim()).append("\n\t");
                    } else {
                        content.append(paragraph);
                    }
                }
            }
        } catch (Exception e) {
            log.error("doc解析正文异常: {}", e.getMessage(), e);
        }
        return content.toString();
    }

}
