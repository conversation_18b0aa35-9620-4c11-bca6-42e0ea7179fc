package com.boryou.web.service;

import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.domain.vo.GraphModelVO;
import com.boryou.web.domain.vo.SearchVO;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.util.LinkedHashMap;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface SearchAnalyseService {

    JSONObject getEmotionAnalyse(EsSearchBO bo);

    JSONArray mediaTypeAnalyse(EsSearchBO bo);

    JSONArray wordAnalyse(EsSearchBO bo, Long planId);

    JSONArray mediaActiveMap(EsSearchBO bo);

    JSONArray areaMap(EsSearchBO bo);

    GraphModelVO timeType(EsSearchBO bo);

    GraphModelVO timeEmotion(EsSearchBO bo);

    JSONObject mediaLevel(EsSearchBO bo);

    JSONObject mediaCentral(EsSearchBO bo);


    LinkedHashMap hotsearch(SearchVO searchVO);

    void createReport(HttpServletResponse response, SearchVO searchVO);

    void updateReport(HttpServletResponse response, SearchVO searchVO);

    JSONArray mediaOpinion(SearchVO searchVO);

    List<EsBean> netizenOpinion(SearchVO searchVO);

    void saveFilter(JSONObject searchVO, HttpServletRequest request);

    JSONObject getAnalyseCondition(HttpServletRequest request, String planId);
}
