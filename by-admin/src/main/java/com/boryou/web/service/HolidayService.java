package com.boryou.web.service;


import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.web.domain.Holiday;
import com.boryou.web.mapper.HolidayMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Service
public class HolidayService extends ServiceImpl<HolidayMapper, Holiday> {

    public boolean saveHoliday() {
        //2024年
        String s = HttpUtil.get("https://timor.tech/api/holiday/year/2024");
        JSONObject entries = JSONUtil.parseObj(s);
        Object holiday1 = entries.get("holiday");
        JSONObject entries1 = JSONUtil.parseObj(holiday1);
        List<Holiday> holidayList = new ArrayList<>();
        entries1.forEach((k, v) -> {
            JSONObject entries2 = JSONUtil.parseObj(v);
            Object holiday = entries2.get("holiday");
            Object name = entries2.get("name");
            Object wage = entries2.get("wage");
            Object after = entries2.get("after");
            Object target = entries2.get("target");
            Object date = entries2.get("date");
            Object rest = entries2.get("rest");
            Holiday holiday2 = new Holiday();
            holiday2.setDay((String) date);
            holiday2.setHolidayFlag((boolean) holiday ? 1 : 0);
            holiday2.setName((String) name);
            holiday2.setWage((Integer) wage);
            Integer afterInt = null;
            if (after != null) {
                afterInt = (boolean) after ? 1 : 0;
            }
            holiday2.setAfter(afterInt);
            holiday2.setTarget((String) target);
            holiday2.setRest((Integer) rest);
            holidayList.add(holiday2);
        });
        return this.saveBatch(holidayList, 20);
    }

    /**
     * 判断是否是节假日
     *
     * @param day 例子 2024-02-16
     * @return 是否节假日
     */
    public boolean isHoliday(String day) {
        Holiday one = this.getOne(new LambdaQueryWrapper<Holiday>()
                .select(Holiday::getHolidayFlag)
                .eq(Holiday::getDay, day));
        if (one == null) {
            return false;
        }
        Integer holidayFlag = one.getHolidayFlag();
        return holidayFlag != null && holidayFlag != 0;
    }

}
