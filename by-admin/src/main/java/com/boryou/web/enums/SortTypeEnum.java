package com.boryou.web.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * 更改排序的类型
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum SortTypeEnum {

    /**
     * 上移
     */
    UNKNOWN("-1", "未知"),
    /**
     * 上移
     */
    UP("1", "上移"),

    /**
     * 下移
     */
    DOWN("2", "下移"),

    /**
     * 置顶
     */
    TOP("3", "置顶"),
    /**
     * 置底
     */
    BOTTOM("4", "置底");

    /**
     * 类型
     */
    private final String type;

    /**
     * 描述
     */
    private final String description;

    /**
     * 提前判断，用于解决Case中出现的Constant expression required问题
     *
     * @param type
     * @return
     */
    public static SortTypeEnum getByType(String type) {
        for (SortTypeEnum x : values()) {
            if (Objects.equals(x.getType(), type)) {
                return x;
            }
        }
        return UNKNOWN;
    }
}
