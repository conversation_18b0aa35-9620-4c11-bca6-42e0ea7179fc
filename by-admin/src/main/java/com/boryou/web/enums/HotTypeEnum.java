package com.boryou.web.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

/**
 * <AUTHOR>
 * @date 2024/9/11 下午2:07
 */
@Getter
@RequiredArgsConstructor
public enum HotTypeEnum {


    WB_REALTIME("WB_REALTIME", "微博实时榜"),
    WB_NEWS("WB_NEWS", "微博要闻"),

    DOUYIN_HOT("DOUYIN_HOT", "抖音热点榜"),
    DOUYIN_VIDEO("DOUYIN_VIDEO", "抖音视频榜"),

    // REALTIME 实时舆情，日 DAY，周 WEEK
    REALTIME("REALTIME", "实时舆情"),
    DAY("DAY", "日"),
    WEEK("WEEK", "周"),

    ;

    private final String type;
    private final String desc;


}
