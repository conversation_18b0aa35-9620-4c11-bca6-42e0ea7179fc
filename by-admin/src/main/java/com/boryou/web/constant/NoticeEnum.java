package com.boryou.web.constant;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.Objects;

/**
 * 通知类型
 *
 * <AUTHOR>
 */
@Getter
@RequiredArgsConstructor
public enum NoticeEnum {


    FOCUS("1", "重点关注信息"),
    SHORT_MESSAGE("2", "短信推送信息"),
    PROCESS_MAKR("3", "处置信息"),
    WARN("4", "预警通知"),
    UNKNOWN("0", "未知信息");


    /**
     * 类型
     */
    private final String type;

    /**
     * 描述
     */
    private final String description;

    /**
     * 提前判断，用于解决Case中出现的Constant expression required问题
     *
     * @param type
     * @return
     */
    public static NoticeEnum getByType(String type) {
        for (NoticeEnum x : values()) {
            if (Objects.equals(x.getType(), type)) {
                return x;
            }
        }
        return UNKNOWN;
    }
}
