package com.boryou.web.constant;


import com.boryou.common.constant.Constants;

public class RedisConstant {

    public final static String system_prefix = Constants.PROJECT_PREFIX;

    //首页开始
    public final static String home_prefix = system_prefix + "home:";
    //一个地市一个缓存开始
    public final static String areaOverview = home_prefix + "areaOverview:";
    public final static String total = home_prefix + "total:";
    public final static String curvestatis = home_prefix + "curvestatis:";
    public final static String authorLineChat = home_prefix + "authorLineChat:";
    public final static String wordcloud = home_prefix + "wordcloud:";
    public final static String emotion = home_prefix + "emotion:";
    //一个地市一个缓存结束

    public final static String lawyer = home_prefix + "lawyer:";
    public final static String hotZJ = home_prefix + "hotZJ:";
    public final static String warn = home_prefix + "warn:";
    public final static String countryplan = home_prefix + "countryplan:";
    public final static String provinceplan = home_prefix + "provinceplan:";
    public final static String mainplan = home_prefix + "mainplan:";
    public final static String plan_all = home_prefix + "plan:";
    //首页结束

    //by_court_name数据
    public final static String court_prefix = system_prefix + "court";

    //Search接口 查询条件缓存
    public final static String criteria_prefix = system_prefix + "search:criteria:";
    //Search接口 信息状态缓存
    public final static String info_status_prefix = system_prefix + "search:info_status:";

    public final static String plan_history = system_prefix + "plan:history:";

    //报送处置任务key
    public final static String submit_task = system_prefix + "submitTask:";
    //信源信息缓存前缀
    public static final String ACCOUNT_INFO_CAHCE_PREFIX = system_prefix + "account_info:";
    //信源信息缓存标识
    public static final String ACCOUNT_INFO_CAHCE_FLAG = ACCOUNT_INFO_CAHCE_PREFIX + "cache_flag";

    //登录次数缓存前缀
    public static final String LOGIN_COUNT_PREFIX = system_prefix + "login:count:";
    public static final String LOGIN_AUTH_CODE_PREFIX = system_prefix + "LOGIN_AUTH_CODE_PREFIX";
    public static final String SMS_LOGIN_TEMPLATE = system_prefix + "SMS_LOGIN_TEMPLATE";

    public static final String LOCK_KEY = system_prefix + "lock_key:";

    // drillTaskId -> userId -> roleInfo
    public static final String DRILL_USER_CACHE = RedisConstant.system_prefix + "drill_user_cache:";
    public static final String DRILL_USER_DELAY_ZSET = RedisConstant.system_prefix + "drill_user_delay_zset:";
    public static final String DRILL_REMAIN_TIME_CACHE = RedisConstant.system_prefix + "drill_remain_time_cache:";
    public static final String DRILL_REMAIN_TIME_STATUS = RedisConstant.system_prefix + "drill_remain_time_status:";
    public static final String DRILL_REMAIN_TIME_START = RedisConstant.system_prefix + "drill_remain_time_start:";
    public static final String DRILL_LIKE_STATUS_PREFIX = RedisConstant.system_prefix + "drill_like_status:";
    public static final String DRILL_LIKE_COUNT_PREFIX = RedisConstant.system_prefix + "drill_like_count:";
}
