package com.boryou.web.feign;

import com.boryou.common.core.domain.AjaxResult;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

/**
 * <AUTHOR>
 * @date 2024/4/8 15:32
 */
@FeignClient(name = "singleLoginService", url = "https://platform.boryou.com/prod-api")
public interface SingleLoginService {

    /**
     * 校验 token 并返回手机号
     *
     * @param token
     * @return
     */
    @GetMapping("/verifyToken")
    AjaxResult verifyToken(@RequestParam(value = "token") String token, @RequestParam(value = "type") Integer type);
}
