package com.boryou.web.mapper.secondary;

import com.baomidou.dynamic.datasource.annotation.DS;
import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.web.domain.Hot;
import com.boryou.web.domain.OutHot;
import com.boryou.web.domain.pg.PgDemo;
import org.apache.ibatis.annotations.Param;

import java.util.List;

@DS("secondary")
public interface OutHotListMapper {

    List<OutHot> selectHotList(@Param("list") List<String> typeList, @Param("keyword") List<String> keyword, @Param("time") String time);
}
