package com.boryou.web.mapper;

import com.boryou.web.domain.ApidemicAnnouncement;
import com.boryou.web.domain.Hot;
import com.boryou.web.domain.KeywordIndex;
import com.boryou.web.domain.OutHot;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 热榜Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
public interface HotListMapper {

    /**
     * 查询热榜列表
     *
     * @param hotList 热榜
     * @return 热榜集合
     */
    List<Hot> selectHotList(@Param("list") List<String> typeList, @Param("userAreaName") String userAreaName, @Param("count") Integer count);

    List<Hot> selectHotAllList(@Param("list") List<String> typeList);

    List<Hot> selectHotBackList(@Param("list") List<String> typeList, @Param("startTime") String startTime, @Param("endTime") String endTime);

    /**
     * 查询指定类型热搜
     *
     * @param type
     * @param limit
     * @return
     */
    List<Hot> selectHotTypeList(@Param("type") String type, @Param("limit") int limit);

    List<ApidemicAnnouncement> selectOutAnnouncementList(@Param("time") String time);

    List<KeywordIndex> selectOutKeywordIndexList(@Param("time") String time);
}
