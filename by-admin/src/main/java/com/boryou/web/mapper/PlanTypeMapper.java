package com.boryou.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.web.domain.PlanType;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 方案文件夹Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
public interface PlanTypeMapper extends BaseMapper<PlanType> {
    /**
     * 查询方案文件夹
     *
     * @param typeId 方案文件夹ID
     * @return 方案文件夹
     */
    public PlanType selectPlanTypeById(Long typeId);

    /**
     * 查询方案文件夹列表
     *
     * @param planType 方案文件夹
     * @return 方案文件夹集合
     */
    public List<PlanType> selectPlanTypeList(PlanType planType);

    /**
     * 新增方案文件夹
     *
     * @param planType 方案文件夹
     * @return 结果
     */
    public int insertPlanType(PlanType planType);

    /**
     * 修改方案文件夹
     *
     * @param planType 方案文件夹
     * @return 结果
     */
    public int updatePlanType(PlanType planType);

    /**
     * 删除方案文件夹
     *
     * @param typeId 方案文件夹ID
     * @return 结果
     */
    public int deletePlanTypeById(Long typeId);

    /**
     * 批量删除方案文件夹
     *
     * @param typeIds 需要删除的数据ID
     * @return 结果
     */
    public int deletePlanTypeByIds(Long[] typeIds);

    int changeSortById(@Param("fid1") Long typeId, @Param("fid2") Long typeId1);

    /**
     * 方案管理列表
     *
     * @param typeName
     * @param deptIds
     * @param userId
     * @return
     */
    List<PlanType> manageList(@Param("typeName") String typeName, @Param("deptIds") List<Long> deptIds, @Param("userId") Long userId);
}
