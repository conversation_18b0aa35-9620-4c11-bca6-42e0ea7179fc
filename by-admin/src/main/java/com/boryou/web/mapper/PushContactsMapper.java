package com.boryou.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.web.domain.PushContacts;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;


/**
 * 【请填写功能名称】Mapper接口
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@Mapper
public interface PushContactsMapper extends BaseMapper<PushContacts> {

    List<PushContacts> selectByPushContactsList(PushContacts pushContacts);

    int insertByPushContacts(PushContacts pushContacts);

    int updateByPushContacts(PushContacts pushContacts);

    int deleteByPushContactsByIds(Long[] ids);
}
