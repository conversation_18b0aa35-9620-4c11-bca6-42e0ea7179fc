package com.boryou.web.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.PlanMain;
import com.boryou.web.domain.bo.PlanMainBO;
import com.boryou.web.domain.dto.PlanManageQueryDTO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * 方案Mapper接口
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
public interface PlanMapper extends BaseMapper<Plan> {

    /**
     * 查询方案列表
     *
     * @param Plan 方案
     * @return 方案集合
     */
    List<Plan> selectPlanAndPlanMain(Plan plan);

    List<Plan> selectByPlanList(Plan plan);

    List<Plan> selectByPlanById(Long planId);

    List<PlanMain> selectPlanMainByPlanId(Long planId);

    void insertByPlan(Plan plan);

    void updateByPlan(Plan plan);

    /**
     * 批量新增各种重点关注方案
     *
     * @param planMainList 各种重点关注方案列表
     * @return 结果
     */
    int batchPlanMain(List<PlanMain> planMainList);

    void changeSortById(@Param("planId1") Long planId, @Param("planId2") Long planId1);

    /**
     * 查询方案列表
     *
     * @param Plan 方案
     * @return 方案集合
     */
    List<Plan> selectByMainTypeList(PlanMainBO plan);

    int selectByMainTypeListCount(PlanMainBO plan);

    /**
     * 方案管理-方案列表
     *
     * @param query
     * @return
     */
    List<Plan> manageList(@Param("query") PlanManageQueryDTO query);

    Plan getGreaterSortPlan(Plan plan);

    Plan getLessSortPlan(Plan plan);

    void updatePlanResetSort(Plan plan4);
}
