package com.boryou.web.util;

import com.aspose.words.*;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.ClassPathResource;
import org.springframework.stereotype.Component;

import java.io.*;

/**
 * <AUTHOR>
 * @Descripetion TODO
 */
@Component
public class WordtoPdfAsposeUtil {

    private static String fontSettingPath;

    @Value("${fontSettingPath}")
    public void setPath(String fontSettingPath) {
        WordtoPdfAsposeUtil.fontSettingPath = fontSettingPath;
    }

    public static String getPath() {
        return fontSettingPath;
    }

    public static boolean getLicense() {
        boolean result = false;
        InputStream is = null;
        try {
            is = new ClassPathResource("license.xml").getInputStream();
//            is = WordtoPdfAsposeUtil.class.getClassLoader().getResourceAsStream("license.xml"); // license.xml应放在..\WebRoot\WEB-INF\classes路径下
            License aposeLic = new License();
            aposeLic.setLicense(is);
            result = true;
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return result;
    }

    public static void doc2pdf(String inPath, String outPath) {
        if (!getLicense()) { // 验证License 若不验证则转化出的pdf文档会有水印产生
            return;
        }
        FileOutputStream os = null;
        try {
            long old = System.currentTimeMillis();
            File file = new File(outPath); // 新建一个空白docx文档
            os = new FileOutputStream(file);
            if (!System.getProperty("os.name").equals("Windows 10")) {
//                FontSettings.getDefaultInstance().setFontsFolder(File.separator + "usr" + File.separator + "share" + File.separator + "fonts" + File.separator + "Fonts", true);
                FontSettings.getDefaultInstance().setFontsFolder(getPath(), true);
            }
            Document doc = new Document(inPath);
            doc.save(os, SaveFormat.PDF);// 支持DOC, DOCX SaveFormat.DOCX, OOXML, RTF HTML, OpenDocument, PDF  SaveFormat.PDF,  linux需提前设置中文字体
            long now = System.currentTimeMillis();
            System.out.println("word文件转换pdf成功，共耗时：" + ((now - old) / 1000.0) + "秒"); // 转化用时
        } catch (UnsupportedFileFormatException e) {
            e.printStackTrace();
            FileInputStream is = null;
            try {
                os = new FileOutputStream(new File(outPath));
                is = new FileInputStream(new File(inPath));
                int len = -1;
                //读取文件输入流，写入到输出流ByteArray中，输入流转成了输出流
                byte[] buf = new byte[1024];
                while ((len = is.read(buf)) != -1) {
                    os.write(buf, 0, len);
                }
                is.close();
            } catch (Exception e1) {
                e1.printStackTrace();
            }
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            if (os != null) {
                try {
                    os.flush();
                    os.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    public static void doc2pdf(InputStream in, OutputStream os) {
        if (!getLicense()) { // 验证License 若不验证则转化出的pdf文档会有水印产生
            return;
        }
        try {
            long old = System.currentTimeMillis();
            if (!System.getProperty("os.name").equals("Windows 10")) {
                FontSettings.getDefaultInstance().setFontsFolder(getPath(), true);
            }
            Document doc = new Document(in);
            doc.save(os, SaveFormat.PDF);// 支持DOC, DOCX SaveFormat.DOCX, OOXML, RTF HTML, OpenDocument, PDF  SaveFormat.PDF,  linux需提前设置中文字体
            long now = System.currentTimeMillis();
            System.out.println("word文件转换pdf成功，共耗时：" + ((now - old) / 1000.0) + "秒"); // 转化用时
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    //pdf转doc
    public static void pdf2doc(String pdfPath) {
        long old = System.currentTimeMillis();
        try {
            //新建一个word文档
            String wordPath = pdfPath.substring(0, pdfPath.lastIndexOf(".")) + ".docx";
            FileOutputStream os = new FileOutputStream(wordPath);
            //doc是将要被转化的word文档
            com.aspose.pdf.Document doc = new com.aspose.pdf.Document(pdfPath);
            //全面支持DOC, DOCX, OOXML, RTF HTML, OpenDocument, PDF, EPUB, XPS, SWF 相互转换
            doc.save(os, com.aspose.pdf.SaveFormat.DocX);
            os.close();
            //转化用时
            long now = System.currentTimeMillis();
            System.out.println("Pdf 转 Word 共耗时：" + ((now - old) / 1000.0) + "秒");
        } catch (Exception e) {
            System.out.println("Pdf 转 Word 失败...");
            e.printStackTrace();
        }
    }

    public static void pdf2doc(InputStream in, OutputStream os) {
        long old = System.currentTimeMillis();
        try {
            com.aspose.pdf.Document doc = new com.aspose.pdf.Document(in);
            //全面支持DOC, DOCX, OOXML, RTF HTML, OpenDocument, PDF, EPUB, XPS, SWF 相互转换
            doc.save(os, com.aspose.pdf.SaveFormat.DocX);
            long now = System.currentTimeMillis();
            System.out.println("Pdf 转 Word 共耗时：" + ((now - old) / 1000.0) + "秒");
        } catch (Exception e) {
            System.out.println("Pdf 转 Word 失败...");
            e.printStackTrace();
        }
    }


    public static void main(String[] arg) throws Exception {

//        System.out.println(getLicense());

//        //word -> pdf
        File f1 = new File("D:\\2.docx");
        String docPath = f1.getPath();
//        String pdfPath = docPath.replaceAll(".docx", ".pdf").replaceAll(".doc", ".pdf");
        String pdfPath = "1.pdf";
        WordtoPdfAsposeUtil.doc2pdf(docPath, pdfPath);

        //pdf -> word
//        File f1 = new File("D:\\2.pdf");
//        String pdfPath = f1.getPath();
//        WordtoPdfAsposeUtil.pdf2doc(pdfPath);
    }


    //jar包破解
//    public static void main(String[] args) throws Exception {
//        String jarPath = "D:\\repo\\com\\aspose\\aspose-pdf\\22.4\\aspose-pdf-22.4.jar";
//        crack(jarPath);
//    }
//    private static void crack(String jarName) {
//        try {
//            ClassPool.getDefault().insertClassPath(jarName);
//            CtClass ctClass = ClassPool.getDefault().getCtClass("com.aspose.pdf.ADocument");
//            CtMethod[] declaredMethods = ctClass.getDeclaredMethods();
//            int num = 0;
//            for (int i = 0; i < declaredMethods.length; i++) {
//                if (num == 2) {
//                    break;
//                }
//                CtMethod method = declaredMethods[i];
//                CtClass[] ps = method.getParameterTypes();
//                if (ps.length == 2
//                        && method.getName().equals("lI")
//                        && ps[0].getName().equals("com.aspose.pdf.ADocument")
//                        && ps[1].getName().equals("int")) {
//                    // 最多只能转换4页 处理
//                    System.out.println(method.getReturnType());
//                    System.out.println(ps[1].getName());
//                    method.setBody("{return false;}");
//                    num = 1;
//                }
//                if (ps.length == 0 && method.getName().equals("lt")) {
//                    // 水印处理
//                    method.setBody("{return true;}");
//                    num = 2;
//                }
//            }
//            File file = new File(jarName);
//            ctClass.writeFile(file.getParent());
//            disposeJar(jarName, file.getParent() + "/com/aspose/pdf/ADocument.class");
//        } catch (NotFoundException e) {
//            e.printStackTrace();
//        } catch (CannotCompileException e) {
//            e.printStackTrace();
//        } catch (IOException e) {
//            e.printStackTrace();
//        }
//    }
//    private static void disposeJar(String jarName, String replaceFile) {
//        ArrayList<String> deletes = new ArrayList<>();
//        deletes.add("META-INF/37E3C32D.SF");
//        deletes.add("META-INF/37E3C32D.RSA");
//        File oriFile = new File(jarName);
//        if (!oriFile.exists()) {
//            System.out.println("######Not Find File:" + jarName);
//            return;
//        }
//        //将文件名命名成备份文件
//        String bakJarName = jarName.substring(0, jarName.length() - 3) + "cracked.jar";
//        //   File bakFile=new File(bakJarName);
//        try {
//            //创建文件（根据备份文件并删除部分）
//            JarFile jarFile = new JarFile(jarName);
//            JarOutputStream jos = new JarOutputStream(new FileOutputStream(bakJarName));
//            Enumeration entries = jarFile.entries();
//            while (entries.hasMoreElements()) {
//                JarEntry entry = (JarEntry) entries.nextElement();
//                if (!deletes.contains(entry.getName())) {
//                    if (entry.getName().equals("com/aspose/pdf/ADocument.class")) {
//                        System.out.println("Replace:-------" + entry.getName());
//                        JarEntry jarEntry = new JarEntry(entry.getName());
//                        jos.putNextEntry(jarEntry);
//                        FileInputStream fin = new FileInputStream(replaceFile);
//                        byte[] bytes = readStream(fin);
//                        jos.write(bytes, 0, bytes.length);
//                    } else {
//                        jos.putNextEntry(entry);
//                        byte[] bytes = readStream(jarFile.getInputStream(entry));
//                        jos.write(bytes, 0, bytes.length);
//                    }
//                } else {
//                    System.out.println("Delete:-------" + entry.getName());
//                }
//            }
//            jos.flush();
//            jos.close();
//            jarFile.close();
//        } catch (FileNotFoundException e) {
//            e.printStackTrace();
//        } catch (IOException e) {
//            e.printStackTrace();
//        } catch (Exception e) {
//            e.printStackTrace();
//        }
//    }
//    private static byte[] readStream(InputStream inStream) throws Exception {
//        ByteArrayOutputStream outSteam = new ByteArrayOutputStream();
//        byte[] buffer = new byte[1024];
//        int len = -1;
//        while ((len = inStream.read(buffer)) != -1) {
//            outSteam.write(buffer, 0, len);
//        }
//        outSteam.close();
//        inStream.close();
//        return outSteam.toByteArray();
//    }
}

