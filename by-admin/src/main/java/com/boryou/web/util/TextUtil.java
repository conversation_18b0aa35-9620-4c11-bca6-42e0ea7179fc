package com.boryou.web.util;

/**
 * <AUTHOR>
 * @date 2024-07-04 10:43
 */
public class TextUtil {
    // 判断是否为纯数字的函数，同上
    public static boolean isNumeric(String text) {
        String pattern = "^[0-9]+(\\.[0-9]+)?$";
        return text.matches(pattern);
    }

    // 判断是否为纯字母的函数，同上
    public static boolean isAlphabetic(String text) {
        String pattern = "^[a-zA-Z]+$";
        return text.matches(pattern);
    }

    public static boolean isSpecialCharacters(String text) {
        String pattern = "^[~!@#$%^&*()-{}+<>?=,/]+$";
        return text.matches(pattern);
    }

    public static void main(String[] args) {
        System.out.println(isSpecialCharacters("+++++"));
        System.out.println(isSpecialCharacters("@"));
        System.out.println(isSpecialCharacters("()"));
        System.out.println(isSpecialCharacters("\\"));
        System.out.println(isSpecialCharacters("+-"));
        System.out.println(isSpecialCharacters("+"));
        System.out.println(isSpecialCharacters("/"));
        System.out.println(isSpecialCharacters(","));
    }
}

