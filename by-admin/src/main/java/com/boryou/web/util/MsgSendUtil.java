package com.boryou.web.util;

import cn.hutool.http.Header;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.web.constant.SmsConstant;

import java.util.List;


/***
 * <AUTHOR>
 * @description //账号ID获取工具
 * @date 16:10 2021/10/13
 */
public class MsgSendUtil {


    public static String sendMsg(String phoneNumber, String nickName) {
        JSONObject json = JSONUtil.createObj();
        json.set("type", "GZAipGFhY3hjZ8ac9aqwa29mbXNkbTEyMzMxWPRWTS");
        json.set("phone", phoneNumber);
        json.set("p1", nickName);
        json.set("p2", "1");
        json.set("p3", "系统、APP或小程序");
        HttpResponse resJsonStr = HttpRequest.post(SmsConstant.SMS_SERVER_URL).header(Header.AUTHORIZATION, "WPXTdDAQWRadcGFhYzMyZm1zZG0xMzEyACF")
                .body(json.toString())
                .execute();
        System.out.println(resJsonStr.body());
        JSONObject object = JSONUtil.parseObj(resJsonStr.body());
        return object.get("code").toString();
    }


    public static void main(String[] args) {
        String result = sendMsg("15105584645", "张三");
        System.out.println(result);
    }


    /**
     * *
     * 拼接短信请求体
     *
     * @param phone  手机号
     * @param type   模板
     * @param params 参数
     * @return
     */
    public static String concatSmsBody(String phone, String type, List<String> params) {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("phone", phone);
        jsonObject.set("type", type);
        for (int i = 0; i < params.size(); i++) {
            jsonObject.set("p" + (i + 1), params.get(i));
        }
        return jsonObject.toString();
    }

    /**
     * *
     *
     * @param phone  手机号
     * @param type   模板
     * @param params 参数
     * @return
     */
    public static String sendSms(String phone, String type, List<String> params) {
        String paramStr = concatSmsBody(phone, type, params);
        return HttpUtil.createPost(SmsConstant.SMS_SERVER_URL)
                .body(paramStr)
                .header("Authorization", SmsConstant.SMS_AUTH_STR)
                .execute()
                .body();
    }
}
