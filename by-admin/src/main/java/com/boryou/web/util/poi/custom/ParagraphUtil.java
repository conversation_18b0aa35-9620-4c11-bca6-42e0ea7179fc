package com.boryou.web.util.poi.custom;

import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xwpf.usermodel.*;
import org.apache.xmlbeans.XmlException;
import org.apache.xmlbeans.XmlToken;
import org.openxmlformats.schemas.drawingml.x2006.main.CTNonVisualDrawingProps;
import org.openxmlformats.schemas.drawingml.x2006.main.CTPositiveSize2D;
import org.openxmlformats.schemas.drawingml.x2006.wordprocessingDrawing.CTInline;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.ByteArrayInputStream;
import java.io.IOException;
import java.math.BigInteger;

/**
 * POI操作word中段落工具类
 *
 * <AUTHOR>
 * @date 2018-01-11 17:24:22
 */
public class ParagraphUtil {

    /**
     * 向段落中添加图片（自动计算合适的宽高）
     *
     * @param xwpfParagraph 要插入图片的段落
     * @param bytes         图片的字节数组
     * @param type          图片类型，可从org.apache.poi.xwpf.usermodel.Document类获取，如Document.PICTURE_TYPE_JPEG
     * @param linkUrl       图片指向的链接
     * <AUTHOR>
     * @date 2018-01-11 17:37:11
     */
    public static void addPicture(XWPFParagraph xwpfParagraph, byte[] bytes, int type, String linkUrl) {
        addPicture(xwpfParagraph, bytes, type, linkUrl, 0, 0);
    }

    /**
     * 向段落中添加图片
     *
     * @param xwpfParagraph 要插入图片的段落
     * @param bytes         图片的字节数组
     * @param type          图片类型，可从org.apache.poi.xwpf.usermodel.Document类获取，如Document.PICTURE_TYPE_JPEG
     * @param linkUrl       图片指向的链接
     * @param imgWidth      指定图片的宽度，小于等于0时自动计算
     * @param imgHeight     指定图片的高度，小于等于0时自动计算
     * <AUTHOR>
     * @date 2018-01-11 17:33:55
     */
    public static void addPicture(XWPFParagraph xwpfParagraph, byte[] bytes, int type, String linkUrl, int imgWidth, int imgHeight) {
        try {
            //图片宽高未指定，自动计算
            if (imgWidth <= 0 || imgHeight <= 0) {
                //获取图片宽高
                BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(bytes));

                imgWidth = bufferedImage.getWidth();
                imgHeight = bufferedImage.getHeight();
                //等比例缩放
                if (imgWidth == 0 || imgHeight == 0) {
                    imgWidth = 500;
                    imgHeight = 300;
                } else {
                    if (imgWidth > 500) {
                        imgHeight = Math.round(imgHeight * 500 / imgWidth);
                        imgWidth = 500;
                    }
                }
            }
            //添加图片数据，返回数据的id
            String blipId = xwpfParagraph.getDocument().addPictureData(new ByteArrayInputStream(bytes), type);
            createPicture(xwpfParagraph, blipId, xwpfParagraph.getDocument().getNextPicNameNumber(type), imgWidth, imgHeight, linkUrl);
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private static void createPicture(XWPFParagraph xwpfParagraph, String blipId, int nextImgId, int width, int height, String linkUrl) {
        final int EMU = 9525;
        width *= EMU;
        height *= EMU;
        CTInline inline = xwpfParagraph.createRun().getCTR().addNewDrawing().addNewInline();
        String picXml = ""
                + "<a:graphic xmlns:a=\"http://schemas.openxmlformats.org/drawingml/2006/main\">"
                + "   <a:graphicData uri=\"http://schemas.openxmlformats.org/drawingml/2006/picture\">"
                + "      <pic:pic xmlns:pic=\"http://schemas.openxmlformats.org/drawingml/2006/picture\">"
                + "         <pic:nvPicPr>" + "            <pic:cNvPr id=\""
                + nextImgId
                + "\" name=\"img_"
                + nextImgId
                + "\"/>"
                + "            <pic:cNvPicPr/>"
                + "         </pic:nvPicPr>"
                + "         <pic:blipFill>"
                + "            <a:blip r:embed=\""
                + blipId
                + "\" xmlns:r=\"http://schemas.openxmlformats.org/officeDocument/2006/relationships\"/>"
                + "            <a:stretch>"
                + "               <a:fillRect/>"
                + "            </a:stretch>"
                + "         </pic:blipFill>"
                + "         <pic:spPr>"
                + "            <a:xfrm>"
                + "               <a:off x=\"0\" y=\"0\"/>"
                + "               <a:ext cx=\""
                + width
                + "\" cy=\""
                + height
                + "\"/>"
                + "            </a:xfrm>"
                + "            <a:prstGeom prst=\"rect\">"
                + "               <a:avLst/>"
                + "            </a:prstGeom>"
                + "         </pic:spPr>"
                + "      </pic:pic>"
                + "   </a:graphicData>" + "</a:graphic>";

        XmlToken xmlToken = null;
        try {
            xmlToken = XmlToken.Factory.parse(picXml);
        } catch (XmlException xe) {
            xe.printStackTrace();
        }
        inline.set(xmlToken);

        inline.setDistT(0);
        inline.setDistB(0);
        inline.setDistL(0);
        inline.setDistR(0);
        CTPositiveSize2D extent = inline.addNewExtent();
        extent.setCx(width);
        extent.setCy(height);
        CTNonVisualDrawingProps docPr = inline.addNewDocPr();
        if (linkUrl != null) {
            String linkId = xwpfParagraph.getDocument().getPackagePart().addExternalRelationship(linkUrl, XWPFRelation.HYPERLINK.getRelation()).getId();
            docPr.addNewHlinkClick().setId(linkId);
        }
        docPr.setId(nextImgId);
        docPr.setName("img_" + nextImgId);
        docPr.setDescr("Picture");
    }

    /**
     * 向段落中添加文本信息
     *
     * @param xwpfParagraph 要添加文本的段落
     * @param content       内容
     * @param fontSize      字体大小
     * @param isBold        加粗
     * @param isItalic      倾斜
     * @param fontFamily    字体(为空默认微软雅黑)
     * @param colorVal      颜色
     * <AUTHOR>
     * @date 2017-06-07 下午19:43:49
     */
    public static void addText(XWPFParagraph xwpfParagraph, String content, int fontSize, boolean isBold, boolean isItalic, String fontFamily, String colorVal) {
        addText(xwpfParagraph, null, content, fontSize, isBold, isItalic, fontFamily, colorVal, null);
    }

    /**
     * 向段落中添加文本信息
     *
     * @param xwpfParagraph 要添加文本的段落
     * @param content       内容
     * @param fontSize      字体大小
     * @param isBold        加粗
     * @param isItalic      倾斜
     * @param fontFamily    字体(为空默认微软雅黑)
     * @param colorVal      颜色
     * <AUTHOR>
     * @date 2017-06-07 下午19:43:49
     */
    public static void addText(XWPFParagraph xwpfParagraph, XWPFRun xwpfRun, String content, int fontSize, boolean isBold, boolean isItalic, String fontFamily, String colorVal, STUnderline.Enum underline) {
        if (xwpfRun == null) {
            xwpfRun = xwpfParagraph.createRun();
        }
        //内容
        if (content != null) {
            xwpfRun.setText(content);
        }
        // 设置字体样式
        if (isBold) {
            xwpfRun.setBold(isBold);
        }
        if (isItalic) {
            xwpfRun.setItalic(isItalic);
        }
        //颜色
        if (colorVal != null) {
            xwpfRun.setColor(colorVal);
        }

        CTRPr pRpr = null;
        if (xwpfRun.getCTR() != null) {
            pRpr = xwpfRun.getCTR().getRPr();
            if (pRpr == null) {
                pRpr = xwpfRun.getCTR().addNewRPr();
            }
        } else {
            pRpr = xwpfParagraph.getCTP().addNewR().addNewRPr();
        }

        CTUnderline ctUnderline = pRpr.getU() == null ? pRpr.addNewU() : pRpr.getU();
        if (underline != null) {
            ctUnderline.setVal(underline);
        } else {
            ctUnderline.setVal(STUnderline.NONE);
        }

        // 设置字体
        CTFonts fonts = pRpr.isSetRFonts() ? pRpr.getRFonts() : pRpr.addNewRFonts();
        if (fontFamily == null) {
            fontFamily = "微软雅黑";
        }
        fonts.setAscii(fontFamily);
        fonts.setEastAsia(fontFamily);
        fonts.setHAnsi(fontFamily);

        // 设置字体大小
        CTHpsMeasure sz = pRpr.isSetSz() ? pRpr.getSz() : pRpr.addNewSz();
        sz.setVal(new BigInteger(fontSize + ""));

        CTHpsMeasure szCs = pRpr.isSetSzCs() ? pRpr.getSzCs() : pRpr.addNewSzCs();
        szCs.setVal(new BigInteger(fontSize + ""));
    }

    /**
     * 添加超链接
     *
     * @param xwpfParagraph 要添加文本的段落
     * @param content       内容
     * @param fontSize      字体大小
     * @param fontFamily    字体(为空默认微软雅黑)
     * @param colorVal      颜色，十六进制，例：FFFFFF
     * @param underLine     是否有下划线
     * @param url           链接
     * <AUTHOR>
     * @date 2017-06-07 下午19:57:49
     */
    public static void addHyperlink(XWPFParagraph xwpfParagraph, String content, String url, int fontSize, String fontFamily, String colorVal, boolean underLine) {
        // Add the link as External relationship
        String linkId = xwpfParagraph.getDocument().getPackagePart().addExternalRelationship(url, XWPFRelation.HYPERLINK.getRelation()).getId();
        // Append the link and bind it to the relationship
        CTHyperlink cLink = xwpfParagraph.getCTP().addNewHyperlink();
        cLink.setId(linkId);
        // Create the linked text
        CTText ctText = CTText.Factory.newInstance();
        ctText.setStringValue(content);
        CTR ctr = CTR.Factory.newInstance();
        CTRPr rpr = ctr.addNewRPr();

        // 设置超链接样式
        CTColor color = CTColor.Factory.newInstance();
        if (colorVal == null) {
            color.setVal("0000FF");
        } else {
            color.setVal(colorVal);
        }
        rpr.setColor(color);

        //下划线
        if (underLine) {
            rpr.addNewU().setVal(STUnderline.SINGLE);
        }

        // 设置字体
        CTFonts fonts = rpr.isSetRFonts() ? rpr.getRFonts() : rpr.addNewRFonts();
        if (fontFamily == null) {
            fontFamily = "微软雅黑";
        }
        fonts.setAscii(fontFamily);
        fonts.setEastAsia(fontFamily);
        fonts.setHAnsi(fontFamily);

        // 设置字体大小
        CTHpsMeasure sz = rpr.isSetSz() ? rpr.getSz() : rpr.addNewSz();
        sz.setVal(new BigInteger(fontSize + ""));

        CTHpsMeasure szCs = rpr.isSetSzCs() ? rpr.getSzCs() : rpr.addNewSzCs();
        szCs.setVal(new BigInteger(fontSize + ""));

        ctr.setTArray(new CTText[]{ctText});
        cLink.setRArray(new CTR[]{ctr});
    }

    /**
     * 添加超链接
     *
     * @param xwpfParagraph 要添加文本的段落
     * @param content       内容
     * @param fontSize      字体大小
     * @param url           链接
     * <AUTHOR>
     * @date 2017-06-07 下午19:57:49
     */
    public static void addHyperlink(XWPFParagraph xwpfParagraph, String content, int fontSize, String fontFamily, String url) {
        addHyperlink(xwpfParagraph, content, url, fontSize, fontFamily, null, true);
    }

    /**
     * 设置对齐方式（水平、垂直）
     *
     * <AUTHOR>
     * @date 2018-01-11 18:52:30
     */
    public static void setAlign(XWPFParagraph xwpfParagraph, ParagraphAlignment paragraphAlignment, TextAlignment textAlignment) {
        if (paragraphAlignment != null) {
            xwpfParagraph.setAlignment(paragraphAlignment);
        }
        if (textAlignment != null) {
            xwpfParagraph.setVerticalAlignment(textAlignment);
        }
    }

    /**
     * 设置段落缩进信息
     *
     * @param xwpfParagraph 设置缩进的段落
     * @param firstLine     首行缩进
     * @param firstLineChar 首行缩进
     * @param hanging       悬挂缩进
     * @param hangingChar   悬挂缩进
     * @param left          左侧缩进
     * @param leftChar      左侧缩进
     * @param right         右侧缩进
     * @param rightChar     右侧缩进
     * <AUTHOR>
     * @date 2018-01-11 18:56:29
     */
    public static void setParagraphIndInfo(XWPFParagraph xwpfParagraph, String firstLine, String firstLineChar, String hanging, String hangingChar, String left, String leftChar, String right, String rightChar) {
        CTPPr ctpPr = null;
        if (xwpfParagraph.getCTP() != null) {
            if (xwpfParagraph.getCTP().getPPr() != null) {
                ctpPr = xwpfParagraph.getCTP().getPPr();
            } else {
                ctpPr = xwpfParagraph.getCTP().addNewPPr();
            }
        } else {
            return;
        }
        CTInd pInd = ctpPr.getInd() != null ? ctpPr.getInd() : ctpPr.addNewInd();
        if (firstLine != null) {
            pInd.setFirstLine(new BigInteger(firstLine));
        }
        if (firstLineChar != null) {
            pInd.setFirstLineChars(new BigInteger(firstLineChar));
        }
        if (hanging != null) {
            pInd.setHanging(new BigInteger(hanging));
        }
        if (hangingChar != null) {
            pInd.setHangingChars(new BigInteger(hangingChar));
        }
        if (left != null) {
            pInd.setLeft(new BigInteger(left));
        }
        if (leftChar != null) {
            pInd.setLeftChars(new BigInteger(leftChar));
        }
        if (right != null) {
            pInd.setRight(new BigInteger(right));
        }
        if (rightChar != null) {
            pInd.setRightChars(new BigInteger(rightChar));
        }
    }

    /**
     * 设置段落首行缩进信息
     *
     * @param xwpfParagraph 设置缩进的段落
     * @param firstLine     首行缩进
     * @param firstLineChar 首行缩进
     * <AUTHOR>
     * @date 2018-01-11 18:56:29
     */
    public static void setParagraphIndInfo(XWPFParagraph xwpfParagraph, String firstLine, String firstLineChar) {
        setParagraphIndInfo(xwpfParagraph, firstLine, firstLineChar, null, null, null, null, null, null);
    }

    /**
     * 设置段落间距
     * 一行=100 一磅=20
     *
     * <AUTHOR>
     * @date 2018-01-11 19:01:00
     */
    public static void setParagraphSpacingInfo(XWPFParagraph xwpfParagraph, boolean isSpace, int before, int after, int beforeLines, int afterLines, boolean isLine, String line, STLineSpacingRule.Enum lineValue) {
        CTPPr pPPr = null;
        if (xwpfParagraph.getCTP() != null) {
            if (xwpfParagraph.getCTP().getPPr() != null) {
                pPPr = xwpfParagraph.getCTP().getPPr();
            } else {
                pPPr = xwpfParagraph.getCTP().addNewPPr();
            }
        }
        if (pPPr == null) {
            return;
        }
        CTSpacing pSpacing = pPPr.getSpacing() != null ? pPPr.getSpacing()
                : pPPr.addNewSpacing();
        if (isSpace) {
            // 段前磅数
            if (before > 0) {
                pSpacing.setBefore(BigInteger.valueOf(before * 20));
            }
            // 段后磅数
            if (after > 0) {
                pSpacing.setAfter(BigInteger.valueOf(after * 20));
            }
            // 段前行数
            if (beforeLines > 0) {
                pSpacing.setBeforeLines(BigInteger.valueOf(beforeLines * 100));
            }
            // 段后行数
            if (afterLines > 0) {
                pSpacing.setAfterLines(BigInteger.valueOf(afterLines * 100));
            }
        }
        if (isLine) {
            if (line != null) {
                pSpacing.setLine(new BigInteger(line));
            }
            if (lineValue != null) {
                pSpacing.setLineRule(lineValue);
            }
        }
    }

    /**
     * 添加标题，此标题可以显示在文档结构图中
     *
     * @param xwpfParagraph 要添加标题的段落
     * @param headStyle     枚举对象HeadStyle，注意使用前需先使用XWPFDocument中的addCustomHeadStyle注册
     * @param title         标题
     * @param fontSize      字体大小
     * @param isBold        是否加粗
     * @param isItalic      是否倾斜
     * @param fontFamily    字体类别，默认微软雅黑
     * @param colorVal      颜色，16进制值
     * @param highLight     是否高亮，传入可识别的颜色名，例：red、yellow等
     * <AUTHOR>
     * @date 2018-01-11 19:07:35
     */
    public static void addTitle(XWPFParagraph xwpfParagraph, HeadStyle headStyle, String title, int fontSize, boolean isBold, boolean isItalic, String fontFamily, String colorVal, String highLight) {

        XWPFRun pRun = xwpfParagraph.createRun();
        xwpfParagraph.setStyle(headStyle.getStyleName());
        //内容
        pRun.setText(title);
        // 设置字体样式
        pRun.setBold(isBold);
        pRun.setItalic(isItalic);
        //颜色
        if (colorVal != null) {
            pRun.setColor(colorVal);
        }

        CTRPr pRpr = null;
        if (pRun.getCTR() != null) {
            pRpr = pRun.getCTR().getRPr();
            if (pRpr == null) {
                pRpr = pRun.getCTR().addNewRPr();
            }
        } else {
            pRpr = xwpfParagraph.getCTP().addNewR().addNewRPr();
        }
        // 设置字体
        CTFonts fonts = pRpr.isSetRFonts() ? pRpr.getRFonts() : pRpr.addNewRFonts();
        if (fontFamily == null) {
            fontFamily = "微软雅黑";
        }
        fonts.setAscii(fontFamily);
        fonts.setEastAsia(fontFamily);
        fonts.setHAnsi(fontFamily);

        // 设置字体大小
        CTHpsMeasure sz = pRpr.isSetSz() ? pRpr.getSz() : pRpr.addNewSz();
        sz.setVal(new BigInteger(fontSize + ""));

        CTHpsMeasure szCs = pRpr.isSetSzCs() ? pRpr.getSzCs() : pRpr
                .addNewSzCs();
        szCs.setVal(new BigInteger(fontSize + ""));

        if (highLight != null) {
            //高亮
            CTHighlight highlight = pRpr.isSetHighlight() ? pRpr.getHighlight() : pRpr.addNewHighlight();
            highlight.setVal(STHighlightColor.Enum.forString(highLight));
        }
    }

    /**
     * 添加分页符
     *
     * <AUTHOR>
     * @date 2018-01-19 16:46:06
     */
    public static void addPageBreak(XWPFParagraph paragraph) {
        paragraph.createRun().addBreak(BreakType.PAGE);
    }

}
