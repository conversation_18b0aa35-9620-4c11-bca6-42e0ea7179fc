package com.boryou.web.util;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 索引查询结果标红工具
 *
 * <AUTHOR>
 * @date 2016-2-5 下午1:35:13
 */
public class HighlightUtil {

    /**
     * 索引查询结果标红（非精确查询结果标注，可能存在分词标红）
     *
     * @param str         待标红字符串
     * @param queryString solr查询字符串，短语或复杂规则语句
     * @return String 带有font标红标签的字符串
     * <AUTHOR>
     * @date 2016-2-5 下午1:14:52
     */
    public static String highlighter(String str, String queryString) {
        return handleLighter(str, queryString, false);
    }

    /**
     * 索引查询结果标红
     *
     * @param str         待标红字符串
     * @param queryString solr查询字符串，短语或复杂规则语句
     * @param isAccurate  是否为精确查询（精确查询时，标红也不分词）
     * @return String 带有font标红标签的字符串
     * <AUTHOR>
     * @date 2016-2-19 下午4:50:39
     */
    public static String highlighter(String str, String queryString, boolean isAccurate) {
        return handleLighter(str, queryString, isAccurate);
    }

    /**
     * 处理标红
     *
     * @param str         待标红字符串
     * @param queryString solr查询字符串，短语或复杂规则语句
     * @param isAccurate  是否为精确查询（精确查询时，标红也不分词）
     * @return String 带有font标红标签的字符串
     * <AUTHOR>
     * @date 2016-2-19 下午4:51:44
     */
    private static String handleLighter(String str, String queryString, boolean isAccurate) {
        if (str == null || str.length() == 0) {
            return str;
        }
        if (queryString == null || queryString.length() == 0) {
            return str;
        }
        try {
            List<String> lightWords = getLightWords(str, queryString, isAccurate);
            // 4-按序标红
            for (String tempStr : lightWords) {
                str = replaceAll(str, tempStr);
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return str;
    }

    /**
     * 获取待标红关键词列表
     *
     * @param str         待标红字符串
     * @param queryString solr查询字符串，短语或复杂规则语句
     * @param isAccurate  是否为精确查询（精确查询时，标红也不分词）
     * @return List<String> 待标红关键词列表
     * <AUTHOR>
     * @date 2017-7-13 下午6:19:23
     */
    public static List<String> getLightWords(String str, String queryString, boolean isAccurate) {
        List<String> lightWords = new ArrayList<String>();
        // 预处理，去除过滤词
        queryString = queryString.replaceAll("-all:\\((.*?)\\)", "");
        // 1-提取精准词段
        Pattern p = Pattern.compile("\"(.*?)\"");
        Matcher m = p.matcher(queryString);
        String temp;
        while (m.find()) {
            temp = m.group();
            temp = temp.replace("\"", ""); // 取正则结果中的左右侧引号
            add2LightWords(str, lightWords, temp);
        }
        // 2-过滤精准词段，留下可拆词部分
        queryString = queryString.replaceAll("\"(.*?)\"", "");
        queryString = queryString.replace("(", "");
        queryString = queryString.replace(")", "");
        queryString = queryString.replace("[", "");
        queryString = queryString.replace("]", "");
        queryString = queryString.replace("ALL", "");
        queryString = queryString.replace("all", "");
        queryString = queryString.replace(" AND ", " ");
        queryString = queryString.replace("advanced", "");
        queryString = queryString.replace("|", " ");
        queryString = queryString.replace("+", " ");
        // queryString = queryString.replace("and", "");
        queryString = queryString.replace(" OR ", " ");
        // queryString = queryString.replace("or", "");
        // queryString = queryString.replace(",", " ");
        queryString = queryString.replace(":", " ");
        queryString = queryString.replaceAll("\\*", " ");
        queryString = queryString.replaceAll("\\^", "");
        // 优先标红精确查询关键词
        String[] accurateWords = queryString.split("\\s+");
        // String[] accurateWords = queryString.split(",|，");
        if (accurateWords.length > 0) {
            for (String accurateWord : accurateWords) {
                add2LightWords(str, lightWords, accurateWord);
            }
        }
        // 模糊查询时分词标红
//		if (!isAccurate) {
//			// IK false表示语义分词，词比较小，true表示精确拆词，词相对大一点
//			List<String> ikWords = new IKSegment().segment(queryString, true);
//			// 3-分词的结果合并到高亮词汇中
//			if (ikWords != null && ikWords.size() > 0) {
//				for (String tempStr : ikWords) {
//					add2LightWords(str, lightWords, tempStr);
//				}
//			}
//		}
        return lightWords;
    }

    /**
     * 加入一个新的待标红词至标红列表中
     *
     * @param str        待标红语句
     * @param lightWords 待标红词语列表
     * @param word       待标红词
     * <AUTHOR>
     * @date 2016-2-5 下午1:15:44
     */
    private static void add2LightWords(String str, List<String> lightWords, String word) {
        if (word == null || word.trim().length() == 0) {
            return;
        }
        if (lightWords.size() == 0) {
            lightWords.add(word);
            return;
        }
        int total = lightWords.size();
        for (int i = 0; i < total; i++) {
            if (lightWords.get(i).equals(word)) {
                break;
            } else if (word.contains(lightWords.get(i)) && str.contains(word)) {
                // word更长，替换当前位置
                lightWords.set(i, word);
                break;
            }
            if (i == lightWords.size() - 1) {
                lightWords.add(word);
            }
        }
    }

    /**
     * 一种保留源大小写的标红替换方法
     *
     * @param str   源字符串
     * @param regex 规则
     * @return String 带有html标红标签的串
     * <AUTHOR>
     * @date 2016-4-11 下午2:38:56
     */
    private static String replaceAll(String str, String regex) {
        // 匹配规则时不区分大小写
        Pattern p = Pattern.compile(regex, Pattern.CASE_INSENSITIVE);
        Matcher m = p.matcher(str);
        StringBuffer sb = new StringBuffer();
        while (m.find()) {
            // 拼接时保留原文的大小写
            m.appendReplacement(sb, "<em>" + m.group() + "</em>");
        }
        m.appendTail(sb);
        return sb.toString();
    }


    public static Set<String> extractEmTagContents(String text) {
        Set<String> contents = new HashSet<>();
        Pattern pattern = Pattern.compile("<em>(.*?)</em>");
        Matcher matcher = pattern.matcher(text);

        while (matcher.find()) {
            contents.add(matcher.group(1));
        }

        return contents;
    }

    public static void main(String[] a) {
        String queryStr = "all:(\"安徽\" ^安徽省 地方政府 中华人民共和国 安徽的方言) AND all:(\"地方\" \"地方政府\" \"大专\")";
        String queryStr2 = "安徽省 安徽的方言";
        System.out.println(highlighter("中华人民安徽省委地方政府 安徽的方言", queryStr));
        System.out.println(highlighter("中华人民安徽省委地方政府 安徽的方言", queryStr2, true));

//		List<String> ikWords = new IKSegment().segment("中国地方委员会 会员 体系制度", false);
//		System.out.println(ikWords.toString());
//		ikWords = new IKSegment().segment("中国地方委员会 会员 体系制度", true);
//		System.out.println(ikWords.toString());
    }
}
