package com.boryou.web.util.poi.custom;

import cn.hutool.core.io.FileUtil;
import org.apache.http.HttpResponse;
import org.apache.http.HttpStatus;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.HttpClients;
import org.apache.poi.openxml4j.exceptions.InvalidFormatException;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import sun.misc.BASE64Decoder;

import javax.imageio.ImageIO;
import java.awt.image.BufferedImage;
import java.io.*;
import java.math.BigInteger;

/**
 * 自定义段落
 *
 * <AUTHOR>
 * @date 2017-06-07 下午18:59:47
 */
public class CustomXWPFParagraph {

    private CustomXWPFDocument document;
    private XWPFParagraph xwpfParagraph;

    /**
     * 包装为自定义对象
     *
     * <AUTHOR>
     * @date 2017-06-07 下午19:05:42
     */
    public CustomXWPFParagraph(CustomXWPFDocument document, XWPFParagraph xwpfParagraph) {
        this.document = document;
        this.xwpfParagraph = xwpfParagraph;
    }

    public XWPFParagraph getXWPFParagraph() {
        return xwpfParagraph;
    }

    public void newLine() {
        xwpfParagraph.createRun().addBreak();
    }

    /**
     * 填充字体
     *
     * @param content    内容
     * @param fontFamily 字体(为空默认微软雅黑)
     * @param colorVal   颜色 如：000000 16进制
     * @param fontSize   字体大小
     * @param isBlod     加粗
     * @param isItalic   倾斜
     * <AUTHOR>
     * @date 2017-06-07 下午19:43:49
     */
    public void addText(String content, int fontSize, boolean isBlod, boolean isItalic, String fontFamily, String colorVal) {
        XWPFRun pRun = xwpfParagraph.createRun();
        //内容
        pRun.setText(content);
        // 设置字体样式
        pRun.setBold(isBlod);
        pRun.setItalic(isItalic);
        //颜色
        if (colorVal != null) {
            pRun.setColor(colorVal);
        }

        CTRPr pRpr = null;
        if (pRun.getCTR() != null) {
            pRpr = pRun.getCTR().getRPr();
            if (pRpr == null) {
                pRpr = pRun.getCTR().addNewRPr();
            }
        } else {
            pRpr = xwpfParagraph.getCTP().addNewR().addNewRPr();
        }
        // 设置字体
        CTFonts fonts = pRpr.isSetRFonts() ? pRpr.getRFonts() : pRpr.addNewRFonts();
        if (fontFamily == null) {
            fontFamily = "微软雅黑";
        }
        fonts.setAscii(fontFamily);
        fonts.setEastAsia(fontFamily);
        fonts.setHAnsi(fontFamily);

        // 设置字体大小
        CTHpsMeasure sz = pRpr.isSetSz() ? pRpr.getSz() : pRpr.addNewSz();
        sz.setVal(new BigInteger(fontSize + ""));

        CTHpsMeasure szCs = pRpr.isSetSzCs() ? pRpr.getSzCs() : pRpr
                .addNewSzCs();
        szCs.setVal(new BigInteger(fontSize + ""));
    }

    public void addText(String content, int fontSize, boolean isBlod, boolean isItalic) {
        addText(content, fontSize, isBlod, isItalic, null, null);
    }

    /**
     * 添加超链接
     *
     * <AUTHOR>
     * @date 2017-06-07 下午19:57:49
     */
    public void addHyperlink(String content, int fontSize, String url, String fontFamily) {
        // Add the link as External relationship
        String id = xwpfParagraph.getDocument().getPackagePart().addExternalRelationship(url, XWPFRelation.HYPERLINK.getRelation()).getId();
        // Append the link and bind it to the relationship
        CTHyperlink cLink = xwpfParagraph.getCTP().addNewHyperlink();
        cLink.setId(id);
        // Create the linked text
        CTText ctText = CTText.Factory.newInstance();
        ctText.setStringValue(content);
        CTR ctr = CTR.Factory.newInstance();
        CTRPr rpr = ctr.addNewRPr();

        // 设置超链接样式
        CTColor color = CTColor.Factory.newInstance();
        color.setVal("0000FF");
        rpr.setColor(color);
        rpr.addNewU().setVal(STUnderline.SINGLE);

        // 设置字体
        CTFonts fonts = rpr.isSetRFonts() ? rpr.getRFonts() : rpr.addNewRFonts();
        if (fontFamily == null) {
            fontFamily = "微软雅黑";
        }
        fonts.setAscii(fontFamily);
        fonts.setEastAsia(fontFamily);
        fonts.setHAnsi(fontFamily);

        // 设置字体大小
        CTHpsMeasure sz = rpr.isSetSz() ? rpr.getSz() : rpr.addNewSz();
        sz.setVal(new BigInteger(fontSize + ""));

        CTHpsMeasure szCs = rpr.isSetSzCs() ? rpr.getSzCs() : rpr.addNewSzCs();
        szCs.setVal(new BigInteger(fontSize + ""));

        ctr.setTArray(new CTText[]{ctText});
        cLink.setRArray(new CTR[]{ctr});
    }

    public void addHyperlink(String content, int fontSize, String url) {
        addHyperlink(content, fontSize, url, null);
    }

    /**
     * 设置对齐方式（水平、垂直）
     *
     * <AUTHOR>
     * @date 2017-06-07 下午19:10:17
     */
    public void setAlign(ParagraphAlignment paragraphAlignment, TextAlignment textAlignment) {
        xwpfParagraph.setAlignment(paragraphAlignment);
        xwpfParagraph.setVerticalAlignment(textAlignment);
    }

    // 设置段落缩进信息 1厘米≈567
    public void setParagraphIndInfo(String firstLine, String firstLineChar, String hanging, String hangingChar,
                                    String right, String rigthChar, String left, String leftChar) {
        CTPPr pPPr = null;
        if (xwpfParagraph.getCTP() != null) {
            if (xwpfParagraph.getCTP().getPPr() != null) {
                pPPr = xwpfParagraph.getCTP().getPPr();
            } else {
                pPPr = xwpfParagraph.getCTP().addNewPPr();
            }
        }
        CTInd pInd = pPPr.getInd() != null ? pPPr.getInd() : pPPr.addNewInd();
        if (firstLine != null) {
            pInd.setFirstLine(new BigInteger(firstLine));
        }
        if (firstLineChar != null) {
            pInd.setFirstLineChars(new BigInteger(firstLineChar));
        }
        if (hanging != null) {
            pInd.setHanging(new BigInteger(hanging));
        }
        if (hangingChar != null) {
            pInd.setHangingChars(new BigInteger(hangingChar));
        }
        if (left != null) {
            pInd.setLeft(new BigInteger(left));
        }
        if (leftChar != null) {
            pInd.setLeftChars(new BigInteger(leftChar));
        }
        if (right != null) {
            pInd.setRight(new BigInteger(right));
        }
        if (rigthChar != null) {
            pInd.setRightChars(new BigInteger(rigthChar));
        }
    }

    public void setParagraphIndInfo(String firstLine, String firstLineChar) {
        setParagraphIndInfo(firstLine, firstLineChar, null, null, null, null, null, null);
    }

    // 设置段落间距信息
    // 一行=100 一磅=20
    public void setParagraphSpacingInfo(XWPFParagraph p, boolean isSpace,
                                        String before, String after, String beforeLines, String afterLines,
                                        boolean isLine, String line, STLineSpacingRule.Enum lineValue) {
        CTPPr pPPr = null;
        if (p.getCTP() != null) {
            if (p.getCTP().getPPr() != null) {
                pPPr = p.getCTP().getPPr();
            } else {
                pPPr = p.getCTP().addNewPPr();
            }
        }
        CTSpacing pSpacing = pPPr.getSpacing() != null ? pPPr.getSpacing()
                : pPPr.addNewSpacing();
        if (isSpace) {
            // 段前磅数
            if (before != null) {
                pSpacing.setBefore(new BigInteger(before));
            }
            // 段后磅数
            if (after != null) {
                pSpacing.setAfter(new BigInteger(after));
            }
            // 段前行数
            if (beforeLines != null) {
                pSpacing.setBeforeLines(new BigInteger(beforeLines));
            }
            // 段后行数
            if (afterLines != null) {
                pSpacing.setAfterLines(new BigInteger(afterLines));
            }
        }
        if (isLine) {
            if (line != null) {
                pSpacing.setLine(new BigInteger(line));
            }
            if (lineValue != null) {
                pSpacing.setLineRule(lineValue);
            }
        }
    }

    /**
     * 段落下添加echarts图片
     *
     * <AUTHOR>
     * @date 2017-06-07 下午20:56:34
     */
    public void addEchartsPicture(String echartsData) {
        try {
            if (echartsData == null || echartsData.isEmpty()) {
                return;
            }
            //取出eharts数据并解析为byte数组
            String base64Data = echartsData.substring(echartsData.indexOf(",") + 1);
            byte[] bytes = new BASE64Decoder().decodeBuffer(base64Data);
            //目前都是png格式
            addPicture(bytes, Document.PICTURE_TYPE_PNG);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加本地图片
     *
     * <AUTHOR>
     * @date 2017-06-07 下午21:07:50
     */
    public void addNormalPicture(String imgPath) {
        try {
            //判断类型
            String suffixName = "." + FileUtil.getSuffix(imgPath);
            int type = 0;
            if (".png".equals(suffixName)) {
                type = Document.PICTURE_TYPE_PNG;
            } else if (".jpg".equals(suffixName) || ".jpeg".equals(suffixName)) {
                type = Document.PICTURE_TYPE_JPEG;
            }
            //读取字节
            byte[] bytes = readInputSteam(new FileInputStream(new File(imgPath)));
            //目前都是png格式
            addPicture(bytes, Document.PICTURE_TYPE_PNG);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 添加本地图片
     *
     * <AUTHOR>
     * @date 2017-06-07 下午21:07:50
     */
    public void addNetPicture(String url) {
        try {
            HttpClient httpClient = HttpClients.createDefault();
            HttpGet httpGet = new HttpGet(url);
            HttpResponse response = httpClient.execute(httpGet);
            if (response.getStatusLine().getStatusCode() == HttpStatus.SC_OK) {
                String contentType = response.getFirstHeader("Content-Type").getValue();
                int type = 0;
                if (contentType.contains("/png")) {
                    type = Document.PICTURE_TYPE_PNG;
                } else if (contentType.contains("/jpg") || contentType.contains("/jpeg")) {
                    type = Document.PICTURE_TYPE_JPEG;
                }
                //读取字节
                byte[] bytes = readInputSteam(response.getEntity().getContent());
                //判断图片格式
                addPicture(bytes, Document.PICTURE_TYPE_PNG);
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    private void addPicture(byte[] bytes, int type) {
        try {
            //获取宽高
            BufferedImage bufferedImage = ImageIO.read(new ByteArrayInputStream(bytes));
            int width = bufferedImage.getWidth();
            int height = bufferedImage.getHeight();
            //等比例缩放
            if (width == 0 || height == 0) {
                width = 500;
                height = 300;
            } else {
                if (width > 500) {
                    height = Math.round(height * 500 / width);
                    width = 500;
                }
            }
            String blipId = xwpfParagraph.getDocument().addPictureData(new ByteArrayInputStream(bytes), type);
            document.createPicture(xwpfParagraph, blipId, document.getNextPicNameNumber(type), width, height);
        } catch (InvalidFormatException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        }
    }

    /**
     * 读取输入流，装载至byte数组
     *
     * <AUTHOR>
     * @date 2017-06-12 下午14:13:51
     */
    private byte[] readInputSteam(InputStream is) {
        ByteArrayOutputStream baos = null;
        byte[] bytes = null;
        try {
            baos = new ByteArrayOutputStream();
            byte[] buffer = new byte[1024];
            int ch;
            while ((ch = is.read(buffer)) != -1) {
                baos.write(buffer, 0, ch);
            }
            bytes = baos.toByteArray();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (baos != null) {
                try {
                    baos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
            if (is != null) {
                try {
                    is.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return bytes;
    }

}
