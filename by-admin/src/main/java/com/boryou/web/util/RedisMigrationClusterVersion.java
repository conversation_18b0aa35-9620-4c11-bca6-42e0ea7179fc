package com.boryou.web.util;

import org.apache.commons.pool2.impl.GenericObjectPoolConfig;
import redis.clients.jedis.*;

import java.util.HashSet;
import java.util.List;
import java.util.Map;
import java.util.Set;

/**
 * redis迁移工具-集群版本！！
 *
 * <AUTHOR>
 * @date 2024-07-22 11:50
 */
public class RedisMigrationClusterVersion {

    public static String keyPrefix = "yq-zhejiang-court:";

    public static void main(String[] args) {
//        clusterToSingle();   //集群到非集群的迁移
        singleToCluster();    //非集群到集群的迁移
    }

    private static void singleToCluster() {
        //单机节点 源 Redis 实例的主机地址和端口
        String sourceHost = "*************";//随便指定集群中一个ip进行测试，如果报错，就换一个试试
        int sourcePort = 6379;
        String sourcePassword = "c@q5Zdf9Pfgk@F4L3lWT"; // 源Redis密码
        int sourceDbIndex = 10; // 源数据库索引

        //目标redis--集群节点
        String destHost = "**************";
        int destPort = 6379;


        // 集群节点  指定一个应该就可以同步其他节点了
//        Set<HostAndPort> nodes = new HashSet<>();
//        HostAndPort e = new HostAndPort(destHost, destPort);
//        nodes.add(e);

        // 连接到源和目标 Redis 实例，并选择相应的数据库
        Set<HostAndPort> nodes = new HashSet<>();
        nodes.add(new HostAndPort(destHost, destPort));
//        JedisCluster cluster1 = new JedisCluster(nodes);
        JedisCluster cluster = new JedisCluster(nodes, 100, 100, 12, "u_yq-zhejiang-court", "UkYtj%Hnfr6RaVeB", "11", new GenericObjectPoolConfig<>());
        Map<String, JedisPool> clusterNodes = cluster.getClusterNodes();
        Set<String> strings = clusterNodes.keySet();
        System.out.println("---集群节点有:" + strings.size() + "个");
        nodes = new HashSet<>();
        for (String string : strings) {
            String ip = string.substring(0, string.indexOf(":"));
            int port = Integer.parseInt(string.substring(string.indexOf(":") + 1));
            System.out.println("ip:" + ip + ",port:" + port);
            nodes.add(new HostAndPort(ip, port));
        }
        cluster.close();
        cluster = new JedisCluster(nodes, 100, 100, 12, "u_yq-zhejiang-court", "UkYtj%Hnfr6RaVeB", "11", new GenericObjectPoolConfig<>());


        Jedis sourceDest = new Jedis(sourceHost, sourcePort);
        sourceDest.auth(sourcePassword); // 认证密码
        sourceDest.select(sourceDbIndex);

        saveToCluster(sourceDest, cluster);

        System.out.println("---迁移完成");
        cluster.close();
        sourceDest.close();
    }

    private static void saveToCluster(Jedis source, JedisCluster destination) {
//        destination.set(keyPrefix+"aaa","111111");
        // 键的前缀和扫描参数
        String cursor = "0";
        int count = 10; // 每次扫描返回的键的数量
        ScanParams scanParams = new ScanParams().count(count).match(keyPrefix + "*");
        do {
            // 扫描源 Redis 中的键
            ScanResult<String> scanResult = source.scan(cursor, scanParams);
            List<String> keys = scanResult.getResult();

            // 遍历扫描结果中的键
            for (String key : keys) {
                String type = source.type(key);
                System.out.println("---" + key);
                switch (type) {
                    case "string":
                        String stringValue = source.get(key);
                        destination.set(key, stringValue);
                        break;
                    case "list":
                        List<String> listValues = source.lrange(key, 0, -1);
                        destination.del(key);
                        for (int i = 0; i < listValues.size(); i++) {
//                            System.out.println(key+"--------------------------------"+listValues.size());
                            destination.lpush(key, listValues.get(i));
                        }
                        break;
                    case "set":
                        Set<String> setValues = source.smembers(key);
                        destination.del(key);
                        setValues.forEach(value -> destination.sadd(key, value));
                        break;
                    case "zset":
                        Set<Tuple> zsetValues = source.zrangeWithScores(key, 0, -1);
                        destination.del(key);
                        zsetValues.forEach(tuple -> destination.zadd(key, tuple.getScore(), tuple.getElement()));
                        break;
                    case "hash":
                        Map<String, String> hashValues = source.hgetAll(key);
                        destination.del(key);
                        hashValues.forEach((field, value) -> destination.hset(key, field, value));
                        break;
                    default:
                        System.out.println("Unsupported type for key: " + key);
                }
            }
            // 准备下一次扫描
            cursor = scanResult.getCursor();
        } while (!"0".equals(cursor));
    }

    /**
     * 集群redis->非集群节点-迁移！！ 如果某一个节点离线或报错，会跳过该节点数据
     * 重复执行该方法会覆盖目标redis的key，并不会产生重复数据
     *
     * <AUTHOR>
     * @date 2024/7/22 14:22
     **/
    public static void clusterToSingle() {
        //指定需要迁移的key
        String keyPrefix = "yq-zhejiang-court:user_habits*";  //也可以是：yq-zhejiang-court*

        // 源 Redis 实例的主机地址和端口
        String sourceHost = "**************";//随便指定集群中一个ip进行测试，如果报错，就换一个试试
        int sourcePort = 7001;
        String sourcePassword = ""; // 源Redis密码

        //目标redis--非集群节点
        String destHost = "************";
        int destPort = 6379;
        String destPassword = "76EVgQ1ITAMCoe8t"; // 目标Redis密码

        // 源和目标 Redis 的数据库索引
        int destDbIndex = 0;   // 目标数据库索引

        Jedis destination = new Jedis(destHost, destPort);
        destination.auth(destPassword); // 认证密码
        destination.select(destDbIndex);

        // 连接到源和目标 Redis 实例，并选择相应的数据库
        Set<HostAndPort> nodes = new HashSet<>();
        nodes.add(new HostAndPort(sourceHost, sourcePort));
        JedisCluster cluster = new JedisCluster(nodes);
        Map<String, JedisPool> clusterNodes = cluster.getClusterNodes();
        Set<String> strings = clusterNodes.keySet();
        System.out.println("---集群节点有:" + strings.size() + "个");
        nodes = new HashSet<>();
        for (String string : strings) {
            String ip = string.substring(0, string.indexOf(":"));
            int port = Integer.parseInt(string.substring(string.indexOf(":") + 1));
            System.out.println("ip:" + ip + ",port:" + port);
            nodes.add(new HostAndPort(ip, port));
        }
        cluster.close();
        JedisCluster cluster1 = new JedisCluster(nodes);
        clusterNodes = cluster1.getClusterNodes();
        System.out.println("开始迁移");
        for (String k : clusterNodes.keySet()) {
            JedisPool jp = clusterNodes.get(k);
            try (Jedis source = jp.getResource()) {
                Set<String> keys = source.keys(keyPrefix);
                for (String key : keys) {
                    String type = source.type(key);
                    System.out.println("---" + key);
                    switch (type) {
                        case "string":
                            String stringValue = source.get(key);
                            destination.set(key, stringValue);
                            break;
                        case "list":
                            List<String> listValues = source.lrange(key, 0, -1);
                            destination.del(key);
                            for (int i = 0; i < listValues.size(); i++) {
                                destination.lpush(key, listValues.get(i));
                            }
                            break;
                        case "set":
                            Set<String> setValues = source.smembers(key);
                            destination.del(key);
                            setValues.forEach(value -> destination.sadd(key, value));
                            break;
                        case "zset":
                            Set<Tuple> zsetValues = source.zrangeWithScores(key, 0, -1);
                            destination.del(key);
                            zsetValues.forEach(tuple -> destination.zadd(key, tuple.getScore(), tuple.getElement()));
                            break;
                        case "hash":
                            Map<String, String> hashValues = source.hgetAll(key);
                            destination.del(key);
                            hashValues.forEach((field, value) -> destination.hset(key, field, value));
                            break;
                        default:
                            System.out.println("Unsupported type for key: " + key);
                    }
                }
            } catch (Exception e) {

            }
        }
        System.out.println("---迁移完成");
        cluster1.close();
    }

    ///////////////////////用于测试////////////////////////////////
    private static void test1() {
        Set<HostAndPort> nodes = new HashSet<>();
        nodes.add(new HostAndPort("**************", 7001)); // 示例节点1
        JedisCluster cluster = new JedisCluster(nodes);
        ScanParams scanParams = new ScanParams().match("{*}").count(1000);
        ScanResult<String> keys1 = cluster.scan("0", scanParams);
        System.out.println(keys1.getCursor());
        System.out.println(keys1.getResult());
        System.out.println("----" + keys1);
        cluster.close();
    }

    public static void test2() {
        Set<HostAndPort> nodes = new HashSet<>();
        nodes.add(new HostAndPort("**************", 7000)); // 示例节点1
        nodes.add(new HostAndPort("**************", 7001)); // 示例节点2
        nodes.add(new HostAndPort("**************", 7000)); // 示例节点2
        nodes.add(new HostAndPort("**************", 7001)); // 示例节点2
        nodes.add(new HostAndPort("**************", 7000)); // 示例节点2
        nodes.add(new HostAndPort("**************", 7001)); // 示例节点2
        JedisCluster cluster = new JedisCluster(nodes);
        Map<String, JedisPool> clusterNodes = cluster.getClusterNodes();

        for (String k : clusterNodes.keySet()) {
            JedisPool jp = clusterNodes.get(k);
            try (Jedis connection = jp.getResource()) {
                Set<String> keys = connection.keys("yq-zhejiang-court*");
                for (String key : keys) {
                    String s = connection.get(key);
                    System.out.println("---" + key);
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        cluster.close();
    }

}

