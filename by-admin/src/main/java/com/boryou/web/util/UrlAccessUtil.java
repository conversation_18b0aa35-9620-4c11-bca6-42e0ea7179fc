package com.boryou.web.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.web.constant.BC;
import lombok.extern.slf4j.Slf4j;

/**
 * 判断URL可达性的工具类
 *
 * <AUTHOR>
 */
@Slf4j
public class UrlAccessUtil {

    public static JSONObject getUrlState(String url) {
        String status;
        try {
            HttpResponse execute = HttpUtil.createGet(BC.CHECK_URL + url, true).execute();
            status = execute.body();
            if (StrUtil.isEmpty(status) || 200 != execute.getStatus()) {
                return new JSONObject();
            }
            return JSONUtil.parseObj(status);
        } catch (Exception e) {
            System.out.println("【err】UrlAccess：" + url);
            return new JSONObject();
        }
    }

    //status 1正常  0不在(删除)
    public static int check(String checkUrl, String url) {
//        String urls = checkUrl + url;
//        String status;
        try {
            if (StrUtil.isEmpty(url)) {
                return 0;
            }
//            if (url.contains("www.iesdouyin.com") || url.contains("mp.weixin.qq.com") ||
//                    url.contains("www.toutiao.com") || url.contains("www.weibo.com") || url.contains("m.weibo.cn") || url.contains("www.douyin.com")) {
//                HttpResponse execute = HttpUtil.createGet(urls, true).execute();
//                status = execute.body();
////            log.info("url:{},状态接口返回：{}",url,status);
//                JSONObject jsonObject = JSONUtil.parseObj(status);
//                if ("1".equals(jsonObject.getStr("status"))) {
//                    return 1;
//                } else {
//                    return 0;
//                }
//            } else {
                return checkNew(url);
//            }
        } catch (Exception e) {
            log.error("【err】UrlAccess：{}", url);
            return 0;
        }
    }

    public static int checkNew(String url) {
        int status;
        try {
            status = HttpUtil.createGet(url, true).setConnectionTimeout(2000).setReadTimeout(1500).execute().getStatus();//2000是一个估值，防止请求太慢
        } catch (Exception e) {
            log.error("【err】UrlAccess：" + url);
            return 0;
        }
        if (status == 200) {
            return 1;
        }
        return 0;
    }
}
