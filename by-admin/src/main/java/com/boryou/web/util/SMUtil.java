package com.boryou.web.util;

import cn.hutool.core.util.HexUtil;
import cn.hutool.crypto.Mode;
import cn.hutool.crypto.Padding;
import cn.hutool.crypto.symmetric.SM4;

public class SMUtil {
    private static final String PRIVATE_KEY = "d1147455c1693fcf8d51c9954a9f6dcb";
    private static final SM4 SM4 = new SM4(Mode.ECB, Padding.PKCS5Padding, HexUtil.decodeHex(PRIVATE_KEY));


    private SMUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 加密数据
     */
    public static String encryptHex(String data) {
        // 输出编码
        return SM4.encryptHex(data);
    }

    /**
     * 解密数据
     */
    public static String decrypt(String data) {
        return SM4.decryptStr(data);
    }

}
