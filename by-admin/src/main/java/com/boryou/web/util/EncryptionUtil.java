package com.boryou.web.util;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;

/**
 * 解密算法
 *
 * <AUTHOR>
 * @time 2016年6月21日 下午12:39:29
 */
public class EncryptionUtil {
	    /**
	     * 解密方法
	     * @param data 要解密的数据
	     * @param key  解密key
	     * @param iv 解密iv
	     * @return 解密的结果
	     * @throws Exception
	     */
	    public static String desEncrypt(String data, String key, String iv) throws Exception {
	        try {
	            byte[] encrypted1 = Base64.decodeBase64(data);

	            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
	            SecretKeySpec keyspec = new SecretKeySpec(key.getBytes(), "AES");
	            IvParameterSpec ivspec = new IvParameterSpec(iv.getBytes());

	            cipher.init(Cipher.DECRYPT_MODE, keyspec, ivspec);

	            byte[] original = cipher.doFinal(encrypted1);
	            String originalString = new String(original);
	            return originalString;
	        } catch (Exception e) {
	            e.printStackTrace();
	            return null;
	        }
	    }
}
