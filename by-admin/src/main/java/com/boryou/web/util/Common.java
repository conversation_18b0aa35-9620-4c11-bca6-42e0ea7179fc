package com.boryou.web.util;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024-11-22 11:12
 */
public class Common {



    public static Date stringToDate(String dateStr) {
        try {
            return stringToDate(dateStr, getDefaultDateFormats());
        } catch (ParseException var2) {
            return null;
        }
    }

    public static Date stringByFillZeroToDate(String time) {
        if (isNullOrEmpty(time)) {
            return null;
        } else {
            time = time.replaceAll("[-: ]", "");
            if (time.length() == 4) {
                time = time + "0000000000";
            } else if (time.length() == 5) {
                time = time + "000000000";
            } else if (time.length() == 6) {
                time = time + "00000000";
            } else if (time.length() == 7) {
                time = time + "0000000";
            } else if (time.length() == 8) {
                time = time + "000000";
            } else if (time.length() == 9) {
                time = time + "00000";
            } else if (time.length() == 10) {
                time = time + "0000";
            } else if (time.length() == 11) {
                time = time + "000";
            } else if (time.length() == 12) {
                time = time + "00";
            } else if (time.length() == 13) {
                time = time + "0";
            } else if (time.length() == 3) {
                time = time + "00000000000";
            } else if (time.length() == 2) {
                time = time + "000000000000";
            } else if (time.length() == 1) {
                time = time + "0000000000000";
            }

            return stringToDateWithFormat(time, "yyyyMMddHHmmss");
        }
    }    public static Date stringToDateWithFormat(String str, String format) {
        Date date = null;
        if (!isNullOrEmpty(str)) {
            try {
                date = (new SimpleDateFormat(format)).parse(str);
            } catch (ParseException var4) {
                date = stringToDate(str);
            }
        }

        return date;
    }

    public static Date stringToDate(String dateStr, Collection<String> dateFormats) throws ParseException {
        if (dateStr != null && dateStr.length() != 0) {
            if (dateFormats == null) {
                dateFormats = getDefaultDateFormats();
            }

            Iterator<String> formatIterator = dateFormats.iterator();
            SimpleDateFormat dateParser = null;

            while(formatIterator.hasNext()) {
                String format = (String)formatIterator.next();
                if (dateParser == null) {
                    dateParser = new SimpleDateFormat(format);
                } else {
                    dateParser.applyPattern(format);
                }

                try {
                    return dateParser.parse(dateStr);
                } catch (ParseException var6) {
                }
            }

            throw new ParseException("Unable to parse the date " + dateStr, 0);
        } else {
            return null;
        }
    }

    private static Collection<String> getDefaultDateFormats() {
        Collection<String> defaultDateFormats = new ArrayList();
        defaultDateFormats.add("yyyy-MM-dd HH:mm:ss");
        defaultDateFormats.add("yyyy-MM-dd HH:mm");
        defaultDateFormats.add("yyyyMMddHHmmss");
        defaultDateFormats.add("yyyy-MM-dd");
        defaultDateFormats.add("yyyyMMddHHmm");
        defaultDateFormats.add("yyyy-MM");
        defaultDateFormats.add("yyyyMMdd");
        defaultDateFormats.add("yyyyMM");
        defaultDateFormats.add("yyyy年MM月dd日 HH时mm分ss秒");
        defaultDateFormats.add("yyyy年MM月dd日");
        defaultDateFormats.add("yyyy年MM月");
        defaultDateFormats.add("yyyy年");
        defaultDateFormats.add("yyyy");
        return defaultDateFormats;
    }

    public static String dateToShortString(Date date) {
        return dateToStringWithFormat(date, "yyyy-MM-dd");
    }

    public static String dateToLongString(Date date) {
        return dateToStringWithFormat(date, "yyyy-MM-dd HH:mm:ss");
    }

    public static String dateToShortStringNoSep(Date date) {
        return dateToStringWithFormat(date, "yyyyMMdd");
    }

    public static String dateToLongStringNoSep(Date date) {
        return dateToStringWithFormat(date, "yyyyMMddHHmmss");
    }

    public static String dateToStringWithFormat(Date date, String format) {
        String str = "";
        if (date != null && format != null && format.length() != 0) {
            try {
                str = (new SimpleDateFormat(format)).format(date);
            } catch (Exception var4) {
            }

            return str;
        } else {
            return str;
        }
    }

    public static String beginsOnThatDay(String time) {
        return beginsOnThatDay(stringToDate(time));
    }

    public static String beginsOnThatDay(Date date) {
        return shortTimeToLongTime(dateToShortStringNoSep(date), "000000");
    }

    public static String endOnThatDay(String time) {
        return endOnThatDay(stringToDate(time));
    }

    public static String endOnThatDay(Date date) {
        return shortTimeToLongTime(dateToShortStringNoSep(date), "235959");
    }

    public static String beginsOnThatDate(String time, int dateField, int interval) {
        return beginsOnThatDate(stringToDate(time), dateField, interval);
    }

    public static String beginsOnThatDate(Date date, int dateField, int interval) {
        String shortTime = dateToShortStringNoSep(addDate(date, dateField, interval));
        return shortTimeToLongTime(shortTime, "000000");
    }

    public static String endOnThatDate(String time, int dateField, int interval) {
        return endOnThatDate(stringToDate(time), dateField, interval);
    }

    public static String endOnThatDate(Date date, int dateField, int interval) {
        String shortTime = dateToShortStringNoSep(addDate(date, dateField, interval));
        return shortTimeToLongTime(shortTime, "235959");
    }

    public static String shortTimeToLongTime(String shortTime, String hhmmssString) {
        if (!isNullOrEmpty(shortTime) && shortTime.length() == 8 && !isNullOrEmpty(hhmmssString) && hhmmssString.length() == 6) {
            return shortTime + hhmmssString;
        } else {
            return shortTime == null ? "" : shortTime;
        }
    }

    public static Date addMilliseconds(Date date, int interval) {
        return addDate(date, 14, interval);
    }

    public static Date addSeconds(Date date, int interval) {
        return addDate(date, 13, interval);
    }

    public static Date addMinutes(Date date, int interval) {
        return addDate(date, 12, interval);
    }

    public static Date addHours(Date date, int interval) {
        return addDate(date, 11, interval);
    }

    public static Date addDays(Date date, int interval) {
        return addDate(date, 5, interval);
    }

    public static Date addWeeks(Date date, int interval) {
        return addDate(date, 3, interval);
    }

    public static Date addMonths(Date date, int interval) {
        return addDate(date, 2, interval);
    }

    public static Date addYears(Date date, int interval) {
        return addDate(date, 1, interval);
    }

    public static Date addDate(Date date, int dateField, int interval) {
        if (date == null) {
            return new Date();
        } else {
            Calendar cal = Calendar.getInstance();
            cal.setTime(date);
            cal.add(dateField, interval);
            return cal.getTime();
        }
    }


    public static String timeStrToLongFormatNoSep(String timeStr) {
        return unknownTimeFormatToDestFormat(timeStr, "yyyyMMddHHmmss");
    }

    public static String unknownTimeFormatToDestFormat(String unknownFormatTime, String destFormat) {
        try {
            return dateToStringWithFormat(stringToDate(unknownFormatTime), destFormat);
        } catch (Exception var3) {
            Exception e = var3;
            e.printStackTrace();
            return "";
        }
    }

    public static boolean isDateIn(Date date, Date startDate, Date endDate) {
        return !date.before(startDate) && !date.after(endDate);
    }

    public static boolean isDateGreaterOrEqual(Date date, Date dateLimit) {
        return !date.before(dateLimit);
    }

    public static boolean isDateLessOrEqual(Date date, Date dateLimit) {
        return !date.after(dateLimit);
    }

    public static long stringToTimeStamp(String dateStr) {
        try {
            if (dateStr != null && dateStr.length() > 0) {
                Date date = stringToDate(dateStr);
                return date == null ? 0L : date.getTime();
            }
        } catch (Exception var2) {
            System.out.println("日期转换出现异常");
        }

        return 0L;
    }

    public static long stringToTimeStampWithFormat(String dateStr, String timeFormat) {
        try {
            if (dateStr != null && dateStr.length() > 0) {
                SimpleDateFormat format = new SimpleDateFormat(timeFormat);
                Date date = format.parse(dateStr);
                return date.getTime();
            }
        } catch (Exception var4) {
            System.out.println("日期转换出现异常");
        }

        return 0L;
    }

    public static boolean isNullOrEmptyOrNegativeOne(String str) {
        return isNullOrEmpty(str) || "-1".equals(str.trim());
    }

    public static boolean isNullOrEmptyOrEqual(String str, String str2) {
        return isNullOrEmpty(str) || str.trim().equals(str2);
    }

    public static boolean isNullOrEmpty(String str) {
        return str == null || str.trim().length() == 0;
    }

    public static boolean isNullOrEmpty(Object object) {
        return object == null || String.valueOf(object).trim().length() == 0;
    }

    public static boolean isNullOrEmpty(Collection<?> collection) {
        return collection == null || collection.size() == 0;
    }

    public static boolean containsNullOrEmpty(String str1, String str2) {
        return isNullOrEmpty(str1) || isNullOrEmpty(str2);
    }

    public static boolean containsNullOrEmpty(String... strs) {
        if (isNullOrEmpty((Object[])strs)) {
            return true;
        } else {
            String[] var1 = strs;
            int var2 = strs.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                String str = var1[var3];
                if (isNullOrEmpty(str)) {
                    return true;
                }
            }

            return false;
        }
    }

    public static boolean containsNullOrEmpty(Object... objects) {
        if (isNullOrEmpty(objects)) {
            return true;
        } else {
            Object[] var1 = objects;
            int var2 = objects.length;

            for(int var3 = 0; var3 < var2; ++var3) {
                Object str = var1[var3];
                if (isNullOrEmpty(str)) {
                    return true;
                }
            }

            return false;
        }
    }

    public static boolean isNull(Object object) {
        return object == null;
    }

    public static boolean containsNull(Object object1, Object object2) {
        return object1 == null || object2 == null;
    }

    public static boolean containsNull(Object object1, Object object2, Object object3) {
        return object1 == null || object2 == null || object3 == null;
    }

    public static boolean isNullOrEmpty(Object[] array) {
        return array == null || array.length == 0;
    }

    public static String join(String[] values, String separator) {
        StringBuilder result = new StringBuilder();
        if (values != null && values.length > 0) {
            String[] var3 = values;
            int var4 = values.length;

            for(int var5 = 0; var5 < var4; ++var5) {
                Object value = var3[var5];
                result.append(value).append(separator);
            }

            result = new StringBuilder(result.substring(0, result.length() - 1));
        }

        return result.toString();
    }

    public static String[] toStringArray(Object object) {
        if (object == null) {
            return null;
        } else {
            String[] strArray = null;
            int i;
            if (object instanceof Object[]) {
                Object[] objArray = (Object[])((Object[])object);
                if (objArray.length > 0) {
                    strArray = new String[objArray.length];

                    for(i = 0; i < objArray.length; ++i) {
                        strArray[i] = String.valueOf(objArray[i]);
                    }
                }
            } else if (object instanceof List) {
                List<?> list = (List)object;
                if (list.size() > 0) {
                    strArray = new String[list.size()];

                    for(i = 0; i < list.size(); ++i) {
                        strArray[i] = String.valueOf(list.get(i));
                    }
                }
            }

            return strArray;
        }
    }

    public static List<String> stringToList(String str, String separator) {
        return (List)(str != null && str.trim().length() > 0 ? new ArrayList(Arrays.asList(str.split(separator))) : Collections.emptyList());
    }

    public static int getIndex(String regex, String input, int group) {
        int index = -1;

        try {
            Pattern p = Pattern.compile(regex);
            Matcher m = p.matcher(input);
            if (m.matches()) {
                index = m.start(group);
            }
        } catch (Exception var6) {
            Exception e = var6;
            e.printStackTrace();
        }

        convertStringToList("", "");
        return index;
    }

    public static List<String> convertStringToList(String str, String separator) {
        return stringToList(str, separator);
    }

    public static boolean isMixType(int type) {
        return type == 4 || type == 5 || type == 8 || type == 9 || type == 10 || type == 12 || type == 14 || type == 15 || type == 16 || type == 19;
    }

    public static String[] splitStrByComma(String str) {
        return str != null && str.length() != 0 ? str.split("\\s*,\\s*|\\s+") : new String[0];
    }

    public static Set<Integer> splitStrByCommaToSet(String str) {
        String[] strArray = splitStrByComma(str);
        Set<Integer> set = new HashSet();
        String[] var3 = strArray;
        int var4 = strArray.length;

        for(int var5 = 0; var5 < var4; ++var5) {
            String string = var3[var5];
            if (string.trim().length() != 0) {
                set.add(Integer.valueOf(string));
            }
        }

        return set;
    }

    public static void waitThreadsFinish(List<Thread> threads) {
        long startTime = System.currentTimeMillis();
        Iterator var3 = threads.iterator();

        while(var3.hasNext()) {
            Thread thread = (Thread)var3.next();

            try {
                thread.join();
            } catch (InterruptedException var6) {
                InterruptedException e = var6;
                e.printStackTrace();
            }
        }

        long endTime = System.currentTimeMillis();
        System.out.println("join花费时间：" + (endTime - startTime));
    }

    public static void sleepWait(long millis) {
        if (millis > 0L) {
            try {
                Thread.sleep(millis);
            } catch (InterruptedException var3) {
                InterruptedException e = var3;
                e.printStackTrace();
            }

        }
    }



    public static String getTypeName(int type) {
        String typeName;
        switch (type) {
            case -1:
                typeName = "默认值";
                break;
            case 0:
                typeName = "论坛";
                break;
            case 1:
                typeName = "新闻";
                break;
            case 2:
                typeName = "博客";
                break;
            case 3:
                typeName = "微博";
                break;
            case 4:
                typeName = "QQ群";
                break;
            case 5:
                typeName = "微信";
                break;
            case 6:
                typeName = "客户端";
                break;
            case 7:
                typeName = "互动问答";
                break;
            case 8:
                typeName = "广播";
                break;
            case 9:
                typeName = "电视";
                break;
            case 10:
                typeName = "图片";
                break;
            case 11:
                typeName = "网视";
                break;
            case 12:
                typeName = "电商";
                break;
            case 13:
                typeName = "学术";
                break;
            case 14:
                typeName = "招聘";
                break;
            case 15:
                typeName = "招投标";
                break;
            case 16:
                typeName = "人事变动";
                break;
            case 17:
                typeName = "电子报";
                break;
            case 18:
                typeName = "百度百科";
                break;
            default:
                typeName = "暂时未识别到";
        }

        return typeName;
    }

    public static String urlDecoder(String encodeUrl) {
        if (isNullOrEmpty(encodeUrl)) {
            return encodeUrl;
        } else {
            try {
                return URLDecoder.decode(encodeUrl, "UTF-8");
            } catch (UnsupportedEncodingException var2) {
                UnsupportedEncodingException e = var2;
                e.printStackTrace();
                return encodeUrl;
            }
        }
    }

    public static String urlEncoder(String url) {
        if (isNullOrEmpty(url)) {
            return url;
        } else {
            try {
                return URLEncoder.encode(url, "UTF-8");
            } catch (UnsupportedEncodingException var2) {
                UnsupportedEncodingException e = var2;
                e.printStackTrace();
                return url;
            }
        }
    }

    public String replaceCRLF(String sourceStr) {
        String replaceSource = null;
        if (!isNullOrEmpty(sourceStr)) {
            Pattern p = Pattern.compile("\\s*|\t|\r|\n");
            Matcher m = p.matcher(sourceStr);
            replaceSource = m.replaceAll("");
        }

        return replaceSource;
    }

    public static float round(double num, int scale) {
        int roundingMode = 4;
        BigDecimal bd = new BigDecimal(num);
        bd = bd.setScale(scale, roundingMode);
        return bd.floatValue();
    }

    public static String subtractTimeShorthandToLongTimeStr(String timeShorthand) {
        return isNullOrEmpty(timeShorthand) ? "" : dateToLongStringNoSep(addDays(new Date(), -timeShorthandToDay(timeShorthand)));
    }

    public static int timeShorthandToDay(String timeShorthand) {
        return (int)(timeShorthandToMs(timeShorthand) / 86400000L);
    }

    public static long timeShorthandToMs(String timeShorthand) {
        if (isNullOrEmpty(timeShorthand)) {
            return 0L;
        } else {
            try {
                long ms;
                if (timeShorthand.endsWith("d")) {
                    ms = Long.parseLong(timeShorthand.replaceAll("d", "")) * 24L * 60L * 60L * 1000L;
                } else if (timeShorthand.contains("h")) {
                    ms = Long.parseLong(timeShorthand.replaceAll("h", "")) * 60L * 60L * 1000L;
                } else if (timeShorthand.endsWith("m")) {
                    ms = Long.parseLong(timeShorthand.replaceAll("m", "")) * 60L * 1000L;
                } else if (timeShorthand.endsWith("s")) {
                    ms = Long.parseLong(timeShorthand.replaceAll("s", "")) * 1000L;
                } else {
                    Date now;
                    if (timeShorthand.endsWith("M")) {
                        now = new Date();
                        ms = now.getTime() - addMonths(now, -Integer.parseInt(timeShorthand.replaceAll("M", ""))).getTime();
                    } else if (timeShorthand.endsWith("y")) {
                        now = new Date();
                        ms = now.getTime() - addYears(now, -Integer.parseInt(timeShorthand.replaceAll("y", ""))).getTime();
                    } else {
                        ms = Long.parseLong(timeShorthand) * 1000L;
                    }
                }

                return ms;
            } catch (Exception var4) {
                Exception e = var4;
                e.printStackTrace();
                return 0L;
            }
        }
    }

    public static void main(String[] args) {
        System.out.println(stringByFillZeroToDate("1999-11-22 11:22:33"));
    }
}

