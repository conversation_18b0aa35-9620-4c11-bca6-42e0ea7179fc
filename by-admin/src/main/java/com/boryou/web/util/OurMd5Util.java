package com.boryou.web.util;

import cn.hutool.core.util.StrUtil;

import java.math.BigInteger;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;

public class OurMd5Util {

    private static final int MD5LEN = 16;

    public static long getId(String url, int type) {
        if (StrUtil.isNotBlank(url)) {
            return getMd5(url + type);
        } else {
            throw new NullPointerException("生成ID的url不能为空");
        }
    }

    public static long getIdSpecialForFile(String url, int type, String sector) {
        if (StrUtil.isNotBlank(url)) {
            return getMd5(url + type + sector);
        } else {
            throw new NullPointerException("生成ID的url不能为空");
        }
    }

    public static byte[] getSign(String text) {
        byte[] sign = new byte[]{-44, 29, -116, -39, -113, 0, -78, 4, -23, -128, 9, -104, -20, -8, 66, 126};
        if (text == null) {
            return sign;
        } else {
            try {
                sign = text.getBytes(StandardCharsets.UTF_8);
            } catch (Exception var4) {
                var4.printStackTrace();
            }

            try {
                MessageDigest digester = MessageDigest.getInstance("MD5");
                digester.update(sign);
                sign = digester.digest();
                if (sign.length == 16) {
                    return sign;
                }
            } catch (NoSuchAlgorithmException var3) {
                var3.printStackTrace();
            }

            return new byte[]{-44, 29, -116, -39, -113, 0, -78, 4, -23, -128, 9, -104, -20, -8, 66, 126};
        }
    }

    public static long getMd5(String text) {
        return (new BigInteger(getSign(text))).longValue();
    }
}
