package com.boryou.web.util;

import java.util.HashMap;
import java.util.Map;

/**
 * 实体识别词性颜色表
 *
 * <AUTHOR>
 * @date 2017-6-21 上午11:51:57
 */
public class WordColorUtil {

    private static Map<String, String> map = new HashMap<>();

    /**
     * 根据词性获取背景色
     *
     * @param key 词性
     * @return String 颜色代码
     * <AUTHOR>
     * @date 2017-6-21 上午11:52:14
     */
    public static String getColor(String key) {
        if (map.isEmpty()) {
            map.put("/n", "#337ab7"); // 名词
            map.put("/v", "#5cb85c");// 动词
            map.put("/a", "#5bc0de");// 形容词
            map.put("/t", "#f0ad4e");// 时间词
            map.put("/f", "#d9534f");// 方位词
            map.put("/m", "#f19ec2");// 数词
            map.put("/r", "#FFBFFF");// 代词
            map.put("/s", "#CA8EFF");// 处所词
            map.put("/b", "#AAAAFF");// 区别词
            map.put("/z", "#97CBFF");// 状态词
            map.put("/q", "#84C1FF");// 量词
            map.put("/d", "#67ddab");// 副词
            map.put("/y", "#BBFFBB");// 语气词
            map.put("/o", "#DEFFAC");// 拟声词
            map.put("/x", "#E2C2DE");// 字符串
            map.put("/p", "#FFFFCE");// 介词
            map.put("/c", "#FFE6D9");// 连词
            map.put("/u", "#D9B3B3");// 助词
            map.put("/e", "#CDCD9A");// 叹词
            map.put("/w", "#b4b4b4");// 标点符号
            map.put("/h", "#A3D1D1");// 前缀
            map.put("/k", "#B8B8DC");// 后缀

            map.put("/", "#FF0000");// 默认，自定义
        }

        return map.get(key);
    }

}
