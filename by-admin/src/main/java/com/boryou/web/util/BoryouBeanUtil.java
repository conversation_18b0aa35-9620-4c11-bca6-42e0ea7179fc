package com.boryou.web.util;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HtmlUtil;
import com.boryou.web.controller.common.entity.BoryouBean;

import java.util.List;

public class BoryouBeanUtil {

    /**
     * 过滤数据的img标签
     */
    public static List<BoryouBean> removeImgTag(List<BoryouBean> boryouBeans) {
        if (boryouBeans == null) {
            return boryouBeans;
        }
        for (BoryouBean boryouBean : boryouBeans) {
            //移除正文的html标签
            if (!StrUtil.isEmptyIfStr(boryouBean.getText())) {
                boryouBean.setText(HtmlUtil.removeHtmlTag(boryouBean.getText(), "img", "section", "p"));
            }
            if (!StrUtil.isEmptyIfStr(boryouBean.getUntreatedText())) {
                boryouBean.setUntreatedText(HtmlUtil.removeHtmlTag(boryouBean.getUntreatedText(), "img", "section", "p"));
            }
        }
        return boryouBeans;
    }

    /**
     * 过滤数据的img标签
     */
    public static BoryouBean removeImgTag(BoryouBean boryouBean) {
        if (boryouBean == null) {
            return boryouBean;
        }
        //移除正文的img标签
        if (!StrUtil.isEmptyIfStr(boryouBean.getText())) {
            boryouBean.setText(HtmlUtil.removeHtmlTag(boryouBean.getText(), "img"));
        }
        if (!StrUtil.isEmptyIfStr(boryouBean.getUntreatedText())) {
            boryouBean.setUntreatedText(HtmlUtil.removeHtmlTag(boryouBean.getUntreatedText(), "img"));
        }
        return boryouBean;
    }

    /**
     * 对图片数据进行处理--电视
     */
    public static BoryouBean convertPic(BoryouBean boryouBean) {
        if (boryouBean == null) {
            return boryouBean;
        }
        List<String> picUrl = boryouBean.getPicUrl();
        if (picUrl != null && picUrl.size() > 0) {
            boryouBean.setAuthorPortraitLink(picUrl.get(0));
        }
        return boryouBean;
    }

    /**
     * 对图片数据进行处理--电视
     */
    public static List<BoryouBean> convertPic(List<BoryouBean> boryouBeans) {
        if (boryouBeans == null || boryouBeans.size() == 0) {
            return boryouBeans;
        }
        for (BoryouBean boryouBean : boryouBeans) {
            List<String> picUrl = boryouBean.getPicUrl();
            if (picUrl != null && picUrl.size() > 0) {
                boryouBean.setAuthorPortraitLink(picUrl.get(0));
            }
        }
        return boryouBeans;
    }
}
