package com.boryou.web.util.poi;

import com.boryou.web.util.poi.custom.*;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.math.BigInteger;

/**
 * poi 生成word工具
 *
 * <AUTHOR>
 * @date 2017-06-07 下午18:21:05
 */
public class POIWordUtil {


    //文档对象
    private CustomXWPFDocument document = new CustomXWPFDocument();

    /**
     * 创建段落
     *
     * <AUTHOR>
     * @date 2017-06-07 下午19:00:25
     */
    public CustomXWPFParagraph createParagraph() {
        return getCoveredParagraph(document.createParagraph());
    }

    /**
     * 包装段落
     *
     * <AUTHOR>
     * @date 2017-06-12 上午11:45:27
     */
    public CustomXWPFParagraph getCoveredParagraph(XWPFParagraph paragraph) {
        return new CustomXWPFParagraph(document, paragraph);
    }

    /**
     * 创建表格
     *
     * <AUTHOR>
     * @date 2017-06-08 上午09:58:01
     */
    public XWPFTable createTable(int rows, int cols) {
        return document.createTable(rows, cols);
    }

    /**
     * 添加标题
     *
     * <AUTHOR>
     * @date 2017-06-07 下午20:49:39
     */
    public void addTitle(String headStyle, String content, int fontSize, boolean isBlod, boolean isItalic, String fontFamily, String colorVal) {
        XWPFParagraph xwpfParagraph = document.createParagraph();
        XWPFRun pRun = xwpfParagraph.createRun();
        xwpfParagraph.setStyle(headStyle);
        //内容
        pRun.setText(content);
        // 设置字体样式
        pRun.setBold(isBlod);
        pRun.setItalic(isItalic);
        //颜色
        if (colorVal != null) {
            pRun.setColor(colorVal);
        }

        CTRPr pRpr = null;
        if (pRun.getCTR() != null) {
            pRpr = pRun.getCTR().getRPr();
            if (pRpr == null) {
                pRpr = pRun.getCTR().addNewRPr();
            }
        } else {
            pRpr = xwpfParagraph.getCTP().addNewR().addNewRPr();
        }
        // 设置字体
        CTFonts fonts = pRpr.isSetRFonts() ? pRpr.getRFonts() : pRpr.addNewRFonts();
        if (fontFamily == null) {
            fontFamily = "微软雅黑";
        }
        fonts.setAscii(fontFamily);
        fonts.setEastAsia(fontFamily);
        fonts.setHAnsi(fontFamily);

        // 设置字体大小
        CTHpsMeasure sz = pRpr.isSetSz() ? pRpr.getSz() : pRpr.addNewSz();
        sz.setVal(new BigInteger(fontSize + ""));

        CTHpsMeasure szCs = pRpr.isSetSzCs() ? pRpr.getSzCs() : pRpr
                .addNewSzCs();
        szCs.setVal(new BigInteger(fontSize + ""));
    }

    public void addTitle(String headStyle, String content, int fontSize, boolean isBlod, boolean isItalic) {
        addTitle(headStyle, content, fontSize, isBlod, isItalic, null, null);
    }

    /**
     * word跨列合并单元格
     *
     * @author: Young
     * @Date: 2021/7/16 19:09
     */
    public static void mergeCellsHorizontal(XWPFTable table, int row, int fromCell, int toCell) {
        for (int cellIndex = fromCell; cellIndex <= toCell; cellIndex++) {
            XWPFTableCell cell = table.getRow(row).getCell(cellIndex);
            if (cellIndex == fromCell) {
                // The first merged cell is set with RESTART merge value
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.RESTART);
            } else {
                // Cells which join (merge) the first one, are set with CONTINUE
                cell.getCTTc().addNewTcPr().addNewHMerge().setVal(STMerge.CONTINUE);
            }
        }
    }

    /**
     * 添加换行符或分页符
     *
     * <AUTHOR>
     * @date 2017-06-07 下午20:01:06
     */
    public void addNewBreak(BreakType breakType) {
        document.createParagraph().createRun().addBreak(breakType);
    }

    /**
     * 保存文件
     *
     * <AUTHOR>
     * @date 2017-06-07 下午20:01:38
     */
    public File saveDocument(String savePath) {
        File file = new File(savePath);
        return saveDocument(file);
    }

    /**
     * 保存文件
     *
     * <AUTHOR>
     * @date 2017-06-07 下午20:05:15
     */
    public File saveDocument(File file) {
        FileOutputStream fos = null;
        try {
            fos = new FileOutputStream(file);
            document.write(fos);
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (fos != null) {
                try {
                    fos.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        return file;
    }

    public static void main(String[] args) {
        POIWordUtil poiWordUtil = new POIWordUtil();

        poiWordUtil.addTitle(HeadStyle.H1.getStyleName(), "标题一", 30, true, false);
        poiWordUtil.addTitle(HeadStyle.H2.getStyleName(), "标题一", 26, true, false);
        poiWordUtil.addTitle(HeadStyle.H3.getStyleName(), "标题一", 22, true, false);

        CustomXWPFParagraph paragraph = poiWordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.CENTER);
        paragraph.addText("媒体统计图", 16, true, false);
        paragraph.addText("123", 16, true, false);
        paragraph.addText("123", 16, true, false);
        paragraph.addHyperlink("123", 16, "http://www.baidu.com/");

        paragraph.newLine();
        paragraph.addNetPicture("https://ss1.bdstatic.com/kvoZeXSm1A5BphGlnYG/newmusic/yaogun.png?v=md5");

        poiWordUtil.saveDocument("C:\\Users\\<USER>\\Desktop\\1.doc");
    }

    /**
     * 添加自定义标题信息以显示结构图
     *
     * <AUTHOR>
     * @date 2017-06-07 下午20:30:31
     */
    private void addCustomHeadingStyle(String strStyleId, int headingLevel) {

        CTStyle ctStyle = CTStyle.Factory.newInstance();
        ctStyle.setStyleId(strStyleId);

        CTString styleName = CTString.Factory.newInstance();
        styleName.setVal(strStyleId);
        ctStyle.setName(styleName);

        CTDecimalNumber indentNumber = CTDecimalNumber.Factory.newInstance();
        indentNumber.setVal(BigInteger.valueOf(headingLevel));

        // lower number > style is more prominent in the formats bar
        ctStyle.setUiPriority(indentNumber);

        CTOnOff onoffnull = CTOnOff.Factory.newInstance();
        ctStyle.setUnhideWhenUsed(onoffnull);

        // style shows up in the formats bar
        ctStyle.setQFormat(onoffnull);

        // style defines a heading of the given level
        CTPPr ppr = CTPPr.Factory.newInstance();
        ppr.setOutlineLvl(indentNumber);
        ctStyle.setPPr(ppr);

        XWPFStyle style = new XWPFStyle(ctStyle);

        // is a null op if already defined
        XWPFStyles styles = document.createStyles();

        style.setType(STStyleType.PARAGRAPH);
        styles.addStyle(style);

    }


    /**
     * 表格的边框颜色
     *
     * @author: Young
     * @Date: 2021/7/16 14:51
     */
    public static void tableBorderColor(CTTblBorders borders, String top, String right, String bottom, String left) {
        //上
        CTBorder tBorder = borders.addNewTop();
        tBorder.setVal(STBorder.SINGLE);
        tBorder.setSz(new BigInteger("2"));
        tBorder.setColor(top);
        //右
        CTBorder rBorder = borders.addNewRight();
        rBorder.setVal(STBorder.SINGLE);
        rBorder.setSz(new BigInteger("2"));
        rBorder.setColor(right);

        //下
        CTBorder bBorder = borders.addNewBottom();
        bBorder.setVal(STBorder.SINGLE);
        bBorder.setSz(new BigInteger("1"));
        bBorder.setColor(bottom);
        //左
        CTBorder lBorder = borders.addNewLeft();
        lBorder.setVal(STBorder.SINGLE);
        lBorder.setSz(new BigInteger("1"));
        lBorder.setColor(left);
    }

    /**
     * 删除段落
     *
     * @author: Young
     * @Date: 2021/7/16 18:04
     */
    public static void deleteParagraph(XWPFParagraph p) {
        XWPFDocument doc = p.getDocument();
        int pPos = doc.getPosOfParagraph(p);
        //doc.getDocument().getBody().removeP(pPos);
        doc.removeBodyElement(pPos);
    }

    public static XWPFTableCell mergeCellsAndSetContent(XWPFTable table, int fromRow, int toRow, int fromCol, int toCol, int oneCellWidth, String content, FontSize fontSize, boolean isBold, boolean isItalic, String fontFamily) {
        //合并单元格
        TableUtil.mergeCells(table, fromRow, toRow, fromCol, toCol);
        //设置宽度
        XWPFTableCell cell = table.getRow(fromRow).getCell(fromCol);
        if (oneCellWidth > 0) {
            TableUtil.setCellWidth(cell, (toCol - fromCol) * oneCellWidth);
        }
        addCellContent(cell, content, fontSize, isBold, isItalic, fontFamily, null, ParagraphAlignment.CENTER, TextAlignment.CENTER);
        return cell;
    }

    public static XWPFTableCell mergeCellsAndSetContentLeft(XWPFTable table, int fromRow, int toRow, int fromCol, int toCol, int oneCellWidth, String content, FontSize fontSize, boolean isBold, boolean isItalic, String fontFamily) {
        //合并单元格
        TableUtil.mergeCells(table, fromRow, toRow, fromCol, toCol);
        //设置宽度
        XWPFTableCell cell = table.getRow(fromRow).getCell(fromCol);
        if (oneCellWidth > 0) {
            TableUtil.setCellWidth(cell, (toCol - fromCol) * oneCellWidth);
        }
        addCellContent(cell, content, fontSize, isBold, isItalic, fontFamily, null, ParagraphAlignment.LEFT, TextAlignment.CENTER);
        return cell;
    }

    public static void addCellContent(XWPFTableCell cell, String content, FontSize fontSize, boolean isBold, boolean isItalic, String fontFamily, String colorVal, ParagraphAlignment paragraphAlignment, TextAlignment textAlignment) {
        if (cell == null) {
            return;
        }
        //设置单元格内容居中
        cell.setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);

        if (content == null) {
            content = "";
        }
        //判断是否存在换行，多行分割为多个段落
        String[] contents = content.split("\n");
        for (int i = 0; i < contents.length; i++) {
            String str = contents[i];
            XWPFParagraph paragraph;
            //第一次段落直接获取，第二次为添加
            if (i == 0 && cell.getParagraphs().size() > 0) {
                if (cell.getParagraphs().size() > 1 || cell.getParagraphs().get(0).getText().length() > 0) {
                    paragraph = cell.addParagraph();
                } else {
                    paragraph = cell.getParagraphs().get(0);
                }
            } else {
                paragraph = cell.addParagraph();
            }
            //设置对齐方式
            if (paragraphAlignment != null) {
                paragraph.setAlignment(paragraphAlignment);
            }
            paragraph.setVerticalAlignment(textAlignment == null ? TextAlignment.CENTER : textAlignment);
            //段落为左对齐，设置缩进2字符
            if (paragraphAlignment == ParagraphAlignment.LEFT) {
                ParagraphUtil.setParagraphIndInfo(paragraph, "400", "200");
            }
            //填充内容
            ParagraphUtil.addText(paragraph, str, fontSize.getSize(), isBold, isItalic, fontFamily, colorVal);
        }
    }

    public static String insertPlusSigns(String text, int lengthOneLine) {
        StringBuilder formattedText = new StringBuilder();
        int lengthCounter = 0; // 用于计数字符长度

        for (char ch : text.toCharArray()) {
            // 如果是中文字符，则增加2个长度
            if (Character.UnicodeBlock.of(ch) == Character.UnicodeBlock.CJK_UNIFIED_IDEOGRAPHS
                    || ch == '，' || ch == '“' || ch == '”' || ch == '。' || ch == '…' || ch == '、' || ch == '（' || ch == '）' || ch == '∶' || ch == '—' || ch == '【' || ch == '】') {
                lengthCounter += 2;
            } else {
                lengthCounter += 1;
            }

            if (lengthCounter == lengthOneLine - 1 || lengthCounter == lengthOneLine) {
                formattedText.append('\n');
                lengthCounter = 0;
            }

            // 添加当前字符
            formattedText.append(ch);
        }

        return formattedText.toString();
    }
}
