package com.boryou.web.util;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import com.boryou.common.utils.DateUtils;
import com.boryou.web.controller.bigscreen.vo.Plate;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.github.pagehelper.PageInfo;

import java.util.*;


/**
 * 用于各种索引统计
 *
 * <AUTHOR>
 * @date 2017-6-28 下午7:21:55
 */
public class IndexStatistics {

    public static Map<String, Integer> getTodayTypeCountFromEs(Plate plate) {
        // 获取当前时间作为结束时间
//        Date today = new Date();
//        String endTime = DateUtil.format(today,"yyyy-MM-dd HH:mm:ss");
//        Date start;
//        if (plate.getTimeRegion() > 0) {
//            // 根据定制的timeIndex,计算开始时间
//            start = Common.addDays(today, -1 * plate.getTimeRegion());
//        } else {// 若time和timeRegion不是有效值，则默认时间范围为当前的3天
//            start = Common.addDays(today, -3);
//        }
//        String startTime = DateUtil.format(start,"yyyy-MM-dd HH:mm:ss");
//        plate.setStartTime(startTime);
//        plate.setEndTime(endTime);
        DateTime dateTime = DateUtil.offsetDay(new Date(), -1);
        plate.setStartTime(DateUtils.beginOfHourStr(dateTime, "yyyyMMddHHmmss"));
        plate.setEndTime(DateUtils.endOfHourStr(new Date()));
//        EsSearchBO esSearchBO = EsSearchUtil.convertPlateBeanToEsSearchBO(plate);
        EsSearchBO esSearchBO=new EsSearchBO();
        esSearchBO.setStartTime(plate.getStartTime());
        esSearchBO.setEndTime(plate.getEndTime());

        return EsSearchUtil.getMediaTypeMap(esSearchBO);
    }


    public static Map<String, Integer> getTodayTypeCountByEs(Plate plate) {
        // 获取当前时间作为结束时间
        Date today = new Date();
        String endTime = Common.dateToLongString(today);
        Date start;
        if (plate.getTimeRegion() > 0) {
            // 根据定制的timeIndex,计算开始时间
            start = Common.addDays(today, -1 * plate.getTimeRegion());
        } else {// 若time和timeRegion不是有效值，则默认时间范围为当前的3天
            start = Common.addDays(today, -3);
        }
        String startTime = Common.dateToLongString(start);
        // return getTypeCount(plate, startTime, endTime);
        EsSearchBO esSearchBO = new EsSearchBO();
        esSearchBO.setStartTime(startTime);
        esSearchBO.setEndTime(endTime);
        Map<String, Integer> integerMap = EsSearchUtil.mediaTypeMap(esSearchBO);
        Map<String, Integer> res = new HashMap<>();
        for (MediaTypeEnum value : MediaTypeEnum.values()) {
            String key = String.valueOf(value.getValue());
            Integer num = integerMap.get(key);
            res.put(value.getDesc(), integerMap.get(key) == null ? 0 : num);
        }
        return res;

    }





}
