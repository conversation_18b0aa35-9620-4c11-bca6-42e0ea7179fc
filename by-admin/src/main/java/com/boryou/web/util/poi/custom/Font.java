package com.boryou.web.util.poi.custom;

/**
 * 导出word使用的字体
 *
 * <AUTHOR>
 * @date 2018-1-19 上午9:31:52
 */
public enum Font {

    FZDBSJT("方正大标宋简体"),
    BLACK("黑体"),
    FS_GB2312("仿宋_GB2312"),
    TIMES("Times New Roman"),
    ST("宋体");

    private String fontValue; // 字体对应的word标识

    Font(String fontValue) {
        this.fontValue = fontValue;
    }

    /**
     * 获取字体标识
     *
     * @return String 字体标识
     * <AUTHOR>
     * @date 2018-1-19 上午9:34:41
     */
    public String getFontValue() {
        return fontValue;
    }

}
