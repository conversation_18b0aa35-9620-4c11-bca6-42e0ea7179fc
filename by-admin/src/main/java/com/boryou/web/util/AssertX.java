package com.boryou.web.util;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;

import java.util.List;

public class AssertX {

    private AssertX() {
    }

    public static void checkStr(String str) {
        if (StrUtil.isBlank(str)) {
            throw new IllegalArgumentException("参数异常！");
        }
    }

    public static void strNotNull(String str) {
        if (StrUtil.isBlank(str)) {
            throw new IllegalArgumentException("参数不能为空！");
        }
    }

    public static void strNotNull(String paramName, String str) {
        if (StrUtil.isBlank(str)) {
            throw new IllegalArgumentException(String.format("参数%s不能为空！", paramName));
        }
    }

    public static void numberNull(int value, String name) {
        if (value < 1) {
            throw new IllegalArgumentException(String.format("参数%s不能为空！", name));
        }
    }

    public static void checkListNull(List list) {
        if (CollUtil.isEmpty(list)) {
            throw new IllegalArgumentException("参数异常！");
        }
    }

    public static void checkListNull(List list, String msg) {
        if (CollUtil.isEmpty(list)) {
            throw new IllegalArgumentException(msg);
        }
    }

}
