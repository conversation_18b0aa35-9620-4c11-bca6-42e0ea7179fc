package com.boryou.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

@Data
@TableName("by_holiday")
public class Holiday {

    /**
     * 表标识
     */
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 日期(2024-02-04)
     */
    private String day;

    /**
     * 节假日标志 (1表示是节假日，0表示是调休)
     */
    private Integer holidayFlag;

    /**
     * 节假日的中文名。如果是调休，则是调休的中文名，例如'国庆前调休'
     */
    private String name;

    /**
     * 薪资倍数，1表示是1倍工资
     */
    private Integer wage;

    /**
     * 只在调休下有该字段。true表示放完假后调休，false表示先调休再放假
     */
    private Integer after;

    /**
     * 只在调休下有该字段。表示调休的节假日
     */
    private String target;

    /**
     * 表示当前时间距离目标还有多少天。比如今天是 2018-09-28，距离 2018-10-01 还有3天
     */
    private Integer rest;
}
