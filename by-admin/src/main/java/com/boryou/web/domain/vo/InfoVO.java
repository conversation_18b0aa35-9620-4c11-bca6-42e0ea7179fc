package com.boryou.web.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

@Data
public class InfoVO {
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long planId;
    private String text;
    private String keyWords;
    private String time;
    private String md5;
    private Integer pageNum;
    private Integer pageSize;
    private String startTime;
    private String endTime;
    private String sort;
}
