package com.boryou.web.domain.vo;

import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 */
@Data
@NoArgsConstructor
public class TimeRoundFlowVO {
    /**
     * 传入时间范围开始时间
     */
    private String startTime;

    /**
     * 传入时间范围结束时间
     */
    private String endTime;

    public TimeRoundFlowVO(String startTime, String endTime) {
        this.startTime = startTime;
        this.endTime = endTime;
    }
}
