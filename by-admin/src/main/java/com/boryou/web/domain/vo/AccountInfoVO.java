package com.boryou.web.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.springframework.data.annotation.Id;

import java.util.Date;

@Data
public class AccountInfoVO {

    @Id
    private String id;

    private Integer accountBadge;

    private String accountId;

    private Integer accountLevel;

    private Integer authorSex;

    private String avatar;

    private String city;

    private String desc;

    private String domain;

    private Integer fansNumber;


    private String nickname;

    private String province;

    private String sector;

    private String signId;

    private Integer type;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date updateTime;

    private String url;

    private String accountGrade;
}
