package com.boryou.web.domain.msg;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/4 10:19
 */
//@TableName(value ="xxlt_msg_yuqing")
@Data
public class XxltMsgYuqing implements Serializable {

    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 短信内容
     */
    private String content;

    /**
     * 字段1-涉及法院
     */
    private String p1;

    /**
     * 字段2-风险等级
     */
    private String p2;

    /**
     * 字段3-来源
     */
    private String p3;

    /**
     * 字段4-事件摘要
     */
    private String p4;

    /**
     * 字段5-链接
     */
    private String p5;

    /**
     * 接收人
     */
    private String users;

    @TableField(exist = false)
    private List<Map<String, String>> usersObject;
    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}
