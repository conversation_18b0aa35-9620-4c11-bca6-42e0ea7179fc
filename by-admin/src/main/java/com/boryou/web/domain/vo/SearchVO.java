package com.boryou.web.domain.vo;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONObject;
import com.boryou.common.core.domain.entity.SysDictData;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.DictUtils;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.system.mapper.SysUserMapper;
import com.boryou.system.service.ISysDictTypeService;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Data
public class SearchVO {
    private String id;
    /**
     * 检索方案的id
     */
    private Long planId;

    private List<Long> ids;

    /**
     * 检索模式(0：普通模式，1：高级模式)
     */
    private Integer searchMode = 0;

    /**
     * 快速方案的关键词1
     */
    private String keyWord1;

    /**
     * 快速方案的关键词2
     */
    private String keyWord2;

    /**
     * 快速方案的关键词3
     */
    private String keyWord3;

    /**
     * 快速方案的二次搜索关键词
     */
    private String quadraticWord;
    /**
     * 二次搜索词位置
     */
    private String quadraticPosition;

    /**
     * 快速方案的排除词
     */
    private String excludeWord;

    /**
     * 快速方案的精准地域
     */
    private String area;

    /**
     * 专业方案的关键词
     */
    private String highMonitorWord;

    /**
     * 专业方案的关键词
     */
    private String highExcludeWord;

    /**
     * 专业方案的精准地域
     */
    private String highArea;

    /**
     * 以上为plate关键词条件  或者  全文搜索传递的关键词条件
     * 以下为页面传递的条件
     */

    /**
     * 检索范围(0：全部，1：标题，2：正文)
     */
    private String searchPosition;
    /**
     * 检索开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String startTime;
    /**
     * 检索结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private String endTime;
    /**
     * 检索数据去重
     */
    private Boolean isOriginal;
    /**
     * 检索数据的排序方式(3：时间降序，4：时间升序，7：相似数量降序)
     */
    private Integer sort;
    /**
     * 信息情感属性(0：中性，1：负面，2：正面)
     */
    private String emotionFlag;
    /**
     * 结果呈现(0：全部，1：正常，2：噪音)
     */
    private String noSpam;
    /**
     * 媒体类型
     */
    private String type;
    /**
     * 短视频媒体类型
     */
    private String videoHost;
    /**
     * 微博内容  1文本，2链接，3视频，4图片，音频，游戏，文件
     */
    private String contentForm;
    /**
     * 微博类型 0 全部  1原创   2转载
     */
    private String forward;

    private String settingType; // 设置类型  1定向选择 2定向排除 3仅使用定向信源
    /**
     * 大V级别 600：金V
     * 0:橙V
     * 1-9：蓝V
     * 200，220，达人
     * -1 普通用户
     */
    private List<String> accountLevel;
    /**
     * 信源等级  央级，省级，地市，重点，中小，企业商业
     */
    private String accountGrade;
    /**
     * 微博地域
     */
    private String accountAreaCode;
    /**
     * 内容地域
     */
    private String contentAreaCode;
    /**
     * md5
     */
    private String md5;
    /**
     * 页大小
     */
    private int pageSize = 10;
    /**
     * 页码
     */
    private int pageNum = 1;
    /**
     * 时间
     */
    private Integer timeIndex;
    /**
     * 处置 0 无 1 已处置
     */
    private Integer deal;
    /**
     * 重点关注 0 无 1 已关注
     */
    private Integer follow;
    /**
     * 预警 0 无 1 已预警
     */
    private Integer warned;
    /**
     * 收藏 0 无 1 已收藏
     */
    private Integer like;

    /**
     * host查询
     */
    private String host;

    /**
     * 作者查询
     */
    private String author;
    //指定用户
    private Long userId;

    private Long indexId;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 舆情监测报告导出 echarts图片
     */
    private JSONObject data;

    /**
     * 查看记录  1是手机端访问
     */
    private String viewFlag;

    /**
     * 大屏传时间间隔
     */
    private Integer offset;

    public EsSearchBO convertEsSearchBO() {
        return convertEsSearchBO(SecurityUtils.getUserId(null));
    }

    public EsSearchBO convertEsSearchBO(String userId) {
        EsSearchBO bo = new EsSearchBO();
        if (CollUtil.isNotEmpty(this.getAccountLevel())) {
            String accountLevel = CollUtil.join(this.getAccountLevel(), ",");
            bo.setAccountLevel(accountLevel);
        }
        BeanUtil.copyProperties(this, bo, "accountLevel");
        getSearchTimeRange(bo);

        if (searchMode != null) {
            bo.setConfigSelect(searchMode);
            if (searchMode == 0) {
                bo.setContentAreaCode(area);
                if (keyWord1 == null) bo.setKeyWord1("");
                if (keyWord2 == null) bo.setKeyWord2("");
                if (keyWord3 == null) bo.setKeyWord3("");
                bo.setKeyWord4("");
            }
            if (searchMode == 1) {
                bo.setProWord(highMonitorWord);
                bo.setExcludeWord(highExcludeWord);
                bo.setContentAreaCode(highArea);
            }
        }

        if (contentAreaCode != null && !"100000".equals(contentAreaCode)) {
            bo.setContentAreaCode(contentAreaCode);
        }
        if (noSpam != null && "1".equals(noSpam)) {
            bo.setIsSpam(false);
        }
        if (noSpam != null && "2".equals(noSpam)) {
            bo.setIsSpam(true);
        }
        if (forward != null && ("1".equals(forward) || "2".equals(forward))) {
            bo.setForward(forward);
        }
        bo.setSortType(sort);
        if (CollUtil.isNotEmpty(ids)) {
            bo.setId(CollUtil.join(ids, ","));
            bo.setSortType(3);
            bo.setIsOriginal(null);
            bo.setPageNum(1);
            bo.setPageSize(ids.size());
        }
        if (StrUtil.isEmpty(type)) {
            List<SysDictData> dictCache = getDictFilter("sys_media_type", userId);
            if (dictCache != null) {
                String type = dictCache.stream().map(SysDictData::getDictValue).collect(Collectors.joining(","));
                bo.setType(type);
            }
        } else {
            String sysMediaType = getDictFilterType(type, "sys_media_type", userId);
            bo.setType(sysMediaType);
        }
        if (planId == null && (keyWord1 != null && (keyWord1.contains("+") || keyWord1.contains("|") || keyWord1.contains("(")))) {
            //全文搜索中使用高级搜索 word1和word2失效 否则是普通搜索
            bo.setConfigSelect(1);
            bo.setProWord(bo.getKeyWord1());
            if (StrUtil.isNotEmpty(bo.getKeyWord2())) {
                bo.setProWord("(" + bo.getKeyWord1() + ") + (" + bo.getKeyWord2() + ")");
            }
        }
        if (StrUtil.isNotEmpty(settingType)) {
            bo.setSourceSetting(settingType);
        }
        return bo;
    }

    public String getDictFilterType(String type, String dictType, String userId) {
        if (CharSequenceUtil.isBlank(type) || !type.contains("24") || CharSequenceUtil.isBlank(userId)) {
            return type;
        }

        SysUserMapper sysUserMapper = SpringUtil.getBean(SysUserMapper.class);
        SysUser sysUser = sysUserMapper.selectUserByIdStr(userId);

        if (DictUtils.checkFilter(dictType, sysUser)) {
            type = CharSequenceUtil.splitTrim(type, ",").stream()
                    .filter(item -> !CharSequenceUtil.equals(item, "24"))
                    .collect(Collectors.joining(","));
        }
        return type;
    }

    public List<SysDictData> getDictFilter(String dictType, String userId) {
        ISysDictTypeService dictTypeService = SpringUtil.getBean(ISysDictTypeService.class);
        List<SysDictData> data1 = dictTypeService.selectDictDataByType(dictType);
        if (CollUtil.isEmpty(data1) || CharSequenceUtil.isBlank(userId)) {
            return data1;
        }

        SysUserMapper sysUserMapper = SpringUtil.getBean(SysUserMapper.class);
        SysUser sysUser = sysUserMapper.selectUserByIdStr(userId);

        if (DictUtils.checkFilter(dictType, sysUser)) {
            data1 = data1.stream().filter(item -> !CharSequenceUtil.equals(item.getDictValue(), "24")).collect(Collectors.toList());
        }
        return data1;
    }

    public EsSearchBO convertSimpleEsSearchBO() {
        EsSearchBO bo = new EsSearchBO();
        BeanUtil.copyProperties(this, bo);
        getSearchTimeRange(bo);
        bo.setConfigSelect(searchMode);
        if (searchMode == 0) {
            bo.setContentAreaCode(area);
        }
        if (searchMode == 1) {
            bo.setProWord(highMonitorWord);
            bo.setExcludeWord(highExcludeWord);
            bo.setContentAreaCode(highArea);
        }
        if (planId == null && (keyWord1 != null && (keyWord1.contains("+") || keyWord1.contains("|") || keyWord1.contains("(")))) {
            //全文搜索中使用高级搜索 word1和word2失效 否则是普通搜索
            bo.setConfigSelect(1);
            bo.setProWord(bo.getKeyWord1());
            if (StrUtil.isNotEmpty(bo.getKeyWord2())) {
                bo.setProWord("(" + bo.getKeyWord1() + ") + (" + bo.getKeyWord2() + ")");
            }
        }
        return bo;
    }

    public void getSearchTimeRange(EsSearchBO bo) {
        if (timeIndex != null) {
            if (timeIndex == 0) {
                bo.setStartTime(DateUtil.format(DateUtil.beginOfDay(DateUtil.date()), DatePattern.NORM_DATETIME_PATTERN));
            } else {
                bo.setStartTime(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -timeIndex), DatePattern.NORM_DATETIME_PATTERN));
            }
            bo.setEndTime(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
        }
    }

    //前端传的参数校验，如果是yyyymmddhhmmss就转为yyyy-mm-dd hh:mm:ss
    public String getStartTime() {
        if (StrUtil.isNotEmpty(startTime) && !startTime.contains(" ")) {
            startTime = DateUtil.formatDateTime(DateUtil.parse(startTime, DatePattern.PURE_DATETIME_FORMAT));
        }
        return startTime;
    }
}
