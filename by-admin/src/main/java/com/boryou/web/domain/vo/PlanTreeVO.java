package com.boryou.web.domain.vo;

import com.boryou.web.domain.PlanMain;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 */
@Data
public class PlanTreeVO {
    //@JsonSerialize(using = ToStringSerializer.class)
    //private Long typeId;
    //@JsonSerialize(using = ToStringSerializer.class)
    //private Long planId;
    //private String planName;
    //private String typeName;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;
    private String name;
    private String planType;
    private List<PlanMain> planMain;
    private List<PlanTreeVO> children;
    private int expire;
    private int historyFlag;
    private int tracking;
    private Long sort;
}
