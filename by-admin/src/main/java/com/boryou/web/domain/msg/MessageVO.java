package com.boryou.web.domain.msg;

import cn.hutool.json.JSONObject;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-18 17:43
 */
@Data
public class MessageVO {
    private String courtName;
    private String riskLevel;
    private String source;
    private String summary;
    private String url;
    private String messageText;
    private List<String> phoneList;

    private JSONObject formdata;
    private Long deptId;
    private String md5;
    private String phone;
    /**
     * 文档id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long docIndexId;
    /**
     * 短信联系人多个
     */
    private List<String> userIds;


    /**
     * 处置截至时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date deadline;

    /**
     * 处理时间范围
     */
    private String deadlineNum;

    /**
     * 处置建议
     */
    private String suggest;

    private String userName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;
}

