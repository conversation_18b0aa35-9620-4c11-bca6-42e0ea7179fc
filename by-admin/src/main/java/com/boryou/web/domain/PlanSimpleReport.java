package com.boryou.web.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName(value = "by_plan_simple_report")
public class PlanSimpleReport {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long tempId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long planId;

    /**
     * 报告类型 1日报 2周报 3月报
     */
    private Integer reportType;

    /**
     * 报告生成状态 是否启用 1启用 0禁用[默认]
     */
    private Integer status;

    /**
     * 生成报告的日期  日报：星期数（多个） 周报：星期数（单个） 月报：每月第几天（单个）
     */
    private String reportDate;

    /**
     * 生成报告的时间  每5分钟生成一次符合生成时间的报告数据   每1分钟推送已生成的报告
     */
    private String reportTime;

    /**
     * 接收人邮箱 最多20人
     */
    private String receiverEmails;

    /**
     * 报告发送状态 0 无未发送文件 1 有未发送文件
     */
    private Integer sendStatus;

}
