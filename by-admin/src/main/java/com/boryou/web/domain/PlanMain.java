package com.boryou.web.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 各种重点关注方案对象 by_plan_main
 *
 * <AUTHOR>
 * @date 2024-05-23
 */
@Data
@TableName("by_plan_main")
public class PlanMain {

    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.NONE)
    private Long id;


    @JsonSerialize(using = ToStringSerializer.class)
    private Long planId;

    /**
     * 重点关注类型
     */
    @Excel(name = "重点关注类型")
    private String mainType;

    /**
     * 用户id
     */
    @Excel(name = "用户id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

}
