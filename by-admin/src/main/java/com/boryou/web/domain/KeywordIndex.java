package com.boryou.web.domain;

import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * 关键词搜索、资讯指数表
 * <AUTHOR>
 */
@Data
@TableName("by_keyword_index")
public class KeywordIndex {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关键词
     */
    private String keyword;

    /**
     * 日期
     */
    private String day;

    /**
     * 指数
     */
    private Long index;

    /**
     * 类型（1：搜索指数，2：资讯指数）
     */
    private String type;

    /**
     * 省（包括自治区、直辖市）
     */
    private String province;

    /**
     * 市（包括自治州、盟、省辖市、直辖市辖区(县)）
     */
    private String city;

    /**
     * 数据更新时间
     */
    private String createTime;
}
