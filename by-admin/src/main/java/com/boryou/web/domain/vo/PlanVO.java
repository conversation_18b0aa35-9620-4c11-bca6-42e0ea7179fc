package com.boryou.web.domain.vo;

import com.boryou.web.domain.Plan;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

@EqualsAndHashCode(callSuper = true)
@Data
public class PlanVO extends Plan {
    private List<PlanMainVO> planMainList;
    //当前方案id
    private String originPlanId;
    //处于目标方案的位置  1是上面  0是下面
    private String position;

    //1是重点关注
    private String mainType;
}
