package com.boryou.web.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2024-06-14 17:18
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
public class BaseSimpleVO {
    private String name;
    private Integer count;
    private int type;
    private String hostDown;

    public BaseSimpleVO(String name, Integer count) {
        this.name = name;
        this.count = count;
    }

    public BaseSimpleVO(String name, Integer count, String hostDown) {
        this.name = name;
        this.count = count;
        this.hostDown = hostDown;
    }

    @Override
    public boolean equals(Object o) {
        if (this == o) return true;
        if (o == null || getClass() != o.getClass()) return false;
        BaseSimpleVO that = (BaseSimpleVO) o;
        return Objects.equals(name, that.name) /*&& Objects.equals(count, that.count)*/;
    }

    @Override
    public int hashCode() {
        return Objects.hash(name, count);
    }
}

