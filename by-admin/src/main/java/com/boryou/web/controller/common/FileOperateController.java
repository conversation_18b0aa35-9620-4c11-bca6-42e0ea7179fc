package com.boryou.web.controller.common;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.service.FileOperateService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

/**
 * <AUTHOR>
 * @date 2024/8/20 14:55
 */
@RestController
@RequiredArgsConstructor
public class FileOperateController extends BaseController {

    private final FileOperateService fileOperateService;


    @PostMapping("/fileOperate/importDoc")
    public AjaxResult importDoc(MultipartFile file) {
        Object text = fileOperateService.importDoc(file);
        return AjaxResult.success(text);
    }

}
