package com.boryou.web.controller.common.entity.bo;

import com.boryou.web.domain.vo.TimeRoundFlowVO;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.List;

/**
 * <AUTHOR>
 * @description ES搜索BO
 * @date 2024/4/22 14:00
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class EsSearchTimeBO extends EsSearchBO {
    private List<TimeRoundFlowVO> timeRoundFlow;
    /**
     * emotionFlag  type
     */
    private String statisticType;
}
