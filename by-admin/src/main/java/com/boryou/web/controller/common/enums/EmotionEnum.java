package com.boryou.web.controller.common.enums;

/**
 * <AUTHOR>
 * @date 2024-05-22 19:47
 */

public enum EmotionEnum {
    NEGATIVE(1, "敏感"),
    POSITIVE(2, "非敏感"),
    NEUTRAL(0, "中性");

    private final String name;
    private final Integer value;
    private static final String VALUES;

    private EmotionEnum(Integer emotionValue, String emotionName) {
        this.name = emotionName;
        this.value = emotionValue;
    }

    public String getName() {
        return this.name;
    }

    public Integer getValue() {
        return this.value;
    }

    public String getStrValue() {
        return String.valueOf(this.value);
    }

    public String getDescribe() {
        return "情感中文名：" + this.name + " 情感对应的数值：" + this.value;
    }

    public static EmotionEnum fromName(String emotionName) {
        EmotionEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            EmotionEnum emotion = var1[var3];
            if (emotion.getName().equals(emotionName)) {
                return emotion;
            }
        }

        return null;
    }

    public static EmotionEnum fromValue(int emotionValue) {
        EmotionEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            EmotionEnum emotion = var1[var3];
            if (emotionValue == emotion.getValue()) {
                return emotion;
            }
        }

        return null;
    }

    public static String getStrValues() {
        return VALUES;
    }

    static {
        StringBuilder sb = new StringBuilder();
        EmotionEnum[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            EmotionEnum emotionEnum = var1[var3];
            if (sb.length() > 0) {
                sb.append(" ");
            }

            sb.append(emotionEnum.getValue());
        }

        VALUES = sb.toString();
    }
}
