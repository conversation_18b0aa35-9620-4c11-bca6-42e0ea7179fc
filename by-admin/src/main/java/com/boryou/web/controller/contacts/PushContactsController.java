package com.boryou.web.controller.contacts;

import java.util.List;

import com.boryou.web.domain.PushContacts;
import com.boryou.web.domain.vo.PushContactsVO;
import com.boryou.web.service.IPushContactsService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;

import javax.annotation.Resource;

/**
 * 【请填写功能名称】Controller
 *
 * <AUTHOR>
 * @date 2024-06-18
 */
@RestController
@RequestMapping("/message/contacts")
public class PushContactsController extends BaseController {
    @Resource
    private IPushContactsService pushContactsService;

    @GetMapping("/all")
    public AjaxResult all(PushContactsVO pushContacts) {
        List<PushContacts> list = pushContactsService.selectByPushContactsList(pushContacts);
        return AjaxResult.success(list);
    }
    /**
     * 查询【请填写功能名称】列表
     */
//    @GetMapping("/list")
//    public TableDataInfo list(ByPushContacts byPushContacts) {
//        startPage();
//        List<ByPushContacts> list = byPushContactsService.selectByPushContactsList(byPushContacts);
//        return getDataTable(list);
//    }

    /**
     * 导出【请填写功能名称】列表
     */
//    @GetMapping("/export")
//    public AjaxResult export(ByPushContacts byPushContacts) {
//        List<ByPushContacts> list = byPushContactsService.selectByPushContactsList(byPushContacts);
//        ExcelUtil<ByPushContacts> util = new ExcelUtil<ByPushContacts>(ByPushContacts.class);
//        return util.exportExcel(list, "contacts");
//    }

    /**
     * 获取【请填写功能名称】详细信息
     */
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id) {
//        return AjaxResult.success(byPushContactsService.selectByPushContactsById(id));
//    }
    @PostMapping
    public AjaxResult add(@RequestBody PushContactsVO pushContacts) {
        boolean row = pushContactsService.changeContacts(pushContacts);
        return toAjax(row);
    }

    @PutMapping
    public AjaxResult edit(@RequestBody PushContactsVO pushContacts) {
        boolean row = pushContactsService.changeContacts(pushContacts);
        return toAjax(row);
    }

    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable List<String> ids) {
        boolean rows = pushContactsService.deleteByPushContactsByIds(ids);
        return toAjax(rows);
    }
}
