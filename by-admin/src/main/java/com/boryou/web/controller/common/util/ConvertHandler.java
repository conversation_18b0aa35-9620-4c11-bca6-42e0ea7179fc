package com.boryou.web.controller.common.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boryou.common.core.domain.entity.SysDictData;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.system.mapper.SysDictDataMapper;
import com.boryou.system.service.ISysDictDataService;
import com.boryou.web.controller.common.entity.BoryouBean;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.entity.vo.SourceMapV2VO;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.module.search.entity.FilterInfo;
import com.boryou.web.module.search.service.FilterInfoService;
import com.boryou.web.module.source.constant.SourceConstant;
import com.boryou.web.module.source.entity.SourceSetting;
import com.boryou.web.module.source.service.SourceSettingService;
import com.boryou.web.module.source.vo.SourceSettingVO;
import com.boryou.web.service.PlanService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

import static com.boryou.web.constant.ESConstant.NETXMAN_YQ_HISPLAN;

@Slf4j
@Service
public class ConvertHandler {
    @Resource
    private SourceSettingService sourceSettingService;
    @Resource
    private PlanService planService;
    @Resource
    private FilterInfoService filterInfoService;
    @Resource
    private ISysDictDataService dictDataService;
    @Resource
    private SysDictDataMapper sysDictDataMapper;


    private ConvertHandler() {
    }

    public static BoryouBean esBeanList2SolrBeanIdList(EsBean esBean) {
        String id = esBean.getId();
        int type = esBean.getType();
        Date publishTime = esBean.getPublishTime();
        String title = esBean.getTitle();
        String text = esBean.getText();
        String url = esBean.getUrl();
        String host = esBean.getHost();
        String author = esBean.getAuthor();
        String authorId = esBean.getAuthorId();
        int authorSex = esBean.getAuthorSex();
        String bizId = esBean.getBizId();
        int accountLevel = esBean.getAccountLevel();
        String siteAreaCode = esBean.getSiteAreaCode();
        List<String> contentAreaCode = esBean.getContentAreaCode();
        List<String> siteMeta = esBean.getSiteMeta();
        List<String> contentMeta = esBean.getContentMeta();
        Integer fansNum = esBean.getFansNum();
        Integer readNum = esBean.getReadNum();
        Integer commentNum = esBean.getCommentNum();
        Integer likeNum = esBean.getLikeNum();
        Integer reprintNum = esBean.getReprintNum();
        String sector = esBean.getSector();
        List<Integer> contentForm = esBean.getContentForm();
        String md5 = esBean.getMd5();
        String srcCodePath = esBean.getSrcCodePath();
        String coverUrl = esBean.getCoverUrl();
        List<String> picUrl = esBean.getPicUrl();
        List<String> avdUrl = esBean.getAvdUrl();
        int emotionFlag = esBean.getEmotionFlag();
        boolean isOriginal = esBean.getIsOriginal();
        boolean isSpam = esBean.getIsSpam();
        Date updateTime = esBean.getUpdateTime();
        String day = esBean.getDay();
        String highLightTitle = esBean.getHighLightTitle();
        String highLightText = esBean.getHighLightText();
        String highLightAuthor = esBean.getHighLightAuthor();

        BoryouBean boryouBean = new BoryouBean();

        boryouBean.setId(id);
        //过滤掉img图片
        boryouBean.setTitle(title);
        boryouBean.setText(text);

        boryouBean.setHost(host);
        boryouBean.setType(type);

        // 将未标红的文本存放入untreatedText字段
        boryouBean.setUntreatedText(boryouBean.getText());
        // 将未标红的标题存放入untreatedTitle字段
        boryouBean.setUntreatedTitle(boryouBean.getTitle());

        boryouBean.setOriginal(isOriginal);
        boryouBean.setEmotional(emotionFlag);
        boryouBean.setSiteAddress(siteAreaCode);
        boryouBean.setTextAddress(contentAreaCode);
        boryouBean.setSpamFlag(isSpam);
        boryouBean.setSiteMeta(siteMeta);
        boryouBean.setPicUrl(picUrl);
        boryouBean.setReadNum(readNum);
        boryouBean.setCommentNum(commentNum);
        boryouBean.setCommentGoodsNum(likeNum);
        boryouBean.setReprintNum(reprintNum);
        boryouBean.setContentType(contentForm);
        boryouBean.setMD5(md5);
        boryouBean.setMapPath(srcCodePath);
        boryouBean.setSector(sector);
        boryouBean.setAuthorId(authorId);
        boryouBean.setAuthor(author);
        boryouBean.setUrl(url);
        if (type == MediaTypeEnum.WEIBO.getValue() && !StrUtil.isBlankIfStr(authorId) && NumberUtil.isLong(authorId)) {
            if (authorId != null && !"null".equals(authorId)) {
                boryouBean.setWbAuthorId(NumberUtil.parseLong(authorId));
            }
        }
        boryouBean.setTime(DateUtil.format(publishTime, "yyyy-MM-dd HH:mm:ss"));
        boryouBean.setSubmitTime(DateUtil.format(updateTime, "yyyy-MM-dd HH:mm:ss"));
//        boryouBean.setHostName(SolrCommonUtils.getHostName(boryouBean));

        return boryouBean;
    }


    //舆情监测条件处理
    public EsSearchBO copyPropertiesFromPlan(SearchVO searchVO, Plan plan) {
        Long planId = plan.getPlanId();
        String settingType = searchVO.getSettingType();
        if (CharSequenceUtil.isNotBlank(settingType) && CharSequenceUtil.equals(settingType, "3")) {
            searchVO.setSettingType("1");
            SourceSettingVO source = new SourceSettingVO();
            source.setUserId(SecurityUtils.getUserId(searchVO.getUserId()));
            source.setPlateId(planId);
            source.setSettingType(searchVO.getSettingType());
            List<SourceSetting> sourceList = sourceSettingService.getSourceSettingList(source);
            if (CollUtil.isEmpty(sourceList)) {
                throw new CustomException("定向信源不能为空!");
            }
            //仅使用定向信源时方案设置不生效
            plan = new Plan();
        }
        setAllVideoHost(searchVO);
        searchVO.setSearchMode(plan.getSearchMode());
        searchVO.setKeyWord1(plan.getKw1());
        searchVO.setKeyWord2(plan.getKw2());
        searchVO.setKeyWord3(plan.getKw3());
        if (plan.getSearchMode() != null && plan.getSearchMode() == 0) {
            searchVO.setExcludeWord(plan.getExcludeWord());
            searchVO.setArea(plan.getArea());
        }
        if (plan.getSearchMode() != null && plan.getSearchMode() == 1) {
            searchVO.setHighMonitorWord(plan.getHighMonitorWord());
            searchVO.setHighExcludeWord(plan.getHighExcludeWord());
            searchVO.setHighArea(plan.getHighArea());
        }

        EsSearchBO bo = searchVO.convertEsSearchBO(SecurityUtils.getUserId(searchVO.getUserId()));
        if (searchVO.getSettingType() != null && (searchVO.getSettingType().equals("1") || searchVO.getSettingType().equals("2"))) {
            //定向信源
            SourceSettingVO source = new SourceSettingVO();
            source.setUserId(SecurityUtils.getUserId(searchVO.getUserId()));
            source.setPlateId(planId);
            source.setSettingType(searchVO.getSettingType());
            List<SourceSetting> sourceList = sourceSettingService.getSourceSettingList(source);
            if (CollUtil.isNotEmpty(sourceList)) {
                // 定向信源
                // this.buildSource(sourceList, bo);
                // 定向信源V2
                this.buildSourceMaoV2(sourceList, bo);
            } else if ("2".equals(bo.getSourceSetting())) { //不排除
                bo.setSourceSetting("0");
            }
        }
        if (null != plan.getExpire() && plan.getExpire() == 1) {
            //如果该方案已经存储到历史es节点了，就查固定的es节点。
            bo.setIndexs(Collections.singletonList(NETXMAN_YQ_HISPLAN));
            bo.setStartTime(DateUtil.formatDateTime(plan.getEffectStartTime()));
            bo.setEndTime(DateUtil.formatDateTime(plan.getEffectEndTime()));
            bo.setBusinessId(planId);
        }
//        FilterInfo filterInfo = new FilterInfo();
//        filterInfo.setPlanId(plan.getPlanId());
        List<FilterInfo> filterInfos = filterInfoService.list(Wrappers.<FilterInfo>lambdaQuery().eq(FilterInfo::getPlanId, planId));
        if (CollUtil.isNotEmpty(filterInfos)) {
            List<String> filterInfoIds = filterInfos.stream().map(f -> String.valueOf(f.getIndexId())).collect(Collectors.toList());
            String ids = CollUtil.join(filterInfoIds, ",");
            bo.setExcludeId(ids);
        }
        return bo;
    }

    private void buildSource(List<SourceSetting> sourceList, EsSearchBO bo) {
        Map<String, String> sourceMap = new HashMap<>();
        Map<String, List<SourceSetting>> collect = sourceList.stream().collect(Collectors.groupingBy(SourceSetting::getSourceType));
        for (Map.Entry<String, List<SourceSetting>> entry : collect.entrySet()) {
            String sourceMapValue;
            if (entry.getKey().equals(String.valueOf(MediaTypeEnum.NEWS.getValue()))) {
                sourceMapValue = entry.getValue().stream().map(SourceSetting::getSettingHost).collect(Collectors.joining(","));
            } else {
                sourceMapValue = entry.getValue().stream().map(SourceSetting::getName).collect(Collectors.joining(","));
            }
            sourceMap.put(entry.getKey(), sourceMapValue);
        }
        bo.setSourceMap(sourceMap);
    }

    private void buildSourceMaoV2(List<SourceSetting> sourceList, EsSearchBO bo) {
        Map<String, Set<String>> map = this.getHostMap();
        List<String> sectorHostList = this.getSectorHost();
        List<SourceMapV2VO> sourceMapV2VOList = new ArrayList<>();
        Map<String, List<SourceSetting>> sourceSettingMap = sourceList.stream().collect(Collectors.groupingBy(SourceSetting::getSourceType));
        sourceSettingMap.forEach((sourceType, sourceSettingList) -> {
            // List<String> sectorList = new ArrayList<>();
            Set<String> authorList = new HashSet<>();
            Set<String> hostList = new HashSet<>();
            Map<Set<String>, List<String>> sectorHostMap = new HashMap<>();
            for (SourceSetting sourceSetting : sourceSettingList) {
                String name = sourceSetting.getName();
                String settingHost = sourceSetting.getSettingHost();

                Set<String> stringSet = CollUtil.newHashSet(settingHost);
                if (map.containsKey(settingHost)) {
                    Set<String> strings = map.get(settingHost);
                    stringSet.addAll(strings);
                }
                stringSet = stringSet.stream().sorted().collect(Collectors.toCollection(LinkedHashSet::new));
                // 先判断特殊处理的贴吧
                if (sectorHostList.contains(settingHost)) {
                    // sectorList.add(name);
                    if (sectorHostMap.containsKey(stringSet)) {
                        List<String> sectorList = sectorHostMap.get(stringSet);
                        sectorList.add(name);
                        sectorHostMap.put(stringSet, sectorList);
                    } else {
                        sectorHostMap.put(stringSet, CollUtil.newArrayList(name));
                    }
                    continue;
                }
                if (SourceConstant.TYPE_HOST.contains(sourceType)) {
                    hostList.addAll(stringSet);
                    continue;
                }
                if (SourceConstant.TYPE_HOST_AUTHOR.contains(sourceType)) {
                    hostList.addAll(stringSet);
                    authorList.add(name);
                    continue;
                }
                if (SourceConstant.TYPE_AUTHOR.contains(sourceType)) {
                    authorList.add(name);
                }
            }
            SourceMapV2VO sourceMapV2VO = new SourceMapV2VO();
            sourceMapV2VO.setType(sourceType);
            boolean flag = false;
            if (CollUtil.isNotEmpty(authorList)) {
                flag = true;
                sourceMapV2VO.setAuthor(CollUtil.newArrayList(authorList));
            }
            if (CollUtil.isNotEmpty(hostList)) {
                flag = true;
                sourceMapV2VO.setHost(CollUtil.newArrayList(hostList));
            }
            if (flag) {
                sourceMapV2VOList.add(sourceMapV2VO);
            }
            if (MapUtil.isNotEmpty(sectorHostMap)) {
                sectorHostMap.forEach((k, v) -> {
                    SourceMapV2VO sourceMapV2VOSpe = new SourceMapV2VO();
                    sourceMapV2VOSpe.setType(sourceType);
                    sourceMapV2VOSpe.setSector(v);
                    sourceMapV2VOSpe.setHost(CollUtil.newArrayList(k));
                    sourceMapV2VOList.add(sourceMapV2VOSpe);
                });
            }
        });
        bo.setSourceMapV2(sourceMapV2VOList);
    }

    private List<String> getSectorHost() {
        List<SysDictData> dictDataList = sysDictDataMapper.selectDictDataByRemark("source_host", "1");
        List<String> sectorHostList = new ArrayList<>();
        for (SysDictData sysDictData : dictDataList) {
            String dictValue = sysDictData.getDictValue();
            if (CharSequenceUtil.isBlank(dictValue)) {
                continue;
            }
            sectorHostList.addAll(CharSequenceUtil.splitTrim(dictValue, ","));
        }
        return sectorHostList;
    }

    private Map<String, Set<String>> getHostMap() {
        List<String> dicTypeList = CollUtil.newArrayList("sys_search_short_video", "sys_client_platform", "source_host");
        List<SysDictData> dictDataList = sysDictDataMapper.selectDictDataHost(dicTypeList)
                .stream().filter(item -> !CharSequenceUtil.equals(item.getDictLabel(), "其他"))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(dictDataList)) {
            return MapUtil.empty();
        }
        Map<String, Set<String>> map = new HashMap<>();
        for (SysDictData dictData : dictDataList) {
            String dictValue = dictData.getDictValue();
            String dictLabel = dictData.getDictLabel();
            if (CharSequenceUtil.isBlank(dictValue)) {
                continue;
            }
            List<String> split = CharSequenceUtil.splitTrim(dictValue, ",");
            for (String host : split) {
                if (map.containsKey(host)) {
                    Set<String> set = map.get(host);
                    set.addAll(split);
                    map.putIfAbsent(host, set);
                } else {
                    map.put(host, CollUtil.newHashSet(split));
                }
            }
        }
        return map;
    }

    //舆情监测与全文搜索条件处理
    public EsSearchBO getEsSearchBOForAnalyse(SearchVO searchVO) {
        if (searchVO.getPlanId() != null) {
            Plan plan = planService.selectPlanById(searchVO.getPlanId());
            if (plan != null) {
                return copyPropertiesFromPlan(searchVO, plan);
            }
        }
        return searchVO.convertEsSearchBO();
    }

    public void setAllVideoHost(SearchVO searchVO) {
        if (searchVO.getType() != null) {
            boolean hasVideo = false;
            for (String s : searchVO.getType().split(",")) {
                if (s.equals("11")) {
                    hasVideo = true;
                }
            }
            //查询视频  未选择任何视频host时  带上全部host进行查询
            if (hasVideo && StrUtil.isEmpty(searchVO.getVideoHost())) {
                SysDictData data = new SysDictData();
                data.setDictType("sys_search_short_video");
                List<SysDictData> sysDictData = dictDataService.selectDictDataList(data);
                if (CollUtil.isNotEmpty(sysDictData)) {
                    List<String> videoHosts = sysDictData.stream().map(SysDictData::getDictValue).collect(Collectors.toList());
                    searchVO.setVideoHost(CollUtil.join(videoHosts, ","));
                }
            }
        }
    }
}
