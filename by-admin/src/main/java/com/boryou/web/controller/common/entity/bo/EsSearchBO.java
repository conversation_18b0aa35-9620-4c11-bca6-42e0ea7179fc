package com.boryou.web.controller.common.entity.bo;

import com.boryou.web.controller.common.entity.vo.SourceMapV2VO;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @description ES搜索BO
 * @date 2024/4/22 14:00
 */
@Data
public class EsSearchBO {
    private static final long serialVersionUID = 1L;
    /**
     * 文章id*
     */
    private String id;
    /**
     * 排除id*
     */
    private String excludeId;
    /**
     * 媒体类型
     */
    private String type;
    /**
     * 情感倾向0 中性  1敏感  2 非敏感
     */
    private String emotionFlag;
    /**
     * 时间类型（0发文时间，1采集时间）
     */

    private int timeType;
    /**
     * 开始时间
     */
    private String startTime;
    /**
     * 结束时间
     */
    private String endTime;
    /**
     * 站点地域地域码
     */
    private String siteAreaCode;
    /**
     * 内容地域 contentAreaCode
     */
    private String contentAreaCode;
    /**
     * 是否原创 true false null
     */
    private Boolean isOriginal;
    /**
     * 是否垃圾 true false null
     */
    private Boolean isSpam;
    /**
     * 关键词1
     */
    private String keyWord1;
    /**
     * 关键词2
     */
    private String keyWord2;
    /**
     * 关键词3
     */
    private String keyWord3;
    /**
     * 关键词4
     */
    private String keyWord4;
    /**
     * 二次搜索词
     */
    private String secondWord;
    /**
     * 二次搜索词
     */
    private String quadraticWord;
    /**
     * 二次搜索过滤词
     */
    private String quadraticFilterWord;
    /**
     * 二次搜索词范围
     */
    private String quadraticPosition;
    /**
     * 排除词
     */
    private String excludeWord;

    /**
     * 排除term
     */
    private List<EsExcludeBO> excludeTermField;
    /**
     * 指定站点
     */
    private String host;
    /**
     * 指定短视频站点
     */
    private String videoHost;
    /**
     * 排除站点
     */
    private String excludeHost;
    /**
     * 关键词位置
     */
    private String searchPosition;
    /**
     * 排序方式
     */
    private Integer sortType;
    /**
     * 作者
     */
    private String author;


    /**
     * authorid集合
     */
    private String authorIds;
    /**
     * 排除作者
     */
//    private List<EsExcludeAuthorBO> excludeAuthor;

    /**
     * 排除作者
     */
    private String excludeAuthor;
    /**
     * 大V级别 0达人  1蓝v  2红v  3橙v  4普通用户
     */
    private String accountLevel;
    /**
     * 信源等级  央级，省级，地市，重点，中小，企业商业
     */
    private String accountGrade;
    /**
     * 内容类型（文本，图片，视频，链接，音频，游戏，文件）
     */
    private String contentForm;
    /**
     * 微博类型
     */
    private String forward;
    /**
     * 微博地域
     */
    private String accountAreaCode;
    /**
     * 站点标签
     */
    private String siteMeta;

    /**
     * 内容标签
     */
    private String contentMeta;

    /**
     * 搜索方式 1 专业模式  0 普通模式
     */
    private int configSelect;

    /**
     * 专业搜索词
     */
    private String proWord;

    /**
     * md5
     */
    private String md5;

    /**
     * 定向信源设置 0 不使用  1 定向选择  2 定向排除
     */
    private String sourceSetting;

    /**
     * 定向信源
     */
    private Map<String, String> sourceMap;

    private List<SourceMapV2VO> sourceMapV2;


    /**
     * 需要分组的字段名称
     */
    private String aggName;
    /**
     * 解决需要在分组时对字段结果进行限定时防止聚合数据量返回过大，agg的size大小不好限定的问题
     */
    private String aggInclude;
    /**
     * 需要分组的字段数量
     */
    private int aggSize = 10;
    /**
     * 页大小
     */
    private int pageSize = 10;
    /**
     * 页码
     */
    private int pageNum = 1;

    /**
     * 项目接口调用标识
     */
    private Integer projectType;

    /**
     * 指定es的索引库查询
     */
    private List<String> indexs;
    /**
     * 指定es的业务id查询，目前适用于高院的历史数据节点查询
     * 方案id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long businessId;

    /**
     * 预警类型 1: 系统 2: 人工
     */
    private Integer warnType;

    /**
     * 查询真实数量 0或null: false 1: true
     */
    private Integer realCount;

    /**
     * 量子系统方案检索第二专业搜索词
     */
    private String proWord2;
}
