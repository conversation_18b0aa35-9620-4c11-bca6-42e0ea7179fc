package com.boryou.web.controller.common.entity.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024-05-22 19:34
 */
@Data
public class HomeVO {
    private String startTime;
    private String endTime;
    private String timeType;
    //当前用户的内容地域
    private String contentAreaCode;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long deptId;

    //重点关注类型 1 重点 2 全省 3 全国
    private String planType;


}

