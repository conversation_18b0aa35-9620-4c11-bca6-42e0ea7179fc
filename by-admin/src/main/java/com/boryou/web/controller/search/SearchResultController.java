package com.boryou.web.controller.search;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONUtil;
import com.boryou.common.annotation.Log;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.constant.NoticeEnum;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.domain.Hot;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.msg.MessageVO;
import com.boryou.web.domain.vo.EsBeanVO;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.module.notice.domain.ByNotice;
import com.boryou.web.module.notice.service.IByNoticeService;
import com.boryou.web.module.search.entity.EsSpecialData;
import com.boryou.web.module.search.service.EsSpecialDataService;
import com.boryou.web.module.submit.entity.Submit;
import com.boryou.web.module.submit.service.ISubmitService;
import com.boryou.web.service.PlanService;
import com.boryou.web.service.SearchService;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;


/**
 * 数据列表检索controller
 *
 * <AUTHOR>
 */
@RequestMapping("/search/result")
@RestController()
@AllArgsConstructor
@Slf4j
public class SearchResultController {

    private final SearchService searchService;
    private final EsSpecialDataService esSpecialDataService;
    private final PlanService planService;
    private final ISubmitService submitService;
    private final IByNoticeService noticeService;

    @Resource(name = "threadPoolTaskExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    /**
     * 发送短信
     */
    @Log(title = "发送短信预警", businessType = BusinessType.INSERT)
    @PostMapping("/sendMsg")
    public boolean sendMsg(@RequestBody MessageVO messageVO) {
        if (StrUtil.isEmpty(messageVO.getPhone()) && CollUtil.isEmpty(messageVO.getUserIds())) {
            throw new CustomException("请输入手机号或勾选联系人!");
        }
        Submit submit = new Submit();
        submit.setDocIndexId(messageVO.getDocIndexId());
        submit.setUserId(SecurityUtils.getUserIdL());
        List<Submit> submits = submitService.selectSubmitList(submit);
        if (CollUtil.isNotEmpty(submits)) {
            throw new CustomException("该信息已报送!");
        }
        // 发送并保存短信
        boolean b = searchService.sendMsg(messageVO);
        if (b) {
//          threadPoolTaskExecutor.execute(() -> {//TODO submit有时候不执行
            boolean flag = submitService.insertShortSubmit(messageVO);
            try {
                EsSpecialData esSpecialData = new EsSpecialData();
                esSpecialData.setMd5(messageVO.getMd5());
                esSpecialData.setWarned(1);
                esSpecialData.setCreateBy(SecurityUtils.getLoginUser().getUser().getUserName());
                int i = esSpecialDataService.updateSearchData(esSpecialData);
                log.info("发送短信成功，预警:{}", i > 0);

                ByNotice byNotice = new ByNotice();
                byNotice.setNoticeTime(new Date());
                byNotice.setDocIndexId(messageVO.getDocIndexId());
                byNotice.setNoticeType(NoticeEnum.SHORT_MESSAGE.getType());
                byNotice.setTime(messageVO.getTime());
                boolean flag1 = noticeService.saveSiteNotice(byNotice);
            } catch (Exception e) {
                e.printStackTrace();
            }
//          });
        }
        return b;
    }

    /**
     * app端查看单个详情
     * 需要考虑舆情监测页面和全文搜索页面的短信报送链接跳转的关键词高亮
     * <p>
     * 舆情监测是传列表的hitowords参数过来，后端设置res的hitwords
     * 全文搜索是通过列表关键词地址栏传参，页面直接显示的
     */
    @Log(title = "H5单条信息页预警详情", businessType = BusinessType.QUERY)
    @RequestMapping("/detail")
    public AjaxResult docDetail(SearchVO searchVO) {
        EsSearchBO esSearchBO = searchVO.convertSimpleEsSearchBO();
        boolean flag = false;
        if (null != searchVO.getPlanId()) {
            Plan plan = planService.selectPlanById(searchVO.getPlanId());
            if (plan != null) {
                esSearchBO.setKeyWord1(plan.getKw1());
                esSearchBO.setKeyWord2(plan.getKw2());
                esSearchBO.setKeyWord3(plan.getKw3());
                flag = true;
            }
        }
        EsBeanVO res = searchService.docDetail(esSearchBO);
        Submit submit = new Submit();
        submit.setDocIndexId(Long.parseLong(esSearchBO.getId()));
        if (null != searchVO.getUserId()) {
            submit.setUserId(searchVO.getUserId());
            List<Submit> submits = submitService.selectSubmitList(submit);
            if (CollUtil.isNotEmpty(submits)) {
                Submit submit1 = submits.get(0);
                String processStatus = submit1.getProcessStatus();
                if ("0".equals(processStatus)) {
                    Date processTime = submit1.getDeadline();
                    if (null != processTime && new Date().after(processTime)) {//after是a大于b返回true
                        //如果已过期就更新过期
                        submit1.setProcessText(null);
                        submit1.setProcessStatus("2");
                        submit1.setProcessTime(null);
                        submitService.insertSubmitProcess(submit1);
                    }
                }
                res.setSubmit(submit1);
            }
        }
        if (flag) {//如果是舆情监测页面过来的，直接去前端从列表取的hitwords返回显示，这样就不用带上关键词去查一遍了
            res.setHitWords(searchVO.getKeyWord1());
        }
        return AjaxResult.success(res);
    }


    /**
     * app端查看30天发文，只用查敏感和个人的，不需要考虑敏感词，不需要高亮
     */
    @PostMapping("/author")
    public TableDataInfo searchByPlate(@RequestBody SearchVO searchVO) {
        PageResult<EsBean> pageResult = new PageResult<>();
//        Plan plan = planService.selectPlanById(searchVO.getPlanId());
//        if (plan != null) {
        searchVO.setTimeIndex(30);
        EsSearchBO bo = JSONUtil.toBean(JSONUtil.toJsonStr(searchVO), EsSearchBO.class);
        searchVO.getSearchTimeRange(bo);
        bo.setEmotionFlag("1");
        bo.setSortType(3);
        pageResult = searchService.search(bo);
//        }
        TableDataInfo rspData = new TableDataInfo(pageResult, pageResult.getTotal());
        rspData.setCode(200);
        return rspData;
    }


    /**
     * app端相关热搜
     */
    @GetMapping("/similarHot")
    public AjaxResult similarHot(SearchVO searchVO) {
        List<Hot> hots = searchService.getSimilarHot(searchVO);
        return AjaxResult.success(hots);
    }


}
