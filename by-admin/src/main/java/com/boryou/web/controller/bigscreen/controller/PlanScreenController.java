package com.boryou.web.controller.bigscreen.controller;

import cn.hutool.json.JSONArray;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.controller.bigscreen.entity.ScreenCount;
import com.boryou.web.controller.bigscreen.service.PlanScreenService;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.ScreenModelVO;
import com.boryou.web.domain.vo.SearchVO;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
public class PlanScreenController {
    private final PlanScreenService planScreenService;

    @PostMapping("/planScreen/total")
    public AjaxResult total(@RequestBody @Validated SearchVO searchVO) {
        Object total = planScreenService.total(searchVO);
        return AjaxResult.success(total);
    }

    @PostMapping("/planScreen/wordsAnalyse")
    public AjaxResult wordsAnalyse(@RequestBody @Validated SearchVO searchVO) {
        JSONArray words = planScreenService.wordsAnalyse(searchVO);
        return AjaxResult.success(words);
    }

    @PostMapping("/planScreen/emotionAnalyse")
    public AjaxResult emotionAnalyse(@RequestBody @Validated SearchVO searchVO) {
        List<ScreenCount> emotion = planScreenService.emotionAnalyse(searchVO);
        return AjaxResult.success(emotion);
    }

    @PostMapping("/planScreen/map")
    public AjaxResult map(@RequestBody @Validated SearchVO searchVO) {
        List<ScreenCount> map = planScreenService.map(searchVO);
        return AjaxResult.success(map);
    }

    @PostMapping("/planScreen/plan")
    public AjaxResult plan() {
        List<Plan> planList = planScreenService.plan();
        return AjaxResult.success(planList);
    }

    @PostMapping("/planScreen/weekType")
    public AjaxResult weekType(@RequestBody @Validated SearchVO searchVO) {
        ScreenModelVO screenModelVO = planScreenService.weekType(searchVO);
        return AjaxResult.success(screenModelVO);
    }

    @PostMapping("/planScreen/hotInfo")
    public AjaxResult hotInfo(@RequestBody @Validated SearchVO searchVO) {
        List<EsBean> esBeanList = planScreenService.hotInfo(searchVO);
        return AjaxResult.success(esBeanList);
    }

    @PostMapping("/planScreen/realTime")
    public AjaxResult realTime(@RequestBody @Validated SearchVO searchVO) {
        List<EsBean> esBeanList = planScreenService.realTime(searchVO);
        return AjaxResult.success(esBeanList);
    }

}
