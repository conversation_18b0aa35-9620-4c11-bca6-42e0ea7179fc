package com.boryou.web.controller.eventContext;

import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONObject;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.utils.StringUtils;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.enums.SortTypeEnum;
import com.boryou.web.controller.common.util.ConvertHandler;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.service.ElasticsearchService;
import com.boryou.web.service.PlanService;
import com.boryou.web.service.SearchService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.*;
import java.util.stream.Collectors;

import static com.boryou.web.constant.ESConstant.*;
import static com.boryou.web.controller.common.enums.SortTypeEnum.getEnumValue;

/**
 * <AUTHOR>
 * @date 2024/7/19 13:45
 */
@RestController
@AllArgsConstructor
@RequestMapping("/eventContext")
public class EventContextController extends BaseController {

    private final PlanService planService;
    private final ConvertHandler convertHandler;
    private final SearchService searchService;
    private final ElasticsearchService elasticsearchService;

    /**
     * 事件脉络
     *
     * @param vo
     * @return
     */
    @PostMapping()
    public TableDataInfo eventContext(@RequestBody SearchVO vo) {
        List<EsBean> list = new PageResult<>();
        TableDataInfo rspData = new TableDataInfo();
        EsSearchBO bo = null;
        if (ObjectUtil.isNotEmpty(vo.getPlanId())) {
            Plan plan = planService.selectPlanById(vo.getPlanId());
            // 舆情监测统计分析
            if (plan != null) {
                bo = convertHandler.copyPropertiesFromPlan(vo, plan);
                bo.setPageNum(vo.getPageNum());
                bo.setPageSize(vo.getPageSize());
                bo.setSortType(getEnumValue(SortTypeEnum.TIME_ASC));
                if (plan.getHistoryFlag() == 1) {
                    bo.setStartTime(DateUtil.formatDateTime(plan.getEffectStartTime()));
                    bo.setEndTime(DateUtil.formatDateTime(plan.getEffectEndTime()));
                    bo.setIndexs(Arrays.asList(NETXMAN_YQ_HISPLAN));
                } else {
                    bo.setEndTime(DateUtil.formatDateTime(new Date()));
                    bo.setStartTime(DateUtil.formatDateTime(DateUtil.offsetDay(new Date(), -30)));
                    bo.setIndexs(Arrays.asList(ES_INDEX_WEEK, ES_INDEX_MONTH));
                }
                JSONObject jsonObject = searchService.eventContext(bo);
                list = jsonObject.getBeanList("records", EsBean.class);
                rspData.setTotal(jsonObject.getInt("total"));
            }
        }
        // 全文搜索统计分析
        else {
            bo = convertHandler.getEsSearchBOForAnalyse(vo);
            JSONObject jsonObject = searchService.eventContext(bo);
            list = jsonObject.getBeanList("records", EsBean.class);
            rspData.setTotal(jsonObject.getInt("total"));
        }


        // 翻译 host
        Map<Integer, List<String>> map = new HashMap<>();
        list.forEach(x -> {
            if (StringUtils.isEmpty(x.getTitle())) {
                x.setTitle(x.getText().substring(0, 20));
            }

            if (StringUtils.isEmpty(x.getHost())) {
                if (StringUtils.isNotEmpty(x.getUrl())) {
                    x.setHost(StringUtils.getHostFromUrl(x.getUrl()));
                }
            }
            if (StringUtils.isEmpty(x.getTitle())) {
                x.setTitle(x.getText().substring(0, 20));
            }
            List<String> values = map.getOrDefault(x.getType(), new ArrayList<>());
            if (!values.contains(x.getHost())) {
                values.add(x.getHost());
            }
            map.put(x.getType(), values);
        });
        List<AccountInfoVO> accountInfoVOList = elasticsearchService.getNameByHosts(map);

        list.forEach(x -> {
            Optional<AccountInfoVO> first = accountInfoVOList.stream().filter(y -> y.getDomain().equals(x.getHost())).findFirst();
            String hostName = "";
            if (first.isPresent()) {
                hostName = first.get().getSector();
            }
            if (StringUtils.isEmpty(hostName)) {
                x.setHost(x.getHost());
            } else {
                x.setHost(hostName);
            }

            // 赋值相似数量
            SearchVO searchVO = new SearchVO();
            searchVO.setEmotionFlag("0,1,2");
            searchVO.setId(x.getId());
            searchVO.setEndTime(DateUtil.formatDateTime(new Date()));
            searchVO.setStartTime(DateUtil.formatDateTime(DateUtil.beginOfDay(DateUtil.offsetDay(new Date(), -30))));
            searchVO.setPlanId(vo.getPlanId());
            searchVO.setIsOriginal(false);
            searchVO.setSearchPosition("0");
            searchVO.setPageSize(1);
            EsSearchBO itemBo = convertHandler.getEsSearchBOForAnalyse(searchVO);
            PageResult<EsBean> data = searchService.searchSimilarity(itemBo);
            x.setSimilarCount(data.getTotal() == 0 ? 1 : data.getTotal());
        });

        List<EsBean> collect = list.stream().sorted(Comparator.comparing(EsBean::getPublishTime)).collect(Collectors.toList());

        rspData.setRows(collect);
        rspData.setCode(200);
        return rspData;
    }


    /**
     * 事件首发
     *
     * @param query
     * @return
     */
    @PostMapping("/firstRelease")
    public AjaxResult firstRelease(@RequestBody SearchVO query) {
        return AjaxResult.success(searchService.getFirstRelease(query));
    }

    /**
     * 相关热文
     *
     * @param query
     * @return
     */
    @PostMapping("/relatedHotArticle")
    public AjaxResult relatedHotArticle(@RequestBody SearchVO query) {
        return AjaxResult.success(searchService.relatedHotArticle(query));
    }

}
