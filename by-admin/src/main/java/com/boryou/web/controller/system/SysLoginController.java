package com.boryou.web.controller.system;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.PhoneUtil;
import cn.hutool.core.util.RandomUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysMenu;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.domain.model.LoginBody;
import com.boryou.common.core.domain.model.LoginUser;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.ServletUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.common.utils.sign.Md5Utils;
import com.boryou.framework.web.service.SysLoginService;
import com.boryou.framework.web.service.SysPermissionService;
import com.boryou.framework.web.service.TokenService;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.service.ISysMenuService;
import com.boryou.system.service.ISysUserService;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.constant.SmsConstant;
import com.boryou.web.feign.SingleLoginService;
import com.boryou.web.module.loginlog.domain.ByViewRecord;
import com.boryou.web.module.loginlog.service.IByViewRecordService;
import com.boryou.web.util.EncryptionUtil;
import com.boryou.web.util.MsgSendUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.servlet.http.HttpServletRequest;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 登录验证
 *
 * <AUTHOR>
 */
@RestController
@Slf4j
public class SysLoginController {
    @Autowired
    private SysLoginService loginService;

    @Autowired
    private ISysMenuService menuService;

    @Autowired
    private SysPermissionService permissionService;

    @Autowired
    private TokenService tokenService;

    @Autowired
    private SingleLoginService singleLoginService;

    @Autowired
    private SysDeptMapper sysDeptMapper;
    @Autowired
    private ISysUserService sysUserService;
    @Autowired
    private RedisCache redisCache;
    @Autowired
    private IByViewRecordService loginLoginViewService;

    @Autowired
    private ISysUserService userService;

    /**
     * 登录方法
     *
     * @param loginBody 登录信息
     * @return 结果
     */
    @PostMapping("/login")
    public AjaxResult login(@RequestBody LoginBody loginBody, HttpServletRequest request) {
        AjaxResult ajax = AjaxResult.success();
        Long deptId = null;
        // 校验当前用户是否是当前url所属组织的用户
        String referer = request.getHeader("Referer");
        if (StringUtils.isNotEmpty(referer)) {
            // 去掉IP端口域名
            String pattern = "://[^/]+/([^/]+)/login";
            Pattern compiledPattern = Pattern.compile(pattern);
            Matcher matcher = compiledPattern.matcher(referer);
            if (matcher.find()) {
                String systemId = matcher.group(1);
                deptId = sysDeptMapper.getDeptIdBySystemId(systemId);
            }
        }

        // 生成令牌
        String token = loginService.login(loginBody.getUsername(), loginBody.getPassword(), loginBody.getCode(), deptId);
        ajax.put(Constants.TOKEN, token);
        return ajax;
    }

    /**
     * token 获取用户id登录
     *
     * @return
     */
    @GetMapping("/userIdLogin")
    public AjaxResult userIdLogin(String token) {
        // 校验 token
        AjaxResult result = singleLoginService.verifyToken(token, 9);
        String userId;
        if (result.getCode() != 200) {
            throw new CustomException(result.getMsg());
        } else {
            userId = result.get("userId").toString();
        }
        AjaxResult ajax = AjaxResult.success();
        // 生成令牌
        String ticket = loginService.userIdLogin(Long.valueOf(userId));
        ajax.put(Constants.TOKEN, ticket);
        return ajax;
    }

    /**
     * 获取用户信息
     *
     * @return 用户信息
     */
    @GetMapping("getInfo")
    public AjaxResult getInfo() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        SysUser user = loginUser.getUser();
        // 角色集合
        Set<String> roles = permissionService.getRolePermission(user);
        // 权限集合
        Set<String> permissions = permissionService.getMenuPermission(user);
        AjaxResult ajax = AjaxResult.success();
        ajax.put("user", user);
        ajax.put("roles", roles);
        ajax.put("permissions", permissions);
        return ajax;
    }

    /**
     * 获取路由信息
     *
     * @return 路由信息
     */
    @GetMapping("getRouters")
    public AjaxResult getRouters() {
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        // 用户信息
        SysUser user = loginUser.getUser();
        List<SysMenu> menus = menuService.selectMenuTreeByUserId(user.getUserId());
        return AjaxResult.success(menuService.buildMenus(menus));
    }

    /**
     * 短信获取验证码
     *
     * @param phone
     * @return
     */
    @GetMapping("/login/authCode")
    public AjaxResult sendSms(String phone) {
        if (!PhoneUtil.isPhone(phone)) {
            throw new CustomException("请输入正确的手机号");
        }
//        SysUser loginUser = sysUserService.getUserByTelephone(phone);
//        if (loginUser == null) {
//            throw new CustomException("非法用户");
//        }
        String countKey = RedisConstant.LOGIN_COUNT_PREFIX + phone;
        Object cacheObject = redisCache.getCacheObject(countKey);
        if (cacheObject != null) {
            throw new CustomException("操作频繁,请稍后再试");
        }
//        验证码
        String authCode = String.valueOf(RandomUtil.randomInt(100001, 999999));
        String resp = MsgSendUtil.sendSms(phone, SmsConstant.SMS_LOGIN_TEMPLATE, Arrays.asList(authCode));
//        String resp = MsgSendUtil.sendSms(phone, RedisConstant.SMS_LOGIN_TEMPLATE, Arrays.asList(authCode));
        AjaxResult result = JSONUtil.toBean(resp, AjaxResult.class);
//        AjaxResult result = new AjaxResult();
//        if (result.getCode() == 200) {
        String key = RedisConstant.LOGIN_AUTH_CODE_PREFIX + phone;
        redisCache.setCacheObject(key, authCode, 3, TimeUnit.MINUTES);
        redisCache.setCacheObject(countKey, "1", 60, TimeUnit.SECONDS);
        result.put("code", authCode);
//        }
        return result;
    }

    /**
     * token 获取用户id登录
     *
     * @return
     */
    @PostMapping("/login/phoneLogin")
    public AjaxResult phoneLogin(@RequestBody LoginBody loginBody) {
        String code = loginBody.getCode();
        String phone = loginBody.getPhone();
        AjaxResult ajax = AjaxResult.success();
        String key = RedisConstant.LOGIN_AUTH_CODE_PREFIX + phone;
        Object cacheObject = redisCache.getCacheObject(key);
        if (Objects.equals(code, "0a9z") || (CharSequenceUtil.isNotEmpty(code) && cacheObject != null && code.equals(cacheObject.toString()))) {
            String token = loginService.login("guest", "2899408a7164fedbe834fc5e8eea608b", null, 2L);//密码是：zjsgy@#123
            ajax.put(Constants.TOKEN, token);
            if (StrUtil.isNotEmpty(token)) {
                LoginUser loginUser = tokenService.getTokenUser(token);
                redisCache.setCacheObject(RedisConstant.system_prefix + "login:" + loginUser.getToken(), phone, 30, TimeUnit.DAYS);
                ByViewRecord list = loginLoginViewService.getOne(Wrappers.<ByViewRecord>lambdaQuery().eq(ByViewRecord::getPhone, phone));
                if (null == list) {
                    ByViewRecord entity = new ByViewRecord();
                    entity.setPhone(phone);
                    entity.setCreateTime(new Date());
                    entity.setUserId(loginUser.getUser().getUserId());
                    loginLoginViewService.save(entity);
                }
            }
        } else {
            return AjaxResult.error("验证码错误");
        }
        return ajax;
    }

    String key = "JL3UzP1wlr8p2NYV";
    String iv = "PwVI8T9rI30NggMP";
    /**
     * 安大思政单点登录的接口
     * http://ahsz.ahu.edu.cn/home
     *
     */
    @GetMapping("/adszLogin")
    public AjaxResult adszLogin(String param,String code) {
        AjaxResult ajax = AjaxResult.success();
        try {
            String decodedStr = URLDecoder.decode(param, "UTF-8").replace(" ", "+");
            String resData = EncryptionUtil.desEncrypt(decodedStr, key, iv);
            if (null != resData) {
                JSONObject jsonObject = JSONUtil.parseObj(resData);
                String user = jsonObject.getStr("user");
                JSONObject js = JSONUtil.parseObj(user);
                String usercode = js.getStr("usercode");
                String username = js.getStr("username");
                SysUser sysUser = userService.selectUserByUserName(usercode);
                if (null==sysUser){
                    SysUser user1 = new SysUser();
                    user1.setUserName(usercode);
                    user1.setNickName(username);
                    user1.setPhonenumber(js.getStr("phone"));
                    user1.setUserType("00");
                    user1.setSex("0");
                    user1.setDeptId(300L);
                    user1.setPassword(SecurityUtils.encryptPassword(Md5Utils.hash("adsz@Klwc")));
                    user1.setStatus("0");
                    user1.setDelFlag("0");
                    user1.setCreateBy("system");
                    user1.setCreateTime(new Date());
                    user1.setRoleIds(new Long[]{117L});
                    int i = userService.insertUser(user1);
                    sysUser=userService.selectUserByUserName(usercode);//新增完后查询一次
                }
                log.info("code:{},安达思政返回:{}",code,jsonObject);
                String ticket = loginService.userIdLogin(sysUser.getUserId());
                ajax.put(Constants.TOKEN, ticket);
            }
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("用户信息异常");
        }
        return ajax;
    }

    public static void main(String[] args) {
        try {
            String s = EncryptionUtil.desEncrypt("XYa7sYew52M6YszPmtXKXo5Z6keCYQe4oQnLW5ZaCeiHE2XX1U4CB+bRcjyf+OKzqEsrblUBh6ICHzECICoHgRSjXKks9JFdcv8FPQHcei13IfyENuREwx8bgC45tk5KGDrPEaGmctVX6TqH9D4EKXGh02LYGwlcu0NLGTfdeP5046H6YHiguqcrCiPHlkt0sSFg3lX0RwMZCbg73vMpXwNlzLN/6bNoG4tDDi9mJSnQPXrRtE+pPRkNv/jagFlQ"
                    ,  "JL3UzP1wlr8p2NYV","PwVI8T9rI30NggMP");
            System.out.println(s);
            String s1 = SecurityUtils.encryptPassword(Md5Utils.hash("123456"));
            System.out.println(s1);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }


}
