package com.boryou.web.controller.bigscreen.controller;

import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Page;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysDictData;
import com.boryou.common.utils.DateUtils;
import com.boryou.system.service.ISysDictDataService;
import com.boryou.web.controller.bigscreen.vo.Plate;
import com.boryou.web.controller.bigscreen.vo.StatisticsBeanBO;
import com.boryou.web.controller.common.entity.BoryouBean;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.service.ElasticsearchService;
import com.boryou.web.util.Common;
import com.boryou.web.util.IndexStatistics;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.json.JsonArray;
import javax.servlet.http.HttpServletResponse;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 数据采集态势controller
 * <AUTHOR>
 * @date 2024-11-22 10:09
 */
@RequiredArgsConstructor
@RestController
@RequestMapping("/plateScreen")
public class DataAcquisitionController
{
    @Resource
    private ISysDictDataService dictDataService;

    @Resource
    ElasticsearchService elasticsearchService;

    /**
     * <AUTHOR>
     * @Description 获取数据采集情况
     * @Date 2020/12/9 15:00
     */
    @RequestMapping("/getGatherCase")
    public AjaxResult getGatherCase() {
        AjaxResult ajaxResult=new AjaxResult();
        try {
            Plate plate = new Plate();
            Map<String, Integer> map = IndexStatistics.getTodayTypeCountFromEs(plate);
            Map<String, Integer> map1 = new HashMap<>();
            SysDictData dictData = new SysDictData();
            dictData.setDictType("sys_media_type");
            dictData.setStatus("0");
            List<SysDictData> mediaType = dictDataService.selectDictDataList(dictData);
            if (mediaType != null) {
                Map<String, String> collect = mediaType.stream().collect(Collectors.toMap(s -> s.getDictValue(), t -> t.getDictLabel()));
                Set<String> strings = collect.keySet();
                for (String string : strings) {
                    Integer integer = map.get(String.valueOf(string));
                    map1.put(collect.get(string), integer != null ? integer : 0);
                }
            }
            Set<String> strings = map1.keySet();
            Long num=0L;
            JSONArray arr=new JSONArray();
            for (String string : strings) {
                Integer i = map1.get(string);
                num+=i;
                JSONObject value = new JSONObject();
                value.set("name",string);
                value.set("value",i);
                arr.put(value);
            }
            sortJSONArrayByValueDesc(arr);
            JSONObject da=new JSONObject();
            da.set("res",arr);
            da.set("total",num);
            // 填入数据
            ajaxResult.put(AjaxResult.DATA_TAG, da);
            // 没有数据返回失败
//            if (map1.size()== 0) {
//                ajaxResult=AjaxResult.error();
//            }

        } catch (Exception e) {
            e.printStackTrace();
        }
        return ajaxResult;
    }
    private static void sortJSONArrayByValueDesc(JSONArray array) {
        List<JSONObject> tempList = new ArrayList<>();
        for (Object obj : array) {
            tempList.add((JSONObject) obj);
        }
        Collections.sort(tempList, new Comparator<JSONObject>() {
            @Override
            public int compare(JSONObject o1, JSONObject o2) {
                Long value1 = o1.getLong("value");
                Long value2 = o2.getLong("value");
                return Long.compare(value2, value1);
            }
        });
        array.clear();
        array.addAll(tempList);
    }
    private static void sortJSONArrayByName(JSONArray array) {
        List<JSONObject> tempList = new ArrayList<>();
        for (Object obj : array) {
            tempList.add((JSONObject) obj);
        }
        Collections.sort(tempList, new Comparator<JSONObject>() {
            @Override
            public int compare(JSONObject o1, JSONObject o2) {
                return o1.getStr("name").compareTo(o2.getStr("name"));
            }
        });
        array.clear();
        array.addAll(tempList);
    }
    /**
     * @param response
     * @return void
     * <AUTHOR>
     * @Description 获取今天24小时数据趋势图
     * @Date 2020/12/14 09:00
     */
    @RequestMapping("/getTodayTrend")
    public AjaxResult getTodayTrend(HttpServletResponse response) {
        AjaxResult res1=new AjaxResult();
        StatisticsBeanBO bo = new StatisticsBeanBO();
        DateTime dateTime = DateUtil.offsetDay(new Date(), -1);
        bo.setStartTime(DateUtils.beginOfHourStr(dateTime, "yyyyMMddHHmmss"));
        bo.setEndTime(DateUtils.endOfHourStr(new Date()));
        Map<String, Object> map = EsSearchUtil.dayTimeTrend(bo);

        // 只显示state为1的媒体类型
        SysDictData dictData = new SysDictData();
        dictData.setDictType("sys_media_type");
        dictData.setStatus("0");
        List<SysDictData> mediaType = dictDataService.selectDictDataList(dictData);
        JSONArray arr=new JSONArray();
        if (mediaType != null) {
            List<String> collect = mediaType.stream().map(SysDictData::getDictLabel).collect(Collectors.toList());
            Map<String,Object> data = MapUtil.get(map, "data", Map.class);
            Map<String,Object> res = new HashMap<>();
            data.forEach((k, v) -> {
                String k1=k;
                if (k.equals("短视频")){
                    k1="视频";
                }
                if (/*!StrUtil.equalsAny(k,"政务","评论","博客")&&*/collect.contains(k1)) {
                    JSONObject d1=new JSONObject();
                    res.put(k1, v);
                    d1.set("name",k1);
                    d1.set("data",v);
                    arr.add(d1);
                }
            });
//            res1.put("data", res);
        }
//        res1.put("timeAxis",map.get("timeAxis"));
//        sortJSONArrayByName(arr);
        JSONObject da=new JSONObject();
        da.set("xs",arr);
        da.set("timeAxis",map.get("timeAxis"));
        // 填入数据
        res1.put(AjaxResult.DATA_TAG, da);
       return res1;
    }


    /**
     * <AUTHOR>
     * @Description 获取数据采集分布
     * @Date 2020/12/14 09:30
     */
    @PostMapping("/getDataDistribute")
    public AjaxResult getDataDistribute() {
        AjaxResult res=new AjaxResult();
        Plate plate = new Plate();
        try {
            Map<String, Integer> map = IndexStatistics.getTodayTypeCountByEs(plate);
            map.remove("暂时未识别到");
//            Set<String> strings = map.keySet();
//            JSONArray arr=new JSONArray();
//            for (String string : strings) {
//                Integer i = map.get(string);
//                JSONObject value = new JSONObject();
//                value.set("name",string);
//                value.set("value",i);
//                arr.put(value);
//            }

            Map<String, Integer> map1 = new HashMap<>();
            SysDictData dictData = new SysDictData();
            dictData.setDictType("sys_media_type");
            dictData.setStatus("0");
            List<SysDictData> mediaType = dictDataService.selectDictDataList(dictData);
            if (mediaType != null) {
                Map<String, String> collect = mediaType.stream().collect(Collectors.toMap(s -> s.getDictLabel(), t -> t.getDictValue()));
                Set<String> strings = collect.keySet();
                for (String string : strings) {
                    String k1=string;
                    if (k1.equals("短视频")){
                        k1="视频";
                    }
                    Integer integer = map.get(String.valueOf(k1));
                    map1.put(string, integer != null ? integer : 0);
                }
            }
            Set<String> strings = map1.keySet();
            JSONArray arr=new JSONArray();
            for (String string : strings) {
                Integer i = map1.get(string);
                JSONObject value = new JSONObject();
                value.set("name",string);
                value.set("value",i);
                arr.put(value);
            }



//            JSONObject da=new JSONObject();
//            da.set("res",arr);
          res.put(AjaxResult.DATA_TAG,arr);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return res;
    }

    /**
     * <AUTHOR>
     * @Description 获取数据实时信息
     * @Date 2020/12/14 10:00
     */
    @PostMapping("/getRealtimeInfo")
    public AjaxResult getRealtimeInfo() {
        AjaxResult res=AjaxResult.success();
        Plate plate = new Plate();
//        Map<String, Object> resultMap = new HashMap<String, Object>();
        // 获取当前时间作为结束时间
        Date today = new Date();
        String endTime = Common.dateToLongString(today);
        Date start;
        // 计算开始时间前一天
        start = Common.addDays(today, -1);
        String startTime = Common.dateToLongString(start);
        Page page = new Page();
        // 放置一百五十条保证展示不会重复
        page.setPageSize(150);
        page.setPageNumber(1);
//        plate.setMediaType("1 0 2 3 5 17 6"); // 不要广播 电视  海外
        StringBuilder key = new StringBuilder();
//        key.append("合肥");
//        plate.setAreaCode("340100");
//        if (null != getLoginUser()) {
//            List<String> list = screenKeywordService.getKeywordByuser(getLoginUser().getOpenId());
//            if (list.size() > 0) {
//                for (String s : list) {
//                    key.append(" ").append(s);
//                }
//            }
//        }
        try {
            plate.setZfflag("1");
            List<BoryouBean> boryouBeanList =EsSearchUtil.searchByPlate(plate, key.toString(), startTime, endTime, page);
            Map<Integer, List<String>> hostMap = boryouBeanList.stream().filter(s -> s != null && !s.getHost().isEmpty()).collect(Collectors.groupingBy(BoryouBean::getType, Collectors.mapping(BoryouBean::getHost, Collectors.toList())));
            List<AccountInfoVO> accountInfoVOList = elasticsearchService.getNameByHosts(hostMap);
            Map<String, String>    hostAndNameMap = accountInfoVOList.stream().distinct().collect(Collectors.toMap(AccountInfoVO::getDomain, AccountInfoVO::getSector, (oldValue, newValue) -> oldValue));
            for (BoryouBean boryouBean : boryouBeanList) {
                boryouBean.setHostName(hostAndNameMap.get(boryouBean.getHost()));
            }
            res.put(AjaxResult.DATA_TAG,boryouBeanList);
        } catch (Exception e) {
            e.printStackTrace();
            res=AjaxResult.error();
        }
        return res;

    }
}

