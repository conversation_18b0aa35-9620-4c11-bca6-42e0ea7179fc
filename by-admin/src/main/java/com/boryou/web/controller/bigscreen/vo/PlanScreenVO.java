package com.boryou.web.controller.bigscreen.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotNull;

@Data
public class PlanScreenVO {

    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "方案不能为空")
    private Long planId;

    private String mapName;

    private String areaName;

}
