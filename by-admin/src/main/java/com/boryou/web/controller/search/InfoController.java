package com.boryou.web.controller.search;

import cn.hutool.core.util.StrUtil;
import com.boryou.common.annotation.Log;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.enums.BusinessType;
import com.boryou.web.controller.common.entity.BoryouBean;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.domain.vo.DetailVO;
import com.boryou.web.domain.vo.InfoVO;
import com.boryou.web.service.InfoService;
import com.boryou.web.util.DoubaoUtil;
import com.hankcs.hanlp.HanLP;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

@RestController
public class InfoController {

    @Resource
    private InfoService infoService;

    @Log(title = "单条信息详情", businessType = BusinessType.QUERY)
    @PostMapping("/info/one")
    public AjaxResult oneInfo(@RequestBody InfoVO infoVO) {
        DetailVO detailVO = infoService.oneInfo(infoVO);
        return AjaxResult.success(detailVO);
    }

    @PostMapping("/info/similar")
    public AjaxResult getSimilarData(@RequestBody InfoVO infoVO) {
        List<BoryouBean> similarInfo = infoService.getSimilarInfo(infoVO);
        return AjaxResult.success(similarInfo);
    }

    @PostMapping("/info/keyWord")
    public AjaxResult getKeyWordsAnalyse(@RequestBody EsBean bean) {
        return AjaxResult.success();
    }

    /**
     * 生成式摘要
     *
     * @param text
     * @return
     */
    @PostMapping("/info/getSummary")
    public AjaxResult getSummary(@RequestBody(required = false) String text) {
        String summary = "";
        if (StrUtil.isNotEmpty(text)) {
            summary = DoubaoUtil.summary(text, 0);
            if (!summary.contains("<br/>")) {
                //使用hanlp生成摘要
                summary = HanLP.getSummary(text, 300);
            }
        }
        return AjaxResult.success(summary);
    }

    /**
     * 获取文章关键词，等信息，用于快速创建方案
     *
     * @return
     */
    @PostMapping("/info/detail")
    public AjaxResult keywordsDetail(@RequestBody InfoVO infoVO) {
        return AjaxResult.success(infoService.keywordsDetail(infoVO));
    }
}
