package com.boryou.web.controller.hot;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import com.boryou.common.annotation.Log;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.domain.Hot;
import com.boryou.web.domain.OutHot;
import com.boryou.web.domain.SearchWord;
import com.boryou.web.domain.vo.CloudWordVO;
import com.boryou.web.domain.vo.HotVO;
import com.boryou.web.feign.SingleLoginService;
import com.boryou.web.mapper.HotListMapper;
import com.boryou.web.mapper.SearchWordMapper;
import com.boryou.web.module.home.service.HomeStatisService;
import com.boryou.web.module.hot.service.IHotListService;
import com.boryou.web.service.HotService;
import com.boryou.web.task.HomeTask;
import lombok.AllArgsConstructor;
import org.springframework.context.annotation.Lazy;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.io.IOException;
import java.util.*;
import java.util.stream.Collectors;

import static com.boryou.web.enums.HotTypeEnum.*;

/**
 * 热榜Controller
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@AllArgsConstructor
@RestController
public class HotController extends BaseController {

    private final RedisCache redisTemplate;
    private final HomeStatisService homeStatisService;
    private final IHotListService hotListService;
    private final HotListMapper hotListMapper;
    private final HotService hotService;
    private final SearchWordMapper searchWordMapper;
    private final SingleLoginService singleLoginService;

    @Resource(name = "threadPoolTaskExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;
    @Resource
    @Lazy
    private final HomeTask homeTask;

    /**
     * 查询热榜列表
     */
    @PostMapping("/hot/rank/list")
    public AjaxResult hotList(@RequestBody HotVO hotVO) {
        List<Hot> list = hotService.selectHotList(hotVO);
        return AjaxResult.success(list);
    }

    /**
     * 查询热榜列表（综合治理）
     */
    @PostMapping("/hot/rank/listForZhzl")
    public AjaxResult listForZhzl(Integer count) {
        Map<String, Object> res = hotService.listForZhzl(count);
        return AjaxResult.success(res);
    }

    @PostMapping("/souce/outRankList")
    public AjaxResult rankList(@RequestBody JSONObject object) {
        String encryKey = object.getStr("key");
        if (!object.containsKey("key") || StrUtil.isEmpty(encryKey) || encryKey.length() != 20 || encryKey.charAt(1) != 'x' || encryKey.charAt(5) != 'Y' || encryKey.charAt(13) != 'z' || encryKey.charAt(7) != '1' || encryKey.charAt(11) != '6' || encryKey.charAt(17) != '9') {
            return AjaxResult.error();
        }
        Integer count = object.getInt("count");
        Integer type = object.getInt("type");
        return AjaxResult.success(rankList(count, type));
    }

    @PostMapping("/souce/outHotList")
    public AjaxResult outHotList(@RequestBody JSONObject object) {
        String encryKey = object.getStr("key");
        if (!object.containsKey("key") || StrUtil.isEmpty(encryKey) || encryKey.length() != 20 || encryKey.charAt(1) != 'x' || encryKey.charAt(5) != 'Y' || encryKey.charAt(13) != 'z' || encryKey.charAt(7) != '1' || encryKey.charAt(11) != '6' || encryKey.charAt(17) != '9') {
            return AjaxResult.error();
        }
        List<String> list = object.getBeanList("typeList", String.class);
        List<String> keyword = new ArrayList<>();
        String time = "";
        if (object.containsKey("keyword")) {
            String word = object.getStr("keyword");
            keyword = new ArrayList<>(Arrays.asList(word.split(" ")));
        }
        if (object.containsKey("time")) {
            time = object.getStr("time");
        }

        List<OutHot> hots = hotService.selectOutHotList(list, keyword, time);
        if (CollUtil.isNotEmpty(keyword)) {
            for (OutHot hot : hots) {
                List<String> words = keyword.stream().filter(w -> hot.getTitle().contains(w)).collect(Collectors.toList());
                hot.setKeyword(CollUtil.join(words, ","));
            }
        }

        return AjaxResult.success(hots);
    }

    @PostMapping("/souce/outAnnouncementList")
    public AjaxResult outAnnouncementList(@RequestBody JSONObject object) {
        String encryKey = object.getStr("key");
        if (!object.containsKey("key") || StrUtil.isEmpty(encryKey) || encryKey.length() != 20 || encryKey.charAt(1) != 'x' || encryKey.charAt(5) != 'Y' || encryKey.charAt(13) != 'z' || encryKey.charAt(7) != '1' || encryKey.charAt(11) != '6' || encryKey.charAt(17) != '9') {
            return AjaxResult.error();
        }
        String time = "";
        if (object.containsKey("time")) {
            time = object.getStr("time");
        }
        return AjaxResult.success(hotService.selectOutAnnouncementList(time));
    }

    @PostMapping("/souce/outKeywordIndexList")
    public AjaxResult outKeywordIndexList(@RequestBody JSONObject object) {
        String encryKey = object.getStr("key");
        if (!object.containsKey("key") || StrUtil.isEmpty(encryKey) || encryKey.length() != 20 || encryKey.charAt(1) != 'x' || encryKey.charAt(5) != 'Y' || encryKey.charAt(13) != 'z' || encryKey.charAt(7) != '1' || encryKey.charAt(11) != '6' || encryKey.charAt(17) != '9') {
            return AjaxResult.error();
        }
        String time = "";
        if (object.containsKey("time")) {
            time = object.getStr("time");
        }
        return AjaxResult.success(hotService.selectOutKeywordIndexList(time));
    }

    /**
     * 微微博、抖音、百度热搜
     * 1：微博；2-抖音；3-百度
     */
    @GetMapping("/hot/rankList")
    public AjaxResult rankList(Integer count, Integer type) {
        Map<String, List<Hot>> res = new HashMap<>();

        if (type == 1) {
            // WB_REALTIME微博实时榜， WB_NEWS微博要闻
            List<Hot> realTime = hotListMapper.selectHotTypeList(WB_REALTIME.getType(), count);
            List<Hot> news = hotListMapper.selectHotTypeList(WB_NEWS.getType(), count);

            res.put("realTime", realTime);
            res.put("news", news);
        } else if (type == 2) {
            // DOUYIN_HOT抖音热点榜，DOUYIN_VIDEO抖音视频榜
            List<Hot> hot = hotListMapper.selectHotTypeList(DOUYIN_HOT.getType(), count);
            List<Hot> video = hotListMapper.selectHotTypeList(DOUYIN_VIDEO.getType(), count);

            res.put("hot", hot);
            res.put("video", video);
        } else if (type == 3) {
            // REALTIME 实时舆情，日 DAY，周 WEEK
            List<Hot> realTime = hotListMapper.selectHotTypeList(REALTIME.getType(), count);
            List<Hot> day = hotListMapper.selectHotTypeList(DAY.getType(), count);
            List<Hot> week = hotListMapper.selectHotTypeList(WEEK.getType(), count);

            res.put("realTime", realTime);
            res.put("day", day);
            res.put("week", week);
        }

        return AjaxResult.success(res);
    }

    /**
     * 抖音热榜
     */
    @GetMapping("/hot/douyin")
    public AjaxResult hotDouyin(Integer count) {
        // DOUYIN_HOT抖音热点榜，DOUYIN_VIDEO抖音视频榜
        List<Hot> hot = hotListMapper.selectHotTypeList("DOUYIN_HOT", count);
        List<Hot> video = hotListMapper.selectHotTypeList("OUYIN_VIDEO", count);

        Map<String, List<Hot>> res = new HashMap<>();
        res.put("hot", hot);
        res.put("video", video);
        return AjaxResult.success(res);
    }


    @PostMapping("/hot/word/list")
    public AjaxResult hotWord(@RequestBody HotVO hotVO) {
        List<String> words = hotService.selectHotWord(hotVO);
        return AjaxResult.success(words);
    }

    @PostMapping("/hot/word/add")
    public AjaxResult addWord(@RequestBody HotVO hotVO) {
        String word = hotVO.getWord();
        if (StrUtil.isBlankIfStr(word)) {
            throw new CustomException("搜索词不能为空");
        }
        int row = hotService.addWord(word);
        if (row > 0) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }

    }

    @DeleteMapping("/hot/word/delete")
    public AjaxResult deleteWord(@RequestBody SearchWord word) {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        if (searchWordMapper.deleteWord(word.getWord(), userId) > 0) {
            return AjaxResult.success();
        }
        return AjaxResult.error();
    }

    @Log(title = "首页添加热词云过滤词", businessType = BusinessType.INSERT)
    @PostMapping("/hot/cloud/add")
    public AjaxResult addCloudWord(@RequestBody CloudWordVO vo) {
        String word = vo.getWord();
        if (StrUtil.isBlankIfStr(word)) {
            throw new CustomException("关键词不能为空");
        }
        int row = hotService.addCloudWord(vo);
        if (row > 0) {
            List<String> areaCodeList = homeStatisService.getAreaCodeList();
            redisTemplate.deleteObject(redisTemplate.keys(RedisConstant.wordcloud + "*"));
            // 删除完立即更新缓存，不然得等到下一次热刺云定时更新才会生效。
            threadPoolTaskExecutor.execute(() -> {
                try {
                    logger.info("更新热词云开始");
                    homeTask.getWordCloud(areaCodeList);
                    logger.info("更新热词云结束");
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/hot/cloud/hide")
    public AjaxResult hideWord(@RequestBody CloudWordVO vo) {
        int row = 0;
        if (StrUtil.isNotEmpty(vo.getIds())) {
            row = hotService.updateWordStateByIds(vo.getIds(), "0");
        }
        if (row > 0) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @GetMapping("/hot/cloud/page")
    public TableDataInfo wordPage(@ModelAttribute CloudWordVO vo) {
        vo.setState("1");
        startPage();
        List<CloudWordVO> words = hotService.cloudWordList(vo);
        return getDataTable(words);
    }
}
