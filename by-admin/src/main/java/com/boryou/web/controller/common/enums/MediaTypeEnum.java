package com.boryou.web.controller.common.enums;

/**
 * <AUTHOR>
 * @description 媒体类型枚举类
 * @date 2024/4/28 11:16
 */
public enum MediaTypeEnum {
    //论坛
    FORUM(0, "论坛社区"),
    //新闻
    NEWS(1, "新闻"),
    //博客
    BLOG(2, "博客"),
    //微博
    WEIBO(3, "微博"),
    //微信
    WECHAT(5, "微信"),
    //手机客户端
    CLIENT(6, "客户端"),
    //广播
    AUDIO(8, "广播"),
    //电视
    TV(9, "电视"),
    //图片
    IMAGE(10, "图片"),
    //小视频   抖音，懂车帝  西瓜  头条  快手
    VIDEO(11, "短视频"),
    //专利
    PATENT(15, "专利"),
    //电子报
    EPAPER(17, "电子报刊"),
    //海外
    OVERSEAS(24, "境外"),
    //政务
    GOV(25, "政务"),
    //评论
    COMMENT(26, "评论");

    int value;
    String desc;

    MediaTypeEnum(int value, String desc) {
        this.value = value;
        this.desc = desc;
    }

    public static String getDesc(String value) {
        for (MediaTypeEnum mediaTypeEnum : MediaTypeEnum.values()) {
            if (mediaTypeEnum.value == Integer.parseInt(value)) {
                return mediaTypeEnum.desc;
            }
        }
        return null;
    }


    public static String getDescByInt(Integer value) {
        for (MediaTypeEnum mediaTypeEnum : MediaTypeEnum.values()) {
            if (mediaTypeEnum.value ==value) {
                return mediaTypeEnum.desc;
            }
        }
        return null;
    }

    public int getValue() {
        return value;
    }

    public String getDesc() {
        return desc;
    }

    public static String getAllType() {
        StringBuilder str = new StringBuilder();
        for (MediaTypeEnum item : MediaTypeEnum.values()) {
            if (str.length() != 0) {
                str.append(",");
            }
            str.append(item.getValue());
        }
        return str.toString();
    }
}
