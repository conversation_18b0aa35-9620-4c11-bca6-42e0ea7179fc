package com.boryou.web.controller.submit;

import java.util.Date;
import java.util.List;

import com.boryou.web.module.submit.entity.Submit;
import com.boryou.web.module.submit.service.ISubmitService;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.boryou.common.annotation.Log;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.utils.poi.ExcelUtil;
import com.boryou.common.core.page.TableDataInfo;

/**
 * 报送记录Controller
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@RestController
@RequestMapping("/submit")
public class SubmitController extends BaseController {
    @Autowired
    private ISubmitService submitService;

    /**
     * 舆情短信处置
     */
    @PostMapping("/process")
    public AjaxResult process(@RequestBody Submit submit) {
        submit.setProcessTime(new Date());
        submit.setProcessStatus("1");
        return toAjax(submitService.insertSubmitProcess(submit));
    }

    @GetMapping("/getProcessInfo")
    public AjaxResult getProcessInfo(Submit submit) {
        Submit processInfo = submitService.getProcessInfo(submit);
        return AjaxResult.success(processInfo);
    }

    /*
     *//**
     * 查询报送记录列表
     *//*
    @PreAuthorize("@ss.hasPermi('submit:message:list')")
    @GetMapping("/list")
            public TableDataInfo list(Submit submit) {
            startPage();
            List<Submit> list = submitService.selectSubmitList(submit);
            return getDataTable(list);
        }*/

    /*   *//**
     * 导出报送记录列表
     *//*
    @PreAuthorize("@ss.hasPermi('submit:message:export')")
    @Log(title = "报送记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(Submit submit) {
        List<Submit> list = submitService.selectSubmitList(submit);
        ExcelUtil<Submit> util = new ExcelUtil<Submit>(Submit. class);
        return util.exportExcel(list, "message");
    }

    *//**
     * 获取报送记录详细信息
     *//*
    @PreAuthorize("@ss.hasPermi('submit:message:query')")
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(submitService.selectSubmitById(id));
    }

    *//**
     * 新增报送记录
     *//*
    @PreAuthorize("@ss.hasPermi('submit:message:add')")
    @Log(title = "报送记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody Submit submit) {
        return toAjax(submitService.insertSubmit(submit));
    }

    *//**
     * 修改报送记录
     *//*
    @PreAuthorize("@ss.hasPermi('submit:message:edit')")
    @Log(title = "报送记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody Submit submit) {
        return toAjax(submitService.updateSubmit(submit));
    }

    *//**
     * 删除报送记录
     *//*
    @PreAuthorize("@ss.hasPermi('submit:message:remove')")
    @Log(title = "报送记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(submitService.deleteSubmitByIds(ids));
    }*/
}
