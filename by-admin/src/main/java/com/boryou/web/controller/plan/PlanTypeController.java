package com.boryou.web.controller.plan;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.web.domain.PlanType;
import com.boryou.web.domain.vo.PlanTypeVO;
import com.boryou.web.domain.vo.SortVO;
import com.boryou.web.service.PlanTypeService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 方案Controller
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@RestController
public class PlanTypeController extends BaseController {
    @Resource
    private PlanTypeService planTypeService;

    @PostMapping("/planType/list")
    public AjaxResult planTypeList(String typeName) {
        List<PlanType> planList = planTypeService.planTypeList(typeName);
        return AjaxResult.success(planList);
    }

    @PostMapping("/planType/add")
    public AjaxResult addPlanType(@RequestBody PlanTypeVO planTypeVO) {
        if (planTypeService.addPlanType(planTypeVO)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/planType/update")
    public AjaxResult updatePlanType(@RequestBody PlanTypeVO planTypeVO) {
        if (planTypeService.updatePlanType(planTypeVO)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/planType/del")
    public AjaxResult delPlanType(@RequestBody PlanTypeVO planTypeVO) {
        if (planTypeService.delPlanType(planTypeVO)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/planType/move")
    public AjaxResult movePlanType(@RequestBody SortVO sortVO) {
        if (planTypeService.movePlanType(sortVO)) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    /**
     * 方案管理方案分类查询
     *
     * @param typeName
     * @return
     */
    @GetMapping("/planType/manage/list")
    public TableDataInfo manageList(String typeName, Long userId, Integer pageNum, Integer pageSize) {
        return getDataTable(planTypeService.manageList(typeName, userId, pageNum, pageSize));
    }
}
