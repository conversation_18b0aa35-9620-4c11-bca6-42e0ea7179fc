package com.boryou.web.controller.common.enums;

/**
 * <AUTHOR>
 * @description 排序方式枚举类
 * @date 2024/5/8 15:14
 */
public enum SortTypeEnum {
    //相关度降序
    RELATION_DESC("1"),
    //热度降序
    HOT_DESC("2"),
    //时间降序
    TIME_DESC("3"),
    //时间升序
    TIME_ASC("4"),
    //热度升序
    HOT_ASC("5"),
    //相关度升序
    RELATION_ASC("6"),
    //综合
    COMPREHENSIVE("0");
    String sortValue;

    SortTypeEnum(String s) {
        this.sortValue = s;
    }

    /**
     * 根据值获取枚举
     *
     * @param value
     * @return
     */
    public static SortTypeEnum getByValue(String value) {
        SortTypeEnum[] values = values();
        for (SortTypeEnum sortTypeEnum : values) {
            if (sortTypeEnum.sortValue.equals(value)) {
                return sortTypeEnum;
            }
        }
        return COMPREHENSIVE;
    }

    /**
     * 根据值获取枚举
     *
     * @param value
     * @return
     */
    public static Integer getEnumValue(SortTypeEnum value) {
        SortTypeEnum[] values = values();
        for (SortTypeEnum sortTypeEnum : values) {
            if (sortTypeEnum.sortValue.equals(value.sortValue)) {
                return Integer.parseInt(sortTypeEnum.sortValue);
            }
        }
        return -1;
    }


}
