package com.boryou.web.controller.source;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.boryou.common.annotation.Log;
import com.boryou.common.constant.HttpStatus;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.model.LoginUser;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.ServletUtils;
import com.boryou.common.utils.poi.ExcelUtil;
import com.boryou.framework.web.service.TokenService;
import com.boryou.web.domain.AccountInfo;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.module.source.entity.SourceSetting;
import com.boryou.web.module.source.service.SourceSettingService;
import com.boryou.web.module.source.vo.SourceSettingVO;
import com.boryou.web.module.source.vo.SourceVO;
import com.boryou.web.service.ElasticsearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.IOUtils;
import org.springframework.core.io.ResourceLoader;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.InputStream;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-05-21 10:15
 */
@RestController
@RequestMapping("/souce")
@RequiredArgsConstructor
public class SourceController extends BaseController {

    private final SourceSettingService sourceSettingService;

    private final ElasticsearchService elasticsearchService;

    private final TokenService tokenService;
    private final ResourceLoader resourceLoader;

    /**
     * 查询用户信源列表
     */

    @RequestMapping("/getSourceSetting")
    public TableDataInfo getSourceSetting(SourceSettingVO source) {
        startPage();
        source.setUserId(SecurityUtils.getUserId());
        List<SourceSetting> sourceSettingList = sourceSettingService.getSourceSettingList(source);
        return getDataTable(sourceSettingList);
    }

    /**
     * 导出信源
     */
    @GetMapping("/export")
    public AjaxResult export(SourceSettingVO source) {
        source.setUserId(SecurityUtils.getUserId());
        List<SourceSetting> sourceSettingList = sourceSettingService.getSourceSettingList(source);
        ExcelUtil<SourceSetting> util = new ExcelUtil<>(SourceSetting.class);
        return util.exportExcel(sourceSettingList, "1".equals(source.getSettingType()) ? "定向信源" : "定向排除信源");
    }

    /**
     * 新增信源设置
     *
     * <AUTHOR>
     * @date 2021-09-01 18:48:45
     */
    @Log(title = "新增方案定向信源", businessType = BusinessType.INSERT)
    @PostMapping("/addSourceSetting")
    public AjaxResult addSourceSetting(@RequestBody List<SourceSetting> sourceSetting) {
        if (CollUtil.isEmpty(sourceSetting)) {
            return AjaxResult.error("参数为空");
        }
        List<String> collect = sourceSetting.stream().distinct().map(SourceSetting::getSettingType).collect(Collectors.toList());
        for (String type : collect) {
            LambdaQueryWrapper<SourceSetting> queryWrapper = new LambdaQueryWrapper<>();
            queryWrapper.eq(SourceSetting::getSettingType, type);
            queryWrapper.eq(SourceSetting::getUserId, SecurityUtils.getUserId());
            int count = sourceSettingService.count(queryWrapper);
            if (count > 300) {
                return AjaxResult.error("信源超过限制");
            }
        }
        sourceSettingService.addSource(sourceSetting);
        return AjaxResult.success();
    }

    /**
     * 删除
     */
    @Log(title = "删除方案定向信源", businessType = BusinessType.DELETE)
    @DeleteMapping("/delete")
    public AjaxResult delete(String ids) {
        if (StrUtil.isEmpty(ids)) {
            return AjaxResult.error("参数异常");
        }
        boolean flag = sourceSettingService.delete(ids);
        return AjaxResult.success(flag);
    }

    /**
     * 修改信源状态
     *
     * @return void
     * <AUTHOR>
     * @date 2021-09-01 18:48:45
     */
    @PutMapping("/updateSourceSettingState")
    public AjaxResult updateSourceSettingState(@RequestBody Map params) {
        try {
            SourceSetting sourceSetting = new SourceSetting();
            sourceSetting.setUpdateTime(new Date());
            sourceSettingService.updateSourceSettingState((Integer) params.get("state"), (String) params.get("ids"));
            return AjaxResult.success(true);
        } catch (Exception e) {
            e.printStackTrace();
            return AjaxResult.error("更新失败");
        }
    }

    /**
     * 修改信源状态
     *
     * @return void
     * <AUTHOR>
     * @date 2021-09-01 18:48:45
     */
    @PostMapping("/searchSource")
    public TableDataInfo searchSource(@RequestBody SourceVO source) {
        TableDataInfo rspData = new TableDataInfo();
        try {
            PageResult<AccountInfoVO> accountInfoVOS = elasticsearchService.searchSourceByType(source);
            rspData.setCode(HttpStatus.SUCCESS);
            rspData.setMsg("查询成功");
            rspData.setRows(accountInfoVOS);
            rspData.setTotal(accountInfoVOS.getTotal());
            return rspData;
        } catch (Exception e) {
            e.printStackTrace();
            throw new CustomException("搜索信源异常！");
        }
    }

    /**
     * 下载模板
     */
//    @GetMapping("/importTemplate")
//    public AjaxResult importTemplate(SourceSetting sourceSetting) {
//        ExcelUtil<SourceSetting> util = new ExcelUtil<>(SourceSetting.class);
//        return util.importTemplateExcel("1".equals(sourceSetting.getSettingType()) ? "定向信源" : "定向排除模板");
//    }


    /**
     * 导入模板
     */
    @PostMapping("/importData")
    public AjaxResult importData(MultipartFile file, String settingType, Long plateId) throws Exception {
        ExcelUtil<SourceSetting> util = new ExcelUtil<>(SourceSetting.class);
        List<SourceSetting> userList = util.importExcel(file.getInputStream());
        LoginUser loginUser = tokenService.getLoginUser(ServletUtils.getRequest());
        String operName = loginUser.getUsername();
        String message = sourceSettingService.importSource(userList, settingType, operName, plateId);
        return AjaxResult.success(message);
    }

    /**
     * 导出模板
     */
    @GetMapping("/importTemplate")
    public void downloadTemplate(HttpServletResponse response, SourceSetting sourceSetting) {
        InputStream inputStream = null;
        ServletOutputStream servletOutputStream = null;
        try {
            String filename = UUID.randomUUID() + "_" + ("1".equals(sourceSetting.getSettingType()) ? "定向信源" : "定向排除模板") + ".xlsx";
            String path = "templates/source_template.xlsx";
            org.springframework.core.io.Resource resource = resourceLoader.getResource("classpath:" + path);
            response.setContentType("application/vnd.ms-excel");
            response.addHeader("Cache-Control", "no-cache, no-store, must-revalidate");
            response.addHeader("charset", "utf-8");
            response.addHeader("Pragma", "no-cache");
            String encodeName = URLEncoder.encode(filename, StandardCharsets.UTF_8.toString());
            response.setHeader("Content-Disposition", "attachment; filename=\"" + encodeName + "\"; filename*=utf-8''" + encodeName);
            inputStream = resource.getInputStream();
            servletOutputStream = response.getOutputStream();
            IOUtils.copy(inputStream, servletOutputStream);
            response.flushBuffer();
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            try {
                if (servletOutputStream != null) {
                    servletOutputStream.close();
                }
                if (inputStream != null) {
                    inputStream.close();
                }
            } catch (Exception e) {
                e.printStackTrace();
            }
        }

    }

    /**
     * 覆盖板块信源设置
     */
    @PostMapping("/overwriteSetting")
    public AjaxResult overwriteSetting(@RequestBody SourceSetting sourceSetting) {
        Long id = sourceSetting.getId();
        Long plateId = sourceSetting.getPlateId();
        try {
            //先删除当前方案，再把引用的方案插入到当前方案中
            sourceSettingService.deleteSourceSetting(id, sourceSetting.getSettingType(), null);
            List<SourceSetting> sourceSettingList = sourceSettingService.getSourceSetting(null, null, plateId, null, sourceSetting.getSettingType(), SecurityUtils.getUserId(), null);
            if (!sourceSettingList.isEmpty()) {
                Date date = new Date();
                for (int i = 0; i < sourceSettingList.size(); i++) {
                    SourceSetting source = new SourceSetting();
                    source.setSettingType(sourceSetting.getSettingType());
                    source.setCreateTime(date);
                    source.setUpdateTime(date);
                    source.setState(1);
                    source.setUserId(SecurityUtils.getUserId());
                    source.setId(IdUtil.getSnowflakeNextId());
                    source.setPlateId(id);
                    source.setSourceType(sourceSettingList.get(i).getSourceType());
                    source.setSettingHost(sourceSettingList.get(i).getSettingHost());
                    source.setName(sourceSettingList.get(i).getName());
                    sourceSettingService.addSourceSetting(source);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.success();
    }


    /**
     * 叠加板块信源设置
     */
    @PostMapping("/addSetting")
    public AjaxResult addSetting(@RequestBody SourceSetting sourceSetting) {
        Long id = sourceSetting.getId();
        Long plateId = sourceSetting.getPlateId();
        try {
            List<SourceSetting> thisSourceSettingList = sourceSettingService.getSourceSetting(null, null, id, null, sourceSetting.getSettingType(), SecurityUtils.getUserId(), null);
            List<SourceSetting> thatSourceSettingList = sourceSettingService.getSourceSetting(null, null, plateId, null, sourceSetting.getSettingType(), SecurityUtils.getUserId(), null);
            thisSourceSettingList.addAll(thatSourceSettingList);
            Date date = new Date();
            for (SourceSetting setting : thisSourceSettingList) {
                setting.setPlateId(id);
                setting.setCreateTime(date);
                setting.setUpdateTime(date);
            }
            // 去重
            List<SourceSetting> newSourceSettingList = new ArrayList<>();
            thisSourceSettingList.forEach(p -> {
                if (!newSourceSettingList.contains(p)) {
                    newSourceSettingList.add(p);
                }
            });
            if (!newSourceSettingList.isEmpty()) {
                sourceSettingService.deleteSourceSetting(id, sourceSetting.getSettingType(), null);
                for (SourceSetting setting : newSourceSettingList) {
                    setting.setId(IdUtil.getSnowflakeNextId());
                    sourceSettingService.addSourceSetting(setting);
                }
            }
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.success();
    }

    @PostMapping("/outSetting")
    public AjaxResult outSetting(@RequestBody JSONObject object) {
        String encryKey = object.getStr("key");
        if (!object.containsKey("key") || StrUtil.isEmpty(encryKey) || encryKey.length() != 20 || encryKey.charAt(1) != 'x' || encryKey.charAt(5) != 'Y' || encryKey.charAt(13) != 'z' || encryKey.charAt(7) != '1' || encryKey.charAt(11) != '6' || encryKey.charAt(17) != '9') {
            return AjaxResult.error();
        }
        PageResult<AccountInfoVO> accountInfoVOS = elasticsearchService.searchSourceByType(object);
        return AjaxResult.success(String.valueOf(accountInfoVOS.getTotal()), accountInfoVOS);
    }

    @PostMapping("/outGetNameByHosts")
    public AjaxResult outGetNameByHosts(@RequestBody JSONObject object) {
        String encryKey = object.getStr("key");
        if (!object.containsKey("key") || StrUtil.isEmpty(encryKey) || encryKey.length() != 20 || encryKey.charAt(1) != 'x' || encryKey.charAt(5) != 'Y' || encryKey.charAt(13) != 'z' || encryKey.charAt(7) != '1' || encryKey.charAt(11) != '6' || encryKey.charAt(17) != '9') {
            return AjaxResult.error();
        }
        List<String> hostList = object.getJSONArray("hostList").toList(String.class);
        if (object.containsKey("type")) {
            Integer type = object.getInt("type");
            return AjaxResult.success(elasticsearchService.getNameByHostsFromEs(hostList, type));
        }
        return AjaxResult.success(elasticsearchService.getNameByHosts(hostList));
    }

    @PostMapping("/outGetNameByHostsMap")
    public AjaxResult outGetNameByHostsMap(@RequestBody JSONObject object) {
        String encryKey = object.getStr("key");
        if (!object.containsKey("key") || StrUtil.isEmpty(encryKey) || encryKey.length() != 20 || encryKey.charAt(1) != 'x' || encryKey.charAt(5) != 'Y' || encryKey.charAt(13) != 'z' || encryKey.charAt(7) != '1' || encryKey.charAt(11) != '6' || encryKey.charAt(17) != '9') {
            return AjaxResult.error();
        }
        Map<Integer, List<String>> map = JSONUtil.toBean(object.getJSONObject("map"), HashMap.class);
        return AjaxResult.success(elasticsearchService.getNameByHosts(map));
    }

    @PostMapping("/outGetAllAccountByType")
    public AjaxResult outGetAllAccountByType(@RequestBody JSONObject object) {
        String encryKey = object.getStr("key");
        if (!object.containsKey("key") || StrUtil.isEmpty(encryKey) || encryKey.length() != 20 || encryKey.charAt(1) != 'x' || encryKey.charAt(5) != 'Y' || encryKey.charAt(13) != 'z' || encryKey.charAt(7) != '1' || encryKey.charAt(11) != '6' || encryKey.charAt(17) != '9') {
            return AjaxResult.error();
        }
        AccountInfo accountInfo = object.getBean("accountInfo", AccountInfo.class);
        Integer type = null;
        if (object.containsKey("type")) {
            type = object.getInt("type");
        }

        return AjaxResult.success(elasticsearchService.getAllAccountByType(type, accountInfo));
    }

}

