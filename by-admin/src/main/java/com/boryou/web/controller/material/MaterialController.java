package com.boryou.web.controller.material;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.collection.entity.Collection;
import com.boryou.web.module.material.entity.Material;
import com.boryou.web.module.material.entity.MaterialFolder;
import com.boryou.web.module.material.vo.MaterialQuery;
import com.boryou.web.module.material.service.MaterialFolderService;
import com.boryou.web.module.material.service.MaterialService;
import com.boryou.web.util.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:45
 */
@RestController
@AllArgsConstructor
@RequestMapping("/material")
public class MaterialController extends BaseController {

    private final MaterialService materialService;
    private final MaterialFolderService folderService;

    @PostMapping("/add")
    public AjaxResult add(@RequestBody Material material) {
        return AjaxResult.optionResult(materialService.add(material));
    }

    @PostMapping("/edit")
    public AjaxResult edit(@RequestBody Material material) {
        return AjaxResult.optionResult(materialService.edit(material));
    }

    @PostMapping("/addBatch")
    public AjaxResult addBatch(@RequestBody List<Material> list) {
        return AjaxResult.optionResult(materialService.addBatch(list));
    }

    @PostMapping("/list")
    public TableDataInfo materialList(@RequestBody MaterialQuery query) {
        PageUtil.startPage(query.getPageNum(), query.getPageSize());
        return getDataTable(materialService.materialList(query));
    }

    @DeleteMapping("/deleteByContentIds/{ids}")
    public AjaxResult delete(@PathVariable Long[] ids) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        return AjaxResult.optionResult(materialService.remove(Wrappers.<Material>lambdaQuery()
                .eq(Material::getUserId, userId)
                .in(Material::getContentId, Arrays.asList(ids))
        ));
    }

    @DeleteMapping("/delete/{ids}")
    public AjaxResult deleteByIds(@PathVariable Long[] ids) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        return AjaxResult.optionResult(materialService.remove(Wrappers.<Material>lambdaQuery()
                .eq(Material::getUserId, userId)
                .in(Material::getId, Arrays.asList(ids))
        ));
    }



    @PostMapping("/folder/save")
    public AjaxResult folderSave(@RequestBody MaterialFolder folder) {
        return AjaxResult.optionResult(folderService.folderSave(folder));
    }

    @DeleteMapping("/folder/delete/{id}")
    public AjaxResult folderDelete(@PathVariable Long id) {
        return AjaxResult.optionResult(folderService.folderDelete(id));
    }

    @GetMapping("/folder/list")
    public AjaxResult folderList(String name) {
        return AjaxResult.success(folderService.folderList(name));
    }

}
