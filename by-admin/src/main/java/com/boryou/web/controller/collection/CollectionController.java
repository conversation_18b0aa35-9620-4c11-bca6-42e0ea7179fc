package com.boryou.web.controller.collection;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.collection.entity.Collection;
import com.boryou.web.module.collection.entity.CollectionFolder;
import com.boryou.web.module.collection.service.CollectionFolderService;
import com.boryou.web.module.collection.service.CollectionService;
import com.boryou.web.module.collection.vo.CollectionQuery;
import com.boryou.web.util.PageUtil;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:45
 */
@RestController
@AllArgsConstructor
@RequestMapping("/collection")
public class CollectionController extends BaseController {

    private final CollectionService collectionService;
    private final CollectionFolderService folderService;

    @PostMapping("/add")
    public AjaxResult planList(@RequestBody Collection collection) {
        return AjaxResult.optionResult(collectionService.add(collection));
    }

    @PostMapping("/list")
    public TableDataInfo collectionList(@RequestBody CollectionQuery query) {
        PageUtil.startPage(query.getPageNum(), query.getPageSize());
        return getDataTable(collectionService.collectionList(query));
    }

    @DeleteMapping("/deleteByContentIds/{ids}")
    public AjaxResult delete(@PathVariable Long[] ids) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        return AjaxResult.optionResult(collectionService.remove(Wrappers.<Collection>lambdaQuery()
                .eq(Collection::getUserId, userId)
                .in(Collection::getContentId, Arrays.asList(ids))
        ));
    }

    @DeleteMapping("/delete/{ids}")
    public AjaxResult deleteByIds(@PathVariable Long[] ids) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        return AjaxResult.optionResult(collectionService.remove(Wrappers.<Collection>lambdaQuery()
                .eq(Collection::getUserId, userId)
                .in(Collection::getId, Arrays.asList(ids))
        ));
    }

    @PostMapping("/folder/save")
    public AjaxResult folderSave(@RequestBody CollectionFolder folder) {
        return AjaxResult.optionResult(folderService.folderSave(folder));
    }

    @DeleteMapping("/folder/delete/{id}")
    public AjaxResult folderDelete(@PathVariable Long id) {
        return AjaxResult.optionResult(folderService.folderDelete(id));
    }

    @GetMapping("/folder/list")
    public AjaxResult folderList(String name) {
        return AjaxResult.success(folderService.folderList(name));
    }

}
