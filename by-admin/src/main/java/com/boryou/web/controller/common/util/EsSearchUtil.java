package com.boryou.web.controller.common.util;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.Page;
import cn.hutool.db.PageResult;
import cn.hutool.http.HttpRequest;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.boryou.common.utils.StringUtils;
import com.boryou.web.controller.bigscreen.vo.Plate;
import com.boryou.web.controller.bigscreen.vo.StatisticsBeanBO;
import com.boryou.web.controller.common.constant.BaseConstant;
import com.boryou.web.controller.common.constant.ProjectFlagConstant;
import com.boryou.web.controller.common.entity.BoryouBean;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.entity.bo.EsSearchTimeBO;
import com.boryou.web.controller.common.entity.vo.BoryouBeanVO;
import com.boryou.web.controller.common.entity.vo.TimeFlowVO;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.service.impl.SearchServiceImpl;
import com.boryou.web.util.BoryouBeanUtil;
import com.boryou.web.util.HighlightUtil;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.*;


/**
 * <AUTHOR>
 * @description Es检索工具类
 * @date 2024/4/26 9:09
 */

public class EsSearchUtil extends BaseConstant {
    public static final String BASE_URL = PT_SERVER_DPLATFORM_IP;    // 用父类统一的IP地址，不然会有两个地址歧义

    public static final String SEARCH_PATH = BASE_URL + "/common/info";
    public static final String ANALYSE_PATH = BASE_URL + "/analyse";
    public static final String YQ_PATH = BASE_URL + "/yq/info";
    // 情感分析
    public static final String EMOTION_ANALYSE = ANALYSE_PATH + "/emotionAnalyse";
    public static final String EMOTION_ANALYSE_FOR_ORIGINAL = ANALYSE_PATH + "/emotionAnalysisForOriginal";
    // 意见领袖
    public static final String OPINION_LEADER = ANALYSE_PATH + "/opinionLeader";
    // 媒体类型分布
    public static final String MEDIA_DISTRIBUTE = ANALYSE_PATH + "/mediaTypeMap";
    public static final String MEDIA_DISTRIBUTE_FOR_ORIGINAL = ANALYSE_PATH + "/mediaTypeMapForOriginal";
    // 活跃媒体统计
    public static final String MEDIA_ACTIVE = ANALYSE_PATH + "/mediaActiveMap";
    // 地域分布统计
    public static final String AREA_MAP = ANALYSE_PATH + "/areaMap";
    public static final String TIME = ANALYSE_PATH + "/time";
    //网民观点
    public static final String NETIZENOPINION = ANALYSE_PATH + "/netizenOpinion";
    // 根据id查询并标红
    public static final String SEARCH_AND_HIGHLIGHT_BY_ID = SEARCH_PATH + "/searchAndHighlightById";
    // 获取微博相似数据查询
    public static final String YQ_WEIBO_SIMILAR_DATA = YQ_PATH + "/getWeiboSimilarData";
    // 通用查询
    public static final String SEARCH = SEARCH_PATH + "/search";
    public static final String SEARCHX = SEARCH_PATH + "/searchx";
    // 通用查询数据条数
    public static final String MEDIA_TYPE_COUNT_FOR_ORIGINAL = SEARCH_PATH + "/mediaTypeCountForOriginal";
    public static final String INFO_COUNT = SEARCH_PATH + "/getInfoCount";
    public static final String SIMILAR_COUNT = SEARCH_PATH + "/getSimilarCount";
    public static final String REAL_TIME_INFO_COUNT = SEARCH_PATH + "/realTimeInfoCount";
    // 查询微博，微信，网站的时间内每天曲线图
    public static final String TYPE_CURVE = SEARCH_PATH + "/getTypeCurve";
    // 查询所有的时间内总条数每天曲线图
    public static final String TOTAL_CURVE = SEARCH_PATH + "/getTotalCurve";
    // 查询抖音时间内曲线图
    public static final String DOUYIN_CURVE = SEARCH_PATH + "/getDouyinCurve";
    // 相似信息*
    public static final String SIMILAR_DATA_BY_ID = YQ_PATH + "/getSimilarDataById";
    public static final String SIMILAR_DATA_BY_MD5 = YQ_PATH + "/getSimilarDataByMd5";
    //    public static final String SEARCH_BY_ID = SEARCH_PATH + "/searchById";
    public static final String SEARCH_BY_ID_TIME = SEARCH_PATH + "/searchByIdTime";
    // author统计量
    public static final String TYPE_TOTAL_COUNT = SEARCH_PATH + "/getTypeTotalCount";
    // 浙江舆情
    public static final String ZHEJIANG_YUQING_AREA_OVERVIEW = BASE_URL + "/zhejiangyuqing/areaOverview";
    public static final String MEDIA_RANK_STATIS = BASE_URL + "/zhejiangyuqing/mediaRank";
    public static final String PLAN_SAVE_HISTORY = BASE_URL + "/zhejiangyuqing/saveHistoryPlan";
    public static final String SEARCH_SIMILARITY = BASE_URL + "/zhejiangyuqing/search/similarity";
    public static final String FIRST_RELEASE = BASE_URL + "/zhejiangyuqing/firstRelease";
    public static final String EVENT_CONTEXT = BASE_URL + "/zhejiangyuqing/eventContext";
    public static final String RELATED_HOT_ARTICLE = BASE_URL + "/zhejiangyuqing/relatedHotArticle";
    //博约舆情
    public static final String SUBJECT_PATH = BASE_URL + "/subjectAnalyseEChart/analyze";

    /**
     * 一周时间趋势
     */
    public static final String WEEK_TIME_TREND = SUBJECT_PATH + "/getTimeTrendMap24H";

    public static final String WARN_GET = BASE_URL + "/api/warn/get";
    public static final String WARN_ADD = BASE_URL + "/api/warn/add";
    public static final String WARN_PUSH = BASE_URL + "/api/warn/push";
    public static final String WARN_CHECK = BASE_URL + "/api/warn/check";
    public static final String WARN_SIMILAR = BASE_URL + "/api/warn/similar";

    private static final Logger log = LoggerFactory.getLogger(EsSearchUtil.class);

    public static HttpRequest createPostRequest(String url) {
        HttpRequest httpRequest = HttpUtil.createPost(url);
        httpRequest.header("contentType", "application/json");
        return httpRequest;
    }


    /**
     * 情感分析(根据情感值)
     *
     * @param bo 检索BO
     * @return 返回值
     */
    public static Map<String, Integer> emotionAnalyse(EsSearchBO bo) {
        return getStringIntegerMap(bo, EMOTION_ANALYSE);

    }

    private static Map<String, Integer> getStringIntegerMap(EsSearchBO bo, String path) {
        HttpRequest postRequest = createPostRequest(path);
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        postRequest.body(JSONUtil.toJsonStr(BeanUtil.beanToMap(bo)));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        return JSONUtil.toBean(body, Map.class);
    }

    /**
     * 情感分析(根据情感值)
     *
     * @param bo 检索BO
     * @return 返回值
     */
    public static Map<String, Integer> emotionAnalysisForOriginal(EsSearchBO bo) {
        return getStringIntegerMap(bo, EMOTION_ANALYSE_FOR_ORIGINAL);
    }

    /**
     * 媒体类型分布
     *
     * @param bo 检索BO
     * @return 返回值
     */
    public static Map<String, Integer> mediaTypeMapForOriginal(EsSearchBO bo) {
        return getStringIntegerMap(bo, MEDIA_DISTRIBUTE_FOR_ORIGINAL);
    }

    /**
     * 媒体类型数据统计
     *
     * @param bo 检索BO
     * @return 返回值
     */
    public static Map<String, Integer> mediaTypeCountForOriginal(EsSearchBO bo) {
        return getStringIntegerMap(bo, MEDIA_TYPE_COUNT_FOR_ORIGINAL);
    }

    /**
     * 媒体活跃统计
     *
     * @param bo 检索BO
     * @return 返回值
     */
    public static Map<String, Integer> mediaActiveMap(EsSearchBO bo) {
        return getStringIntegerMap(bo, MEDIA_ACTIVE);
    }

    /**
     * 媒体活跃统计
     *
     * @param bo 检索BO
     * @return 返回值
     */
    public static Map<String, Integer> areaMap(EsSearchBO bo) {
        return getStringIntegerMap(bo, AREA_MAP);
    }


    /**
     * 媒体类型分布
     *
     * @param bo
     * @return
     */
    public static Map<String, Integer> getMediaTypeMap(EsSearchBO bo) {
        HttpRequest postRequest = createPostRequest(MEDIA_DISTRIBUTE);
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        postRequest.body(JSONUtil.toJsonStr(bo));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        return JSONUtil.toBean(body, Map.class);
    }

    public static PageResult<EsBean> searchEsBeanList(EsSearchBO bo) {
        PageResult<EsBean> esBeanList = new PageResult<>();
        JSONObject resultJson = getPostBodyJSONObject(bo, SEARCH);
        if (resultJson.containsKey("records")) {
            List<EsBean> list = new ArrayList<>(JSONUtil.toList(resultJson.getJSONArray("records"), EsBean.class));
            if (CollUtil.isNotEmpty(list)) {
                String keyWord = SearchServiceImpl.getSearchKeyWord(bo);
                for (EsBean esBean : list) {
                    String title = esBean.getTitle();
                    String text = esBean.getText();
                    String title1 = HighlightUtil.highlighter(title, keyWord, true);
                    String text2 = HighlightUtil.highlighter(text, keyWord, true);
                    Set<String> hitWordsSet = HighlightUtil.extractEmTagContents(title1 + text2);
                    List<String> hitWords = new ArrayList<>();
                    for (String hitWord : hitWordsSet) {
                        hitWords.add(hitWord.replace("<em>", ""));
                    }
                    if (CollUtil.isNotEmpty(hitWords)) {
                        esBean.setHitWords(CollUtil.join(hitWords, " "));
                    }
                }
                esBeanList.addAll(list);
                esBeanList.setTotal(resultJson.getInt("total"));
            }
        }
        return esBeanList;
    }

    public static PageResult<BoryouBean> search(EsSearchBO bo, String path) {
        PageResult<BoryouBean> boryouBeanList = new PageResult<>();
        JSONObject resultJson = getPostBodyJSONObject(bo, path);
        if (resultJson.containsKey("records")) {
            List<EsBean> esBeanList = new ArrayList<>(JSONUtil.toList(resultJson.getJSONArray("records"), EsBean.class));
            boryouBeanList.setTotal(resultJson.getInt("total"));
            for (EsBean bean : esBeanList) {
                boryouBeanList.add(ConvertHandler.esBeanList2SolrBeanIdList(bean));
            }
        }
        return boryouBeanList;
    }

    public static Integer getSimilarCount(SearchVO searchVO) {
        EsSearchBO bo = new EsSearchBO();
        if (StringUtils.isEmpty(searchVO.getMd5())) {
            return 1;
        }
        bo.setMd5(searchVO.getMd5());
        bo.setStartTime(searchVO.getStartTime());
        bo.setEndTime(searchVO.getEndTime());
        return getIntegerFromResult(bo, SIMILAR_COUNT);
    }

    public static Integer realTimeInfoCount(EsSearchBO bo) {
        return getIntegerFromResult(bo, REAL_TIME_INFO_COUNT);
    }

    private static Integer getIntegerFromResult(EsSearchBO bo, String path) {
        HttpRequest postRequest = createPostRequest(path);
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        postRequest.body(JSONUtil.toJsonStr(bo));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        if (StrUtil.isNumeric(body)) {
            return Integer.valueOf(body);
        }
        return 0;
    }

    public static JSONObject getPostBodyJSONObject(EsSearchBO bo, String path) {
        HttpRequest postRequest = createPostRequest(path);
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        postRequest.body(JSONUtil.toJsonStr(bo));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        return JSONUtil.parseObj(body);
    }

    public static String getPostBodyStr(EsSearchBO bo, String path) {
        HttpRequest postRequest = createPostRequest(path);
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        postRequest.body(JSONUtil.toJsonStr(bo));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        return body;
    }

    /**
     * 查询信息数量
     *
     * @param bo
     * @return
     */
    public static Long getInfoCount(EsSearchBO bo) {
        HttpRequest postRequest = createPostRequest(EsSearchUtil.INFO_COUNT);
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        postRequest.body(JSON.toJSONString(bo));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        return CharSequenceUtil.isNotEmpty(body) ? Long.parseLong(body) : 0L;
    }

    /**
     * 根据id获取相似信息*
     *
     * @param bo
     * @return
     */
    public static List<EsBean> getSimilarDataByMD5(EsSearchBO bo) {
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        String post = HttpUtil.post(SIMILAR_DATA_BY_MD5, JSONUtil.toJsonStr(bo));
        if (CharSequenceUtil.isEmpty(post)) {
            return Collections.emptyList();
        }
        JSONObject resultJson = JSONUtil.parseObj(post);
        JSONArray records = resultJson.getJSONArray("records");
        return JSONUtil.toList(records, EsBean.class);
    }

    /**
     * 网民观点*
     *
     * @param bo
     * @return
     */
    public static List<EsBean> netizenOpinion(EsSearchBO bo) {
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        HttpRequest postRequest = createPostRequest(NETIZENOPINION);
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        postRequest.body(JSONUtil.toJsonStr(bo));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        if (body.startsWith("[")) {
            return JSONUtil.toList(body, EsBean.class);
        }
        return new ArrayList<>();
    }

 /*   public static BoryouBeanVO searchById(EsSearchBO bo) {
        return getBoryouBean(bo, SEARCH_BY_ID);
    }*/

    public static EsBean searchByIdTimex(EsSearchBO bo) {
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        try {
            String post = HttpUtil.post(SEARCH_BY_ID_TIME, JSONUtil.toJsonStr(bo));
            return JSONUtil.toBean(post, EsBean.class);
        } catch (Exception e) {
            e.printStackTrace();
            log.warn("searchByIdTime查询有问题: {}", JSONUtil.toJsonStr(bo));
            return null;
        }
    }

    public static BoryouBeanVO searchByIdTime(EsSearchBO bo) {
        EsBean esBean = searchByIdTimex(bo);
        return esBean != null ? esBean.convertEsBeanToBoryouBean() : null;
    }

    private static BoryouBeanVO getBoryouBean(EsSearchBO bo, String path) {
        HttpRequest postRequest = createPostRequest(path);
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        postRequest.body(JSON.toJSONString(bo));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        EsBean esBean = JSONUtil.toBean(body, EsBean.class, true);
        return esBean.convertEsBeanToBoryouBean();
    }

    public static List<TimeFlowVO> time(EsSearchTimeBO esSearchTimeBO) {
        esSearchTimeBO.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        try {
            String post = HttpUtil.post(TIME, JSONUtil.toJsonStr(esSearchTimeBO), 120000);
            return JSONUtil.toList(post, TimeFlowVO.class);
        } catch (Exception e) {
            log.warn("timeES查询有问题:{}", JSONUtil.toJsonStr(esSearchTimeBO));
            return Collections.emptyList();
        }
    }

    public static List<EsBean> firstRelease(EsSearchBO bo) {
        JSONObject resultJson = getPostBodyJSONObject(bo, FIRST_RELEASE);
        if (resultJson.containsKey("data")) {
            List<EsBean> esBeanList = new ArrayList<>(JSONUtil.toList(resultJson.getJSONArray("data"), EsBean.class));
            esBeanList.forEach(x -> x.setTypeName(MediaTypeEnum.getDesc(String.valueOf(x.getType()))));
            return esBeanList;
        }
        return new ArrayList<>();
    }

    public static JSONObject eventContext(EsSearchBO bo) {
        JSONObject resultJson = getPostBodyJSONObject(bo, EVENT_CONTEXT);
        if (resultJson.containsKey("data")) {
            // return resultJson.getJSONObject("data").getBeanList("records", EsBean.class);
            return resultJson.getJSONObject("data");
        }
        return null;
    }

    public static List<EsBean> relatedHotArticle(EsSearchBO bo) {
        JSONObject resultJson = getPostBodyJSONObject(bo, RELATED_HOT_ARTICLE);
        if (resultJson.containsKey("data")) {
            List<EsBean> esBeanList = new ArrayList<>(JSONUtil.toList(resultJson.getJSONArray("data"), EsBean.class));
            esBeanList.forEach(x -> {
                x.setTypeName(MediaTypeEnum.getDesc(String.valueOf(x.getType())));
                Date publishTime = x.getPublishTime();
                Date updateTime = x.getUpdateTime();
                if (publishTime != null) {
                    publishTime = DateUtil.offsetHour(publishTime, 8);
                }
                if (updateTime != null) {
                    updateTime = DateUtil.offsetHour(updateTime, 8);
                }
                x.setPublishTime(publishTime);
                x.setUpdateTime(updateTime);
            });
            return esBeanList;
        }
        return new ArrayList<>();
    }





    /**
     * 对象转化为EsSearchBO对象
     *
     * @param plate 原统计对象
     * @return 返回值
     */
    public static EsSearchBO convertPlateBeanToEsSearchBO(com.boryou.web.controller.bigscreen.vo.Plate plate) {
        EsSearchBO bo = new EsSearchBO();
        bo.setStartTime(plate.getStartTime());
        bo.setEndTime(plate.getEndTime());
        if (bo.getStartTime() == null) {
            bo.setStartTime(DateUtil.format(DateUtil.offsetDay(DateUtil.date(), -1), DatePattern.NORM_DATETIME_PATTERN));
        }
        if (bo.getEndTime() == null) {
            bo.setEndTime(DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN));
        }

        if (StrUtil.isNotEmpty(plate.getMediaType())) {
            bo.setType(plate.getMediaType().replaceAll(" ", ","));
        }
        bo.setKeyWord1(plate.getRegexArea());
        bo.setKeyWord2(plate.getRegexSubject());
        bo.setKeyWord3(plate.getRegexEvent());
        bo.setSearchPosition(StrUtil.isNotEmpty(plate.getRegexRegion()) ? plate.getRegexRegion().replace("title", "1")
                .replace("text", "2")
                .replace("author", "3")
                .replace("all", "0") : "0");
        //是否垃圾 0 全部   1 去除垃圾
        if (plate.getTrash() == 1) {
            bo.setIsSpam(false);
        }
        bo.setEmotionFlag(plate.getZfflag());
        bo.setConfigSelect(plate.getConfigSelect());
        bo.setExcludeId(plate.getFilterBeanIds());
        bo.setHost(plate.getNeedHost());
        bo.setExcludeHost(plate.getFilterHost());
        // 注意不要被全局过滤词覆盖
        bo.setExcludeWord(plate.getRegexNo());
        bo.setQuadraticWord(plate.getSecondWord());
        bo.setSourceSetting(plate.getSourceSetting());
        bo.setSourceMap(plate.getSourceMap());
        bo.setContentAreaCode(plate.getAreaCode());
        bo.setAccountLevel(plate.getWeiBoLevel());
        bo.setProWord(plate.getMonitorWords());
        bo.setIsOriginal(plate.getQuchong() == 1);
        bo.setVideoHost(plate.getVideoTypeHost());
        bo.setSourceMap(plate.getSourceMap());
        return bo;
    }

    /**
     * 媒体类型分布统计
     *
     * @param bo
     * @return
     */
    public static Map<String, Integer> mediaTypeMap(EsSearchBO bo) {
        HttpRequest postRequest = createPostRequest(MEDIA_DISTRIBUTE);
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        postRequest.body(JSON.toJSONString(bo));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        return JSONUtil.toBean(body, Map.class);

    }

    /**
     * 一天媒体走势
     *
     * @param bo 检索BO
     * @return 返回值
     */
    public static Map<String, Object> dayTimeTrend(StatisticsBeanBO bo) {
        HttpRequest postRequest = createPostRequest(WEEK_TIME_TREND);
        bo.setProjectType(ProjectFlagConstant.PROJECT_TYPE_ZJGY);
        postRequest.body(JSON.toJSONString(bo));
        HttpResponse execute = postRequest.execute();
        String body = execute.body();
        com.alibaba.fastjson.JSONObject jsonObject = com.alibaba.fastjson.JSONObject.parseObject(body);
        Map<String, Object> res = new HashMap<>();
        List<String> timeAxis = new LinkedList<>();
        for (int i = 0; i < 24; i++) {
            DateTime time = DateUtil.offsetHour(DateUtil.date(), i - 24);
            String format = DateUtil.format(time, "yyyy-MM-dd HH");
            timeAxis.add(format + "H");
        }
        res.put("data", jsonObject);
        res.put("timeAxis", timeAxis);
        return res;
    }

    /**
     * @param
     * @return
     * @description 通过版块规则+地域名称限制标题地域，查询版块舆则情信息
     * @auther xlf
     * @date 2021/12/27
     */
    public static List<BoryouBean> searchByPlate(Plate plate, String areaName, String startTime, String endTime, Page page) {
//        return BoryouBeanUtil.removeImgTag(searchByPlate(plate, areaName, startTime, endTime, page, plate.getRegexRegion(), returnFields));
        return BoryouBeanUtil.removeImgTag(searchByPlate(plate, startTime, endTime, page, plate.getRegexRegion()));
    }


    /**
     * 通过版块规则，查询版块舆情信息
     *
     * @param plate        版块规则
     * @param startTime    开始时间
     * @param endTime      结束时间
     * @param page         分页对象
     * @param returnFields 返回字段
     * @return List<BoryouBean>
     * <AUTHOR>
     * @date 2017-4-13 下午4:23:34
     */
    private static PageResult searchByPlate(Plate plate, String startTime, String endTime, Page page, String hgField) {
        EsSearchBO bo = EsSearchUtil.convertPlateBeanToEsSearchBO(plate);
//        if (plate.getOrderType() == 1 || plate.getOrderType() == 2 || plate.getOrderType() == 3) {
//            bo.setSortType(String.valueOf(plate.getOrderType()));
//        }
        bo.setSortType(3);
        bo.setStartTime(startTime);
        bo.setEndTime(endTime);
        bo.setPageSize(page.getPageSize());
        bo.setPageNum(page.getPageNumber());
        PageResult<BoryouBean> beanPageResult = EsSearchUtil.search(bo,SEARCH);
//        for (BoryouBean boryouBean : beanPageResult) {
//            boryouBean.setHostName(SysSite.getName(boryouBean.getHost()));
//        }
//        page.setTotalItem(beanPageResult.getTotal());
        beanPageResult.setTotal(beanPageResult.getTotal());
        return beanPageResult;

    }
}
