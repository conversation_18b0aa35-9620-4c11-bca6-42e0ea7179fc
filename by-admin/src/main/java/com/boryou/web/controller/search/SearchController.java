package com.boryou.web.controller.search;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONObject;
import com.baomidou.mybatisplus.extension.api.R;
import com.boryou.common.annotation.Log;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysDictData;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.poi.ExcelUtil;
import com.boryou.system.service.ISysDictDataService;
import com.boryou.web.constant.BC;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.entity.vo.EsBeanExportVO;
import com.boryou.web.controller.common.enums.EmotionEnum;
import com.boryou.web.controller.common.util.ConvertHandler;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.HomeDownVO;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.module.home.service.HomeStatisService;
import com.boryou.web.service.PlanService;
import com.boryou.web.service.SearchService;
import com.boryou.web.util.UrlAccessUtil;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.*;
import java.util.stream.Collectors;

/**
 * 数据列表检索controller
 *
 * <AUTHOR>
 */
@RestController()
public class SearchController {

    @Resource
    private SearchService searchService;
    @Resource
    private PlanService planService;
    @Resource
    ConvertHandler convertHandler;
    @Resource
    private HomeStatisService homeStatisService;
    @Resource
    private ISysDictDataService dictDataService;

    @Resource(name = "threadPoolTaskExecutor")
    ThreadPoolTaskExecutor threadPoolTaskExecutor;

    @Log(title = "方案舆情数据查询", businessType = BusinessType.QUERY)
    @PostMapping("/search/plan")
    public TableDataInfo searchByPlate(@RequestBody SearchVO searchVO) {
        PageResult<EsBean> pageResult = new PageResult<>();
        Plan plan = planService.selectPlanById(searchVO.getPlanId());
        if (plan != null) {
            EsSearchBO bo = convertHandler.copyPropertiesFromPlan(searchVO, plan);
            pageResult = searchService.search(bo);
        }
        TableDataInfo rspData = new TableDataInfo(pageResult, pageResult.getTotal());
        rspData.setCode(200);
        return rspData;
    }

    @Log(title = "全文搜索查询", businessType = BusinessType.QUERY)
    @PostMapping("/search/search")
    public TableDataInfo search(@RequestBody SearchVO searchVO) {
        searchVO.setSearchMode(0);
        convertHandler.setAllVideoHost(searchVO);
        EsSearchBO bo = searchVO.convertEsSearchBO();
        PageResult<EsBean> pageResult = searchService.search(bo);
        TableDataInfo rspData = new TableDataInfo(pageResult, pageResult.getTotal());
        rspData.setCode(200);
        return rspData;
    }

    @Log(title = "首页下钻数据查询", businessType = BusinessType.QUERY)
    @PostMapping("/search/home/<USER>")
    public TableDataInfo searchHomeDown(@RequestBody HomeDownVO homeDownVO) {
        Integer useArea = homeDownVO.getUseArea();
        if (useArea == null || useArea == 1) {
            homeDownVO.setContentAreaCode(homeStatisService.getAreaCode());
        }
        EsSearchBO bo = searchService.buildEsSearchBO(homeDownVO);
        PageResult<EsBean> pageResult = searchService.search(bo);
        TableDataInfo rspData = new TableDataInfo(pageResult, pageResult.getTotal());
        rspData.setCode(200);
        return rspData;
    }

    @PostMapping("/search/home/<USER>/count")
    public AjaxResult searchHomeDownCount(@RequestBody HomeDownVO homeDownVO) {
        Integer useArea = homeDownVO.getUseArea();
        if (useArea == null || useArea == 1) {
            homeDownVO.setContentAreaCode(homeStatisService.getAreaCode());
        }
        EsSearchBO bo = searchService.buildEsSearchBO(homeDownVO);
        Integer num = searchService.realTimeInfoCount(bo);
        return AjaxResult.success("", num);
    }

    @PostMapping("/search/home/<USER>/export")
    public AjaxResult searchHomeDownExport(@RequestBody HomeDownVO homeDownVO) {
        Integer useArea = homeDownVO.getUseArea();
        if (useArea == null || useArea == 1) {
            homeDownVO.setContentAreaCode(homeStatisService.getAreaCode());
        }
        EsSearchBO bo = searchService.buildEsSearchBO(homeDownVO);
        PageResult<EsBean> pageResult = searchService.search(bo);
        SysDictData data = new SysDictData();
        data.setDictType("sys_search_account_level");
        List<SysDictData> dictDataList = dictDataService.selectDictDataList(data);
        Map<String, String> dictValueLabelMap = dictDataList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (oldValue, newValue) -> oldValue, HashMap::new));
        List<EsBeanExportVO> esBeanExportVOList = new ArrayList<>();
        for (EsBean bean : pageResult) {
            EsBeanExportVO esBeanExportVO = new EsBeanExportVO();
            BeanUtil.copyProperties(bean, esBeanExportVO, "siteMeta", "emotionFlag", "isOriginal", "isSpam");
            if (CollUtil.isNotEmpty(bean.getSiteMeta())) {
                esBeanExportVO.setSiteMeta(CollUtil.join(bean.getSiteMeta(), ","));
            }
            esBeanExportVO.setEmotionFlag(EmotionEnum.fromValue(bean.getEmotionFlag()).getName());
            esBeanExportVO.setIsOriginal(bean.getIsOriginal() ? "是" : "否");
//            esBeanExportVO.setIsSpam(bean.getIsSpam() ? "是" : "否");
            esBeanExportVO.setPublishTime(DateUtil.format(bean.getPublishTime(), "yyyy-MM-dd HH:mm:ss"));
            if (esBeanExportVO.getSimilarCount() == null) {
                esBeanExportVO.setSimilarCount(1);
            }
            if (StrUtil.isEmpty(esBeanExportVO.getAccountLevel()) || esBeanExportVO.getAccountLevel().equals("-1")) {
                esBeanExportVO.setAccountLevel("\\");
            } else if (esBeanExportVO.getAccountLevel().equals("0") || esBeanExportVO.getAccountLevel().length() > 1) {
                esBeanExportVO.setAccountLevel(dictValueLabelMap.get(esBeanExportVO.getAccountLevel()));
            } else {
                for (Map.Entry<String, String> entry : dictValueLabelMap.entrySet()) {
                    if (entry.getKey().contains(",") && entry.getKey().contains(esBeanExportVO.getAccountLevel())) {
                        String[] split = entry.getKey().split(",");
                        for (String s : split) {
                            if (s.equals(esBeanExportVO.getAccountLevel())) {
                                esBeanExportVO.setAccountLevel(dictValueLabelMap.get(entry.getKey()));
                                break;
                            }
                        }
                    }
                }
            }
            esBeanExportVOList.add(esBeanExportVO);
        }
        ExcelUtil<EsBeanExportVO> util = new ExcelUtil<EsBeanExportVO>(EsBeanExportVO.class);
        return util.exportExcel(esBeanExportVOList, "导出列表");
    }

    @PostMapping("/search/similarCount")
    public AjaxResult similarCount(@RequestBody SearchVO searchVO) {
        Integer num = searchService.similarCount(searchVO);
        return AjaxResult.success("", num);
    }

    @PostMapping("/search/realTimeInfoCount")
    public AjaxResult realTimeInfoCount(@RequestBody SearchVO searchVO) {
        Integer realTimeInfoCount = searchService.getRealTimeInfoCount(searchVO);
        return AjaxResult.success("", realTimeInfoCount);
    }


    @PostMapping("/search/export")
    public AjaxResult export(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        PageResult<EsBean> pageResult = searchService.search(bo);
        List<EsBeanExportVO> esBeanExportVOList = new ArrayList<>();
        SysDictData data = new SysDictData();
        data.setDictType("sys_search_account_level");
        List<SysDictData> dictDataList = dictDataService.selectDictDataList(data);
        Map<String, String> dictValueLabelMap = dictDataList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (oldValue, newValue) -> oldValue, HashMap::new));
        for (EsBean bean : pageResult) {
            EsBeanExportVO esBeanExportVO = new EsBeanExportVO();
            BeanUtil.copyProperties(bean, esBeanExportVO, "siteMeta", "emotionFlag", "isOriginal", "isSpam", "publishTime");
            if (CollUtil.isNotEmpty(bean.getSiteMeta())) {
                esBeanExportVO.setSiteMeta(CollUtil.join(bean.getSiteMeta(), ","));
            }
            esBeanExportVO.setEmotionFlag(EmotionEnum.fromValue(bean.getEmotionFlag()).getName());
            esBeanExportVO.setIsOriginal(bean.getIsOriginal() ? "是" : "否");
//            esBeanExportVO.setIsSpam(bean.getIsSpam() ? "是" : "否");
            if (bean.getPublishTime() != null) {
                esBeanExportVO.setPublishTime(DateUtil.format(bean.getPublishTime(), DatePattern.NORM_DATETIME_PATTERN));
            } else {
                esBeanExportVO.setPublishTime("");
            }
            if (esBeanExportVO.getSimilarCount() == null) {
                esBeanExportVO.setSimilarCount(1);
            }
            if (StrUtil.isEmpty(esBeanExportVO.getAccountLevel()) || esBeanExportVO.getAccountLevel().equals("-1")) {
                esBeanExportVO.setAccountLevel("\\");
            } else if (esBeanExportVO.getAccountLevel().equals("0") || esBeanExportVO.getAccountLevel().length() > 1) {
                esBeanExportVO.setAccountLevel(dictValueLabelMap.get(esBeanExportVO.getAccountLevel()));
            } else {
                for (Map.Entry<String, String> entry : dictValueLabelMap.entrySet()) {
                    if (entry.getKey().contains(",") && entry.getKey().contains(esBeanExportVO.getAccountLevel())) {
                        String[] split = entry.getKey().split(",");
                        for (String s : split) {
                            if (s.equals(esBeanExportVO.getAccountLevel())) {
                                esBeanExportVO.setAccountLevel(dictValueLabelMap.get(entry.getKey()));
                                break;
                            }
                        }
                    }
                }
            }
            esBeanExportVOList.add(esBeanExportVO);
        }
        ExcelUtil<EsBeanExportVO> util = new ExcelUtil<EsBeanExportVO>(EsBeanExportVO.class);
        return util.exportExcel(esBeanExportVOList, "导出列表");
    }

    /**
     * 获取用户搜索条件
     */
    @PostMapping("/search/getSearchCriteria")
    public AjaxResult getSearchCriteria(@RequestBody SearchVO searchVO) {
        if (searchVO.getPlanId() != null) {
            return AjaxResult.success(searchService.getSearchCriteria(searchVO.getPlanId()));
        } else {
            return AjaxResult.error();
        }
    }

    /**
     * 保存用户搜索条件
     */
    @Log(title = "更新方案过滤条件", businessType = BusinessType.INSERT)
    @PostMapping("/search/saveSearchCriteria")
    public AjaxResult saveSearchCriteria(@RequestBody JSONObject jsonObject) {
        if (jsonObject.containsKey("planId")) {
            searchService.saveSearchCriteria(jsonObject);
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/search/getUrlAccessStatus")
    public R<List<Integer>> getUrlAccessStatus(@RequestBody List<String> urlList) {
        List<Integer> accessStatusList = new ArrayList<>();
//        ExecutorService executor = Executors.newFixedThreadPool(10);
        Map<String, Future<Integer>> map = new HashMap<>();
        for (String url : urlList) {
            Future<Integer> future = threadPoolTaskExecutor.submit(() -> UrlAccessUtil.check(BC.CHECK_URL, url));
            map.put(url, future);
        }
        for (String url : urlList) {
            Future<Integer> future = map.get(url);
            try {
                int status = future.get();
                accessStatusList.add(status);
            } catch (InterruptedException | ExecutionException e) {
                e.printStackTrace();
            }
        }
//        executor.shutdown();
        return R.ok(accessStatusList);
    }

    /**
     * 信息来源数量统计
     */
    @PostMapping("/search/mediaTypeCount")
    public AjaxResult mediaTypeCount(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        bo.setAggName("type");
        return AjaxResult.success("", searchService.mediaTypeCount(bo));
    }

    /**
     * 信息来源数量统计
     */
    @PostMapping("/search/accountLevelCount")
    public AjaxResult accountLevelCount(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        bo.setAggName("accountLevel");
        bo.setType("3");
        return AjaxResult.success("", searchService.accountLevelCount(bo));
    }

    /**
     * 信息来源数量统计
     */
    @PostMapping("/search/similarity")
    public TableDataInfo searchSimilarity(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        Long planId = searchVO.getPlanId();
        if ((null == planId || planId == 0) && (StrUtil.isEmpty(searchVO.getStartTime()) || StrUtil.isEmpty(searchVO.getEndTime()))) {
            throw new CustomException("参数异常");
        }
        PageResult<EsBean> data = searchService.searchSimilarity(bo);
        TableDataInfo rspData = new TableDataInfo(data, data.getTotal());
        rspData.setCode(200);
        return rspData;
    }

    @PostMapping("/similarity/export")
    public AjaxResult similarityExport(@RequestBody SearchVO searchVO) {
        EsSearchBO bo = convertHandler.getEsSearchBOForAnalyse(searchVO);
        List<EsBeanExportVO> esBeanExportVOList = new ArrayList<>();
        PageResult<EsBean> data = searchService.search(bo);
        SysDictData dictData = new SysDictData();
        dictData.setDictType("sys_search_account_level");
        List<SysDictData> dictDataList = dictDataService.selectDictDataList(dictData);
        Map<String, String> dictValueLabelMap = dictDataList.stream().collect(Collectors.toMap(SysDictData::getDictValue, SysDictData::getDictLabel, (oldValue, newValue) -> oldValue, HashMap::new));
        for (EsBean bean : data) {
            EsBeanExportVO esBeanExportVO = new EsBeanExportVO();
            BeanUtil.copyProperties(bean, esBeanExportVO, "siteMeta", "emotionFlag", "isOriginal", "isSpam", "publishTime");
            if (CollUtil.isNotEmpty(bean.getSiteMeta())) {
                esBeanExportVO.setSiteMeta(CollUtil.join(bean.getSiteMeta(), ","));
            }
            esBeanExportVO.setEmotionFlag(EmotionEnum.fromValue(bean.getEmotionFlag()).getName());
            esBeanExportVO.setIsOriginal(bean.getIsOriginal() ? "是" : "否");
//            esBeanExportVO.setIsSpam(bean.getIsSpam() ? "是" : "否");
            if (bean.getPublishTime() != null) {
                esBeanExportVO.setPublishTime(DateUtil.format(bean.getPublishTime(), DatePattern.NORM_DATETIME_PATTERN));
            } else {
                esBeanExportVO.setPublishTime("");
            }
            if (esBeanExportVO.getSimilarCount() == null) {
                esBeanExportVO.setSimilarCount(1);
            }
            if (StrUtil.isEmpty(esBeanExportVO.getAccountLevel()) || esBeanExportVO.getAccountLevel().equals("-1")) {
                esBeanExportVO.setAccountLevel("\\");
            } else if (esBeanExportVO.getAccountLevel().equals("0") || esBeanExportVO.getAccountLevel().length() > 1) {
                esBeanExportVO.setAccountLevel(dictValueLabelMap.get(esBeanExportVO.getAccountLevel()));
            } else {
                for (Map.Entry<String, String> entry : dictValueLabelMap.entrySet()) {
                    if (entry.getKey().contains(",") && entry.getKey().contains(esBeanExportVO.getAccountLevel())) {
                        String[] split = entry.getKey().split(",");
                        for (String s : split) {
                            if (s.equals(esBeanExportVO.getAccountLevel())) {
                                esBeanExportVO.setAccountLevel(dictValueLabelMap.get(entry.getKey()));
                                break;
                            }
                        }
                    }
                }
            }
            esBeanExportVOList.add(esBeanExportVO);
        }
        ExcelUtil<EsBeanExportVO> util = new ExcelUtil<EsBeanExportVO>(EsBeanExportVO.class);
        return util.exportExcel(esBeanExportVOList, "导出列表");
    }

}
