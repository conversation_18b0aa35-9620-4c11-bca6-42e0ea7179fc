package com.boryou.web.module.mark.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 排除字段注解
 * 用于标记在字段比较时需要排除的字段
 * 
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface ExcludeField {
    
    /**
     * 排除原因说明
     * 可选的说明信息，用于记录为什么要排除这个字段
     */
    String reason() default "";
    
    /**
     * 排除的操作类型
     * 可以指定在哪些操作中排除该字段
     */
    ExcludeType[] operations() default {ExcludeType.ALL};
    
    /**
     * 排除类型枚举
     */
    enum ExcludeType {
        ALL,        // 所有操作都排除
        UPDATE,     // 更新操作时排除
        COMPARE,    // 比较操作时排除
        INSERT      // 插入操作时排除
    }
}
