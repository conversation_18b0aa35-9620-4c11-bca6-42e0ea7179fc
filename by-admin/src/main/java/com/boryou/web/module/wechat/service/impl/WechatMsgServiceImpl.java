package com.boryou.web.module.wechat.service.impl;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.RandomUtil;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.wechat.constant.WXConstant;
import com.boryou.web.module.wechat.domain.WarnCode;
import com.boryou.web.module.wechat.domain.vo.StatusVO;
import com.boryou.web.module.wechat.domain.vo.WechatCodeVO;
import com.boryou.web.module.wechat.service.WechatMsgService;
import com.boryou.web.module.wechat.util.WXUtil;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.api.WxConsts;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.result.WxMpQrCodeTicket;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * Description: websocket处理类
 * Author: <a href="https://github.com/zongzibinbin">abin</a>
 * Date: 2023-03-19 16:21
 */
@Component
@Slf4j
public class WechatMsgServiceImpl implements WechatMsgService {

    @Resource
    private WxMpService wxMpService;

    /**
     * 处理用户登录请求，需要返回一张带code的二维码
     *
     * @param warnCode
     */
    @SneakyThrows
    @Override
    public WechatCodeVO handleWarnReq(WarnCode warnCode) {
        //生成随机不重复的登录码
        String username = SecurityUtils.getUsername();
        if (CharSequenceUtil.isNotBlank(username)) {
            warnCode.setUserName(username);
        }
        Integer code = generateLoginCode(warnCode);
        log.warn("登陆码为code: {}", code);
        //请求微信接口，获取登录码地址
        WxMpQrCodeTicket wxMpQrCodeTicket = wxMpService.getQrcodeService().qrCodeCreateTmpTicket(code, WXConstant.EXPIRE_SECONDS);
        WechatCodeVO wechatCodeVO = new WechatCodeVO();
        if (wxMpQrCodeTicket != null) {
            String ticket = wxMpQrCodeTicket.getTicket();
            String url = wxMpQrCodeTicket.getUrl();
            wechatCodeVO.setUrl(url);
            wechatCodeVO.setTicket(ticket);
            wechatCodeVO.setCode(code);
        }
        return wechatCodeVO;
    }

    @SneakyThrows
    @Override
    public WechatCodeVO handleLoginReq(WarnCode warnCode) {
        //生成随机不重复的登录码
        Integer code = generateLoginCode(warnCode);
        log.warn("登陆码为code: {}", code);
        //String url = WeChatConstant.QR_CODE_REDIRECT_URL;
        String url = "";
        String s = wxMpService.getOAuth2Service().buildAuthorizationUrl(url, WxConsts.OAuth2Scope.SNSAPI_USERINFO, String.valueOf(code));
        WechatCodeVO wechatCodeVO = new WechatCodeVO();
        if (CharSequenceUtil.isNotBlank(s)) {
            wechatCodeVO.setUrl(url);
            wechatCodeVO.setCode(code);
        }
        return wechatCodeVO;
    }

    @Override
    public StatusVO checkScanStatus(WarnCode warnCode) {
        Integer code = warnCode.getCode();
        StatusVO statusVO = new StatusVO();
        if (code == null) {
            statusVO.setStatus("BINDING");
        }
        //WxOAuth2UserInfo wxOAuth2UserInfo = WXConstant.WX_USER_MAP.get(code);
        WxOAuth2UserInfo wxOAuth2UserInfo = WXUtil.wxUserGet(code);
        //if (WXConstant.WAIT_BIND_MAP.containsKey(code) ||
        if (WXUtil.waitBandContainsKey(code) ||
                wxOAuth2UserInfo == null ||
                CharSequenceUtil.isBlank(wxOAuth2UserInfo.getNickname())) {
            statusVO.setStatus("BINDING");
        } else {
            statusVO.setStatus("BIND_SUCCESS");
            statusVO.setUserInfo(wxOAuth2UserInfo);
        }
        return statusVO;
    }

    /**
     * 获取不重复的登录的code，微信要求最大不超过int的存储极限
     * 防止并发，可以给方法加上synchronize，也可以使用cas乐观锁
     *
     * @return
     */
    private Integer generateLoginCode(WarnCode warnCode) {
        int code;
        do {
            String codeStr = "1000" + RandomUtil.randomInt(10000, 99999);
            code = Integer.parseInt(codeStr);
            //code = RandomUtil.randomInt(Integer.MAX_VALUE);
            //} while (Boolean.TRUE.equals(MapUtil.getBool(WXConstant.WAIT_BIND_MAP, code))
            //        || Objects.nonNull(WXConstant.WAIT_BIND_MAP.putIfAbsent(code, warnCode)));
        } while (WXUtil.waitBandContainsKey(code));
        WXUtil.waitBandPut(code, warnCode);
        return code;
    }

}
