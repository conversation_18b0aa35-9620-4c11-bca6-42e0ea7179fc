package com.boryou.web.module.update.example;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.boryou.web.module.mark.domain.EsBeanMark;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import com.boryou.web.module.update.util.SimpleUpdateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 简化更新工具使用示例
 * 展示如何使用 SimpleUpdateUtil 进行通用字段更新
 * 
 * <AUTHOR>
 */
@Slf4j
public class SimpleUpdateExample {

    /**
     * 示例1：使用字段更新器的基础更新
     */
    public boolean updateExample1(EsBeanMarkVO sourceData, EsBeanMark targetData, 
                                LambdaUpdateChainWrapper<EsBeanMark> chainWrapper) {
        
        // 创建字段更新器映射
        Map<String, SimpleUpdateUtil.FieldUpdater<EsBeanMark>> fieldUpdaters = new HashMap<>();
        fieldUpdaters.put("title", SimpleUpdateUtil.createUpdater(EsBeanMark::getTitle));
        fieldUpdaters.put("content", SimpleUpdateUtil.createUpdater(EsBeanMark::getContent));
        fieldUpdaters.put("tags", SimpleUpdateUtil.createUpdater(EsBeanMark::getTags));
        fieldUpdaters.put("userId", SimpleUpdateUtil.createUpdater(EsBeanMark::getUserId));
        fieldUpdaters.put("deptId", SimpleUpdateUtil.createUpdater(EsBeanMark::getDeptId));

        // 执行比较和更新
        boolean hasUpdate = SimpleUpdateUtil.compareAndUpdate(
                sourceData, 
                targetData, 
                chainWrapper,
                fieldUpdaters,
                // 排除系统字段
                "esBeanMarkId", "createTime", "createBy", "updateTime", "delFlag"
        );
        
        if (hasUpdate) {
            // 设置公共更新字段
            chainWrapper.set(EsBeanMark::getUTime, new Date())
                       .set(EsBeanMark::getUBy, "system");
            
            // 执行更新
            return chainWrapper.update();
        }
        
        return false;
    }

    /**
     * 示例2：使用批量创建字段更新器的便捷方法
     */
    public boolean updateExample2(EsBeanMarkVO sourceData, EsBeanMark targetData, 
                                LambdaUpdateChainWrapper<EsBeanMark> chainWrapper) {
        
        // 使用便捷方法批量创建字段更新器
        Map<String, SimpleUpdateUtil.FieldUpdater<EsBeanMark>> fieldUpdaters = 
                SimpleUpdateUtil.createUpdaters(
                        SimpleUpdateUtil.FieldMapping.of("title", EsBeanMark::getTitle),
                        SimpleUpdateUtil.FieldMapping.of("content", EsBeanMark::getContent),
                        SimpleUpdateUtil.FieldMapping.of("tags", EsBeanMark::getTags),
                        SimpleUpdateUtil.FieldMapping.of("userId", EsBeanMark::getUserId),
                        SimpleUpdateUtil.FieldMapping.of("deptId", EsBeanMark::getDeptId),
                        SimpleUpdateUtil.FieldMapping.of("status", EsBeanMark::getStatus),
                        SimpleUpdateUtil.FieldMapping.of("remark", EsBeanMark::getRemark)
                );

        boolean hasUpdate = SimpleUpdateUtil.compareAndUpdate(
                sourceData, 
                targetData, 
                chainWrapper,
                fieldUpdaters,
                "esBeanMarkId", "createTime", "createBy", "updateTime", "delFlag"
        );
        
        if (hasUpdate) {
            chainWrapper.set(EsBeanMark::getUTime, new Date());
            return chainWrapper.update();
        }
        
        return false;
    }

    /**
     * 示例3：使用Lambda表达式的简化方法
     */
    public boolean updateExample3(EsBeanMarkVO sourceData, EsBeanMark targetData, 
                                LambdaUpdateChainWrapper<EsBeanMark> chainWrapper) {
        
        // 创建Lambda表达式映射
        Map<String, com.baomidou.mybatisplus.core.toolkit.support.SFunction<EsBeanMark, ?>> fieldSetters = new HashMap<>();
        fieldSetters.put("title", EsBeanMark::getTitle);
        fieldSetters.put("content", EsBeanMark::getContent);
        fieldSetters.put("tags", EsBeanMark::getTags);
        fieldSetters.put("userId", EsBeanMark::getUserId);
        fieldSetters.put("deptId", EsBeanMark::getDeptId);

        boolean hasUpdate = SimpleUpdateUtil.compareAndUpdateWithLambda(
                sourceData, 
                targetData, 
                chainWrapper,
                fieldSetters,
                "esBeanMarkId", "createTime", "createBy", "updateTime", "delFlag"
        );
        
        if (hasUpdate) {
            chainWrapper.set(EsBeanMark::getUTime, new Date());
            return chainWrapper.update();
        }
        
        return false;
    }

    /**
     * 示例4：支持字段映射的更新（源字段名与目标字段名不同）
     */
    public boolean updateExample4(Object sourceData, EsBeanMark targetData, 
                                LambdaUpdateChainWrapper<EsBeanMark> chainWrapper) {
        
        // 创建字段更新器
        Map<String, SimpleUpdateUtil.FieldUpdater<EsBeanMark>> fieldUpdaters = 
                SimpleUpdateUtil.createUpdaters(
                        SimpleUpdateUtil.FieldMapping.of("title", EsBeanMark::getTitle),
                        SimpleUpdateUtil.FieldMapping.of("content", EsBeanMark::getContent),
                        SimpleUpdateUtil.FieldMapping.of("userId", EsBeanMark::getUserId),
                        SimpleUpdateUtil.FieldMapping.of("deptId", EsBeanMark::getDeptId)
                );

        // 创建字段映射（源字段名 -> 目标字段名）
        Map<String, String> fieldMappings = new HashMap<>();
        fieldMappings.put("userIdSource", "userId");    // 源对象中的userIdSource映射到目标对象的userId
        fieldMappings.put("deptIdSource", "deptId");    // 源对象中的deptIdSource映射到目标对象的deptId
        fieldMappings.put("titleText", "title");        // 源对象中的titleText映射到目标对象的title

        boolean hasUpdate = SimpleUpdateUtil.compareAndUpdate(
                sourceData, 
                targetData, 
                chainWrapper,
                fieldUpdaters,
                fieldMappings,
                "id", "createTime", "updateTime"
        );
        
        if (hasUpdate) {
            chainWrapper.set(EsBeanMark::getUTime, new Date());
            return chainWrapper.update();
        }
        
        return false;
    }

    /**
     * 示例5：使用便捷的单字段更新方法
     */
    public boolean updateExample5(EsBeanMarkVO sourceData, EsBeanMark targetData, 
                                LambdaUpdateChainWrapper<EsBeanMark> chainWrapper) {
        
        boolean hasUpdate = false;

        // 使用便捷的单字段更新方法
        hasUpdate |= SimpleUpdateUtil.updateStringField(
                sourceData.getTitle(), targetData.getTitle(), chainWrapper, EsBeanMark::getTitle);
        
        hasUpdate |= SimpleUpdateUtil.updateStringField(
                sourceData.getContent(), targetData.getContent(), chainWrapper, EsBeanMark::getContent);
        
        hasUpdate |= SimpleUpdateUtil.updateField(
                sourceData.getUserId(), targetData.getUserId(), chainWrapper, EsBeanMark::getUserId);
        
        hasUpdate |= SimpleUpdateUtil.updateField(
                sourceData.getDeptId(), targetData.getDeptId(), chainWrapper, EsBeanMark::getDeptId);

        if (hasUpdate) {
            chainWrapper.set(EsBeanMark::getUTime, new Date());
            return chainWrapper.update();
        }
        
        return false;
    }

    /**
     * 示例6：使用批量字段更新
     */
    public boolean updateExample6(EsBeanMarkVO sourceData, EsBeanMark targetData, 
                                LambdaUpdateChainWrapper<EsBeanMark> chainWrapper) {
        
        // 使用批量字段更新
        boolean hasUpdate = SimpleUpdateUtil.updateFields(chainWrapper,
                SimpleUpdateUtil.FieldUpdate.ofString(
                        sourceData.getTitle(), targetData.getTitle(), EsBeanMark::getTitle),
                SimpleUpdateUtil.FieldUpdate.ofString(
                        sourceData.getContent(), targetData.getContent(), EsBeanMark::getContent),
                SimpleUpdateUtil.FieldUpdate.of(
                        sourceData.getUserId(), targetData.getUserId(), EsBeanMark::getUserId),
                SimpleUpdateUtil.FieldUpdate.of(
                        sourceData.getDeptId(), targetData.getDeptId(), EsBeanMark::getDeptId),
                SimpleUpdateUtil.FieldUpdate.of(
                        sourceData.getStatus(), targetData.getStatus(), EsBeanMark::getStatus)
        );

        if (hasUpdate) {
            chainWrapper.set(EsBeanMark::getUTime, new Date())
                       .set(EsBeanMark::getUBy, "system");
            return chainWrapper.update();
        }
        
        return false;
    }

    /**
     * 示例7：完整的Service方法示例
     */
    public boolean updateEsBeanMarkService(EsBeanMarkVO esBeanMarkVO, EsBeanMark existEntity, 
                                         LambdaUpdateChainWrapper<EsBeanMark> chainWrapper,
                                         String userName, Date updateTime, Long userId) {
        
        // 设置查询条件
        chainWrapper.eq(EsBeanMark::getArticleId, esBeanMarkVO.getArticleId())
                   .eq(EsBeanMark::getUserId, userId);

        // 创建字段更新器映射
        Map<String, SimpleUpdateUtil.FieldUpdater<EsBeanMark>> fieldUpdaters = 
                SimpleUpdateUtil.createUpdaters(
                        SimpleUpdateUtil.FieldMapping.of("title", EsBeanMark::getTitle),
                        SimpleUpdateUtil.FieldMapping.of("content", EsBeanMark::getContent),
                        SimpleUpdateUtil.FieldMapping.of("tags", EsBeanMark::getTags),
                        SimpleUpdateUtil.FieldMapping.of("userId", EsBeanMark::getUserId),
                        SimpleUpdateUtil.FieldMapping.of("deptId", EsBeanMark::getDeptId),
                        SimpleUpdateUtil.FieldMapping.of("status", EsBeanMark::getStatus),
                        SimpleUpdateUtil.FieldMapping.of("remark", EsBeanMark::getRemark),
                        SimpleUpdateUtil.FieldMapping.of("markType", EsBeanMark::getMarkType),
                        SimpleUpdateUtil.FieldMapping.of("priority", EsBeanMark::getPriority)
                );

        // 执行字段比较和更新
        boolean hasUpdate = SimpleUpdateUtil.compareAndUpdate(
                esBeanMarkVO, 
                existEntity, 
                chainWrapper,
                fieldUpdaters,
                // 排除系统字段和主键字段
                "esBeanMarkId", "createTime", "createBy", "delFlag"
        );

        // 如果有字段需要更新，设置公共字段并执行更新
        if (hasUpdate) {
            chainWrapper.set(EsBeanMark::getDelFlag, 0)
                       .set(EsBeanMark::getUTime, updateTime)
                       .set(EsBeanMark::getUBy, userName);
            
            return chainWrapper.update();
        }

        return false;
    }
}
