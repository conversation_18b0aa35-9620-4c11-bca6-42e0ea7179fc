package com.boryou.web.module.drill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.web.handle.ListStringTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

/**
 * 演练评论记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "by_drill_comment", autoResultMap = true)
public class DrillComment {

    /**
     * 评论ID
     */
    @TableId(value = "comment_id", type = IdType.INPUT)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commentId;

    /**
     * 演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    /**
     * 所属阶段ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processStageId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long scoreProcessStageId;

    /**
     * 评论属于的指令
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentCommentId;

    /**
     * 指令排序(指令一,指令二,只有指令有)
     */
    private String commentOrder;

    /**
     * 队伍类型（1:红队 2:蓝队）
     */
    private Integer teamType;

    /**
     * 评论类型
     */
    private String commentType;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 热搜榜排名
     */
    private String rank;

    /**
     * 热度值
     */
    private String hotNum;

    /**
     * 热搜文章类型
     */
    private String rankType;

    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> file;

    /**
     * 发布人ID
     */
    private Long userId;

    /**
     * 角色类型
     */
    private String roleInfo;

    /**
     * 是否队长发布（0:否 1:是）
     */
    private Boolean isCaptain;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 得分
     */
    private String score;

    private Integer likeCount;

    private Integer relatedCommentType;

}
