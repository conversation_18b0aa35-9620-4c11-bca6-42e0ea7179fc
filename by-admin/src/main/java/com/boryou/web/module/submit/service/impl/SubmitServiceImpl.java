package com.boryou.web.module.submit.service.impl;

import java.util.Date;
import java.util.List;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.domain.msg.MessageVO;
import com.boryou.web.module.submit.entity.Submit;
import com.boryou.web.module.submit.mapper.SubmitMapper;
import com.boryou.web.module.submit.service.ISubmitService;
import com.boryou.web.task.SubmitTask;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * 报送记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Service
public class SubmitServiceImpl implements ISubmitService {
    @Autowired
    private SubmitMapper submitMapper;
    @Autowired
    private ISubmitService submitService;
    @Autowired
    private SubmitTask submitTask;


    /**
     * 查询报送记录
     *
     * @param id 报送记录ID
     * @return 报送记录
     */
    @Override
    public Submit selectSubmitById(Long id) {
        return submitMapper.selectSubmitById(id);
    }

    /**
     * 查询报送记录列表
     *
     * @param submit 报送记录
     * @return 报送记录
     */
    @Override
    public List<Submit> selectSubmitList(Submit submit) {
        return submitMapper.selectSubmitList(submit);
    }

    /**
     * 新增报送记录
     *
     * @param submit 报送记录
     * @return 结果
     */
    @Override
    public int insertSubmit(Submit submit) {
        return submitMapper.insertSubmit(submit);
    }

    @Override
    public int updateSubmit(Submit submit) {
        return submitMapper.updateSubmit(submit);
    }


    /**
     * 批量删除报送记录
     *
     * @param ids 需要删除的报送记录ID
     * @return 结果
     */
    @Override
    public int deleteSubmitByIds(Long[] ids) {
        return submitMapper.deleteSubmitByIds(ids);
    }

    /**
     * 删除报送记录信息
     *
     * @param id 报送记录ID
     * @return 结果
     */
    @Override
    public int deleteSubmitById(Long id) {
        return submitMapper.deleteSubmitById(id);
    }

    @Override
    public boolean insertShortSubmit(MessageVO messageVO) {
        Submit bean = JSONUtil.toBean(JSONUtil.toJsonStr(messageVO), Submit.class);
        if (CollUtil.isNotEmpty(messageVO.getUserIds())) {
            bean.setContactIds(CollUtil.join(messageVO.getUserIds(), ","));
        }
        String deadlineNum = messageVO.getDeadlineNum();
        if (StrUtil.isNotEmpty(deadlineNum)) {
            bean.setDeadline(getDeadTime(deadlineNum));
            bean.setDeadlineNum(deadlineNum);
        }
        bean.setUserId(SecurityUtils.getUserIdL());
        submitMapper.insertSubmit(bean);
        submitTask.addTask(bean.getId(), bean);
        return true;
    }

    private DateTime getDeadTime(String deadlineNum) {
        return DateUtil.offsetHour(new Date(), Integer.parseInt(deadlineNum));
    }

    @Override
    public int insertSubmitProcess(Submit submit) {
        Long docIndexId = submit.getDocIndexId();
        Long userId = submit.getUserId();
        Submit submit3 = new Submit();
        submit3.setUserId(userId);
        submit3.setDocIndexId(docIndexId);
        List<Submit> submit2 = submitMapper.selectSubmitList(submit3);
        if (null == submit2) {
            throw new CustomException("该信息未被报送");
        }
        for (Submit submit1 : submit2) {
            submit1.setProcessTime(submit.getProcessTime());
            submit1.setProcessText(submit.getProcessText());
            submit1.setProcessStatus(submit.getProcessStatus());
            submit1.setId(submit1.getId());
            int flag = submitMapper.updateSubmit(submit1);
//            break;
        }
        return 1;
    }

    @Override
    public Submit getProcessInfo(Submit submit) {
        List<Submit> submits = submitService.selectSubmitList(submit);
        if (CollUtil.isNotEmpty(submits)) {
            Submit submit1 = submits.get(0);
            return submit1;
        }
        return new Submit();
    }
}
