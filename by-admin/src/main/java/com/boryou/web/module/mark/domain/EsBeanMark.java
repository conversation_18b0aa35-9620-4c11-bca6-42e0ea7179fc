package com.boryou.web.module.mark.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.handlers.JacksonTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;
import java.util.Set;

@Data
@TableName(value = "by_es_bean_mark", autoResultMap = true)
public class EsBeanMark {

    /**
     * 主键ID
     */
    @TableId(type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long esBeanMarkId;

    /**
     * 文章id
     */
    private String id;

    /**
     * 媒体类型
     */
    private Integer type;

    /**
     * 媒体类型名称
     */
    private String typeName;

    /**
     * 发文时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    /**
     * 标题
     */
    private String title;

    /**
     * 正文
     */
    private String text;

    /**
     * 摘要，非es字段
     */
    private String summary;

    /**
     * 原文链接
     */
    private String url;

    /**
     * host
     */
    private String host;

    /**
     * 域名
     */
    private String domain;

    /**
     * (账号/作者)昵称
     */
    private String author;

    /**
     * (账号/作者)id
     */
    private String authorId;

    /**
     * 作者性别(0:女,1:男)
     */
    private Integer authorSex;

    /**
     * 平台业务ID
     */
    private String bizId;

    /**
     * 账号级别 (0达人  1蓝v  2红v  3橙v  4普通用户)
     */
    private Integer accountLevel;

    /**
     * 站点地域(码)
     */
    private String siteAreaCode;

    /**
     * 站点地域
     */
    private String siteAreaCodeName;

    /**
     * 内容地域(码) - List<String>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> contentAreaCode;

    /**
     * 内容地域
     */
    private String contentAreaCodeName;

    /**
     * 站点标签 - List<String>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> siteMeta;

    /**
     * 内容标签 - List<String>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> contentMeta;

    /**
     * 粉丝数
     */
    private Integer fansNum;

    /**
     * 阅读数
     */
    private Integer readNum;

    /**
     * 评论数
     */
    private Integer commentNum;

    /**
     * 点赞数
     */
    private Integer likeNum;

    /**
     * 转发数
     */
    private Integer reprintNum;

    /**
     * 所属板块
     */
    private String sector;

    /**
     * 内容形式 - List<Integer>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<Integer> contentForm;

    /**
     * 内容MD5
     */
    private String md5;

    /**
     * 网页源码路径
     */
    private String srcCodePath;

    /**
     * 封面图片链接
     */
    private String coverUrl;

    /**
     * 图片链接数组 - List<String>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> picUrl;

    /**
     * 音视频链接数组 - List<String>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> avdUrl;

    /**
     * 情感标识
     */
    private Integer emotionFlag;

    /**
     * 是否为原创
     */
    private Boolean isOriginal;

    /**
     * 是否被标记为垃圾内容
     */
    private Boolean isSpam;

    /**
     * 发布日期
     */
    private String day;

    /**
     * 是否已读
     */
    private Integer isRead;

    /**
     * 信源级别
     */
    private Integer accountGrade;

    /**
     * 数据原始更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 命中关键词 (系统预警) - List<String>
     */
    @TableField(typeHandler = JacksonTypeHandler.class)
    private List<String> hitWord;

    /**
     * 方案id
     */
    private String planId;

    /**
     * 用户id
     */
    private String userId;

    /**
     * 部门id
     */
    private String deptId;

    private Integer delFlag;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    private String uBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date uTime;

}
