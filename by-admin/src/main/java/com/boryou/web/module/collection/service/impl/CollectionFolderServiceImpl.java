package com.boryou.web.module.collection.service.impl;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.collection.entity.Collection;
import com.boryou.web.module.collection.entity.CollectionFolder;
import com.boryou.web.module.collection.mapper.CollectionFolderMapper;
import com.boryou.web.module.collection.mapper.CollectionMapper;
import com.boryou.web.module.collection.service.CollectionFolderService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

import static com.boryou.common.constant.Constants.STR_FALSE;
import static com.boryou.common.constant.Constants.STR_TRUE;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:48
 */
@Service
@AllArgsConstructor
public class CollectionFolderServiceImpl extends ServiceImpl<CollectionFolderMapper, CollectionFolder> implements CollectionFolderService {

    private final CollectionFolderMapper folderMapper;
    private final CollectionMapper collectionMapper;

    @Override
    public boolean folderSave(CollectionFolder folder) {
        folder.setUserId(SecurityUtils.getUserId());
        // 判断父级是否存在
        if (folder.getParentId() != null && folder.getParentId() != 0L) {
            CollectionFolder group = this.getById(folder.getParentId());
            if (group == null) {
                throw new CustomException("父级文件夹不存在");
            }
        }

        if (folder.getParentId() == null || folder.getParentId() == 0L) {
            folder.setIsGroup(STR_TRUE);
        } else {
            folder.setIsGroup(STR_FALSE);
        }
        int count = this.count(Wrappers.<CollectionFolder>lambdaQuery()
                .eq(CollectionFolder::getUserId, SecurityUtils.getUserId())
                .eq(CollectionFolder::getFolderName, folder.getFolderName())
        );
        if (count > 0) {
            throw new CustomException("收藏夹名称重复");
        }
        return this.saveOrUpdate(folder);
    }

    @Override
    public List<CollectionFolder> folderList(String name) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());

        // 获取父级
        List<CollectionFolder> res = this.list(Wrappers.<CollectionFolder>lambdaQuery()
                .eq(CollectionFolder::getUserId, userId)
                .eq(CollectionFolder::getIsGroup, STR_TRUE)
                .orderByAsc(CollectionFolder::getCreateTime)
        );

        // 获取所有子级和count
        List<CollectionFolder> list = folderMapper.folderList(userId);

        // 如果没有文件夹，新建默认素材库
        if (list.isEmpty()) {
            CollectionFolder folderGroup = new CollectionFolder();
            folderGroup.setIsGroup(STR_TRUE);
            folderGroup.setFolderName("默认收藏组");
            this.folderSave(folderGroup);

            CollectionFolder folder = new CollectionFolder();
            folder.setParentId(folderGroup.getId());
            folder.setIsGroup(STR_FALSE);
            folder.setFolderName("默认收藏夹");
            this.folderSave(folder);

            list = folderMapper.folderList(userId);
        }

        // 构建树结构
        List<CollectionFolder> finalList = list;
        res.forEach(x -> x.setChildren(finalList.stream().filter(y -> y.getParentId().equals(x.getId())).collect(Collectors.toList())));

        // 过滤树结构中的文件夹
        if (name != null && !name.isEmpty()) {
            res = filterFoldersByName(res, name);
        }

        return res;
    }

    @Override
    public boolean folderDelete(Long id) {
        CollectionFolder folder = this.getById(id);
        if (folder == null) {
            throw new CustomException("收藏夹不存在");
        }
        if (folder.getIsGroup().equals(STR_TRUE)) {
            int count = this.count(Wrappers.<CollectionFolder>lambdaQuery()
                    .eq(CollectionFolder::getParentId, id)
            );
            if (count > 0) {
                throw new CustomException("请先删除下级收藏夹");
            }
        } else {
            int count = collectionMapper.selectCount(Wrappers.<Collection>lambdaQuery().eq(Collection::getFolderId, id));
            if (count > 0) {
                throw new CustomException("请先删除收藏");
            }
        }
        return this.removeById(id);
    }

    // 过滤树结构
    private List<CollectionFolder> filterFoldersByName(List<CollectionFolder> folders, String name) {
        List<CollectionFolder> filtered = new ArrayList<>();
        folders.forEach(x -> {
            if (x.getFolderName().contains(name)) {
                filtered.add(x);
            } else {
                List<CollectionFolder> collect = x.getChildren().stream().filter(y -> y.getFolderName().contains(name)).collect(Collectors.toList());
                if (!collect.isEmpty()) {
                    x.setChildren(collect);
                    filtered.add(x);
                }
            }
        });
        return filtered;
    }

}
