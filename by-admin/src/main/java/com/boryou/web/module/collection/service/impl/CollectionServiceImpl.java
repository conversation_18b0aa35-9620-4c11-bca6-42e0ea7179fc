package com.boryou.web.module.collection.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.web.module.collection.entity.Collection;
import com.boryou.web.module.collection.entity.CollectionFolder;
import com.boryou.web.module.collection.mapper.CollectionMapper;
import com.boryou.web.module.collection.service.CollectionFolderService;
import com.boryou.web.module.collection.service.CollectionService;
import com.boryou.web.module.collection.vo.CollectionQuery;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

import static com.boryou.common.constant.Constants.STR_TRUE;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:48
 */
@Service
@AllArgsConstructor
public class CollectionServiceImpl extends ServiceImpl<CollectionMapper, Collection> implements CollectionService {

    private final CollectionFolderService collectionService;

    @Override
    public boolean add(Collection collection) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        collection.setUserId(userId);
        if (collection.getContentId() == null || collection.getFolderId() == null) {
            throw new CustomException("请选择素材和收藏夹目录");
        }
        // 校验收藏夹是否存在
        CollectionFolder folder = collectionService.getById(collection.getFolderId());
        if (folder == null) {
            throw new CustomException("收藏夹不存在");
        } else {
            if (folder.getIsGroup().equals(STR_TRUE)) {
                throw new CustomException("不可以直接添加到素材组");
            }
        }

        int count = this.count(Wrappers.<Collection>lambdaQuery()
                .eq(Collection::getUserId, userId)
                .eq(Collection::getContentId, collection.getContentId())
                .eq(Collection::getFolderId, collection.getFolderId())
        );
        if (count > 0) {
            throw new CustomException("已添加过该素材");
        }
        // todo 媒体类型处理
        return this.save(collection);
    }

    @Override
    public List<Collection> collectionList(CollectionQuery query) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        LambdaQueryWrapper<Collection> lambdaQueryWrapper = Wrappers.<Collection>lambdaQuery()
                .eq(Collection::getUserId, userId)
                .eq(ObjectUtil.isNotEmpty(query.getFolderId()), Collection::getFolderId, query.getFolderId())
                .ge(ObjectUtil.isNotEmpty(query.getStartTime()), Collection::getCreateTime, query.getStartTime())
                .le(ObjectUtil.isNotEmpty(query.getEndTime()), Collection::getCreateTime, query.getEndTime())
                .eq(ObjectUtil.isNotEmpty(query.getType()), Collection::getType, query.getType())
                .orderByDesc(Collection::getCreateTime);

        if (StringUtils.isNotEmpty(query.getSearchType())) {
            if ("0".equals(query.getSearchType())) {
                lambdaQueryWrapper.and(x -> x
                        .like(Collection::getTitle, query.getSearch())
                        .or()
                        .like(Collection::getTitle, query.getSearch())
                );
            }
            if ("1".equals(query.getSearchType())) {
                lambdaQueryWrapper.like(Collection::getTitle, query.getSearch());
            }
            if ("2".equals(query.getSearchType())) {
                lambdaQueryWrapper.like(Collection::getText, query.getSearch());
            }
        }
        List<Collection> list = this.list(lambdaQueryWrapper);
        list.forEach(x -> {
            if (x.getPicUrl() == null) {
                x.setPicUrl(new ArrayList<>());
            }
        });
        return list;

    }
}
