package com.boryou.web.module.collection.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.boryou.web.handle.ListStringTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/2 11:18
 */
@TableName(value = "by_collection", autoResultMap = true)
@Data
public class Collection {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 素材的 id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contentId;

    /**
     * 素材文件夹 id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long folderId;

    /**
     * 媒体类型
     */
    private Integer type;

    /**
     * 媒体名称
     */
    private String typeName;

    /**
     * 标题
     */
    private String title;

    /**
     * 正文
     */
    private String text;

    /**
     * url
     */
    private String url;

    /**
     * 域名
     */
    private String host;

    /**
     * 作者
     */
    private String author;

    /**
     * 情感标识
     */
    private Integer emotionFlag;

    /**
     * 站点地域
     */
    private String siteAreaCodeName;

    /**
     * 是否原创
     */
    private String originFlag;

    /**
     * 命中词
     */
    private String hitWords;

    /**
     * md5
     */
    private String md5;

    /**
     * 相似条数
     */
    private Integer similarCount;

    /**
     * 图片链接
     */
    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> picUrl;

    /**
     * 发布时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
