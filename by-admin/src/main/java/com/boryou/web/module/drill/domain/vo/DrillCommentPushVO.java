package com.boryou.web.module.drill.domain.vo;

import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.EqualsAndHashCode;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.Date;

@EqualsAndHashCode(callSuper = true)
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DrillCommentPushVO extends DrillCommentVO {

    private String nickName;

    private String userName;

    private String avatar;

    private String stageName;
    private String scoreStageName;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 得分
     */
    private String score;
}
