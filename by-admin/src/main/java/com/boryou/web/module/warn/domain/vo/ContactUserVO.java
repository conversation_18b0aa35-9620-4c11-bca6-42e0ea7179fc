package com.boryou.web.module.warn.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.List;

@Data
public class ContactUserVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;


    private String username;

    /**
     * 手机号
     */
    private String phone;

    /**
     * 邮箱
     */
    private String email;

    /**
     * 微信名
     */
    private String wxUserName;

    /**
     * 微信 openId
     */
    private String wxOpenId;

    /**
     * 微信头像
     */
    private String headImgUrl;

    private List<String> area;
}
