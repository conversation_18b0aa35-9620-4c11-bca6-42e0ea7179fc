package com.boryou.web.module.area.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONUtil;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.exception.CustomException;
import com.boryou.utils.AreaUtil;
import com.boryou.web.module.area.entity.Area;
import com.boryou.web.module.area.entity.AreaTree;
import com.boryou.web.module.area.mapper.AreaMapper;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * 方案Service接口
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@Service
public class AreaService extends ServiceImpl<AreaMapper, Area> {

    public Object areaInfo(String areaId) {
        //深度1,标识只获取下一级的地域信息
        AjaxResult areaInfo = AreaUtil.getAreaInfo(areaId);
        return areaInfo.getData();
    }

    public List<AreaTree> areaTree(String areaId, Integer deep) {
        try {
            if (deep == null || deep < 0) {
                throw new CustomException("深度不对");
            }
            //全国
            if ("0".equals(areaId)) {
                deep = Math.min(3, deep + 1);
                Object treeListR = AreaUtil.getTreeList(areaId, deep).getData();
                return JSONUtil.toList(JSONUtil.toJsonStr(treeListR), AreaTree.class);
            }

            Object data = AreaUtil.getAreaInfo(areaId).getData();
            JSONObject jsonObject = JSON.parseObject(JSON.toJSONString(data));
            Integer areaLevel = jsonObject.getInteger("areaLevel");
            if (areaLevel == 1) {
                deep = Math.min(2, deep);
            } else if (areaLevel == 2) {
                deep = Math.min(1, deep);
            } else if (areaLevel == 3) {
                deep = 0;
            }
            Object treeListR = AreaUtil.getTreeList(areaId, deep).getData();
            AreaTree areaTree = new AreaTree();
            areaTree.setId(jsonObject.get("id").toString());
            areaTree.setName(jsonObject.getString("areaName"));
            areaTree.setSname(jsonObject.getString("areaShortName"));
            areaTree.setLevel(areaLevel);
            if (deep != 0) {
                List<AreaTree> treeList = JSONUtil.toList(JSONUtil.toJsonStr(treeListR), AreaTree.class);
                areaTree.setChildren(treeList);
            }
            return Collections.singletonList(areaTree);

        } catch (Exception e) {
            throw new CustomException("深度不对");
        }
    }

    public void areaUpdate() {
        Object treeListR = AreaUtil.getTreeList("0", 4).getData();
        List<AreaTree> areaTreeList = JSONUtil.toList(JSONUtil.toJsonStr(treeListR), AreaTree.class);
        List<Area> areaList = new ArrayList<>();
        getArea(areaList, areaTreeList, "");
        log.warn(JSONUtil.toJsonStr(areaList));
        //boolean b = this.saveBatch(areaList, 1000);
    }

    private void getArea(List<Area> areaList, List<AreaTree> areaTreeList, String code) {
        for (AreaTree areaTree : areaTreeList) {
            String id = areaTree.getId();
            String pid = areaTree.getPid();
            Integer level = areaTree.getLevel();
            String name = areaTree.getName();
            String sname = areaTree.getSname();
            String lng = areaTree.getLng();
            String lat = areaTree.getLat();
            Area area = new Area();
            area.setId(Integer.parseInt(id));
            area.setShortName(sname);
            area.setAreaName(name);
            area.setParentId(Integer.parseInt(pid));
            if (lng == null) {
                lng = "0.0";
            }
            if (lat == null) {
                lat = "0.0";
            }
            area.setLng(Double.parseDouble(lng));
            area.setLat(Double.parseDouble(lat));
            area.setLevel(level);
            String a = code + id;
            area.setCode(a);
            areaList.add(area);
            List<AreaTree> children = areaTree.getChildren();
            if (CollUtil.isNotEmpty(children)) {
                getArea(areaList, children, a);
            }
        }
    }

    public List<Area> getNextArea(String parentId) {
        if (StrUtil.isBlankIfStr(parentId)) {
            return Collections.emptyList();
        }
        LambdaQueryWrapper<Area> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(Area::getParentId, parentId);
        return this.list(queryWrapper);
    }

}
