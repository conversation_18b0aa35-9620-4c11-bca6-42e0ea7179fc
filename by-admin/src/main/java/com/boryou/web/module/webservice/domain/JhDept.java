package com.boryou.web.module.webservice.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/6 下午5:19
 */
@TableName(value = "jh_dept")
@Data
public class JhDept implements Serializable {


    /**
     * 科室编码
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String deptCode;

    /**
     * 所属机构编码
     */
    private Long orgCode;

    /**
     * 科室名称
     */
    private String deptName;

    /**
     * 科室别名简称
     */
    private String deptAlias;

    /**
     * 父科室编码
     */
    private String deptFatherCode;

    /**
     * 科室联系电话
     */
    private String deptPhone;

    /**
     * 科室负责人
     */
    private String deptHeadName;

    /**
     * 科室地址
     */
    private String deptAddr;

    /**
     * 备注
     */
    private String deptRemark;

    /**
     * 更新时间，格式YYYY - MM - DD HH:MM:SS
     */
    private Date modifiedTime;

    /**
     * 创建时间，格式YYYY - MM - DD HH:MM:SS
     */
    private Date createTime;

    /**
     * 接口方法 add/update/del
     */
    @TableField(exist = false)
    private String identification;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}