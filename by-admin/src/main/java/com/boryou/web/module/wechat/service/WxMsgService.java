package com.boryou.web.module.wechat.service;

import cn.hutool.core.net.URLEncodeUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.boryou.web.module.wechat.adapter.TextBuilder;
import com.boryou.web.module.wechat.constant.WXConstant;
import com.boryou.web.module.wechat.domain.WarnCode;
import com.boryou.web.module.wechat.domain.vo.WeChatRequest;
import com.boryou.web.module.wechat.util.WXUtil;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.message.WxMpXmlMessage;
import me.chanjar.weixin.mp.bean.message.WxMpXmlOutMessage;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

/**
 * Description: 处理与微信api的交互逻辑
 * Author: <a href="https://github.com/zongzibinbin">abin</a>
 * Date: 2023-03-19
 */
@Service
@Slf4j
public class WxMsgService {

    @Value("${wx.callbackUrl}")
    private String callbackUrl;
    //@Resource
    //private IUserWechatService userWechatService;
    //@Resource
    //private WechatWarnMapper wechatWarnMapper;
    public WxMpXmlOutMessage scan(WxMpService wxMpService, WxMpXmlMessage wxMpXmlMessage) {
        String openId = wxMpXmlMessage.getFromUser();
        String eventKeyStr = getEventKey(wxMpXmlMessage);
        if (CharSequenceUtil.isBlank(eventKeyStr)) {
            return null;
        }
        Integer eventKey = Integer.parseInt(eventKeyStr);
        //保存openid和场景code的关系，后续才能通知到前端
        //WXConstant.OPENID_EVENT_CODE_MAP.put(openId, eventKey);
        WXUtil.openEventCodePut(openId, eventKey);
        String appId = wxMpService.getWxMpConfigStorage().getAppId();
        String callback = callbackUrl + appId + "/callBack";
        String encode = URLEncodeUtil.encode(callback);
        String skipUrl = String.format(WXConstant.URL, appId, encode);
        WxMpXmlOutMessage.TEXT().build();
        return new TextBuilder().build("请点击链接授权：<a href=\"" + skipUrl + "\">授权</a>", wxMpXmlMessage, wxMpService);
    }

    private String getEventKey(WxMpXmlMessage wxMpXmlMessage) {
        //扫码关注的渠道事件有前缀，需要去除
        return wxMpXmlMessage.getEventKey().replace("qrscene_", "");
    }

    /**
     * 用户授权
     *
     * @param userInfo
     */
    public String authorize(WxOAuth2UserInfo userInfo) {
        try {
            String openid = userInfo.getOpenid();
            String nickname = userInfo.getNickname();
            Integer sex = userInfo.getSex();
            String city = userInfo.getCity();
            String province = userInfo.getProvince();
            String country = userInfo.getCountry();
            String headImgUrl = userInfo.getHeadImgUrl();
            //Integer eventKey = WXConstant.OPENID_EVENT_CODE_MAP.get(openid);
            Integer eventKey = WXUtil.openEventCodeGet(openid);
            //WarnCode warnCode = WXConstant.WAIT_BIND_MAP.get(eventKey);
            WarnCode warnCode = WXUtil.waitBandGet(eventKey);
            if (warnCode == null) {
                return "请刷新二维码重新扫码";
            }
            //WXConstant.WX_USER_MAP.put(eventKey, userInfo);
            WXUtil.wxUserPut(eventKey, userInfo);
            return "获取成功";
        } catch (Exception e) {
            return "请刷新二维码重新扫码";
        } finally {
            if (userInfo != null) {
                String openid = userInfo.getOpenid();
                Integer eventKey = WXUtil.openEventCodeGet(openid);
                //Integer eventKey = WXConstant.OPENID_EVENT_CODE_MAP.get(openid);
                //WXConstant.OPENID_EVENT_CODE_MAP.remove(openid);
                WXUtil.openEventCodeRemove(openid);
                WXUtil.waitBandRemove(eventKey);
                //WXConstant.WAIT_BIND_MAP.remove(eventKey);
            }
        }
    }

    public String openId(WeChatRequest weChatRequest) {
        String openId = weChatRequest.getOpenId();
        Integer eventKey = weChatRequest.getWarnCode();
        try {
            String nickName = weChatRequest.getNickName();
            String headImgUrl = weChatRequest.getHeadImgUrl();
            Integer sex = weChatRequest.getSex();

            //Integer eventKey = WXConstant.OPENID_EVENT_CODE_MAP.get(openId);
            //WarnCode warnCode = WXConstant.WAIT_BIND_MAP.get(eventKey);
            if (eventKey == null) {
                return "请刷新二维码重新扫码";
            }
            WxOAuth2UserInfo userInfo = new WxOAuth2UserInfo();
            userInfo.setNickname(nickName);
            userInfo.setOpenid(openId);
            userInfo.setHeadImgUrl(headImgUrl);
            userInfo.setSex(sex);
            //WXConstant.WX_USER_MAP.put(eventKey, userInfo);
            WXUtil.wxUserPut(eventKey, userInfo);
            return "获取成功";
        } catch (Exception e) {
            return "请刷新二维码重新扫码";
        } finally {
            if (CharSequenceUtil.isNotBlank(openId)) {
                //WXConstant.OPENID_EVENT_CODE_MAP.remove(openId);
                WXUtil.openEventCodeRemove(openId);
            }
            if (eventKey != null) {
                //WXConstant.WAIT_BIND_MAP.remove(eventKey);
                WXUtil.waitBandRemove(eventKey);
            }
        }
    }
}
