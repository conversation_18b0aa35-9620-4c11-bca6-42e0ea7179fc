package com.boryou.web.module.report.entity.vo;

import cn.hutool.json.JSONObject;
import com.boryou.web.module.report.entity.Report;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;

/**
 * <AUTHOR>
 */
@EqualsAndHashCode(callSuper = true)
@Data
public class ReportVO extends Report {

    /**
     * 系统1 上传2
     */
    private Integer isFile;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date endTime;

    private JSONObject data;

    private String tempName;

    private String fileType;

    private Long deptId;
}
