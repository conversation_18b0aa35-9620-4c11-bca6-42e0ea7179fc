package com.boryou.web.module.search.entity.vo;

import com.boryou.web.module.search.entity.EsSpecialData;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.util.Date;


/**
 * <AUTHOR>
 */
@Data
@EqualsAndHashCode(callSuper = true)
public class EsSpecialDataVO extends EsSpecialData {

    //条件
    private Integer specialType;
    private String content;
    private Integer isRead;
    private String type;
    private String startTime;
    private String endTime;
    private Long deptId;
    //报送
    private Date deadLine;
    private String processStatus;
    private String userName;
    private String phone;


    private int pageSize = 10;
    private int pageNum = 1;

    //信息
    private String title;
    private String text;
    private String typeName;
    private String author;
    private String hostName;
    private Boolean isOriginal;
    private String url;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;
}
