package com.boryou.web.module.mail.service;

import com.boryou.common.exception.CustomException;
import com.boryou.web.module.mail.config.MailSenderConfig;
import com.boryou.web.module.mail.domain.vo.MailVO;
import lombok.AllArgsConstructor;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.mail.javamail.JavaMailSenderImpl;
import org.springframework.mail.javamail.MimeMessageHelper;
import org.springframework.stereotype.Service;

import javax.mail.internet.MimeMessage;
import java.util.Objects;

/**
 * <AUTHOR>
 */
@Service
@AllArgsConstructor
public class MailService {
    private static final Logger log = LoggerFactory.getLogger(MailService.class);

    private final MailSenderConfig senderConfig;

    public boolean sendMail(Integer i, String to, String subject, String content) {
        JavaMailSenderImpl mailSender = senderConfig.getSender(i);

        try {
            MimeMessage mimeMessage = mailSender.createMimeMessage();
            MimeMessageHelper messageHelper = new MimeMessageHelper(mimeMessage, true, "utf-8");

            messageHelper.setFrom(Objects.requireNonNull(mailSender.getUsername()));
            messageHelper.setTo(to);
            messageHelper.setSubject(subject);
            // 发送html邮件
            messageHelper.setText(content, true);

            mailSender.send(mimeMessage);
            return true;
        } catch (Exception e) {
            e.printStackTrace();
            return false;
        }
    }

    public void sendWarnMail(String to, String username, String ruleName, String title, int totalNum, String url) {
        String content = "<table style='width: 100%;padding: 5%;'>\n" +
                "                            <tbody>\n" +
                "                            <tr>\n" +
                "                                <td style='padding: 20px;'>\n" +
                "                                    <table border='0' cellspacing='0' cellpadding='0'\n" +
                "                                           style='font-size: 16px;width: 100%;padding: 10px;background-color: #F2F5FC;'>\n" +
                "                                        <tbody>\n" +
                " <tr>\n" +
                "                                            <td><p>尊敬的用户：" + username + "</p>\n" +
                "                                                <p>【" + ruleName + "】方案: 新增" + totalNum + "条预警信息</p></td>\n" +
                "                                        </tr>" +
                "                                        </tbody>\n" +
                "                                    </table>\n" +
                "                                </td>\n" +
                "                            </tr>\n" +
                " <tr>\n" +
                "                                <td style='padding: 0 20px 20px 20px;'><p>详细信息请点击查看：</p>\n" +
                "                            </tr>" +
                "                            <tr>\n" +
                "                                <td align='center' style='padding: 20px;'><a \n" +
                "                                                                             href='" + url + "'\n" +
                "                                                                             style='background-color: lavender;width: 120px;height: 36px;text-align: center;line-height: 36px;display: inline-block;text-decoration: none;border-radius: 2px;'>查看详情</a>\n" +
                "                                </td>\n" +
                "                            </tr>\n" +
                "<tr><td><h4>-- 此邮件由舆情系统自动发出，请勿直接回复 --</h4></td></tr>" +
                "                            </tbody>\n" +
                "                        </table>";

        boolean sendOK = this.sendMail(null, to, title, content);
        if (!sendOK) {
            log.error("邮件发送失败, 接收人: {}", to);
        }
    }

    public boolean sendTestMail(MailVO mailVO) {
        String toEmail = mailVO.getToEmail();
        boolean sendOK = this.sendMail(null, toEmail, "测试邮件", "测试邮件");
        if (!sendOK) {
            throw new CustomException("验证失败!");
        }
        return true;
    }

}
