package com.boryou.web.module.socket.enums;

import lombok.Getter;

/**
 * <AUTHOR>
 * @since 2023/9/6 10:24
 */
@Getter
public enum SocketChannelEnum {

    DEFAULTS("default", "默认channel", "push_or_receive"),
    CLOSE("close", "断开连接", "push"),
    DRILL_COMMENT("DRILL_COMMENT", "评论或指令", "push"),
    DRILL_COMMENT_REPLY("DRILL_COMMENT_REPLY", "回复指令", "push"),
    DRILL_COMMENT_LIKE("DRILL_COMMENT_LIKE", "点赞", "push"),
    DRILL_STAGE("DRILL_STAGE", "下一阶段", "push"),
    DRILL_TIME("DRILL_TIME", "倒计时", "push"),
    DRILL_TIME_SCORE("DRILL_TIME_SCORE", "倒计时结束显示的分数", "push"),
    DRILL_DANMU_SCORE("DRILL_DANMU_SCORE", "弹幕", "push"),
    PING("PING", "前端心跳", "push"),
    PONG("PONG", "回复心跳", "push"),
    ;

    /**
     * 字符串标识
     */
    private final String name;

    /**
     * 备注
     */
    private final String remark;

    /**
     * 状态：push 发送、1 receive、2 push_or_receive
     */
    private final String status;

    SocketChannelEnum(String name, String remark, String status) {
        this.name = name;
        this.remark = remark;
        this.status = status;
    }
}
