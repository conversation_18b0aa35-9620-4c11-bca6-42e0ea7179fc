package com.boryou.web.module.source.mapper;


import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.web.domain.Plan;
import com.boryou.web.module.source.entity.SourceSetting;
import com.boryou.web.module.source.vo.SourceSettingVO;

import java.util.List;
import java.util.Map;

/**
 * 信源设置dao层
 *
 * <AUTHOR>
 * @date 2017-6-9 下午2:25:32
 */
public interface SourceSettingMapper extends BaseMapper<SourceSetting> {

    /**
     * 根据筛选条件获得信源设置
     *
     * @param map
     * @return java.util.List<com.boryou.yuqing.entity.SourceSetting>
     * <AUTHOR>
     * @date 2021-09-02 17:38:44
     */
    List<SourceSetting> getSourceSettingList(SourceSettingVO source);

    /**
     * 根据筛选条件获得信源设置数量
     *
     * @param map
     * @return java.util.List<com.boryou.yuqing.entity.SourceSetting>
     * <AUTHOR>
     * @date 2021-09-02 17:38:44
     */
    int getSourceSettingListCount(Map<String, Object> map);

    /**
     * 根据筛选条件获得信源设置
     *
     * @param map
     * @return java.util.List<com.boryou.yuqing.entity.WarningRule>
     * <AUTHOR>
     * @date 2021-09-01 16:46:46
     */
    List<SourceSetting> getSourceSetting(Map<String, Object> map);


    /**
     * 根据板块id删除信源设置
     *
     * @return int
     * <AUTHOR>
     * @date 2021-09-01 17:03:59
     */
    int deleteSourceSetting(SourceSetting sourceSetting);

    /**
     * 添加信源设置
     *
     * @param sourceSetting
     * @return void
     * <AUTHOR>
     * @date 2021-09-01 17:04:09
     */
    void addSourceSetting(SourceSetting sourceSetting);

    /**
     * 更新信源设置
     *
     * @param sourceSetting
     * @return void
     * <AUTHOR>
     * @date 2021-09-01 17:04:33
     */
    void updateSourceSetting(SourceSetting sourceSetting);

    /**
     * 更新信源设置状态
     *
     * @param map 多个id
     * @return void
     * <AUTHOR>
     * @date 2021-09-01 17:04:52
     */
    void updateSourceSettingState(Map<String, Object> map);

    /**
     * 根据主键id删除信源设置
     *
     * @param id
     * @return void
     * <AUTHOR>
     * @date 2021-09-01 17:05:14
     */
    void delSourceSetting(String id);

}
