package com.boryou.web.module.notice.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.module.notice.domain.ByNotice;

import java.util.List;


/**
 * 站内信Service接口
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
public interface IByNoticeService extends IService<ByNotice> {
    /**
     * 查询站内信
     *
     * @param noticeId 站内信ID
     * @return 站内信
     */
    ByNotice selectByNoticeById(Long noticeId);

    /**
     * 查询站内信列表
     *
     * @param byNotice 站内信
     * @return 站内信集合
     */
    List<ByNotice> selectByNoticeList(ByNotice byNotice);

    /**
     * 新增站内信
     *
     * @param byNotice 站内信
     * @return 结果
     */
    int insertByNotice(ByNotice byNotice);

    /**
     * 修改站内信
     *
     * @param byNotice 站内信
     * @return 结果
     */
    int updateByNotice(ByNotice byNotice);

    /**
     * 批量删除站内信
     *
     * @param noticeIds 需要删除的站内信ID
     * @return 结果
     */
    int deleteByNoticeByIds(Long[] noticeIds);

    /**
     * 删除站内信信息
     *
     * @param noticeId 站内信ID
     * @return 结果
     */
    int deleteByNoticeById(Long noticeId);


    boolean saveSiteNotice(ByNotice byNotice);

    boolean saveWarnNotice(ByNotice byNotice, List<String> users);

    void insertByNoticeRela(ByNotice byNotice);

    int latest(ByNotice byNotice);
}
