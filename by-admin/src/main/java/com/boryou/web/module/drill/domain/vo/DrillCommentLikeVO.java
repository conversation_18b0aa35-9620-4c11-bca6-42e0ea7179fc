package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;
import java.util.Date;

/**
 * 评论点赞表实体
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DrillCommentLikeVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long commentLikeId;

    /**
     * 阶段ID（主键第二部分）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "processStageId 不能为空")
    private Long processStageId;

    /**
     * 关联演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "drillTaskId 不能为空")
    private Long drillTaskId;

    /**
     * 所属微博id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "commentId 不能为空")
    private Long commentId;

    /**
     * 被赞评论ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "commentReplyId 不能为空")
    private Long commentReplyId;

    /**
     * 2: 文章 7:回复评论
     */
    @NotNull(message = "commentType 不能为空")
    private Integer commentType;

    /**
     * 点赞用户ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 点赞时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 状态（0=取消，1=有效）
     */
    private Integer status;

    private Integer teamType;

    private String roleInfo;

    private String comment;

}
