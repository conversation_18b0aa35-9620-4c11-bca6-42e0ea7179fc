package com.boryou.web.module.home.service;

import com.boryou.web.controller.common.entity.bo.AreaOverviewBO;
import com.boryou.web.controller.common.entity.vo.HomeVO;
import com.boryou.web.domain.Hot;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.HotVO;
import com.boryou.web.module.area.entity.Area;
import com.boryou.web.module.home.entity.bo.LawyerAuthorBO;
import com.boryou.web.module.home.entity.bo.LawyerBO;
import com.boryou.web.module.home.entity.vo.SimpleVO;

import java.io.IOException;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-05-23 09:52
 */
public interface HomeStatisService {

    com.alibaba.fastjson.JSONArray getEmontionStatis(HomeVO homeVO, boolean flag);

    List<LawyerBO> getLawyerMoment(HomeVO homeVO, boolean flag);

    List<LawyerAuthorBO.Author> getLawyer(List<LawyerBO> lawyerList);

    List<Hot> hotZJ(HomeVO homeVO, boolean b);

    List<Hot> warn(HomeVO homeVO, boolean b);

    List<Map<String, Object>> getWordCloud(HomeVO homeVO, boolean flag) throws IOException;

    Long total(HomeVO homeVO, boolean flag);

    Map<String, Object> curveStatis(HomeVO homeVO, boolean flag);

    List<Map> authorLineChat(HomeVO homeVO, boolean flag);

    List<AreaOverviewBO> areaOverview(HomeVO homeVO, boolean flag);

    List<SimpleVO> getMainPlan(HomeVO homeVO, boolean flag);

    List<SimpleVO> getProvinceHotPlan(HomeVO homeVO, boolean flag);

    List<SimpleVO> getCountryHotPlan(HomeVO homeVO, boolean flag);

    Object getCacheValue(HomeVO homeVO, String key, String key1, String timeType);

    List<SimpleVO> casePaln(HomeVO homeVO, boolean b);

    List<Plan> planTypeList(HomeVO homeVO);

    String getAreaCode();

    List<String> getAreaCodeList();

    List<Area> getAllArea();

    List<Long> getAllDeptIdList();

    List<Hot> hotZJMore(HotVO hotVO);
}

