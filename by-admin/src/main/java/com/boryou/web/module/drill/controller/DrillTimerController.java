package com.boryou.web.module.drill.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.drill.domain.vo.DrillStageTimerVO;
import com.boryou.web.module.drill.service.DrillService;
import com.boryou.web.module.drill.service.DrillTimerService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

/**
 * 倒计时控制接口
 */
@Slf4j
@RestController
@RequiredArgsConstructor
public class DrillTimerController {

    private final DrillService drillService;
    private final DrillTimerService drillTimerService;

    /**
     * 开始倒计时
     */
    @PostMapping("/drill/timer/start")
    public AjaxResult drillTimerStart(@RequestBody @Valid DrillStageTimerVO drillStageTimerVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        drillService.drillTimerStart(drillStageTimerVO, user);
        return AjaxResult.success();
    }

    /**
     * 暂停倒计时
     */
    @PostMapping("/drill/timer/pause")
    public AjaxResult drillTimerPause(@RequestBody @Valid DrillStageTimerVO drillStageTimerVO) {
        Long drillStageTimeId = drillStageTimerVO.getDrillStageTimeId();
        drillTimerService.pauseTimer(drillStageTimeId);
        return AjaxResult.success();
    }

    /**
     * 恢复倒计时
     */
    @PostMapping("/drill/timer/resume")
    public AjaxResult drillTimerResume(@RequestBody @Valid DrillStageTimerVO drillStageTimerVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        drillTimerService.resumeTimer(drillStageTimerVO, user, drillService);
        return AjaxResult.success();
    }

    /**
     * 结束倒计时
     */
    @PostMapping("/drill/timer/end")
    public AjaxResult drillTimerEnd(@RequestBody @Valid DrillStageTimerVO drillStageTimerVO) {
        Long drillStageTimeId = drillStageTimerVO.getDrillStageTimeId();
        drillTimerService.endTimer(drillStageTimeId);
        return AjaxResult.success();
    }
}
