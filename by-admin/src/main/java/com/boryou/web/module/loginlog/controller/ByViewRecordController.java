package com.boryou.web.module.loginlog.controller;

import com.boryou.common.annotation.Log;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.utils.poi.ExcelUtil;
import com.boryou.web.module.loginlog.domain.ByViewRecord;
import com.boryou.web.module.loginlog.service.IByViewRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * h5登录日志查看记录Controller
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@RestController
@RequestMapping("/loginlog")
public class ByViewRecordController extends BaseController {
    @Autowired
    private IByViewRecordService byViewRecordService;

    /**
     * 查询h5登录日志查看记录列表
     */
    @GetMapping("/list")
    public TableDataInfo list(ByViewRecord byViewRecord) {
        startPage();
        List<ByViewRecord> list = byViewRecordService.selectByViewRecordList(byViewRecord);
        return getDataTable(list);
    }

    /**
     * 导出h5登录日志查看记录列表
     */
    @Log(title = "登录日志查看记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(ByViewRecord byViewRecord) {
        List<ByViewRecord> list = byViewRecordService.selectByViewRecordList(byViewRecord);
        ExcelUtil<ByViewRecord> util = new ExcelUtil<ByViewRecord>(ByViewRecord.class);
        return util.exportExcel(list, "loginlog");
    }

    /**
     * 获取h5登录日志查看记录详细信息
     */
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id) {
        return AjaxResult.success(byViewRecordService.selectByViewRecordById(id));
    }

    /**
     * 新增h5登录日志查看记录
     */
    @Log(title = "h5登录日志查看记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody ByViewRecord byViewRecord) {
        return toAjax(byViewRecordService.insertByViewRecord(byViewRecord));
    }

    /**
     * 修改h5登录日志查看记录
     */
    @Log(title = "h5登录日志查看记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody ByViewRecord byViewRecord) {
        return toAjax(byViewRecordService.updateByViewRecord(byViewRecord));
    }

    /**
     * 删除h5登录日志查看记录
     */
    @Log(title = "h5登录日志查看记录", businessType = BusinessType.DELETE)
    @DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids) {
        return toAjax(byViewRecordService.deleteByViewRecordByIds(ids));
    }
}
