package com.boryou.web.module.drill.enums;

import cn.hutool.core.collection.CollUtil;
import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Getter
@RequiredArgsConstructor
public enum CommentEnum {

    HOT_RANK("热搜榜", "1"),
    HOT_TOPIC("爆料贴文", "2"),
    NORMAL_COMMENT("普通评论", "3"),
    SITUATION_COMMENT("情况通报", "4"),
    POLICE_COMMENT("分析报告", "5"),
    COMMAND("指令", "6"),
    COMMENT_REPLY("回复评论", "7"),
    EXPERT_COMMENT("专家点评", "8"),
    ;

    private static final Map<String, CommentEnum> map = new HashMap<>();

    static {
        CommentEnum[] ens = CommentEnum.values();
        for (CommentEnum en : ens) {
            map.put(en.getType(), en);
        }
    }

    private final String name;
    private final String type;

    public static CommentEnum getEnumByType(String type) {
        return map.get(type);
    }

    public static Set<String> getScoreType() {
        return CollUtil.newHashSet(CommentEnum.HOT_RANK.getType(),
                CommentEnum.HOT_TOPIC.getType(),
                CommentEnum.NORMAL_COMMENT.getType(),
                CommentEnum.SITUATION_COMMENT.getType(),
                CommentEnum.POLICE_COMMENT.getType(),
                CommentEnum.HOT_TOPIC.getType()
        );
    }

}
