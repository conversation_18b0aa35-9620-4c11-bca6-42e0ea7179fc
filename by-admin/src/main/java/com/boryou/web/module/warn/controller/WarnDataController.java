package com.boryou.web.module.warn.controller;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONUtil;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.common.utils.poi.ExcelUtil;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.enums.EmotionEnum;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.module.warn.domain.vo.WarnDataVO;
import com.boryou.web.module.warn.domain.vo.WarnExportVO;
import com.boryou.web.module.warn.domain.vo.WarnSMVO;
import com.boryou.web.module.warn.service.WarnDataService;
import com.boryou.web.service.ElasticsearchService;
import com.boryou.web.util.SMUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Slf4j
@RestController
public class WarnDataController extends BaseController {

    @Resource
    private WarnDataService warnDataService;
    @Resource
    private ElasticsearchService elasticsearchService;

    @PostMapping("/warn/data/get")
    public TableDataInfo warnDataGet(@RequestBody WarnDataVO warnDataVO) {
        PageResult<EsBean> pageResult = warnDataService.warnDataGet(warnDataVO);
        TableDataInfo rspData = new TableDataInfo(pageResult, pageResult.getTotal());
        rspData.setCode(200);
        return rspData;
    }

    @PostMapping("/warn/data/push/get")
    public TableDataInfo warnDataPushGet(@RequestBody WarnDataVO warnDataVO) {
        warnDataService.buildPushString(warnDataVO);
        PageResult<EsBean> pageResult = warnDataService.warnDataGet(warnDataVO);
        TableDataInfo rspData = new TableDataInfo(pageResult, pageResult.getTotal());
        rspData.setCode(200);
        return rspData;
    }

    @PostMapping("/warn/data/sm/build")
    public AjaxResult warnDataSmBuild(@RequestBody WarnSMVO warnSMVO) {
        String s = SMUtil.encryptHex(JSONUtil.toJsonStr(warnSMVO));
        return AjaxResult.success("操作成功", s);
    }

    @PostMapping("/warn/data/export")
    public AjaxResult warnDataExport(@RequestBody WarnDataVO warnDataVO) {
        warnDataService.judgeIds(warnDataVO);
        PageResult<EsBean> pageResult = warnDataService.warnDataGet(warnDataVO);
        List<WarnExportVO> esBeanExportVOList = new ArrayList<>();
        List<String> host = pageResult.stream().map(EsBean::getHost).filter(s -> s != null && !s.isEmpty()).collect(Collectors.toList());
        Map<String, String> hostAndNameMap = elasticsearchService.getDomainMap(host);
        for (EsBean bean : pageResult) {
            WarnExportVO esBeanExportVO = new WarnExportVO();
            BeanUtil.copyProperties(bean, esBeanExportVO, "siteMeta", "emotionFlag", "isOriginal", "isSpam");
            if (CollUtil.isNotEmpty(bean.getSiteMeta())) {
                esBeanExportVO.setSiteMeta(CollUtil.join(bean.getSiteMeta(), ","));
            }
            esBeanExportVO.setEmotionFlag(EmotionEnum.fromValue(bean.getEmotionFlag()).getName());
            esBeanExportVO.setIsOriginal(bean.getIsOriginal() ? "是" : "否");
            esBeanExportVO.setIsSpam(bean.getIsSpam() ? "是" : "否");
            //站点名称
            if (hostAndNameMap.containsKey(bean.getHost())) {
                //双微显示作者
                if (bean.getType() == MediaTypeEnum.WECHAT.getValue() || bean.getType() == MediaTypeEnum.WEIBO.getValue()) {
                    esBeanExportVO.setHost(bean.getAuthor());
                } else {
                    esBeanExportVO.setHost(hostAndNameMap.get(bean.getHost()));
                }
            }
            esBeanExportVOList.add(esBeanExportVO);
        }
        ExcelUtil<WarnExportVO> util = new ExcelUtil<>(WarnExportVO.class);
        return util.exportExcel(esBeanExportVOList, "导出列表");
    }

    @PostMapping("/warn/data/material/add")
    public AjaxResult addMaterial(@RequestBody WarnDataVO warnDataVO) {
        warnDataService.judgeIds(warnDataVO);
        PageResult<EsBean> pageResult = warnDataService.warnDataGet(warnDataVO);
        return toAjax(warnDataService.addMaterial(pageResult, warnDataVO));
    }

}
