package com.boryou.web.module.update.util;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;

/**
 * 简化的通用更新工具类
 * 提供最常用的字段比较和更新功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class SimpleUpdateUtil {

    private SimpleUpdateUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 字段更新器接口
     */
    @FunctionalInterface
    public interface FieldUpdater<T> {
        void update(LambdaUpdateChainWrapper<T> wrapper, Object value);
    }

    /**
     * 通用字段比较和更新（使用LambdaUpdateChainWrapper）
     * 
     * @param sourceData 源数据对象
     * @param targetData 目标数据对象  
     * @param chainWrapper 链式更新包装器
     * @param fieldUpdaters 字段更新器映射 (字段名 -> 更新器)
     * @param excludeFields 排除的字段
     * @return 是否有字段需要更新
     */
    public static <T> boolean compareAndUpdate(Object sourceData, Object targetData,
                                             LambdaUpdateChainWrapper<T> chainWrapper,
                                             Map<String, FieldUpdater<T>> fieldUpdaters,
                                             String... excludeFields) {
        return compareAndUpdate(sourceData, targetData, chainWrapper, fieldUpdaters, 
                              Collections.emptyMap(), excludeFields);
    }

    /**
     * 通用字段比较和更新（支持字段映射）
     * 
     * @param sourceData 源数据对象
     * @param targetData 目标数据对象
     * @param chainWrapper 链式更新包装器
     * @param fieldUpdaters 字段更新器映射 (字段名 -> 更新器)
     * @param fieldMappings 字段映射 (源字段名 -> 目标字段名)
     * @param excludeFields 排除的字段
     * @return 是否有字段需要更新
     */
    public static <T> boolean compareAndUpdate(Object sourceData, Object targetData,
                                             LambdaUpdateChainWrapper<T> chainWrapper,
                                             Map<String, FieldUpdater<T>> fieldUpdaters,
                                             Map<String, String> fieldMappings,
                                             String... excludeFields) {
        if (sourceData == null || targetData == null) {
            return false;
        }

        Set<String> excludeSet = new HashSet<>(Arrays.asList(excludeFields));
        boolean hasUpdate = false;

        try {
            // 获取源对象和目标对象的所有字段
            Field[] sourceFields = getAllFields(sourceData.getClass());
            Field[] targetFields = getAllFields(targetData.getClass());
            
            // 创建目标字段映射
            Map<String, Field> targetFieldMap = new HashMap<>();
            for (Field field : targetFields) {
                targetFieldMap.put(field.getName(), field);
            }

            // 遍历源对象字段
            for (Field sourceField : sourceFields) {
                String sourceFieldName = sourceField.getName();
                
                // 跳过排除的字段
                if (excludeSet.contains(sourceFieldName)) {
                    continue;
                }

                // 获取目标字段名（考虑字段映射）
                String targetFieldName = fieldMappings.getOrDefault(sourceFieldName, sourceFieldName);
                
                // 获取目标字段
                Field targetField = targetFieldMap.get(targetFieldName);
                if (targetField == null) {
                    continue;
                }

                // 获取字段值
                Object sourceValue = getFieldValue(sourceData, sourceField);
                Object targetValue = getFieldValue(targetData, targetField);

                // 判断是否需要更新
                if (shouldUpdate(sourceValue, targetValue)) {
                    // 获取字段更新器
                    FieldUpdater<T> updater = fieldUpdaters.get(targetFieldName);
                    if (updater != null) {
                        updater.update(chainWrapper, sourceValue);
                        hasUpdate = true;
                        log.debug("更新字段: {} = {}", targetFieldName, sourceValue);
                    } else {
                        log.warn("字段 {} 没有配置对应的更新器，跳过更新", targetFieldName);
                    }
                }
            }

        } catch (Exception e) {
            log.error("字段比较更新过程中发生错误", e);
        }

        return hasUpdate;
    }

    /**
     * 使用Lambda表达式的简化更新方法
     * 
     * @param sourceData 源数据对象
     * @param targetData 目标数据对象
     * @param chainWrapper 链式更新包装器
     * @param fieldSetters 字段Lambda表达式映射 (字段名 -> Lambda表达式)
     * @param excludeFields 排除的字段
     * @return 是否有字段需要更新
     */
    public static <T> boolean compareAndUpdateWithLambda(Object sourceData, Object targetData,
                                                       LambdaUpdateChainWrapper<T> chainWrapper,
                                                       Map<String, SFunction<T, ?>> fieldSetters,
                                                       String... excludeFields) {
        // 将Lambda表达式转换为FieldUpdater
        Map<String, FieldUpdater<T>> fieldUpdaters = new HashMap<>();
        fieldSetters.forEach((fieldName, setter) -> {
            fieldUpdaters.put(fieldName, (wrapper, value) -> {
                @SuppressWarnings("unchecked")
                SFunction<T, Object> objectSetter = (SFunction<T, Object>) setter;
                wrapper.set(objectSetter, value);
            });
        });

        return compareAndUpdate(sourceData, targetData, chainWrapper, fieldUpdaters, excludeFields);
    }

    /**
     * 创建字段更新器的便捷方法
     */
    public static <T, V> FieldUpdater<T> createUpdater(SFunction<T, V> setter) {
        return (wrapper, value) -> {
            @SuppressWarnings("unchecked")
            V typedValue = (V) value;
            wrapper.set(setter, typedValue);
        };
    }

    /**
     * 批量创建字段更新器
     */
    @SafeVarargs
    public static <T> Map<String, FieldUpdater<T>> createUpdaters(FieldMapping<T>... mappings) {
        Map<String, FieldUpdater<T>> updaters = new HashMap<>();
        for (FieldMapping<T> mapping : mappings) {
            updaters.put(mapping.fieldName, createUpdater(mapping.setter));
        }
        return updaters;
    }

    /**
     * 字段映射辅助类
     */
    public static class FieldMapping<T> {
        public final String fieldName;
        public final SFunction<T, ?> setter;

        public FieldMapping(String fieldName, SFunction<T, ?> setter) {
            this.fieldName = fieldName;
            this.setter = setter;
        }

        public static <T, V> FieldMapping<T> of(String fieldName, SFunction<T, V> setter) {
            return new FieldMapping<>(fieldName, setter);
        }
    }

    /**
     * 获取对象的所有字段（包括父类字段）
     */
    private static Field[] getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;

        while (currentClass != null && currentClass != Object.class) {
            Field[] declaredFields = currentClass.getDeclaredFields();
            Collections.addAll(fields, declaredFields);
            currentClass = currentClass.getSuperclass();
        }

        return fields.toArray(new Field[0]);
    }

    /**
     * 获取字段值
     */
    private static Object getFieldValue(Object obj, Field field) {
        try {
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            log.warn("获取字段值失败: {}.{}", obj.getClass().getSimpleName(), field.getName(), e);
            return null;
        }
    }

    /**
     * 判断是否需要更新字段
     */
    private static boolean shouldUpdate(Object sourceValue, Object targetValue) {
        // 如果源值为空，则不更新
        if (sourceValue == null) {
            return false;
        }

        // 如果源值为空字符串，则不更新
        if (sourceValue instanceof String && CharSequenceUtil.isBlank((String) sourceValue)) {
            return false;
        }

        // 比较值是否不同
        return !Objects.equals(sourceValue, targetValue);
    }

    // ==================== 便捷方法 ====================

    /**
     * 字符串字段更新
     */
    public static <T> boolean updateStringField(String newValue, String currentValue, 
                                              LambdaUpdateChainWrapper<T> wrapper, 
                                              SFunction<T, String> setter) {
        if (CharSequenceUtil.isNotBlank(newValue) && !Objects.equals(newValue, currentValue)) {
            wrapper.set(setter, newValue);
            return true;
        }
        return false;
    }

    /**
     * 通用字段更新
     */
    public static <T, V> boolean updateField(V newValue, V currentValue, 
                                           LambdaUpdateChainWrapper<T> wrapper, 
                                           SFunction<T, V> setter) {
        if (newValue != null && !Objects.equals(newValue, currentValue)) {
            wrapper.set(setter, newValue);
            return true;
        }
        return false;
    }

    /**
     * 批量字段更新
     */
    public static <T> boolean updateFields(LambdaUpdateChainWrapper<T> wrapper, 
                                         FieldUpdate<T>... updates) {
        boolean hasUpdate = false;
        for (FieldUpdate<T> update : updates) {
            if (update.execute(wrapper)) {
                hasUpdate = true;
            }
        }
        return hasUpdate;
    }

    /**
     * 字段更新辅助类
     */
    @FunctionalInterface
    public interface FieldUpdate<T> {
        boolean execute(LambdaUpdateChainWrapper<T> wrapper);
        
        static <T, V> FieldUpdate<T> of(V newValue, V currentValue, SFunction<T, V> setter) {
            return wrapper -> updateField(newValue, currentValue, wrapper, setter);
        }
        
        static <T> FieldUpdate<T> ofString(String newValue, String currentValue, SFunction<T, String> setter) {
            return wrapper -> updateStringField(newValue, currentValue, wrapper, setter);
        }
    }
}
