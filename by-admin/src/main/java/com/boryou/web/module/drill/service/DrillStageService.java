package com.boryou.web.module.drill.service;

import cn.hutool.core.collection.CollUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.exception.CustomException;
import com.boryou.web.module.drill.domain.DrillStage;
import com.boryou.web.module.drill.mapper.DrillStageMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrillStageService extends ServiceImpl<DrillStageMapper, DrillStage> {

    public List<DrillStage> getStageWithThrow() {
        List<DrillStage> drillStageList = this.lambdaQuery()
                .eq(DrillStage::getType, 1)
                .eq(DrillStage::getDelFlag, 0)
                .orderByAsc(DrillStage::getStageOrder)
                .list();
        if (CollUtil.isEmpty(drillStageList)) {
            throw new CustomException("阶段未配置");
        }
        return drillStageList;
    }

}
