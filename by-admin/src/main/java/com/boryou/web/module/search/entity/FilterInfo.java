package com.boryou.web.module.search.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@TableName("by_filter_info")
@Data
public class FilterInfo {
    @TableField(exist = false)
    private List<String> ids;

    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.NONE)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long planId;

    @TableField(exist = false)
    private String planIds;

    @TableField(exist = false)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long typeId;

    /**
     * 方案名称
     */
    @TableField(exist = false)
    private String planName;

    /**
     * 过滤时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date filterTime;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long indexId;

    private Integer type;

    /**
     * 媒体名称
     */
    private String typeName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    private String title;

    private String text;

    private String url;

    private String host;

    private String author;

    private String authorId;

    private Integer accountLevel;

    private String siteAreaCodeName;

    private Integer emotionFlag;

    private Boolean isOriginal;

    /**
     * 命中词
     */
    private String hitWords;

    private String md5;

    /**
     * 是否已读 0未读 1已读
     */
    @TableField(exist = false)
    private Integer isRead;

    @TableField(exist = false)
    private Integer pageNum = 1;

    @TableField(exist = false)
    private Integer size = 10;
}
