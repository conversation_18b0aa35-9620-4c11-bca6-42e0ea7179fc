package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;


@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class DrillProcessStageVO {
    /**
     * 阶段ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processStageId;

    /**
     * 关联演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "演练任务id不能为空")
    private Long drillTaskId;

    /**
     * 阶段名称
     */
    // @NotBlank(message = "阶段名称不能为空")
    private String stageName;

    /**
     * 倒计时时长(秒)
     */
    private Integer timerDuration;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long previousStageId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long currentStageId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long nextStageId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long newPreviousStageId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long newCurrentStageId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long newNextStageId;

    private DrillProcessStageDTO newCurrentStage;

}
