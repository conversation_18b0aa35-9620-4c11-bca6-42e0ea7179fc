package com.boryou.web.module.mark.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字段映射注解
 * 用于标记两个不同实体类中需要进行比较的字段映射关系
 * 
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldMapping {
    
    /**
     * 目标字段名
     * 指定在目标实体类中对应的字段名
     */
    String targetField();
    
    /**
     * 目标实体类
     * 指定目标实体类的Class类型
     */
    Class<?> targetClass() default Object.class;
    
    /**
     * 是否忽略大小写
     * 在字符串比较时是否忽略大小写
     */
    boolean ignoreCase() default false;
    
    /**
     * 自定义比较器类
     * 可以指定自定义的比较器来处理特殊的比较逻辑
     */
    Class<?> comparator() default Object.class;
}
