package com.boryou.web.module.api.enums;

import java.util.HashMap;
import java.util.Map;

public enum AuthEnum {
    QINGHAI("eb30e51879f6af3b1e2a2c", "青海cdc");

    private static final Map<String, AuthEnum> categoryMap = new HashMap<>();

    static {
        AuthEnum[] ens = AuthEnum.values();
        for (AuthEnum en : ens) {
            categoryMap.put(en.getAuth(), en);
        }
    }

    private final String auth;
    private final String info;

    AuthEnum(String auth, String info) {
        this.auth = auth;
        this.info = info;
    }

    public static AuthEnum getEnumByAuth(String auth) {
        return categoryMap.get(auth);
    }

    public String getAuth() {
        return auth;
    }

    public String getInfo() {
        return info;
    }
}
