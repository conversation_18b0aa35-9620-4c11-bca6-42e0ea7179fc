package com.boryou.web.module.warn.controller;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.module.warn.domain.vo.WarnReadVO;
import com.boryou.web.module.warn.service.WarnReadService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequiredArgsConstructor
public class WarnReadController extends BaseController {

    private final WarnReadService warnReadService;

    @PostMapping("/WarnRead/add")
    public AjaxResult addWarnRead(@RequestBody WarnReadVO warnReadVO) {
        boolean b = warnReadService.addWarnRead(warnReadVO);
        return toAjax(b);
    }
}
