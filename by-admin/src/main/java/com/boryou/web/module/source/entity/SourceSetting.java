package com.boryou.web.module.source.entity;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;
import lombok.ToString;

import java.util.Date;

/**
 * 信源设置
 *
 * <AUTHOR>
 * @date 2021-09-01 15:07:13
 */
@Data
@TableName("t_source_setting")
public class SourceSetting /*extends BaseEntity*/ {
    @JsonSerialize(using = ToStringSerializer.class)
    @TableField("id")
    private Long id; // 主键ID

    @Excel(name = "名称")
    @TableField("name")
    private String name; // 信源名称

    @Excel(name = "类别", dictType = "sys_media_type")
    @TableField("sourceType")
    private String sourceType; // 信源类型 0 论坛  1 新闻  2 博客 3 微博 5微信  6 客户端  8 广播  9 电视 17 电子报

    @TableField("settingType")
    private String settingType; // 设置类型  1定向选择 2定向排除

    @Excel(name = "网址")
    @TableField("settingHost")
    private String settingHost; // 信源站点

    @TableField("userId")
    private String userId; // 用户ID

    @Excel(name = "状态", readConverterExp = "0=禁用,1=启用", type = Excel.Type.EXPORT)
    @TableField("state")
    private int state = 1; // 状态 0禁用 1启用

    @TableField("createTime")
    private Date createTime; // 添加时间

    @TableField("updateTime")
    private Date updateTime; // 更新时间

    @TableField("plateId")
    private Long plateId; // 关联的板块方案id


    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) return false;
        SourceSetting sourceSetting = (SourceSetting) o;
        return this.getSettingHost().equals(sourceSetting.getSettingHost()) && this.getName().equals(sourceSetting.getName()) && this.getSourceType().equals(sourceSetting.getSourceType())
                && this.getPlateId().equals(sourceSetting.getPlateId()) && this.getUserId().equals(sourceSetting.getUserId());
    }
}
