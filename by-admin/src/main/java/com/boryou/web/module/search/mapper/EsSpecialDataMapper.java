package com.boryou.web.module.search.mapper;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.boryou.web.module.search.entity.EsLikeData;
import com.boryou.web.module.search.entity.EsSpecialData;
import com.boryou.web.module.search.entity.IndexData;
import com.boryou.web.module.search.entity.vo.EsSpecialDataVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EsSpecialDataMapper extends BaseMapper<EsSpecialData> {
    int insertEsLikeData(EsLikeData data);

    List<EsLikeData> selectEsLikeData(EsLikeData data);

    List<EsSpecialDataVO> getEsSpecialDataPage(EsSpecialDataVO esSpecialDataVO);

    int insertIndexData(IndexData data);

    List<IndexData> selectIndexDataById(@Param("indexId") Long indexId);

    int updateIndexDataEmotionFlag(IndexData data);
}
