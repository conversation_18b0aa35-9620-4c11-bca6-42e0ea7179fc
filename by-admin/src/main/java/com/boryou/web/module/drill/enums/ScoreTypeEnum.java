package com.boryou.web.module.drill.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Getter
@RequiredArgsConstructor
public enum ScoreTypeEnum {

    SCORE_NO_SHOW("不显示得分", 1),
    SCORE_SHOW("显示得分", 2),
    ;

    private static final Map<Integer, ScoreTypeEnum> map = new HashMap<>();

    static {
        ScoreTypeEnum[] ens = ScoreTypeEnum.values();
        for (ScoreTypeEnum en : ens) {
            map.put(en.code, en);
        }
    }

    private final String name;
    private final Integer code;

    public static ScoreTypeEnum getEnumByRight(Integer right) {
        return map.get(right);
    }

    public static Set<Integer> getTypes() {
        return map.keySet();
    }

}
