package com.boryou.web.module.material.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.module.material.entity.Material;
import com.boryou.web.module.material.entity.MaterialFolder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:48
 */
public interface MaterialFolderService extends IService<MaterialFolder> {

    /**
     * 保存素材文件夹
     *
     * @param folder
     * @return
     */
    boolean folderSave(MaterialFolder folder);

    /**
     * 查询素材文件夹列表
     *
     * @param name
     * @return
     */
    List<MaterialFolder> folderList(String name);

    /**
     * 删除素材库
     *
     * @param id
     * @return
     */
    boolean folderDelete(Long id);
}
