package com.boryou.web.module.socket.constant;

import com.boryou.web.constant.RedisConstant;

/**
 * 常量
 *
 * <AUTHOR>
 * @version 1.0
 * @since 2022/8/23
 */
public class WsConstant {

    /**
     * redis的发布订阅channel（用于 [服务->websocket]方向的消息传递）
     */
    public static final String SERVICE_TO_WS = RedisConstant.system_prefix + "channel:service_to_ws";
    /**
     * redis的发布订阅channel（用于 [websocket->服务]方向的消息传递）
     */
    public static final String WS_TO_SERVICE = RedisConstant.system_prefix + "channel:ws_to_service";

    /**
     * 在线用户列表
     */
    public static final String ONLINE_UID_ZET = RedisConstant.system_prefix + "online";

    /**
     * 离线用户列表
     */
    public static final String OFFLINE_UID_ZET = RedisConstant.system_prefix + "offline";


    private WsConstant() {
        throw new IllegalStateException("GlobalConstant class Illegal");
    }


}
