package com.boryou.web.module.loginlog.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;
import java.util.List;

/**
 * h5登录日志查看记录对象 by_view_record
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Data
public class ByViewRecord/* extends BaseEntity*/ {
    private static final long serialVersionUID = 1L;

    /**
     * 主键
     */
    private Long id;

    /**
     * 手机号
     */
    @Excel(name = "手机号")
    private String phone;

    /**
     * 用户id
     */
    private Long userId;

    /**
     * 创建时间
     */
    @Excel(name = "创建时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;


    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
                .append("id", getId())
                .append("phone", getPhone())
                .append("createTime", getCreateTime())
                .append("userId", getUserId())
                .toString();
    }

    @TableField(exist = false)
    private List<String> ids;
}
