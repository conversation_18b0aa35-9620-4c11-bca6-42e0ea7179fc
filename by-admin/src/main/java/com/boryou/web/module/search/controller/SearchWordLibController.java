package com.boryou.web.module.search.controller;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.web.module.search.entity.WordLib;
import com.boryou.web.module.search.entity.WordLibType;
import com.boryou.web.module.search.entity.vo.WordLibVO;
import com.boryou.web.module.search.service.WordLibService;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

/**
 * 数据列表检索controller
 *
 * <AUTHOR>
 */
@RestController()
public class SearchWordLibController extends BaseController {
    @Resource
    private WordLibService wordLibService;

    @PostMapping("/word/selectWordLibTree")
    public AjaxResult selectWordLibTree(@RequestBody WordLibVO wordLib) {
        return AjaxResult.success(wordLibService.selectWordLibTree(wordLib));
    }

    @PostMapping("/word/areaTree")
    public AjaxResult areaTree(@RequestBody WordLibVO wordLib) {
        return AjaxResult.success(wordLibService.selectAreaTree(wordLib));
    }

    @PostMapping("/word/selectWordLib")
    public AjaxResult selectWordLib(@RequestBody(required = false) WordLibType wordLibType) {
        if (wordLibType == null) {
            wordLibType = new WordLibType();
            wordLibType.setState("0");
        }
        return AjaxResult.success(wordLibService.selectWordLib(wordLibType));
    }

    @GetMapping("/word/selectWordLibPage")
    public TableDataInfo selectWordLibPage(@ModelAttribute WordLibType wordLibType) {
        startPage();
        return getDataTable(wordLibService.selectWordLib(wordLibType));
    }

    @PostMapping("/word/selectWord")
    public AjaxResult selectWord(@RequestBody WordLib wordLib) {
        return AjaxResult.success(wordLibService.selectWord(wordLib));
    }

    @GetMapping("/word/selectWordPage")
    public TableDataInfo selectWordPage(@ModelAttribute WordLib wordLib) {
        startPage();
        return getDataTable(wordLibService.selectWord(wordLib));
    }

    @GetMapping("/word/selectWordById/{id}")
    public AjaxResult selectWordById(@PathVariable Long id) {
        return AjaxResult.success(wordLibService.selectWordById(id));
    }

    @PostMapping("/word/insertWordLib")
    public AjaxResult insertWordLib(@RequestBody WordLibType wordLibType) {
        if (wordLibService.insertWordLib(wordLibType) > 0) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/word/insertWord")
    public AjaxResult insertWord(@RequestBody WordLib wordLib) {
        if (wordLibService.insertWord(wordLib) > 0) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/word/updateWordLibById")
    public AjaxResult updateWordLibById(@RequestBody WordLibType wordLibType) {
        if (wordLibService.updateWordLibById(wordLibType) > 0) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/word/updateWordById")
    public AjaxResult updateWordById(@RequestBody WordLib wordLib) {
        if (wordLibService.updateWordById(wordLib) > 0) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/word/deleteWordType")
    public AjaxResult deleteWordType(@RequestBody WordLibType wordLibType) {
        if (wordLibService.deleteWordLibByIds(wordLibType.getId().toString()) > 0) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

    @PostMapping("/word/deleteWord")
    public AjaxResult deleteWord(@RequestBody WordLibVO wordLib) {
        if (wordLibService.deleteWordByIds(wordLib.getIds()) > 0) {
            return AjaxResult.success();
        } else {
            return AjaxResult.error();
        }
    }

}
