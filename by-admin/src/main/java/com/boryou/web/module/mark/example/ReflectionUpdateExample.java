package com.boryou.web.module.mark.example;

import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.boryou.web.module.mark.domain.EsBeanMark;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import com.boryou.web.module.mark.util.FieldUpdateUtil;

import java.util.HashMap;
import java.util.Map;

/**
 * 反射更新功能使用示例
 * 展示如何使用增强的 FieldUpdateUtil 进行字段比较和更新
 * 
 * <AUTHOR>
 */
public class ReflectionUpdateExample {

    /**
     * 示例1：简单的反射更新（字段名相同，支持排除字段）
     */
    public boolean updateExample1(EsBeanMarkVO newData, EsBeanMark existData, LambdaUpdateWrapper<EsBeanMark> updateWrapper) {
        // 使用反射自动比较并更新，排除指定字段
        return FieldUpdateUtil.compareAndUpdateSimple(
            newData, 
            existData, 
            updateWrapper,
            // 排除的字段
            "esBeanMarkId", "createTime", "createBy", "updateTime", "delFlag"
        );
    }

    /**
     * 示例2：带字段映射的反射更新
     */
    public boolean updateExample2(EsBeanMarkVO newData, EsBeanMark existData, LambdaUpdateWrapper<EsBeanMark> updateWrapper) {
        // 定义字段映射关系（当两个实体类字段名不同时）
        Map<String, String> fieldMappings = new HashMap<>();
        fieldMappings.put("userId", "userId");  // VO中的userId映射到Entity中的userId
        fieldMappings.put("deptId", "deptId");  // VO中的deptId映射到Entity中的deptId
        // 可以添加更多映射关系...
        
        return FieldUpdateUtil.compareAndUpdateWithMapping(
            newData,
            existData,
            updateWrapper,
            fieldMappings,
            // 排除的字段
            "esBeanMarkId", "createTime", "createBy", "updateTime", "delFlag"
        );
    }

    /**
     * 示例3：高级配置的反射更新
     */
    public boolean updateExample3(EsBeanMarkVO newData, EsBeanMark existData, LambdaUpdateWrapper<EsBeanMark> updateWrapper) {
        // 创建高级配置
        FieldUpdateUtil.ReflectionUpdateConfig<EsBeanMark> config = FieldUpdateUtil.createReflectionUpdate(updateWrapper)
            // 排除字段
            .excludeFields("esBeanMarkId", "createTime", "createBy", "updateTime", "delFlag")
            // 添加字段映射
            .addMapping("sourceField", "targetField")
            .addMapping("anotherSourceField", "anotherTargetField");
        
        // 执行反射更新
        return FieldUpdateUtil.compareAndUpdateByReflection(newData, existData, config);
    }

    /**
     * 示例4：在Service中的实际使用
     */
    public boolean updateWithReflection(EsBeanMarkVO esBeanMarkVO, EsBeanMark existEntity, 
                                       String userName, java.util.Date date, Long userId) {
        
        LambdaUpdateWrapper<EsBeanMark> updateWrapper = new LambdaUpdateWrapper<EsBeanMark>()
                .eq(EsBeanMark::getArticleId, esBeanMarkVO.getArticleId())
                .eq(EsBeanMark::getUserId, userId);

        // 使用反射进行字段比较和更新
        boolean hasUpdate = FieldUpdateUtil.compareAndUpdateSimple(
            esBeanMarkVO, 
            existEntity, 
            updateWrapper,
            // 排除系统字段和主键字段
            "esBeanMarkId", "createTime", "createBy", "delFlag"
        );

        // 如果有字段需要更新，则设置公共更新字段
        if (hasUpdate) {
            updateWrapper.set(EsBeanMark::getDelFlag, 0)
                    .set(EsBeanMark::getUTime, date)
                    .set(EsBeanMark::getUBy, userName);
            
            // 这里需要调用实际的update方法
            // return this.update(updateWrapper);
        }

        return hasUpdate;
    }

    /**
     * 示例5：处理不同实体类之间的字段映射
     */
    public boolean updateWithDifferentEntities(Object sourceEntity, Object targetEntity, 
                                              LambdaUpdateWrapper<?> updateWrapper) {
        
        // 定义复杂的字段映射关系
        Map<String, String> mappings = new HashMap<>();
        mappings.put("sourceFieldName1", "targetFieldName1");
        mappings.put("sourceFieldName2", "targetFieldName2");
        mappings.put("sourceUserId", "userId");
        mappings.put("sourceDeptId", "deptId");
        
        // 使用字段映射进行更新
        return FieldUpdateUtil.compareAndUpdateWithMapping(
            sourceEntity,
            targetEntity,
            (LambdaUpdateWrapper<Object>) updateWrapper,
            mappings,
            // 排除字段
            "id", "createTime", "updateTime", "version"
        );
    }
}
