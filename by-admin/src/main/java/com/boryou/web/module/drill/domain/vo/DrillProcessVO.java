package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotNull;


@Data
// @SuperBuilder
// @AllArgsConstructor
// @NoArgsConstructor
public class DrillProcessVO {

    /**
     * 关联演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "演练任务id不能为空")
    private Long drillTaskId;

    private Long userId;


}
