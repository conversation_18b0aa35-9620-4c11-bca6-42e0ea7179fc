package com.boryou.web.module.drill.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Getter
@RequiredArgsConstructor
public enum StageTypeEnum {

    TYPE_COMMENT("评论", "1"),
    TYPE_LAST("总结", "2"),
    ;

    private static final Map<String, StageTypeEnum> map = new HashMap<>();

    static {
        StageTypeEnum[] ens = StageTypeEnum.values();
        for (StageTypeEnum en : ens) {
            map.put(en.name, en);
        }
    }

    private final String name;
    private final String code;

    public static StageTypeEnum getEnumByLeft(String left) {
        return map.get(left);
    }

}
