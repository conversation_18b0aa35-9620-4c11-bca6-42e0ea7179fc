package com.boryou.web.module.drill.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;
import java.util.Set;

@Getter
@RequiredArgsConstructor
public enum TeamTypeEnum {

    RED("红队", 1),
    BLUE("蓝队", 2);

    private static final Map<Integer, TeamTypeEnum> map = new HashMap<>();

    static {
        TeamTypeEnum[] ens = TeamTypeEnum.values();
        for (TeamTypeEnum en : ens) {
            map.put(en.code, en);
        }
    }

    private final String name;
    private final Integer code;

    public static TeamTypeEnum getEnumByRight(Integer right) {
        return map.get(right);
    }

    public static Set<Integer> getTypes() {
        return map.keySet();
    }

}
