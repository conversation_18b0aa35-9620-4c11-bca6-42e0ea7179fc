package com.boryou.web.module.material.service.impl;

import cn.hutool.core.lang.tree.TreeUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.web.module.material.entity.Material;
import com.boryou.web.module.material.entity.MaterialFolder;
import com.boryou.web.module.material.mapper.MaterialFolderMapper;
import com.boryou.web.module.material.mapper.MaterialMapper;
import com.boryou.web.module.material.service.MaterialFolderService;
import com.boryou.web.module.material.service.MaterialService;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

import static com.boryou.common.constant.Constants.STR_FALSE;
import static com.boryou.common.constant.Constants.STR_TRUE;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:48
 */
@Service
@AllArgsConstructor
public class MaterialFolderServiceImpl extends ServiceImpl<MaterialFolderMapper, MaterialFolder> implements MaterialFolderService {

    private final MaterialFolderMapper folderMapper;
    private final MaterialMapper materialMapper;

    @Override
    public boolean folderSave(MaterialFolder folder) {
        folder.setUserId(SecurityUtils.getUserId());
        if (folder.getFolderName().length() > 20) {
            throw new CustomException("素材库命名最大为20");
        }

        // 判断父级是否存在
        if (folder.getParentId() != null && folder.getParentId() != 0L) {
            MaterialFolder group = this.getById(folder.getParentId());
            if (group == null) {
                throw new CustomException("父级文件夹不存在");
            }
        }

        if (folder.getId() == null) {
            if (folder.getParentId() == null || folder.getParentId() == 0L) {
                folder.setIsGroup(STR_TRUE);
            } else {
                folder.setIsGroup(STR_FALSE);
            }
        }

        int count = this.count(Wrappers.<MaterialFolder>lambdaQuery()
                .eq(MaterialFolder::getUserId, SecurityUtils.getUserId())
                .eq(MaterialFolder::getFolderName, folder.getFolderName())
        );
        if (count > 0) {
            throw new CustomException("文件夹名称重复");
        }
        return this.saveOrUpdate(folder);
    }

    @Override
    public List<MaterialFolder> folderList(String name) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());

        // 获取父级
        List<MaterialFolder> res = this.list(Wrappers.<MaterialFolder>lambdaQuery()
                .eq(MaterialFolder::getUserId, userId)
                .eq(MaterialFolder::getIsGroup, STR_TRUE)
                .orderByAsc(MaterialFolder::getCreateTime)
        );

        // 获取所有子级和count
        List<MaterialFolder> list = folderMapper.folderList(userId);

        // 如果没有文件夹，新建默认素材库
        if (list.isEmpty()) {
            MaterialFolder folderGroup = new MaterialFolder();
            folderGroup.setIsGroup(STR_TRUE);
            folderGroup.setFolderName("默认素材组");
            this.folderSave(folderGroup);

            MaterialFolder folder = new MaterialFolder();
            folder.setParentId(folderGroup.getId());
            folder.setIsGroup(STR_FALSE);
            folder.setFolderName("默认素材库");
            this.folderSave(folder);

            list = folderMapper.folderList(userId);
        }

        // 构建树结构
        List<MaterialFolder> finalList = list;
        res.forEach(x -> x.setChildren(finalList.stream().filter(y -> y.getParentId().equals(x.getId())).collect(Collectors.toList())));

        // 过滤树结构中的文件夹
        if (name != null && !name.isEmpty()) {
            res = filterFoldersByName(res, name);
        }

        return res;
    }

    @Override
    public boolean folderDelete(Long id) {
        MaterialFolder folder = this.getById(id);
        if (folder == null) {
            throw new CustomException("素材库不存在");
        }
        if (folder.getIsGroup().equals(STR_TRUE)) {
            int count = this.count(Wrappers.<MaterialFolder>lambdaQuery()
                    .eq(MaterialFolder::getParentId, id)
            );
            if (count > 0) {
                throw new CustomException("请先删除下级素材库");
            }
        } else {
            int count = materialMapper.selectCount(Wrappers.<Material>lambdaQuery().eq(Material::getFolderId, id));
            if (count > 0) {
                throw new CustomException("请先删除素材");
            }
        }
        return this.removeById(id);
    }

    // 过滤树结构
    private List<MaterialFolder> filterFoldersByName(List<MaterialFolder> folders, String name) {
        List<MaterialFolder> filtered = new ArrayList<>();
        folders.forEach(x -> {
            if (x.getFolderName().contains(name)) {
                filtered.add(x);
            } else {
                List<MaterialFolder> collect = x.getChildren().stream().filter(y -> y.getFolderName().contains(name)).collect(Collectors.toList());
                if (!collect.isEmpty()) {
                    x.setChildren(collect);
                    filtered.add(x);
                }
            }
        });
        return filtered;
    }

}
