package com.boryou.web.module.webservice.service.impl;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.web.module.webservice.domain.JhOrg;
import com.boryou.web.module.webservice.mapper.JhOrgMapper;
import com.boryou.web.module.webservice.service.JhOrgService;
import com.boryou.web.module.webservice.util.XmlUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.w3c.dom.Element;

import java.util.HashMap;
import java.util.Map;

import static com.boryou.web.module.webservice.util.XmlUtil.strToElement;
import static com.boryou.web.module.webservice.util.XmlUtil.traverseNodes;

/**
 * <AUTHOR>
 * @date 2024/12/6 下午4:36
 */
@Service
@RequiredArgsConstructor
public class JhOrgServiceImpl extends ServiceImpl<JhOrgMapper, JhOrg> implements JhOrgService {

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String synOrgInfo(String str) {
        Element element = strToElement(str);
        Map<String, Object> map = new HashMap<>();
        traverseNodes(element, map);
        JhOrg bean = BeanUtil.toBean(map, JhOrg.class);

        // 新增，这里新增的时候需要指定id，否则会使用系统的自增自动生成，可能会出现重复的情况
        if (bean.getIdentification().equals("add")) {
            if (this.count(Wrappers.<JhOrg>lambdaQuery().eq(JhOrg::getOrgCode, bean.getOrgCode())) == 0) {
                return XmlUtil.returnXMLStr(this.save(bean));
            } else {
                return XmlUtil.returnXMLStr(false, "新增失败，机构编码已存在");
            }
        } else if (bean.getIdentification().equals("update")) {
            JhOrg one = this.getOne(Wrappers.<JhOrg>lambdaQuery().eq(JhOrg::getOrgCode, bean.getOrgCode()));
            bean.setId(one.getId());
            return XmlUtil.returnXMLStr(this.updateById(bean));
        } else if (bean.getIdentification().equals("del")) {
            return XmlUtil.returnXMLStr(this.remove(Wrappers.<JhOrg>lambdaQuery().eq(JhOrg::getOrgCode, bean.getOrgCode())));
        }
        return XmlUtil.returnXMLStr(false, "非法接口类型");
    }
}




