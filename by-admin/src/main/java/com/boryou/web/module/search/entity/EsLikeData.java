package com.boryou.web.module.search.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class EsLikeData {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long indexId;

    private Integer type;

    /**
     * 媒体名称
     */
    private String typeName;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    private String title;

    private String text;

    private String url;

    private String host;

    private String author;

    private String authorId;

    private Integer accountLevel;

    private String siteAreaCodeName;

    private Integer emotionFlag;

    private Boolean isOriginal;

    /**
     * 命中词
     */
    private String hitWords;

    private String md5;

    /**
     * 是否已读 0未读 1已读
     */
    private Integer isRead;

    private Integer pageNum = 1;
    private Integer size = 10;
}
