package com.boryou.web.module.message.service.impl;

import cn.hutool.core.collection.CollUtil;
import com.boryou.common.core.domain.TreeSelect;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.DateUtils;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.service.ISysDeptService;
import com.boryou.system.service.ISysUserService;
import com.boryou.web.module.message.domain.MessageTemplate;
import com.boryou.web.module.message.domain.vo.DeptMessage;
import com.boryou.web.module.message.mapper.MessageTemplateMapper;
import com.boryou.web.module.message.service.IMessageTemplateService;
import org.apache.commons.lang3.ArrayUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.Comparator;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 短信模板Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@Service
public class MessageTemplateServiceImpl implements IMessageTemplateService {
    @Autowired
    private MessageTemplateMapper messageTemplateMapper;
    @Autowired
    private ISysDeptService deptService;
    @Resource
    private SysDeptMapper deptMapper;
    @Resource
    private ISysUserService sysUserService;

    /**
     * 查询短信模板
     *
     * @param id 短信模板ID
     * @return 短信模板
     */
    @Override
    public MessageTemplate selectMessageTemplateById(Long id) {
        return messageTemplateMapper.selectMessageTemplateById(id);
    }

    /**
     * 查询短信模板列表
     *
     * @param messageTemplate 短信模板
     * @return 短信模板
     */
    @Override
    public List<MessageTemplate> selectMessageTemplateList(MessageTemplate messageTemplate) {
        return messageTemplateMapper.selectMessageTemplateList(messageTemplate);
    }

    /**
     * 新增短信模板
     *
     * @param messageTemplate 短信模板
     * @return 结果
     */
    @Override
    public int insertMessageTemplate(MessageTemplate messageTemplate) {
        messageTemplate.setCreateTime(DateUtils.getNowDate());
        return messageTemplateMapper.insertMessageTemplate(messageTemplate);
    }

    /**
     * 修改短信模板
     *
     * @param messageTemplate 短信模板
     * @return 结果
     */
    @Override
    public int updateMessageTemplate(MessageTemplate messageTemplate) {
        return messageTemplateMapper.updateMessageTemplate(messageTemplate);
    }

    /**
     * 批量删除短信模板
     *
     * @param ids 需要删除的短信模板ID
     * @return 结果
     */
    @Override
    public int deleteMessageTemplateByIds(Long[] ids) {
        return messageTemplateMapper.deleteMessageTemplateByIds(ids);
    }

    /**
     * 删除短信模板信息
     *
     * @param id 短信模板ID
     * @return 结果
     */
    @Override
    public int deleteMessageTemplateById(Long id) {
        return messageTemplateMapper.deleteMessageTemplateById(id);
    }


    @Override
    public int saveDeptMessage(DeptMessage deptMessage) {
        int flag1 = messageTemplateMapper.deleteDeptMessage(deptMessage);
        int flag = messageTemplateMapper.saveDeptMessage(deptMessage);
        return 1;
    }

    @Override
    public MessageTemplate getDeptMessageTemplate(DeptMessage deptMessage) {
        if (null == deptMessage.getDeptId()) {
            throw new CustomException("参数异常！");
        }
        SysDept sysDept = deptService.selectDeptById(deptMessage.getDeptId());
        if (null == sysDept) {
//            if (1L != sysDept.getParentId()) {
            throw new CustomException("部门不存在！");
//            }
        }
        if (sysDept.getAncestors().split(",").length <= 2) {
            MessageTemplate deptMessage1 = messageTemplateMapper.getDeptMessage(sysDept.getDeptId());
            if (null==deptMessage1){
                return  messageTemplateMapper.getDeptMessage(1L);//如果没有配置短信模板，就是用博约短信模板
            }
            return deptMessage1;
        }
        // 找组织架构-》xxx这第二级的部门是哪个，因为短信模板只配置在这一级
        Long provinceDeptId = 0L;
        SysDept dept = new SysDept();
        dept.setDeptId(sysUserService.selectUserById(1L).getDeptId());
        List<SysDept> depts = deptMapper.selectPermiDeptList(dept);
        String substring = sysDept.getAncestors().substring(sysDept.getAncestors().lastIndexOf(",") + 1);
        String[] split = sysDept.getAncestors().split(",");
        if (split.length > 3) {
            //如果是区级或一下，就取省级
            substring = split[2];
        }
        // 获取所有机构信息--对机构遍历填充人员
        List<TreeSelect> treeSelectList = deptService.buildDeptTreeSelect(depts);
        for (TreeSelect treeSelect : treeSelectList) {
            List<TreeSelect> children = treeSelect.getChildren();
            for (TreeSelect child : children) {
                if (substring.equals(String.valueOf(child.getId()))) {
                    provinceDeptId = child.getId();
                    break;
                }
            }
            if (provinceDeptId != 0) {
                break;
            }
        }
        if (provinceDeptId == 0) {
            throw new CustomException("部门级别配置变动异常!");
        }
        MessageTemplate deptMessage1 = messageTemplateMapper.getDeptMessage(provinceDeptId);
        if (null==deptMessage1){
            return  messageTemplateMapper.getDeptMessage(1L);//如果没有配置短信模板，就是用博约短信模板
        }
        return deptMessage1;
    }

    @Override
    public List<DeptMessage> getDeptTemplatesByIds(List<Long> deptIds) {
        return messageTemplateMapper.getDeptTemplatesByIds(deptIds);
    }
}
