package com.boryou.web.module.home.controller;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.ObjectUtil;
import com.boryou.common.annotation.Log;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.enums.BusinessType;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.controller.common.entity.bo.AreaOverviewBO;
import com.boryou.web.controller.common.entity.bo.HabitBO;
import com.boryou.web.controller.common.entity.vo.HomeVO;
import com.boryou.web.domain.Hot;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.vo.HotVO;
import com.boryou.web.module.home.entity.bo.LawyerAuthorBO;
import com.boryou.web.module.home.entity.bo.LawyerBO;
import com.boryou.web.module.home.entity.vo.SimpleVO;
import com.boryou.web.module.home.service.HomeStatisService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.io.IOException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024-05-21 10:15
 */
@RestController
@RequestMapping("/home")
@RequiredArgsConstructor
public class HomeController {
    private final HomeStatisService homeStatisService;

    private final RedisCache redisTemplate;

    /**
     * 大屏-情感分布
     */
    @GetMapping("/getHabit")
    public AjaxResult habit() {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        Object o = redisTemplate.getCacheObject(RedisConstant.system_prefix + "habit:" + userId);
        if (ObjectUtil.isEmpty(o)) {
            o = "dark";
        }
        return AjaxResult.success(o);
    }


    /**
     * 大屏-情感分布
     */
    @PostMapping("/setHabit")
    public AjaxResult setHabit(@RequestBody HabitBO habitBO) {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        redisTemplate.setCacheObject(RedisConstant.system_prefix + "habit:" + userId, habitBO.getThemeColor());
        return AjaxResult.success();
    }

    /**
     * 大屏-情感分布
     */
    @GetMapping("/getUserSetting")
    public AjaxResult getUserSetting() {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        HashMap o = redisTemplate.getCacheObject(RedisConstant.system_prefix + "user_setting:" + userId);
        if (ObjectUtil.isEmpty(o)) {
            o = new HashMap<>();
            o.put("enableNotify", true);
        }
        return AjaxResult.success(o);
    }


    /**
     * 大屏-情感分布
     */
    @PostMapping("/setUserSetting")
    public AjaxResult setUserSetting(@RequestBody HabitBO habitBO) {
        Long userId = SecurityUtils.getLoginUser().getUser().getUserId();
        HashMap<Object, Object> value = new HashMap<>();
        value.put("enableNotify", habitBO.isEnableNotify());
        redisTemplate.setCacheObject(RedisConstant.system_prefix + "user_setting:" + userId, value);
        return AjaxResult.success();
    }


    /**
     * 大屏-情感分布
     */
    @PostMapping("/emontionStatis")
    public AjaxResult emontionStatis(@RequestBody HomeVO homeVO) {
        homeVO.setContentAreaCode(homeStatisService.getAreaCode());
        com.alibaba.fastjson.JSONArray arr = homeStatisService.getEmontionStatis(homeVO, true);
        return AjaxResult.success(arr);
    }

    /**
     * 大屏-律师动态
     */
    @PostMapping("/lawyerMoment")
    public AjaxResult lawyerMoment(@RequestBody HomeVO homeVO) {
        List<LawyerBO> arr = homeStatisService.getLawyerMoment(homeVO, true);
        return AjaxResult.success(arr);
    }

    /**
     * 大屏-律师动态及律师
     */
    @PostMapping("/lawyerMomentAuthor")
    public AjaxResult lawyerMomentAuthor(@RequestBody HomeVO homeVO) {
        List<LawyerBO> arr = homeStatisService.getLawyerMoment(homeVO, true);
        LawyerAuthorBO lawyerAuthorBO = new LawyerAuthorBO();
        if (CollUtil.isEmpty(arr)) {
            return AjaxResult.success(lawyerAuthorBO);
        }
        List<LawyerAuthorBO.Author> lawyer = homeStatisService.getLawyer(arr);
        lawyerAuthorBO.setLawyerMoment(arr);
        lawyerAuthorBO.setLawyer(lawyer);
        return AjaxResult.success(lawyerAuthorBO);
    }

    /**
     * 大屏-浙江政法热点
     */
    @PostMapping("/hotZJ")
    public AjaxResult hotZJ(@RequestBody HomeVO homeVO) {
        List<Hot> arr = homeStatisService.hotZJ(homeVO, true);
        return AjaxResult.success(arr);
    }

    /**
     * 大屏-浙江政法热点
     */
    @PostMapping("/hotZJMore")
    public AjaxResult hotZJMore(@RequestBody HotVO hotVO) {
        List<Hot> arr = homeStatisService.hotZJMore(hotVO);
        return AjaxResult.success(arr);
    }

    /**
     * 大屏预警信息
     *
     * @param homeVO
     * @return
     */
    @PostMapping("/warn")
    public AjaxResult warn(@RequestBody HomeVO homeVO) {
        Long deptId = SecurityUtils.getLoginUser().getUser().getDeptId();
        homeVO.setDeptId(deptId);
        List<Hot> arr = homeStatisService.warn(homeVO, true);
        return AjaxResult.success(arr);
    }

    /**
     * 字符云
     *
     * <AUTHOR>
     */
    @PostMapping("/wordsAnalyse")
    public AjaxResult wordsAnalyse(@RequestBody HomeVO homeVO) throws IOException {
        homeVO.setContentAreaCode(homeStatisService.getAreaCode());
        List<Map<String, Object>> words = homeStatisService.getWordCloud(homeVO, true);
        return AjaxResult.success(words);
    }

    /**
     * 浙江信息总量-统计6类（微信，微博，抖音，快手，今日头条，网站）
     *
     * <AUTHOR>
     */
    @Log(title = "首页数据统计", businessType = BusinessType.QUERY)
    @PostMapping("/total")
    public AjaxResult total(@RequestBody HomeVO homeVO) throws IOException {
        homeVO.setContentAreaCode(homeStatisService.getAreaCode());
        Long total = homeStatisService.total(homeVO, true);
        return AjaxResult.success(total);
    }

    /**
     * 浙江信息总量-统计6类（微信，微博，抖音，快手，今日头条，网站）
     *
     * <AUTHOR>
     */
    @PostMapping("/curve")

    public AjaxResult curve(@RequestBody HomeVO homeVO) throws IOException {
        homeVO.setContentAreaCode(homeStatisService.getAreaCode());
        Map<String, Object> total = null;
        try {
            total = homeStatisService.curveStatis(homeVO, true);
        } catch (Exception e) {
            e.printStackTrace();
        }
        return AjaxResult.success(total);
    }

    /**
     * 活跃账号-统计账号发文量
     *
     * <AUTHOR>
     */
    @PostMapping("/authorLineChat")

    public AjaxResult authorLineChat(@RequestBody HomeVO homeVO) throws IOException {
        homeVO.setContentAreaCode(homeStatisService.getAreaCode());
        List<Map> total = homeStatisService.authorLineChat(homeVO, true);
        return AjaxResult.success(total);
    }

    /**
     * 辖区概览
     *
     * <AUTHOR>
     */
    @PostMapping("/areaOverview")

    public AjaxResult areaOverview(@RequestBody HomeVO homeVO) throws IOException {
        homeVO.setContentAreaCode(homeStatisService.getAreaCode());
        List<AreaOverviewBO> total = homeStatisService.areaOverview(homeVO, true);
        return AjaxResult.success(total);
    }


    //   案件查询：1.重点案件查询  2.全省热点案件 3全国案案件查询    //


    /**
     * 1.重点案件查询
     *
     * <AUTHOR>
     */
    @PostMapping("/mainPlan")

    public AjaxResult mainPlan(@RequestBody HomeVO homeVO) {
        homeVO.setContentAreaCode(homeStatisService.getAreaCode());
        List<SimpleVO> rows = homeStatisService.getMainPlan(homeVO, true);
        return AjaxResult.success(rows);
    }


    /**
     * 2.全省热点案件
     *
     * <AUTHOR>
     */
    @PostMapping("/provinceHotPlan")

    public AjaxResult provinceHotPlan(@RequestBody HomeVO homeVO) {
        List<SimpleVO> sortedList = homeStatisService.getProvinceHotPlan(homeVO, true);
        return AjaxResult.success(sortedList);
    }


    /**
     * 3全国案案件查询
     *
     * <AUTHOR>
     */
    @PostMapping("/countryHotPlan")
    public AjaxResult hotPlan(@RequestBody HomeVO homeVO) {
        List<SimpleVO> sortedList = homeStatisService.getCountryHotPlan(homeVO, true);
        return AjaxResult.success(sortedList);
    }

    /**
     * 案件统计查询
     *
     * <AUTHOR>
     */
    @PostMapping("/casePaln")
    public AjaxResult casePaln(@RequestBody HomeVO homeVO) {
        List<SimpleVO> sortedList = homeStatisService.casePaln(homeVO, true);
        return AjaxResult.success(sortedList);
    }


    /**
     * 大屏下钻页面-案件类型统计查询
     *
     * <AUTHOR>
     */
    @GetMapping("/planTypeList")
    public AjaxResult planTypeList(HomeVO homeVO) {
        List<Plan> sortedList = homeStatisService.planTypeList(homeVO);
        return AjaxResult.success(sortedList);
    }


}

