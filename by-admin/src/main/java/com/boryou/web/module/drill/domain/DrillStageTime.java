package com.boryou.web.module.drill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 演练阶段配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("by_drill_stage_time")
public class DrillStageTime {

    @TableId(value = "drill_stage_time_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillStageTimeId;


    @JsonSerialize(using = ToStringSerializer.class)
    private Long processStageId;

    /**
     * 关联演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    /**
     * 计时类型（1:同时计时 2:分开计时）
     */
    private Integer timerType;

    private Integer teamType;

    /**
     * 倒计时时长(秒)
     */
    private Integer timerDuration;

    /**
     * 倒计时剩余(秒)
     */
    private Integer remainTimerDuration;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

}
