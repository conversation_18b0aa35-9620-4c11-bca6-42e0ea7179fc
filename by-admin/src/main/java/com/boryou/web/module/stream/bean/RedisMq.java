package com.boryou.web.module.stream.bean;

import lombok.Data;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Configuration;

import java.util.List;

@EnableConfigurationProperties
@Configuration
@ConfigurationProperties(prefix = "spring.redis.mq")
@Data
public class RedisMq {
    public List<RedisMqStream> streams;

    @Data
    public static class RedisMqStream {

        public String name;
        public List<RedisMqGroup> groups;

        @Data
        public static class RedisMqGroup {

            private String name;
            private String[] consumers;
        }
    }

}
