package com.boryou.web.module.material.entity;

import com.baomidou.mybatisplus.annotation.*;
import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/2 11:18
 */
@TableName("by_material")
@Data
public class Material {

    @TableId(type = IdType.ASSIGN_ID)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 用户id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 素材的 id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long contentId;

    /**
     * 素材文件夹 id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long folderId;

    /**
     * 媒体类型
     */
    private Integer type;

    /**
     * 媒体名称
     */
    @Excel(name = "媒体类型")
    private String typeName;

    /**
     * 标题
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 正文
     */
    @Excel(name = "正文")
    private String text;

    /**
     * url
     */
    @Excel(name = "链接")
    private String url;

    /**
     * 域名
     */
    @Excel(name = "域名")
    private String host;

    /**
     * 作者
     */
    @Excel(name = "作者")
    private String author;

    /**
     * 情感标识
     */
    @Excel(name = "情感类型", readConverterExp = "0=中性,1=敏感,2=非敏感")
    private Integer emotionFlag;

    /**
     * 站点地域
     */
    private String siteAreaCodeName;

    /**
     * 是否原创
     */
    private String originFlag;

    /**
     * 命中词
     */
    @Excel(name = "情感类型", readConverterExp = "0=中性,1=敏感,2=非敏感")
    private String hitWords;

    /**
     * md5
     */
    private String md5;

    /**
     * 相似条数
     */
    @Excel(name = "相似条数")
    private Integer similarCount;

    /**
     * 发布时间
     */
    @Excel(name = "发布时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date publishTime;

    /**
     * 创建时间
     */
    @Excel(name = "加入时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT)
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @TableField(fill = FieldFill.INSERT_UPDATE)
    private Date updateTime;
}
