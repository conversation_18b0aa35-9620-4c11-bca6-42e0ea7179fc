package com.boryou.web.module.drill.domain.vo;

import cn.hutool.core.util.NumberUtil;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.function.BiConsumer;

public class ScoreAccumulator {
    private BigDecimal redScore = BigDecimal.ZERO;
    private BigDecimal blueScore = BigDecimal.ZERO;

    public void accumulate(BigDecimal red, BigDecimal blue) {
        this.redScore = this.redScore.add(red);
        this.blueScore = this.blueScore.add(blue);
    }

    // 结果输出接口（保持与原有BiConsumer兼容）
    public BiConsumer<BigDecimal, BigDecimal> getScoreSetter() {
        return (red, blue) -> {
            this.redScore = this.redScore.add(red);
            this.blueScore = this.blueScore.add(blue);
        };
    }

    public void scoreResult(BiConsumer<String, String> scoreConsumer) {
        BigDecimal redScoreBig = this.getTotalRed();
        BigDecimal blueScoreBig = this.getTotalBlue();
        if (redScoreBig.compareTo(BigDecimal.ZERO) <= 0 && blueScoreBig.compareTo(BigDecimal.ZERO) <= 0) {
            scoreConsumer.accept(BigDecimal.ZERO.toString(), BigDecimal.ZERO.toString());
            return;
        }
        BigDecimal total = NumberUtil.add(redScoreBig, blueScoreBig);
        BigDecimal redScore100 = NumberUtil.mul(redScoreBig, BigDecimal.valueOf(100));
        BigDecimal redStageScore = NumberUtil.div(redScore100, total, 1);
        BigDecimal blueStageScore = NumberUtil.sub(100, redStageScore);
        scoreConsumer.accept(redStageScore.toString(), blueStageScore.toString());
    }

    // 获取最终结果
    public BigDecimal getTotalRed() {
        return redScore.setScale(1, RoundingMode.HALF_UP);
    }

    public BigDecimal getTotalBlue() {
        return blueScore.setScale(1, RoundingMode.HALF_UP);
    }

}
