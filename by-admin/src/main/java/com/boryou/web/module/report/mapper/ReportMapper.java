package com.boryou.web.module.report.mapper;


import com.boryou.web.module.report.entity.Report;
import com.boryou.web.module.report.entity.ReportTemplate;
import com.boryou.web.module.report.entity.vo.ReportVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @description 针对表【by_report】的数据库操作Mapper
 * @createDate 2024-05-22 19:55:31
 * @Entity com.boryou.web.module.report.entity.Report
 */
public interface ReportMapper {

    Report selectById(@Param("reportId") Long reportId);

    List<ReportVO> selectReport(ReportVO report);

    List<Report> selectByIds(@Param("reportIds") String reportIds);

    int deleteByIds(@Param("ids") String ids);

    Report selectIssue(Report report);

    int insert(Report report);

    int updateById(Report report);

    int clearFileById(Report report);

    ReportTemplate selectTemplateById(@Param("tempId") Long tempId);

    List<ReportTemplate> selectTemplate(ReportTemplate reportTemplate);

    int deleteReportByTempIds(@Param("ids") String ids);

    int deleteTemplateByIds(@Param("ids") String ids);

    int insertTemplate(ReportTemplate reportTemplate);

    int updateTemplateById(ReportTemplate reportTemplate);

    Long selectDefault(@Param("userId") Long userId);

    int insertDefault(ReportTemplate reportTemplate);

    int updateDefault(ReportTemplate reportTemplate);

    int changeTemplateToSys(@Param("tempId") Long tempId);
}
