package com.boryou.web.module.drill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.web.handle.ListStringTypeHandler;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName(value = "by_drill_task", autoResultMap = true)
public class DrillTask {

    @TableId(value = "drill_task_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    private String taskTitle;

    private String taskContent;

    private String drillEvent;

    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date estimateDrillTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date drillStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date drillEndTime;

    private String blueCaptain;

    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> blueMember;

    private String blueScore;

    private String redCaptain;

    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> redMember;

    private String redScore;

    /**
     * 1: 未开始 2:进行中 3:已结束
     */
    private Integer status;

    /**
     * 是否删除 0: 否 1:是
     */
    private Integer delFlag;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long deptId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

}
