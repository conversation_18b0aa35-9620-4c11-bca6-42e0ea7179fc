package com.boryou.web.module.search.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.IService;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.web.module.search.entity.EsSpecialData;
import com.boryou.web.module.search.entity.FilterInfo;
import com.boryou.web.module.search.mapper.FilterInfoMapper;
import com.boryou.web.module.search.service.EsSpecialDataService;
import com.boryou.web.module.search.service.FilterInfoService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class FilterInfoServiceImpl extends ServiceImpl<FilterInfoMapper, FilterInfo> implements FilterInfoService {

    @Resource
    private FilterInfoMapper filterInfoMapper;
    @Resource
    EsSpecialDataService esSpecialDataService;

    @Override
    public List<FilterInfo> selectFilterInfo(FilterInfo filterInfo) {
        List<FilterInfo> filterInfos = filterInfoMapper.selectFilterInfo(filterInfo);
        List<String> md5List = filterInfos.stream().map(FilterInfo::getMd5).collect(Collectors.toList());
        List<EsSpecialData> esSpecialDataList = esSpecialDataService.getEsSpecialDatasByList(new ArrayList<>(), md5List);
        Map<String, EsSpecialData> md5Special = esSpecialDataList.stream().collect(Collectors.toMap(EsSpecialData::getMd5, Function.identity()));
        for (FilterInfo info : filterInfos) {
            if (md5Special.containsKey(info.getMd5())) {
                if (md5Special.get(info.getMd5()).getEmotionFlag() != info.getEmotionFlag()) {
                    info.setEmotionFlag(md5Special.get(info.getMd5()).getEmotionFlag());
                }
            }
        }
        return filterInfos;
    }

    @Override
    public FilterInfo selectFilterInfoById(Long id) {
        return filterInfoMapper.selectFilterInfoById(id);
    }

    @Override
    public int insertFilterInfo(FilterInfo filterInfo) {
        if (filterInfoMapper.selectFilterInfoByIndexIdAndPlanId(filterInfo.getIndexId(), filterInfo.getPlanId()) == null) {
            filterInfo.setId(IdUtil.getSnowflakeNextId());
            filterInfo.setFilterTime(DateUtil.date());
            return filterInfoMapper.insertFilterInfo(filterInfo);
        } else {
            return 0;
        }
    }

    @Override
    public int deleteFilterInfoByIds(String ids) {
        return filterInfoMapper.deleteFilterInfoByIds(ids);
    }

    @Override
    public int deleteFilterInfoBySize(FilterInfo filterInfo) {
        List<String> ids = filterInfoMapper.selectFilterInfoBySize(filterInfo);
        if (CollUtil.isNotEmpty(ids)) {
            return filterInfoMapper.deleteFilterInfoByIds(CollUtil.join(ids, ","));
        }
        return 0;
    }
}
