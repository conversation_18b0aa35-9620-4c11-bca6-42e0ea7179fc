package com.boryou.web.module.source.service.impl;


import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.web.domain.Plan;
import com.boryou.web.mapper.PlanMapper;
import com.boryou.web.module.source.entity.SourceSetting;
import com.boryou.web.module.source.mapper.SourceSettingMapper;
import com.boryou.web.module.source.service.SourceSettingService;
import com.boryou.web.module.source.vo.SourceSettingVO;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.*;

@Service
public class SourceSettingServiceImpl extends ServiceImpl<SourceSettingMapper, SourceSetting> implements SourceSettingService {

    @Resource
    private SourceSettingMapper dao;

    @Override
    public List<SourceSetting> getSourceSettingList(SourceSettingVO source) {
        return dao.getSourceSettingList(source);
    }

    @Override
    public List<SourceSetting> getSourceSetting(String id, String name, Long plateId, String state, String settingType, String userId, String sourceType) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("id", id);
        map.put("name", name);
        map.put("plateId", plateId);
        map.put("state", state);
        map.put("settingType", settingType);
        map.put("userId", userId);
        map.put("sourceType", sourceType);
        return dao.getSourceSetting(map);
    }

    @Override
    public void addSourceSetting(SourceSetting sourceSetting) {
        dao.addSourceSetting(sourceSetting);
    }

    @Override
    public void updateSourceSetting(SourceSetting sourceSetting) {
        dao.updateSourceSetting(sourceSetting);
    }

    @Override
    public void updateSourceSettingState(int state, String ids) {
        Map<String, Object> map = new HashMap<String, Object>();
        map.put("ids", ids);
        map.put("state", state);
        dao.updateSourceSettingState(map);
    }

    @Override
    public void delSourceSetting(String id) {
        dao.delSourceSetting(id);
    }

    @Override
    public int deleteSourceSetting(Long plateId, String state, String sourceType) {
        SourceSetting sourceSetting = new SourceSetting();
        sourceSetting.setSettingType(state);
        sourceSetting.setPlateId(plateId);
        sourceSetting.setSourceType(sourceType);
        return dao.deleteSourceSetting(sourceSetting);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean addSource(List<SourceSetting> sourceSettings) {
        List<SourceSetting> unAdd = new ArrayList<>();
        Date date = new Date();
        for (SourceSetting sourceSetting : sourceSettings) {
            if (StrUtil.isEmpty(sourceSetting.getName()) || StrUtil.isEmpty(sourceSetting.getSettingHost())) {
                throw new CustomException("参数异常！");
            }
            List<SourceSetting> thisSourceSettingList = getSourceSetting(null, sourceSetting.getName(), sourceSetting.getPlateId(), null, sourceSetting.getSettingType(), SecurityUtils.getUserId(), null);
            sourceSetting.setCreateTime(date);
            sourceSetting.setUpdateTime(date);
            sourceSetting.setState(1);
            sourceSetting.setUserId(SecurityUtils.getUserId());
            sourceSetting.setId(IdUtil.getSnowflakeNextId());
            if (thisSourceSettingList.contains(sourceSetting)) {
                throw new CustomException("信源已存在！");
            } else {
                unAdd.add(sourceSetting);
            }
        }
        if (CollUtil.isNotEmpty(sourceSettings)) {
            for (SourceSetting sourceSetting : unAdd) {
                addSourceSetting(sourceSetting);
            }
        }
        return true;
    }

    @Override
    public boolean delete(String ids) {
        return this.removeByIds(Arrays.asList(ids.split(",")));
    }

    @Override
    public String importSource(List<SourceSetting> userList, String settingType, String operName, Long plateId) {
        if (StringUtils.isNull(userList) || userList.size() == 0) {
            throw new CustomException("导入数据不能为空！");
        }
        int successNum = 0;
        int failureNum = 0;
        StringBuilder successMsg = new StringBuilder();
        StringBuilder failureMsg = new StringBuilder();
        Date date = new Date();
        for (SourceSetting sourceSetting : userList) {
            if (StrUtil.isEmpty(sourceSetting.getName()) && StrUtil.isEmpty(sourceSetting.getSourceType()) && StrUtil.isEmpty(sourceSetting.getSettingHost())) {
                continue;
            }
            sourceSetting.setSettingType(settingType);
            sourceSetting.setUserId(SecurityUtils.getUserId());
            sourceSetting.setPlateId(plateId);
            if (plateId == null || StrUtil.isEmpty(sourceSetting.getSettingHost()) || StrUtil.isEmpty(sourceSetting.getSourceType()) || StrUtil.isEmpty(settingType)) {
                throw new CustomException("参数异常!");
            }
            try {
                // 验证是否存在这个用户
                List<SourceSetting> thisSourceSettingList = getSourceSetting(null, sourceSetting.getName(), sourceSetting.getPlateId(), null, sourceSetting.getSettingType(), SecurityUtils.getUserId(), null);
                if (!thisSourceSettingList.contains(sourceSetting)) {
                    sourceSetting.setId(IdUtil.getSnowflakeNextId());
                    sourceSetting.setCreateTime(date);
                    sourceSetting.setUpdateTime(date);
                    sourceSetting.setState(1);
                    sourceSetting.setSettingType(settingType);
                    sourceSetting.setUserId(SecurityUtils.getUserId());
                    sourceSetting.setId(IdUtil.getSnowflakeNextId());
                    addSourceSetting(sourceSetting);
                    successNum++;
                    successMsg.append("<br/>" + successNum + "、名称 " + sourceSetting.getName() + " 导入成功");
                } else {
                    failureNum++;
                    failureMsg.append("<br/>" + failureNum + "、名称 " + sourceSetting.getName() + " 已存在");
                }
            } catch (Exception e) {
                failureNum++;
                String msg = "<br/>" + failureNum + "、名称 " + sourceSetting.getName() + " 导入失败：";
                failureMsg.append(msg + e.getMessage());
                log.error(msg, e);
            }
        }
        if (failureNum > 0) {
            failureMsg.insert(0, "很抱歉，导入失败！共 " + failureNum + " 条数据格式不正确，错误如下：");
            throw new CustomException(failureMsg.toString());
        } else {
            successMsg.insert(0, "恭喜您，数据已全部导入成功！共 " + successNum + " 条，数据如下：");
        }
        return successMsg.toString();
    }

}
