package com.boryou.web.module.search.service;

import cn.hutool.json.JSONUtil;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.entity.vo.BoryouBeanVO;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.vo.InfoVO;
import com.boryou.web.util.WordColorUtil;
import com.hankcs.hanlp.HanLP;
import com.hankcs.hanlp.seg.Segment;
import com.hankcs.hanlp.seg.common.Term;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.net.MalformedURLException;
import java.net.URL;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

@Service
@Slf4j
public class EntityIdentificationService {

    public Map<String, Object> getEntityOption(InfoVO infoVO, SysUser user) {
        Map<String, Object> map = new HashMap<>();
        Long id = infoVO.getId();
        EsSearchBO esSearchBO = new EsSearchBO();
        esSearchBO.setId(String.valueOf(id));
        String time = infoVO.getTime();
        esSearchBO.setStartTime(time);
        esSearchBO.setEndTime(time);
        BoryouBeanVO bean = EsSearchUtil.searchByIdTime(esSearchBO);
        if (bean == null) {
            return null;
        }

        // 分词 +背景标色
        String text = painting(bean.getText());
        map.put("text", text);

        // 关键词提取,5个
        List<String> keywordList = HanLP.extractKeyword(bean.getText(), 5);
        map.put("keywordList", keywordList);

        // HanLP人名提取算法
        Segment segment = HanLP.newSegment().enableNameRecognize(true);
        String regex = "[\u4E00-\u9FA5]{2,5}(?:·[\u4E00-\u9FA5]{2,5})*/nr";
        Map<String, Integer> map1 = matching(segment, regex, bean.getText());

        // HanLP地名提取算法
        Segment segment2 = HanLP.newSegment().enablePlaceRecognize(true);
        String regex2 = "\\S{1,}[^\\.]/ns";
        Map<String, Integer> map2 = matching(segment2, regex2, bean.getText());

        // HanLP机构提取算法
        Segment segment3 = HanLP.newSegment().enableOrganizationRecognize(true);
        String regex3 = "\\S{1,}[^\\.]/nt";
        Map<String, Integer> map3 = matching(segment3, regex3, bean.getText());

        // 人名图json，地名图json，机构名json
        map.put("nameJson", map1);
        map.put("placeJson", map2);
        map.put("organizationJson", map3);

        // 媒体，作者，地域
        URL url = null;
        try {
            url = new URL(bean.getUrl());
        } catch (MalformedURLException e) {
            e.printStackTrace();
        }
        String site = url.getHost();
        String author = bean.getAuthor();
        String address = bean.getSiteAddress();
        // 关联关系图json
        String relevance = makeRelevanceTree(map1, map2, map3, author, address, site, keywordList);
        map.put("relevance", JSONUtil.parseObj(relevance));
        // 摘要
        List<String> sentenceList = HanLP.extractSummary(bean.getText(), 3);
        map.put("sentence", sentenceList.toString());
        return map;
    }

    /**
     * 关联关系
     *
     * @param map_name    人名
     * @param map_place   地名
     * @param map_org     机构名
     * @param author      作者
     * @param address     地域
     * @param site        媒体
     * @param keywordList
     * @return
     * <AUTHOR>
     * @time 2017-4-24 下午4:33:17
     */
    private String makeRelevanceTree(Map<String, Integer> map_name, Map<String, Integer> map_place,
                                     Map<String, Integer> map_org, String author, String address, String site, List<String> keywordList) {

        StringBuffer links = new StringBuffer();
        links.append("links : [{source : '人名', target : '文本'},");
        links.append("{source : '地名', target : '文本'},");
        links.append("{source : '机构名', target : '文本'},");
        links.append("{source : '关键词', target : '文本'},");
        links.append("{source : '媒体', target : '文本'},");
        links.append("{source : '地域', target : '文本'},");
        links.append("{source : '作者', target : '文本'},");
        // 合并
        Set<String> set = new HashSet<>();
        if (author != null && author.length() > 0) {
            links.append("{source : '" + author + "', target : '作者'},");
            set.add(author);
        }
        if (address != null && address.length() > 0) {
            links.append("{source : '" + address + "', target : '地域'},");
            set.add(address);
        }
        if (site != null && site.length() > 0) {
            links.append("{source : '" + site + "', target : '媒体'},");
            set.add(site);
        }

        for (String keyword : keywordList) {
            set.add(keyword);
            links.append("{source : '" + keyword + "', target : '关键词'},");
        }
        if (map_name != null) {
            for (String name : map_name.keySet()) {
                set.add(name);
                links.append("{source : '" + name + "', target : '人名'},");
            }
        }
        if (map_place != null) {
            for (String place : map_place.keySet()) {
                set.add(place);
                links.append("{source : '" + place + "', target : '地名'},");
            }
        }
        if (map_org != null) {
            for (String org : map_org.keySet()) {
                set.add(org);
                links.append("{source : '" + org + "', target : '机构名'},");
            }
        }
        links.append("],");
        StringBuffer nodes = new StringBuffer();
        nodes.append("nodes:[{category:0, name: '文本', value : 20, label: '文章'},");
        nodes.append("{category:1, name: '人名',value : 16},");
        nodes.append("{category:1, name: '地名',value : 16},");
        nodes.append("{category:1, name: '机构名',value : 16},");
        nodes.append("{category:1, name: '关键词',value : 16},");
        nodes.append("{category:1, name: '媒体',value : 16},");
        nodes.append("{category:1, name: '作者',value : 16},");
        nodes.append("{category:1, name: '地域',value : 16},");
        for (String ele : set) {
            nodes.append("{category:2, name: '" + ele + "',value : 15},");
        }
        nodes.append("]");
        return "{" + links + nodes + "}";
    }

    /**
     * 根据算法类型和匹配规则统计
     *
     * @param segment 算法类型
     * @param regex   匹配规则
     * @param text
     * @return
     * <AUTHOR>
     * @time 2017-4-24 下午5:03:18
     */
    private Map<String, Integer> matching(Segment segment, String regex, String text) {
        Pattern pattern = Pattern.compile(regex);
        // 统计数量插入map中
        Map<String, Integer> map = new HashMap<>();
        // 分词 + 实体识别
        List<Term> termList3 = segment.seg(text);
        String term = termList3.toString();
        Matcher matcher = pattern.matcher(term.substring(1, term.length() - 1));
        while (matcher.find()) {
            if (map.containsKey(matcher.group())) {
                // 已经存在的+1
                map.put(matcher.group(), map.get(matcher.group()) + 1);
            } else {
                map.put(matcher.group(), 1);
            }
        }
        // 按照数量从大到小排序
        map = this.sortMapByValue(map);
        return map;
    }

    /**
     * 给分词着色
     *
     * @param word 分词
     * @return
     * <AUTHOR>
     * @time 2017-4-24 下午4:45:07
     */
    private String painting(String word) {
        StringBuffer text = new StringBuffer();
        // 去除换行
        word = word.replaceAll("\n", " ");
        // 分词
        List<Term> terms = HanLP.segment(word);
        // 默认白色
        String color = "#FFFFFF";
        // 设置背景色
        for (Term term : terms) {
            color = WordColorUtil.getColor(this.getColorKey(term.toString()));
            text.append(term.toString().replace(term.toString(),
                    "<font style='background-color:" + color + "'>" + term + "</font> "));
        }
        return text.toString();
    }

    /**
     * 寻找颜色匙特征，并color key
     *
     * @param string
     * @return
     * <AUTHOR>
     * @time 2017-4-24 下午4:45:47
     */
    private String getColorKey(String string) {
        Pattern pattern = Pattern.compile("/[a-z]");
        Matcher matcher = pattern.matcher(string);
        if (matcher.find())
            return matcher.group(0);
        else
            return "/";
    }

    /**
     * map 排序（按照map<value>排序）
     *
     * @param map
     * @return
     * <AUTHOR>
     * @time 2017-4-24 下午5:03:55
     */
    private Map<String, Integer> sortMapByValue(Map<String, Integer> map) {
        if (map == null || map.isEmpty()) {
            return null;
        }
        Map<String, Integer> sortedMap = new LinkedHashMap<>();

        List<Map.Entry<String, Integer>> mapList = new ArrayList<>(map.entrySet());

        // 排序
        mapList.sort((o1, o2) -> (o2.getValue() - o1.getValue()));
        // 返回有序map集合
        for (Map.Entry<String, Integer> mapping : mapList) {
            // 只返回前10条
            if (sortedMap.size() < 10) {
                sortedMap.put(mapping.getKey().replaceAll("/\\w+", ""), mapping.getValue());
            }
        }
        return sortedMap;
    }

}
