package com.boryou.web.module.wechat.util;

import cn.hutool.core.util.NumberUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.extra.spring.SpringUtil;
import cn.hutool.json.JSONUtil;
import com.boryou.common.utils.RedisUtil;
import com.boryou.web.module.wechat.constant.WXConstant;
import com.boryou.web.module.wechat.domain.WarnCode;
import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;

import java.util.concurrent.TimeUnit;

@Slf4j
public class WXUtil {

    private WXUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static WarnCode waitBandGet(Integer eventKey) {
        try {
            if (eventKey == null) {
                return null;
            }
            RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
            String warnCodeStr = redisUtil.get(WXConstant.WAIT_BIND + eventKey);
            if (StrUtil.isBlankIfStr(warnCodeStr)) {
                return null;
            }
            return JSONUtil.toBean(warnCodeStr, WarnCode.class);
        } catch (Exception e) {
            log.warn("WXUtil.waitBandGet 失败, evenKey: {}, 错误: {}", eventKey, e.getMessage());
            return null;
        }
    }

    public static void waitBandRemove(Integer eventKey) {
        try {
            if (eventKey == null) {
                return;
            }
            RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
            redisUtil.delete(WXConstant.WAIT_BIND + eventKey);
        } catch (Exception e) {
            log.warn("WXUtil.waitBandRemove 失败, evenKey: {}, 错误: {}", eventKey, e.getMessage());
        }
    }

    public static boolean waitBandContainsKey(Integer eventKey) {
        try {
            if (eventKey == null) {
                return false;
            }
            RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
            return redisUtil.hasKey(WXConstant.WAIT_BIND + eventKey);
        } catch (Exception e) {
            log.warn("WXUtil.waitBandContainsKey 失败, evenKey: {}, 错误: {}", eventKey, e.getMessage());
            return false;
        }
    }

    public static void waitBandPut(Integer eventKey, WarnCode warnCode) {
        try {
            if (eventKey == null || warnCode == null) {
                return;
            }
            RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
            redisUtil.setEx(WXConstant.WAIT_BIND + eventKey, JSONUtil.toJsonStr(warnCode), 30, TimeUnit.DAYS);
        } catch (Exception e) {
            log.warn("WXUtil.waitBandSet 失败, evenKey: {}, 错误: {}", eventKey, e.getMessage());
        }
    }

    public static void wxUserPut(Integer eventKey, WxOAuth2UserInfo userInfo) {
        try {
            if (eventKey == null || userInfo == null) {
                return;
            }
            RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
            redisUtil.setEx(WXConstant.WX_USER + eventKey, JSONUtil.toJsonStr(userInfo), 29, TimeUnit.DAYS);
        } catch (Exception e) {
            log.warn("WXUtil.wxUserSet 失败, evenKey: {}, 错误: {}", eventKey, e.getMessage());
        }
    }

    public static WxOAuth2UserInfo wxUserGet(Integer eventKey) {
        try {
            if (eventKey == null) {
                return null;
            }
            RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
            String wxOAuth2UserInfoStr = redisUtil.get(WXConstant.WX_USER + eventKey);
            if (StrUtil.isBlankIfStr(wxOAuth2UserInfoStr)) {
                return null;
            }
            return JSONUtil.toBean(wxOAuth2UserInfoStr, WxOAuth2UserInfo.class);
        } catch (Exception e) {
            log.warn("WXUtil.wxUserGet 失败, evenKey: {}, 错误: {}", eventKey, e.getMessage());
            return null;
        }
    }

    public static void openEventCodePut(String openId, Integer eventKey) {
        try {
            if (eventKey == null || StrUtil.isBlankIfStr(openId)) {
                return;
            }
            RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
            redisUtil.setEx(WXConstant.OPENID_EVENT_CODE + openId, String.valueOf(eventKey), 29, TimeUnit.DAYS);
        } catch (Exception e) {
            log.warn("WXUtil.openEventCodePut 失败, evenKey: {}, 错误: {}", eventKey, e.getMessage());
        }
    }

    public static Integer openEventCodeGet(String openId) {
        try {
            if (StrUtil.isBlankIfStr(openId)) {
                return null;
            }
            RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
            String openEvenCodeStr = redisUtil.get(WXConstant.OPENID_EVENT_CODE + openId);
            if (StrUtil.isBlankIfStr(openEvenCodeStr)) {
                return null;
            }
            if (NumberUtil.isInteger(openEvenCodeStr)) {
                return Integer.valueOf(openEvenCodeStr);
            } else {
                return null;
            }
        } catch (Exception e) {
            log.warn("WXUtil.openEventCodeGet 失败, openId: {}, 错误: {}", openId, e.getMessage());
            return null;
        }
    }

    public static void openEventCodeRemove(String openId) {
        try {
            if (StrUtil.isBlankIfStr(openId)) {
                return;
            }
            RedisUtil redisUtil = SpringUtil.getBean(RedisUtil.class);
            redisUtil.delete(WXConstant.OPENID_EVENT_CODE + openId);
        } catch (Exception e) {
            log.warn("WXUtil.openEventCodeRemove 失败, openId: {}, 错误: {}", openId, e.getMessage());
        }
    }

}
