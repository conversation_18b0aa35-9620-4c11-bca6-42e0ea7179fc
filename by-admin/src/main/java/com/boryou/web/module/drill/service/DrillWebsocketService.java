package com.boryou.web.module.drill.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.RedisUtil;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.module.drill.domain.DrillTask;
import com.boryou.web.module.drill.domain.vo.*;
import com.boryou.web.module.drill.enums.RoleEnum;
import com.boryou.web.module.socket.domain.SocketMessage;
import com.boryou.web.module.socket.domain.TransferMessage;
import com.boryou.web.module.socket.enums.SocketChannelEnum;
import com.boryou.web.module.socket.service.WebsocketRedisService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

import static com.boryou.web.module.socket.enums.SocketChannelEnum.*;

@Service
@RequiredArgsConstructor
@Slf4j
public class DrillWebsocketService {

    private final WebsocketRedisService websocketRedisService;
    private final RedisUtil redisUtil;

    public void commentToUser(DrillCommentPushVO data, Long drillTaskId, SysUser user) {
        pushToUser(data, drillTaskId, user, SocketChannelEnum.DRILL_COMMENT);
    }

    public void commentReplyToUser(DrillCommentReplyPushVO data, Long drillTaskId, SysUser user) {
        pushToUser(data, drillTaskId, user, SocketChannelEnum.DRILL_COMMENT_REPLY);
    }

    public void commentLikeToUser(DrillCommentLikePushVO data, Long drillTaskId, SysUser user) {
        pushToUser(data, drillTaskId, user, DRILL_COMMENT_LIKE);
    }

    public void stageToUser(DrillProcessStageVO data, Long drillTaskId, SysUser user) {
        pushToUser(data, drillTaskId, user, DRILL_STAGE);
    }

    public void timeToUser(DrillStageTimerVO data, Long drillTaskId, SysUser user) {
        pushToUser(data, drillTaskId, user, DRILL_TIME);
    }

    public void timeScoreToUser(DrillStageTimerScoreVO data, Long drillTaskId, SysUser user) {
        pushToUser(data, drillTaskId, user, DRILL_TIME_SCORE);
    }

    public void danmuToUser(DrillDanmuVO data, Long drillTaskId, SysUser user) {
        pushToUser(data, drillTaskId, user, DRILL_DANMU_SCORE);
    }

    public void pushToUser(Object data, Long drillTaskId, SysUser user, SocketChannelEnum socketChannelEnum) {
        String nickName = "system";

        if (user != null && CharSequenceUtil.isNotBlank(user.getNickName())) {
            nickName = user.getNickName();
        }
        Map<Long, String> longStringMap = drillGetUser(drillTaskId);
        Set<Long> toUserSet = longStringMap.keySet();
        if (CollUtil.isEmpty(toUserSet)) {
            return;
        }
        List<String> toUsers = Convert.toList(String.class, toUserSet);
        SocketMessage socketMessage = SocketMessage.builder()
                .data(data)
                .channel(socketChannelEnum)
                .build();
        TransferMessage transferMessage = TransferMessage.builder()
                .message(socketMessage)
                .fromUser(nickName)
                .toUsers(toUsers)
                .build();
        websocketRedisService.sendServiceToWs(transferMessage);
    }

    public List<String> getToUsers(DrillTask drillTaskOne) {
        String redCaptain = drillTaskOne.getRedCaptain();
        List<String> redMember = drillTaskOne.getRedMember();
        String blueCaptain = drillTaskOne.getBlueCaptain();
        List<String> blueMember = drillTaskOne.getBlueMember();

        List<String> toUsers = new ArrayList<>();
        if (CharSequenceUtil.isNotBlank(redCaptain)) {
            toUsers.add(redCaptain);
        }
        if (CollUtil.isNotEmpty(redMember)) {
            toUsers.addAll(redMember);
        }
        if (CharSequenceUtil.isNotBlank(blueCaptain)) {
            toUsers.add(blueCaptain);
        }
        if (CollUtil.isNotEmpty(blueMember)) {
            toUsers.addAll(blueMember);
        }
        return toUsers;
    }

    public String getRoleInfoByUser(SysUser user, DrillTaskVO drillTaskVO) {
        String redCaptain = drillTaskVO.getRedCaptain();
        List<String> redMember = drillTaskVO.getRedMember();
        String blueCaptain = drillTaskVO.getBlueCaptain();
        List<String> blueMember = drillTaskVO.getBlueMember();
        return getRoleInfoByUser(user, DrillTask.builder().redCaptain(redCaptain).redMember(redMember)
                .blueCaptain(blueCaptain).blueMember(blueMember).build());
    }

    public String getRoleInfoByUser(SysUser user, DrillTask drillTask) {
        if (user == null) {
            throw new CustomException("用户不能为空");
        }
        boolean isModerator = user.getRoles().stream().anyMatch(i -> i.getRoleKey().equals(RoleEnum.MODERATOR.getCode()));
        if (isModerator) {
            return RoleEnum.MODERATOR.getCode();
        }
        boolean isExpert = user.getRoles().stream().anyMatch(i -> i.getRoleKey().equals(RoleEnum.EXPERT.getCode()));
        if (isExpert) {
            return RoleEnum.EXPERT.getCode();
        }
        Long userId = user.getUserId();
        String userIdStr = String.valueOf(userId);
        String redCaptain = drillTask.getRedCaptain();
        if (CharSequenceUtil.equals(redCaptain, userIdStr)) {
            return RoleEnum.RED_CAP.getCode();
        }
        List<String> redMember = drillTask.getRedMember();
        if (redMember.contains(userIdStr)) {
            return RoleEnum.RED_MEM.getCode();
        }
        String blueCaptain = drillTask.getBlueCaptain();
        if (CharSequenceUtil.equals(blueCaptain, userIdStr)) {
            return RoleEnum.BLUE_CAP.getCode();
        }
        List<String> blueMember = drillTask.getBlueMember();
        if (blueMember.contains(userIdStr)) {
            return RoleEnum.BLUE_MEM.getCode();
        }
        return RoleEnum.SPECTATOR.getCode();
    }

    public Map<Long, String> drillGetUser(Long drillTaskId) {
        if (drillTaskId == null) {
            return MapUtil.newHashMap();
        }
        String key = RedisConstant.DRILL_USER_CACHE + drillTaskId;
        Map<Object, Object> redisMap = redisUtil.hGetAll(key);
        if (MapUtil.isEmpty(redisMap)) {
            return MapUtil.newHashMap();
        }
        Map<Long, String> map = new HashMap<>();
        redisMap.forEach((k, v) -> {
            Long userId = Convert.toLong(k);
            String roleInfoByUser = Convert.toStr(v);
            map.put(userId, roleInfoByUser);
        });
        return map;
    }

    public void drillJoinUser(DrillTask drillTask, SysUser user) {
        if (drillTask == null || user == null) {
            return;
        }
        String roleInfoByUser = getRoleInfoByUser(user, drillTask);
        Long drillTaskId = drillTask.getDrillTaskId();
        Long userId = user.getUserId();
        String key = RedisConstant.DRILL_USER_CACHE + drillTaskId;
        // redisUtil.hPut(key, String.valueOf(userId), roleInfoByUser);

        String userIdStr = Convert.toStr(userId);

        redisUtil.hPutIfAbsent(key, userIdStr, roleInfoByUser);

        // 2. 取消延迟队列中的删除
        String delKey = RedisConstant.DRILL_USER_DELAY_ZSET + drillTaskId;
        redisUtil.zRemove(delKey, userIdStr);
    }

    public void drillLeaveUser(Long drillTaskId, Long userId) {
        if (drillTaskId == null || userId == null) {
            return;
        }
        long delayMillis = 5 * 60 * 1000;
        String delKey = RedisConstant.DRILL_USER_DELAY_ZSET + drillTaskId;
        long executeAt = System.currentTimeMillis() + delayMillis;

        redisUtil.zAdd(delKey, Convert.toStr(userId), executeAt);

        // String key = RedisConstant.DRILL_USER_CACHE + drillTaskId;
        // redisUtil.hDelete(key, String.valueOf(userId));
    }

    public void drillAllLeave(Long drillTaskId) {
        if (drillTaskId == null) {
            return;
        }

        String key = RedisConstant.DRILL_USER_CACHE + drillTaskId;
        redisUtil.delete(key);
    }

}
