package com.boryou.web.module.warn.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Data;

import java.util.Date;

@Data
@TableName("by_warn_read")
public class WarnRead {
    @TableId(value = "id", type = IdType.ASSIGN_ID)
    private Long id;
    private Long warnId;
    private Long userId;
    private Long deptId;
    private Date createTime;
    private String createBy;
}
