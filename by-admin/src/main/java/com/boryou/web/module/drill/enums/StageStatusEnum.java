package com.boryou.web.module.drill.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Getter
@RequiredArgsConstructor
public enum StageStatusEnum {

    NOT_BEGIN("未开始", 1),
    RUNNING("进行中", 2),
    OVER("已结束", 3),
    ;

    private static final Map<String, StageStatusEnum> map = new HashMap<>();

    static {
        StageStatusEnum[] ens = StageStatusEnum.values();
        for (StageStatusEnum en : ens) {
            map.put(en.name, en);
        }
    }

    private final String name;
    private final Integer code;

    public static StageStatusEnum getEnumByLeft(String left) {
        return map.get(left);
    }

}
