package com.boryou.web.module.drill.util;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.extra.spring.SpringUtil;
import com.boryou.web.module.drill.domain.DrillProcessStage;
import com.boryou.web.module.drill.service.DrillProcessStageService;

import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

public class DrillStageNavigationUtil {

    // 缓存结构: drillTaskId -> [按stageOrder排序的阶段列表]
    private static final Map<Long, List<DrillProcessStage>> CACHE = new ConcurrentHashMap<>();

    private DrillStageNavigationUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 获取演练任务的所有阶段（自动缓存）
     */
    public static List<DrillProcessStage> getStages(Long drillTaskId) {
        List<DrillProcessStage> drillProcessStageList = CACHE.get(drillTaskId);
        if (CollUtil.isNotEmpty(drillProcessStageList)) {
            return drillProcessStageList;
        }
        DrillProcessStageService drillProcessStageService = SpringUtil.getBean(DrillProcessStageService.class);
        // 缓存未命中，从数据库查询并排序
        List<DrillProcessStage> stages = drillProcessStageService.lambdaQuery()
                .eq(DrillProcessStage::getDrillTaskId, drillTaskId)
                .eq(DrillProcessStage::getStageShow, true)
                .orderByAsc(DrillProcessStage::getStageOrder)
                .list();
        if (CollUtil.isEmpty(stages)) {
            return Collections.emptyList();
        }
        return Collections.synchronizedList(stages); // 线程安全包装
    }

    /**
     * 获取首阶段ID
     */
    public static Long getFirstStageId(Long drillTaskId) {
        List<DrillProcessStage> stages = getStages(drillTaskId);
        return stages.isEmpty() ? null : stages.get(0).getProcessStageId();
    }

    public static Long getFirstStageId(List<DrillProcessStage> stages) {
        if (CollUtil.isEmpty(stages)) {
            return null;
        }
        stages.sort(Comparator.comparingInt(DrillProcessStage::getStageOrder));
        return stages.isEmpty() ? null : stages.get(0).getProcessStageId();
    }

    /**
     * 获取尾阶段ID
     */
    public static Long getLastStageId(Long drillTaskId) {
        List<DrillProcessStage> stages = getStages(drillTaskId);
        return stages.isEmpty() ? null : stages.get(stages.size() - 1).getProcessStageId();
    }

    /**
     * 获取当前阶段ID
     */
    public static Long getCurrentId(Long drillTaskId) {
        refreshCache(drillTaskId);
        List<DrillProcessStage> stages = getStages(drillTaskId);
        return stages.isEmpty() ? null : stages.stream()
                .filter(s -> s.getStageStatus() == 2)
                .findFirst()
                .map(DrillProcessStage::getProcessStageId)
                .orElse(null);
    }

    /**
     * 获取上一阶段ID
     */
    public static Long getPreviousStageId(Long drillTaskId, Long processStageId) {
        List<DrillProcessStage> stages = getStages(drillTaskId);
        int index = findStageIndex(stages, processStageId);
        return (index > 0) ? stages.get(index - 1).getProcessStageId() : null;
    }

    /**
     * 获取下一阶段ID
     */
    public static Long getNextStageId(Long drillTaskId, Long processStageId) {
        List<DrillProcessStage> stages = getStages(drillTaskId);
        int index = findStageIndex(stages, processStageId);
        return (index < stages.size() - 1) ? stages.get(index + 1).getProcessStageId() : null;
    }

    private static int findStageIndex(List<DrillProcessStage> stages, Long stageId) {
        for (int i = 0; i < stages.size(); i++) {
            if (stages.get(i).getProcessStageId().equals(stageId)) {
                return i;
            }
        }
        return -1;
    }

    /**
     * 刷新任务缓存（数据变更时调用）
     *
     * @param drillTaskId
     */
    public static void refreshCache(Long drillTaskId) {
        CACHE.remove(drillTaskId);
    }

}
