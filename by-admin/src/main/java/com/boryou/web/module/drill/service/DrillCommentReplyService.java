package com.boryou.web.module.drill.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.map.MapUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.web.module.drill.domain.DrillCommentLike;
import com.boryou.web.module.drill.domain.DrillCommentReply;
import com.boryou.web.module.drill.domain.vo.DrillCommentReplyRes;
import com.boryou.web.module.drill.enums.CommentEnum;
import com.boryou.web.module.drill.mapper.DrillCommentReplyMapper;
import com.boryou.web.module.drill.util.LikeRedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;

@Slf4j
@Service
@RequiredArgsConstructor
public class DrillCommentReplyService extends ServiceImpl<DrillCommentReplyMapper, DrillCommentReply> {

    private final LikeRedisUtil likeRedisUtil;
    private final DrillCommentLikeService drillCommentLikeService;

    public Map<Long, List<DrillCommentReplyRes>> convertToTreeStructure(List<DrillCommentReply> commentReplyList,
                                                                        Map<Long, SysUser> userMap,
                                                                        List<Long> commentLikeId,
                                                                        SysUser user) {
        if (CollUtil.isEmpty(commentReplyList) || user == null) {
            return MapUtil.newHashMap();
        }

        // 将数据库中的点赞记录转换为集合，方便快速查找
        Set<Long> likedIdSet = new HashSet<>(commentLikeId);

        // 获取当前用户ID
        Long currentUserId = user.getUserId();

        // 收集所有评论回复ID，用于批量获取Redis中的点赞状态
        List<Long> allReplyIds = new ArrayList<>();
        for (DrillCommentReply reply : commentReplyList) {
            allReplyIds.add(reply.getCommentReplyId());
        }

        // 如果有当前用户ID，尝试从Redis中获取最新的点赞状态
        Map<Long, Boolean> redisLikeStatus = new HashMap<>();
        if (currentUserId != null && !allReplyIds.isEmpty()) {
            try {
                // 假设所有评论都是类型7（回复评论）
                String commentType = CommentEnum.COMMENT_REPLY.getType();

                // 逐个检查并恢复数据
                for (Long replyId : allReplyIds) {
                    // 检查Redis中的点赞数据是否需要恢复
                    boolean needRecover = likeRedisUtil.needRecoverLikeData(currentUserId, replyId, commentType);

                    if (needRecover) {
                        // 从数据库恢复Redis数据
                        final Long finalReplyId = replyId; // 在Lambda中使用需要final变量
                        likeRedisUtil.recoverLikeData(
                                currentUserId,
                                replyId,
                                commentType,
                                // 从数据库获取点赞状态
                                () -> {
                                    DrillCommentLike like = drillCommentLikeService.lambdaQuery()
                                            .eq(DrillCommentLike::getUserId, currentUserId)
                                            .eq(DrillCommentLike::getCommentReplyId, finalReplyId)
                                            .eq(DrillCommentLike::getCommentType, commentType)
                                            .one();
                                    return like != null && Objects.equals(like.getStatus(), 1);
                                },
                                // 从数据库获取点赞数量
                                () -> Convert.toLong(drillCommentLikeService.lambdaQuery()
                                        .eq(DrillCommentLike::getCommentReplyId, finalReplyId)
                                        .eq(DrillCommentLike::getCommentType, commentType)
                                        .eq(DrillCommentLike::getStatus, 1)
                                        .count())
                        );
                    }

                    // 获取最新的点赞状态
                    Boolean isLiked = likeRedisUtil.getLikeStatus(currentUserId, replyId, commentType);
                    redisLikeStatus.put(replyId, isLiked);

                    // 更新点赞集合
                    if (isLiked) {
                        likedIdSet.add(replyId);
                    } else {
                        likedIdSet.remove(replyId);
                    }
                }
            } catch (Exception e) {
                log.error("从Redis获取点赞状态失败", e);
            }
        }

        List<DrillCommentReplyRes> drillCommentReplyResList = BeanUtil.copyToList(commentReplyList, DrillCommentReplyRes.class);
        for (DrillCommentReplyRes drillCommentReplyRes : drillCommentReplyResList) {
            Long userId = drillCommentReplyRes.getUserId();
            if (userMap.containsKey(userId)) {
                SysUser sysUser = userMap.get(userId);
                String userName = sysUser.getUserName();
                String nickName = sysUser.getNickName();
                String avatar = sysUser.getAvatar();

                drillCommentReplyRes.setUserName(userName);
                drillCommentReplyRes.setNickName(nickName);
                drillCommentReplyRes.setAvatar(avatar);
            }

            // 使用更新后的点赞集合
            Long commentReplyId = drillCommentReplyRes.getCommentReplyId();
            drillCommentReplyRes.setIsLike(likedIdSet.contains(commentReplyId));

            // 从Redis中获取最新的点赞数
            try {
                if (currentUserId != null) {
                    String commentType = CommentEnum.COMMENT_REPLY.getType();

                    // 检查Redis中的点赞数据是否需要恢复
                    boolean needRecover = likeRedisUtil.needRecoverLikeData(currentUserId, commentReplyId, commentType);

                    if (needRecover) {
                        // 从数据库恢复Redis数据
                        final Long finalReplyId = commentReplyId; // 在Lambda中使用需要final变量
                        likeRedisUtil.recoverLikeData(
                                currentUserId,
                                commentReplyId,
                                commentType,
                                // 从数据库获取点赞状态
                                () -> {
                                    DrillCommentLike like = drillCommentLikeService.lambdaQuery()
                                            .eq(DrillCommentLike::getUserId, currentUserId)
                                            .eq(DrillCommentLike::getCommentReplyId, finalReplyId)
                                            .eq(DrillCommentLike::getCommentType, commentType)
                                            .one();
                                    return like != null && Objects.equals(like.getStatus(), 1);
                                },
                                // 从数据库获取点赞数量
                                () -> Convert.toLong(drillCommentLikeService.lambdaQuery()
                                        .eq(DrillCommentLike::getCommentReplyId, finalReplyId)
                                        .eq(DrillCommentLike::getCommentType, commentType)
                                        .eq(DrillCommentLike::getStatus, 1)
                                        .count())
                        );
                    }

                    // 获取最新的点赞数
                    Long likeCount = likeRedisUtil.getLikeCount(commentReplyId, commentType);
                    if (likeCount != null && likeCount > 0) {
                        drillCommentReplyRes.setLikeCount(likeCount.intValue());
                    }
                }
            } catch (Exception e) {
                log.error("从Redis获取点赞数失败: commentReplyId={}", commentReplyId, e);
            }
        }

        Map<Long, List<DrillCommentReplyRes>> drillComentReplyComentMapReturn = new HashMap<>();

        Map<Long, List<DrillCommentReplyRes>> drillComentReplyComentMap = CollStreamUtil.groupByKey(drillCommentReplyResList, DrillCommentReplyRes::getCommentId);

        drillComentReplyComentMap.forEach((commentId, drillCommentReplyRes) -> {
            // 创建父ID到子评论列表的映射
            Map<Long, List<DrillCommentReplyRes>> parentIdToChildren = CollStreamUtil.groupByKey(drillCommentReplyRes,
                    DrillCommentReplyRes::getParentId);

            // 获取顶级评论并按时间排序
            List<DrillCommentReplyRes> topLevelComments = parentIdToChildren.getOrDefault(0L, new ArrayList<>());
            topLevelComments.sort(Comparator.comparing(DrillCommentReplyRes::getCreatedTime));

            // 为每个顶级评论构建子树
            for (DrillCommentReplyRes comment : topLevelComments) {
                populateChildren(comment, parentIdToChildren);
            }
            if (CollUtil.isNotEmpty(topLevelComments)) {
                drillComentReplyComentMapReturn.put(commentId, topLevelComments);
            }
        });
        return drillComentReplyComentMapReturn;

    }

    private void populateChildren(DrillCommentReplyRes parent,
                                  Map<Long, List<DrillCommentReplyRes>> parentIdToChildren) {
        Long parentId = parent.getCommentReplyId();
        List<DrillCommentReplyRes> children = parentIdToChildren.get(parentId);
        if (children == null) {
            parent.setChildren(Collections.emptyList());
            return;
        }

        // 按时间排序子评论
        children.sort(Comparator.comparing(DrillCommentReplyRes::getCreatedTime));
        parent.setChildren(children);

        // 递归处理子评论的子节点
        for (DrillCommentReplyRes child : children) {
            populateChildren(child, parentIdToChildren);
        }
    }

}
