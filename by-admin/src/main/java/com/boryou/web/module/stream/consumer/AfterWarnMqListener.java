package com.boryou.web.module.stream.consumer;

import cn.hutool.json.JSONUtil;
import com.boryou.web.module.search.entity.EsSpecialData;
import com.boryou.web.module.search.entity.vo.WarnAfterVO;
import com.boryou.web.module.search.service.EsSpecialDataService;
import com.boryou.web.module.stream.util.RedisStreamUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.connection.stream.RecordId;
import org.springframework.data.redis.stream.StreamListener;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Map;

@Slf4j
@Component
public class AfterWarnMqListener implements StreamListener<String, MapRecord<String, String, String>> {
    @Resource
    private EsSpecialDataService esSpecialDataService;
    @Resource
    private RedisStreamUtil redisStreamUtil;

    @Override
    public void onMessage(MapRecord<String, String, String> message) {
        // stream的key值
        String streamKey = message.getStream();
        //消息ID
        RecordId recordId = message.getId();
        //消息内容
        Map<String, String> msg = message.getValue();
        log.warn("处理消息: {}", msg);
        if (msg.containsKey("b")) {
            try {
                String body = msg.get("b");
                WarnAfterVO warnAfterVO = JSONUtil.toBean(body, WarnAfterVO.class);
                String m = warnAfterVO.getM();
                Long i = warnAfterVO.getI();
                String c = warnAfterVO.getC();
                Integer s = warnAfterVO.getS();
                EsSpecialData esSpecialData = new EsSpecialData();
                esSpecialData.setMd5(m);
                esSpecialData.setIndexId(i);
                esSpecialData.setSubmit(s);
                esSpecialData.setCreateBy(c);
                esSpecialDataService.updateSearchData(esSpecialData);
            } catch (Exception e) {
                e.printStackTrace();
            }
        }
        //逻辑处理完成后，ack消息，删除消息，group为消费组名称
        String group = "VS_GROUP";
        redisStreamUtil.ack(streamKey, group, recordId.getValue());
        redisStreamUtil.del(streamKey, recordId.getValue());
        log.warn("消息处理完成");
    }

}
