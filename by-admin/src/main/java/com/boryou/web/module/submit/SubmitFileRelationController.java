package com.boryou.web.module.submit;

import com.boryou.common.core.controller.BaseController;
import com.boryou.submit.service.ISubmitFileRelationService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

/**
 * 信息报送附件Controller
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@RestController
@RequestMapping("/submit/file")
public class SubmitFileRelationController extends BaseController {
    @Autowired
    private ISubmitFileRelationService submitFileRelationService;

//    /**
//     * 查询信息报送附件列表
//     */
//    @Secret(value = SubmitFileRelation.class)
//    @GetMapping("/list")
//    public TableDataInfo list(SubmitFileRelation submitFileRelation)
//    {
//        startPage();
//        List<SubmitFileRelation> list = submitFileRelationService.selectSubmitFileRelationList(submitFileRelation);
//        return getDataTable(list);
//    }
//    /**
//     * 导出信息报送附件列表
//     */
//    @Log(title = "信息报送附件", businessType = BusinessType.EXPORT)
//    @GetMapping("/export")
//    public AjaxResult export(SubmitFileRelation submitFileRelation)
//    {
//        List<SubmitFileRelation> list = submitFileRelationService.selectSubmitFileRelationList(submitFileRelation);
//        ExcelUtil<SubmitFileRelation> util = new ExcelUtil<SubmitFileRelation>(SubmitFileRelation.class);
//        return util.exportExcel(list, "submitfile");
//    }
//
//    /**
//     * 获取信息报送附件详细信息
//     */
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id)
//    {
//        return AjaxResult.success(submitFileRelationService.selectSubmitFileRelationById(id));
//    }
//
//    /**
//     * 新增信息报送附件
//     */
//    @Log(title = "信息报送附件", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody SubmitFileRelation submitFileRelation)
//    {
//        return toAjax(submitFileRelationService.insertSubmitFileRelation(submitFileRelation));
//    }
//
//    /**
//     * 修改信息报送附件
//     */
//    @Log(title = "信息报送附件", businessType = BusinessType.UPDATE)
//    @PutMapping
//    public AjaxResult edit(@RequestBody SubmitFileRelation submitFileRelation)
//    {
//        return toAjax(submitFileRelationService.updateSubmitFileRelation(submitFileRelation));
//    }
//
//    /**
//     * 删除信息报送附件
//     */
//    @Log(title = "信息报送附件", businessType = BusinessType.DELETE)
//	@DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable String[] ids)
//    {
//        return toAjax(submitFileRelationService.deleteSubmitFileRelationByIds(ids));
//    }
}
