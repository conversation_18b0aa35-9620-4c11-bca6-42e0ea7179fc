package com.boryou.web.module.submit.mapper;

import com.boryou.web.module.search.entity.EsSpecialData;
import com.boryou.web.module.search.entity.vo.EsSpecialDataVO;
import com.boryou.web.module.submit.entity.Submit;

import java.util.List;


/**
 * 报送记录Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
public interface SubmitMapper {
    /**
     * 查询报送记录
     *
     * @param id 报送记录ID
     * @return 报送记录
     */
    Submit selectSubmitById(Long id);

    /**
     * 查询报送记录列表
     *
     * @param submit 报送记录
     * @return 报送记录集合
     */
    List<Submit> selectSubmitList(Submit submit);

    List<EsSpecialDataVO> selectSubmitListBySpecialData(EsSpecialDataVO specialData);

    /**
     * 新增报送记录
     *
     * @param submit 报送记录
     * @return 结果
     */
    int insertSubmit(Submit submit);

    /**
     * 修改报送记录
     *
     * @param submit 报送记录
     * @return 结果
     */
    int updateSubmit(Submit submit);

    /**
     * 删除报送记录
     *
     * @param id 报送记录ID
     * @return 结果
     */
    int deleteSubmitById(Long id);

    /**
     * 批量删除报送记录
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    int deleteSubmitByIds(Long[] ids);
}
