package com.boryou.web.module.search.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.constant.NoticeEnum;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.module.notice.domain.ByNotice;
import com.boryou.web.module.notice.service.IByNoticeService;
import com.boryou.web.module.search.entity.EsLikeData;
import com.boryou.web.module.search.entity.EsSpecialData;
import com.boryou.web.module.search.entity.IndexData;
import com.boryou.web.module.search.entity.vo.EsSpecialDataVO;
import com.boryou.web.module.search.mapper.EsSpecialDataMapper;
import com.boryou.web.module.search.service.EsSpecialDataService;
import com.boryou.web.module.submit.mapper.SubmitMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class EsSpecialDataServiceImpl extends ServiceImpl<EsSpecialDataMapper, EsSpecialData> implements EsSpecialDataService {

    @Resource
    private SubmitMapper submitMapper;

    private final IByNoticeService noticeService;

    @Override
    public int updateSearchData(EsSpecialData specialData) {
        //检查是否存在
        QueryWrapper<EsSpecialData> qw = new QueryWrapper<>();
        qw.eq(specialData.getCreateBy() != null, "create_by", specialData.getCreateBy())
                .and(
                        q -> q.eq(specialData.getIndexId() != null, "index_id", specialData.getIndexId())
                                .or()
                                .eq(StrUtil.isNotEmpty(specialData.getMd5()), "md5", specialData.getMd5())
                );
        List<EsSpecialData> list = baseMapper.selectList(qw);
        Date createTime = specialData.getCreateTime();
        if (CollUtil.isNotEmpty(list)) {
            //修改情感数据同时修改舆情要情IndexData
            if (specialData.getIndexId() != null && specialData.getEmotionFlag() != null) {
                IndexData data = new IndexData();
                data.setIndexId(specialData.getIndexId());
                data.setEmotionFlag(specialData.getEmotionFlag());
                baseMapper.updateIndexDataEmotionFlag(data);
            }
            specialData.setId(list.get(0).getId());
            baseMapper.updateById(specialData);//取消打标
        } else {//第一次打标
            if (createTime != null) {
                //保存索引数据
                List<IndexData> datas = baseMapper.selectIndexDataById(specialData.getIndexId());
                if (CollUtil.isEmpty(datas) && specialData.getIndexId() != null) {
                    EsSearchBO bo = new EsSearchBO();
                    String format = DateUtil.format(createTime, DatePattern.NORM_DATETIME_PATTERN);
                    bo.setStartTime(format);
                    bo.setEndTime(format);
                    bo.setId(String.valueOf(specialData.getIndexId()));
                    EsBean esBean = EsSearchUtil.searchByIdTimex(bo);
                    if (esBean != null) {
                        IndexData data = new IndexData();
                        BeanUtil.copyProperties(esBean, data, "contentAreaCode");
                        data.setContentAreaCode(CollUtil.join(esBean.getContentAreaCode(), ","));
                        data.setIndexId(Long.valueOf(esBean.getId()));
                        baseMapper.insertIndexData(data);
                        if (specialData.getMd5() == null) {
                            specialData.setMd5(esBean.getMd5());
                        }
                    }
                }

            }
            specialData.setCreateTime(DateUtil.date());
            baseMapper.insert(specialData);
        }
        //通知逻辑
        if (createTime != null && ((null != specialData.getDeal() && specialData.getDeal() > 0) || (null != specialData.getFollow() && specialData.getFollow() > 0))) {
            ByNotice byNotice = new ByNotice();
            if ((null != specialData.getDeal() && specialData.getDeal() > 0)) {
                byNotice.setNoticeType(NoticeEnum.PROCESS_MAKR.getType());
            } else if ((null != specialData.getFollow() && specialData.getFollow() > 0)) {
                byNotice.setNoticeType(NoticeEnum.FOCUS.getType());
            }
            byNotice.setNoticeTime(new Date());
            byNotice.setDocIndexId(specialData.getIndexId());
            byNotice.setTime(createTime);
            noticeService.saveSiteNotice(byNotice);
        }
        return 1;
    }

    @Override
    public int insertLikeData(EsLikeData data) {
        data.setUserId(Long.valueOf(SecurityUtils.getUserId()));
        EsLikeData check = new EsLikeData();
        check.setUserId(data.getUserId());
        check.setIndexId(data.getIndexId());
        if (CollUtil.isEmpty(baseMapper.selectEsLikeData(check))) {
            data.setId(IdUtil.getSnowflakeNextId());
            return baseMapper.insertEsLikeData(data);
        }
        return 0;
    }

    @Override
    public List<EsLikeData> getLikeDatas(EsLikeData data) {
        data.setUserId(Long.valueOf(SecurityUtils.getUserId()));
        return baseMapper.selectEsLikeData(data);
    }

    @Override
    public List<EsSpecialData> getEsSpecialDatasByList(List<String> indexs, List<String> md5s) {
        boolean hasIndex = CollUtil.isNotEmpty(indexs);
        boolean hasMd5 = CollUtil.isNotEmpty(md5s);
        QueryWrapper<EsSpecialData> qw = new QueryWrapper<>();
        if (hasIndex && hasMd5) {
            qw.eq("create_by", SecurityUtils.getLoginUser().getUser().getUserName())
                    .and(
                            q -> q.in("index_id", indexs)
                                    .or()
                                    .in("md5", md5s)
                    );
            return baseMapper.selectList(qw);
        }
        if (!hasIndex && hasMd5) {
            qw.eq("create_by", SecurityUtils.getLoginUser().getUser().getUserName())
                    .and(
                            q -> q.in("md5", md5s)
                    );
            return baseMapper.selectList(qw);
        }
        return new ArrayList<>();
    }

    @Override
    public List<EsSpecialDataVO> getEsSpecialDataPage(EsSpecialDataVO specialData) {
        //同组织隔离
        specialData.setDeptId(SecurityUtils.getLoginUser().getUser().getDeptId());
        List<EsSpecialDataVO> esSpecialDatas = new ArrayList<>();
        if (specialData.getSpecialType() == 1) {
//            specialData.setCreateBy(SecurityUtils.getUserId());
            esSpecialDatas = submitMapper.selectSubmitListBySpecialData(specialData);
        } else if (specialData.getSpecialType() == 2 || specialData.getSpecialType() == 3) {
//            specialData.setCreateBy(SecurityUtils.getUsername());
            esSpecialDatas = baseMapper.getEsSpecialDataPage(specialData);
        }

        for (EsSpecialDataVO esSpecialData : esSpecialDatas) {
            esSpecialData.setTypeName(MediaTypeEnum.getDesc(String.valueOf(esSpecialData.getType())));
        }

//        Map<String, List<EsSpecialDataVO>> dataMap = esSpecialDatas.stream().collect(Collectors.groupingBy(vo -> vo.getIndexId().toString()));
//        PageResult<EsSpecialDataVO> result = new PageResult<>();
//        if (dataMap.size() == 0) {
//            result.setTotal(0);
//            return result;
//        }
//        EsSearchBO bo = new EsSearchBO();
//        bo.setId(CollUtil.join(dataMap.keySet(), ","));
//        bo.setStartTime(specialData.getStartTime());
//        bo.setEndTime(specialData.getEndTime());
//        //处置时间不等于文章时间
//        if (StrUtil.isEmpty(bo.getStartTime()) && StrUtil.isEmpty(bo.getEndTime())) {
//
//
////            List<Long> time = esSpecialDatas.stream().filter(vo -> vo.getCreateTime() != null).map(vo -> vo.getCreateTime().getTime()).collect(Collectors.toList());
////            time.sort(Comparator.comparingLong(a -> a));
////
////            bo.setStartTime(DateUtil.format(DateUtil.date(time.get(0)), DatePattern.NORM_DATETIME_PATTERN));
////            bo.setEndTime(DateUtil.format(DateUtil.date(time.get(time.size() - 1)), DatePattern.NORM_DATETIME_PATTERN));
//        }
//
//        if (specialData.getEmotionFlag() != null) {
//            bo.setEmotionFlag(String.valueOf(specialData.getEmotionFlag()));
//        }
//        bo.setType(specialData.getType());
//        bo.setPageSize(specialData.getPageSize());
//        bo.setPageNum(specialData.getPageNum());
//        if (StrUtil.isNotEmpty(specialData.getContent())) {
//            bo.setKeyWord1(specialData.getContent());
//        }
//        //时间降序
//        bo.setSortType(3);
//        PageResult<EsBean> pageResult = searchService.search(bo);
//        BeanUtil.copyProperties(pageResult, result);
//        for (EsBean bean : pageResult) {
//            List<EsSpecialDataVO> esSpecialDataVOs = dataMap.get(bean.getId());
//            for (EsSpecialDataVO esSpecialDataVO : esSpecialDataVOs) {
//                if (esSpecialDataVO.getDeadLine() != null && DateUtil.date().getTime() > esSpecialDataVO.getDeadLine().getTime()) {
//                    esSpecialDataVO.setProcessStatus("已过期");
//                }
//                esSpecialDataVO.setTitle(bean.getTitle());
//                esSpecialDataVO.setText(bean.getText());
//                esSpecialDataVO.setEmotionFlag(bean.getEmotionFlag());
//                esSpecialDataVO.setType(bean.getType().toString());
//                esSpecialDataVO.setTypeName(bean.getTypeName());
//                esSpecialDataVO.setIsOriginal(bean.getIsOriginal());
//                esSpecialDataVO.setAuthor(bean.getAuthor());
//                esSpecialDataVO.setHostName(bean.getHost());
//                esSpecialDataVO.setUrl(bean.getUrl());
//                esSpecialDataVO.setPublishTime(bean.getPublishTime());
//                esSpecialDataVO.setMd5(bean.getMd5());
//                result.add(esSpecialDataVO);
//            }
//
//        }
        return esSpecialDatas;
    }

}
