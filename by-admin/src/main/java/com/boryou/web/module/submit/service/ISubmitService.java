package com.boryou.web.module.submit.service;

import com.boryou.web.domain.msg.MessageVO;
import com.boryou.web.module.submit.entity.Submit;

import java.util.List;


/**
 * 报送记录Service接口
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
public interface ISubmitService {
    /**
     * 查询报送记录
     *
     * @param id 报送记录ID
     * @return 报送记录
     */
    public Submit selectSubmitById(Long id);

    /**
     * 查询报送记录列表
     *
     * @param submit 报送记录
     * @return 报送记录集合
     */
    public List<Submit> selectSubmitList(Submit submit);

    /**
     * 新增报送记录
     *
     * @param submit 报送记录
     * @return 结果
     */
    public int insertSubmit(Submit submit);

    /**
     * 修改报送记录
     *
     * @param submit 报送记录
     * @return 结果
     */
    public int updateSubmit(Submit submit);

    /**
     * 批量删除报送记录
     *
     * @param ids 需要删除的报送记录ID
     * @return 结果
     */
    public int deleteSubmitByIds(Long[] ids);

    /**
     * 删除报送记录信息
     *
     * @param id 报送记录ID
     * @return 结果
     */
    public int deleteSubmitById(Long id);

    boolean insertShortSubmit(MessageVO messageVO);

    int insertSubmitProcess(Submit submit);

    Submit getProcessInfo(Submit submit);
}
