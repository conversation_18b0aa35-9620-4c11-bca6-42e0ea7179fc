package com.boryou.web.module.area.controller;

import cn.hutool.json.JSONUtil;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.module.area.entity.AreaTree;
import com.boryou.web.module.area.service.AreaService;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.List;

/**
 * 方案Controller
 *
 * <AUTHOR>
 * @date 2024-05-22
 */
@RestController
public class AreaController extends BaseController {

    @Resource
    private AreaService areaService;

    @GetMapping("/area/info/{areaId}")
    public AjaxResult areaInfo(@PathVariable("areaId") String areaId) {
        Object data = areaService.areaInfo(areaId);
        if (JSONUtil.isNull(data)) {
            return AjaxResult.success();
        }
        return AjaxResult.success(data);
    }

    @GetMapping("/area/tree/{areaId}/{deep}")
    public AjaxResult areaTree(@PathVariable("areaId") String areaId, @PathVariable("deep") Integer deep) {
        List<AreaTree> areaTrees = areaService.areaTree(areaId, deep);
        return AjaxResult.success(areaTrees);
    }

    @GetMapping("/area/update")
    public AjaxResult areaUpdate() {
        areaService.areaUpdate();
        return AjaxResult.success();
    }

}
