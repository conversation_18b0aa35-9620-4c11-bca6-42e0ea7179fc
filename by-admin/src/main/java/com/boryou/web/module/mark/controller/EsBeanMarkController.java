package com.boryou.web.module.mark.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkPage;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import com.boryou.web.module.mark.service.EsBeanMarkService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequiredArgsConstructor
public class EsBeanMarkController {

    private final EsBeanMarkService esBeanMarkService;

    /**
     * 分页查询EsBeanMark信息
     *
     * @param esBeanMarkVO 查询参数
     * @return 分页结果
     */
    @PostMapping("/esBeanMark/list")
    public AjaxResult getEsBeanMarkList(@RequestBody @Validated EsBeanMarkPage esBeanMarkPage) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Page<EsBeanMarkVO> page = esBeanMarkService.getEsBeanMarkPage(esBeanMarkPage, user);
        return AjaxResult.success(page);
    }

    /**
     * 添加或更新EsBeanMark信息
     *
     * @param esBeanMarkVO EsBeanMark信息
     * @return 操作结果
     */
    @PostMapping("/esBeanMark/save")
    public AjaxResult saveEsBeanMark(@RequestBody @Validated EsBeanMarkVO esBeanMarkVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        boolean result = esBeanMarkService.saveOrUpdateEsBeanMark(esBeanMarkVO, user);
        return AjaxResult.optionResult(result);
    }

    /**
     * 删除EsBeanMark信息
     *
     * @param esBeanMarkVO 删除参数
     * @return 操作结果
     */
    @PostMapping("/esBeanMark/delete")
    public AjaxResult deleteEsBeanMark(@RequestBody EsBeanMarkVO esBeanMarkVO) {
        boolean result = esBeanMarkService.deleteEsBeanMark(esBeanMarkVO);
        return AjaxResult.optionResult(result);
    }

}
