package com.boryou.web.module.report.service;

import cn.hutool.json.JSONObject;
import com.boryou.web.module.report.entity.Report;
import com.boryou.web.module.report.entity.ReportTemplate;
import com.boryou.web.module.report.entity.vo.ReportVO;
import org.springframework.web.multipart.MultipartFile;

import javax.servlet.http.HttpServletResponse;
import java.io.File;
import java.io.InputStream;
import java.util.List;

/**
 * <AUTHOR>
 */
public interface ReportService {

    Report selectById(Long reportId);

    Report selectIssue(Report report);

    List<Report> selectByIds(String reportIds);

    List<ReportVO> selectReport(ReportVO report);

    int deleteByIds(String ids);

    int insert(Report report);

    Long upload(MultipartFile file);

    int updateById(Report report);

    ReportTemplate selectTemplateById(Long tempId);

    JSONObject selectTemplate(ReportTemplate reportTemplate);

    int deleteTemplateByIds(String ids);

    int insertTemplate(ReportTemplate reportTemplate);

    int updateDefaultTemplate(Long id);

    int updateTemplateById(ReportTemplate reportTemplate);

    List<ReportTemplate> selectTemplateSortDefault(ReportTemplate reportTemplate);

    int changeTemplateToSys(Long id);

    void changeFileTypeToDownload(InputStream in, HttpServletResponse response, String fileType);

    InputStream createLocalReportFile(ReportVO reportVO, File file);
}
