package com.boryou.web.module.drill.service;

import cn.hutool.core.bean.BeanUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.web.module.drill.domain.DrillProcessStage;
import com.boryou.web.module.drill.domain.vo.DrillProcessStageDTO;
import com.boryou.web.module.drill.mapper.DrillProcessStageMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrillProcessStageService extends ServiceImpl<DrillProcessStageMapper, DrillProcessStage> {

    public void updateStageStatus(Integer stageStatus, Date start, Date end, Long processStageId) {
        if (stageStatus == null || processStageId == null) {
            return;
        }
        this.lambdaUpdate().set(DrillProcessStage::getStageStatus, stageStatus)
                .set(start != null, DrillProcessStage::getStartTime, start)
                .set(end != null, DrillProcessStage::getEndTime, end)
                .eq(DrillProcessStage::getProcessStageId, processStageId)
                .update();
    }

    public DrillProcessStageDTO getDrillProcessStageById(Long processStageId) {
        if (processStageId == null) {
            return null;
        }
        DrillProcessStage drillProcessStage = this.lambdaQuery().eq(DrillProcessStage::getProcessStageId, processStageId).one();
        if (drillProcessStage == null) {
            return null;
        }
        return BeanUtil.copyProperties(drillProcessStage, DrillProcessStageDTO.class);
    }

    public List<DrillProcessStageDTO> getDrillProcessStageByDrillTaskId(Long drillTaskId, Boolean stageShow) {
        if (drillTaskId == null) {
            return Collections.emptyList();
        }
        List<DrillProcessStage> drillProcessStageList = this.lambdaQuery()
                .eq(DrillProcessStage::getDrillTaskId, drillTaskId)
                .eq(stageShow != null, DrillProcessStage::getStageShow, stageShow)
                .list();
        return BeanUtil.copyToList(drillProcessStageList, DrillProcessStageDTO.class);
    }

    public List<DrillProcessStage> getDrillProcessStageByDrillTaskIdStatus(Long drillTaskId) {
        if (drillTaskId == null) {
            return Collections.emptyList();
        }
        return this.lambdaQuery()
                .eq(DrillProcessStage::getDrillTaskId, drillTaskId)
                .list();
    }

}
