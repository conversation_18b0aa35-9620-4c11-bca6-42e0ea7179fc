package com.boryou.web.module.drill.util;

import cn.hutool.core.text.CharSequenceUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

public class UpdateUtil {

    private UpdateUtil() {
        throw new IllegalStateException("Utility class");
    }

    // 通用字段更新方法
    public static void updateStringField(String srcValue, String currentValue, Consumer<String> setter) {
        if (CharSequenceUtil.isNotBlank(srcValue) && !srcValue.equals(currentValue)) {
            setter.accept(srcValue);
        }
    }

    public static void updateDateField(Date srcValue, Date currentValue, Consumer<Date> setter) {
        if (srcValue != null && !srcValue.equals(currentValue)) {
            setter.accept(srcValue);
        }
    }

    public static void updateListField(List<String> srcValue, List<String> currentValue, Consumer<List<String>> setter) {
        if (srcValue != null && !srcValue.equals(currentValue)) {
            setter.accept(new ArrayList<>(srcValue));
        }
    }

    public static <T extends Number> void updateNumericField(T srcValue, T currentValue, Consumer<T> setter) {
        if (srcValue != null && !srcValue.equals(currentValue)) {
            setter.accept(srcValue);
        }
    }

}
