package com.boryou.web.module.submit.entity;

import java.util.Date;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.NumberSerializers;
import com.fasterxml.jackson.databind.ser.std.SerializableSerializer;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import com.google.common.primitives.Longs;
import lombok.Data;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import com.boryou.common.annotation.Excel;
import com.boryou.common.core.domain.BaseEntity;

/**
 * 报送记录对象 by_submit
 *
 * <AUTHOR>
 * @date 2024-07-22
 */
@Data
public class Submit {
    private static final long serialVersionUID = 1L;

    /**
     * id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 涉及法院
     */
    @Excel(name = "涉及法院")
    private String courtName;

    /**
     * 风险级别
     */
    @Excel(name = "风险级别")
    private String riskLevel;

    /**
     * 来源
     */
    @Excel(name = "来源")
    private String source;

    /**
     * 摘要
     */
    @Excel(name = "摘要")
    private String summary;

    /**
     * url
     */
    @Excel(name = "url")
    private String url;

    /**
     * 手填手机号
     */
    @Excel(name = "手填手机号")
    private String phone;

    /**
     * 索引id
     */
    @Excel(name = "索引id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long docIndexId;
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 联系人id，可能有多个
     */
    @Excel(name = "联系人id，可能有多个")
    private String contactIds;

    /**
     * 处置截至时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "处置截至时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date deadline;

    /**
     * 处理时间范围
     */
    @Excel(name = "处理时间范围")
    private String deadlineNum;

    /**
     * 处置建议
     */
    @Excel(name = "处置建议")
    private String suggest;

    @Excel(name = "用户姓名")
    private String userName;

    /**
     * 处置文本
     */
    @Excel(name = "处置文本")
    private String processText;
    /**
     * 0未处置 1.已处置
     */
    @Excel(name = "0未处置 1.已处置")
    private String processStatus;
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date processTime;


}
