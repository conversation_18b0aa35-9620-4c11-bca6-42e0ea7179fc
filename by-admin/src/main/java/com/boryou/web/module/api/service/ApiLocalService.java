package com.boryou.web.module.api.service;


import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.URLUtil;
import cn.hutool.db.PageResult;
import cn.hutool.json.JSONUtil;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.module.api.domain.vo.ApiAccountVO;
import com.boryou.web.service.SearchService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
@RequiredArgsConstructor
public class ApiLocalService {

    private final SearchService searchService;

    public PageResult<EsBean> article(ApiAccountVO apiAccountVO) {
        String author = apiAccountVO.getAuthor();
        String type = apiAccountVO.getType();
        String url = apiAccountVO.getUrl();
        String hostGet = apiAccountVO.getHost();
        if (CharSequenceUtil.isAllBlank(author, type, url)) {
            return new PageResult<>();
        }
        Integer pageNum = apiAccountVO.getPageNum();
        Integer pageSize = apiAccountVO.getPageSize();
        if (pageNum == null || pageSize == null) {
            pageNum = 1;
            pageSize = 10;
        }
        String videoHost = "";
        String host = "";
        if (CharSequenceUtil.isNotBlank(url)) {
            host = URLUtil.url(url).getHost();
            if (CharSequenceUtil.isNotBlank(hostGet)) {
                host = host + "," + hostGet;
            }
            if (CharSequenceUtil.isNotBlank(host) && !host.startsWith("www.")) {
                host = host + ",www." + host;
            }
        }
        SearchVO searchVO = new SearchVO();
        if (CharSequenceUtil.equals(type, "11")) {
            String douyin = "www.douyin.com,www.iesdouyin.com,v.douyin.com,live.douyin.com";
            String bilibili = "www.bilibili.com,t.bilibili.com";
            if (CharSequenceUtil.split(douyin, ",").contains(host)) {
                videoHost = douyin;
                host = "";
            } else if (CharSequenceUtil.split(bilibili, ",").contains(host)) {
                host = "";
                videoHost = bilibili;
            }
            searchVO.setVideoHost(videoHost);
        }
        searchVO.setHost(host);
        searchVO.setAuthor(author);
        searchVO.setType(type);
        searchVO.setPageNum(pageNum);
        searchVO.setPageSize(pageSize);
        searchVO.setTimeIndex(29);
        EsSearchBO bo = JSONUtil.toBean(JSONUtil.toJsonStr(searchVO), EsSearchBO.class);
        searchVO.getSearchTimeRange(bo);
        //bo.setEmotionFlag("1");
        bo.setSortType(3);
        return searchService.search(bo);
    }

}
