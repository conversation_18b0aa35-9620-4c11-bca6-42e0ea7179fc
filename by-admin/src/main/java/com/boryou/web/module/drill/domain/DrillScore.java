package com.boryou.web.module.drill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 得分规则配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("by_drill_score")
public class DrillScore {

    /**
     * 规则ID（自增主键）
     */
    @TableId(value = "drill_score_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillScoreId;

    /**
     * 评论类型（空表示默认）
     */
    private Integer commentType;

    private String commentName;

    /**
     * 得分值
     */
    private String score;

}
