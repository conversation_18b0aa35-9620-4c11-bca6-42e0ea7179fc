package com.boryou.web.module.business;

import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.boryou.common.annotation.Secret;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.domain.InfoType;
import com.boryou.query.TypeQuery;
import com.boryou.service.IInfoTypeService;
import com.boryou.submit.domain.SubmitInfo;
import com.boryou.submit.service.ISubmitInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2023/12/29 15:26
 */
@RestController
@RequestMapping("/type")
@RequiredArgsConstructor
public class InfoTypeController extends BaseController {

    public final IInfoTypeService typeService;
    public final ISubmitInfoService submitInfoService;

    @PostMapping("/typeList")
    @Secret(value = TypeQuery.class)
    public TableDataInfo typeList(@RequestBody TypeQuery query) {
        return getDataTable(typeService.typeList(query));
    }

    @PostMapping("/saveOrUpdate")
    @Secret(value = InfoType.class)
    public AjaxResult saveOrUpdate(@RequestBody InfoType infoType) {
        return AjaxResult.success(typeService.saveOrUpdate(infoType));
    }

    @DeleteMapping("/delete/{ids}")
    public AjaxResult delete(@PathVariable("ids") Long[] ids) {
        List<InfoType> list = typeService.list(Wrappers.<InfoType>lambdaQuery().in(InfoType::getId, ids));
        List<String> collect = list.stream().map(InfoType::getId).collect(Collectors.toList());
        if (!collect.isEmpty()) {
            long count = submitInfoService.count(Wrappers.<SubmitInfo>lambdaQuery()
                    .in(SubmitInfo::getInfoType, collect)
            );
            if (count > 0) {
                return AjaxResult.error("该类型下存在信息报送，无法删除");
            }
        }

        boolean delete = typeService.removeByIds(Arrays.asList(ids));
        return delete ? AjaxResult.success("删除成功") : AjaxResult.error("删除失败");
    }

}
