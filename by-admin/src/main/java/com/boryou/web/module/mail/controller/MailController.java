package com.boryou.web.module.mail.controller;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.module.mail.domain.vo.MailVO;
import com.boryou.web.module.mail.service.MailService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
public class MailController extends BaseController {

    @Resource
    private MailService mailService;

    @PostMapping("/mail/test")
    public AjaxResult mailTest(@RequestBody MailVO mailVO) {
        boolean b = mailService.sendTestMail(mailVO);
        return toAjax(b);
    }

}
