package com.boryou.web.module.account.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.module.account.entity.vo.JumpLinkVO;
import com.boryou.web.module.account.service.AccountService;
import com.boryou.web.module.account.entity.vo.AccountInfo;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024-07-02 15:47
 */
@RestController
@RequestMapping("/account")
@RequiredArgsConstructor
public class AccountController {
    private final AccountService accountService;

    /**
     * -
     */
    @GetMapping("/getAccountInfo")
    public AjaxResult getAccountInfo(AccountInfo accountInfo) {
        AccountInfo res = accountService.getAccountInfo(accountInfo);
        return AjaxResult.success(res);
    }

    @PostMapping("/jumpLink")
    public AjaxResult jumpLink(@RequestBody JumpLinkVO jumpLinkVO) {
        Object link = accountService.jumpLink(jumpLinkVO);
        return AjaxResult.success(link);
    }

}

