package com.boryou.web.module.mark.config;

import lombok.Data;

import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.HashSet;
import java.util.Map;
import java.util.Set;

/**
 * 字段映射配置类
 * 用于配置两个实体类之间的字段映射关系
 * 
 * <AUTHOR>
 */
@Data
public class FieldMappingConfig {
    
    /**
     * 源实体类
     */
    private Class<?> sourceClass;
    
    /**
     * 目标实体类
     */
    private Class<?> targetClass;
    
    /**
     * 字段映射关系
     * Key: 源字段名, Value: 目标字段名
     */
    private Map<String, String> fieldMappings = new HashMap<>();
    
    /**
     * 排除的字段集合
     * 存储需要排除的源字段名
     */
    private Set<String> excludedFields = new HashSet<>();
    
    /**
     * 字段对象映射关系
     * Key: 源字段对象, Value: 目标字段对象
     */
    private Map<Field, Field> fieldObjectMappings = new HashMap<>();
    
    /**
     * 是否启用严格模式
     * 严格模式下，如果找不到对应的字段映射会抛出异常
     */
    private boolean strictMode = false;
    
    /**
     * 是否忽略大小写
     * 在字段名匹配时是否忽略大小写
     */
    private boolean ignoreCase = false;
    
    /**
     * 构造函数
     */
    public FieldMappingConfig() {}
    
    /**
     * 构造函数
     * 
     * @param sourceClass 源实体类
     * @param targetClass 目标实体类
     */
    public FieldMappingConfig(Class<?> sourceClass, Class<?> targetClass) {
        this.sourceClass = sourceClass;
        this.targetClass = targetClass;
    }
    
    /**
     * 添加字段映射
     * 
     * @param sourceField 源字段名
     * @param targetField 目标字段名
     * @return 当前配置对象，支持链式调用
     */
    public FieldMappingConfig addMapping(String sourceField, String targetField) {
        fieldMappings.put(sourceField, targetField);
        return this;
    }
    
    /**
     * 添加排除字段
     * 
     * @param fieldName 要排除的字段名
     * @return 当前配置对象，支持链式调用
     */
    public FieldMappingConfig excludeField(String fieldName) {
        excludedFields.add(fieldName);
        return this;
    }
    
    /**
     * 批量添加排除字段
     * 
     * @param fieldNames 要排除的字段名数组
     * @return 当前配置对象，支持链式调用
     */
    public FieldMappingConfig excludeFields(String... fieldNames) {
        for (String fieldName : fieldNames) {
            excludedFields.add(fieldName);
        }
        return this;
    }
    
    /**
     * 设置严格模式
     * 
     * @param strictMode 是否启用严格模式
     * @return 当前配置对象，支持链式调用
     */
    public FieldMappingConfig setStrictMode(boolean strictMode) {
        this.strictMode = strictMode;
        return this;
    }
    
    /**
     * 设置忽略大小写
     * 
     * @param ignoreCase 是否忽略大小写
     * @return 当前配置对象，支持链式调用
     */
    public FieldMappingConfig setIgnoreCase(boolean ignoreCase) {
        this.ignoreCase = ignoreCase;
        return this;
    }
    
    /**
     * 获取目标字段名
     * 
     * @param sourceFieldName 源字段名
     * @return 目标字段名，如果没有映射则返回源字段名
     */
    public String getTargetFieldName(String sourceFieldName) {
        return fieldMappings.getOrDefault(sourceFieldName, sourceFieldName);
    }
    
    /**
     * 检查字段是否被排除
     * 
     * @param fieldName 字段名
     * @return 如果字段被排除返回true，否则返回false
     */
    public boolean isFieldExcluded(String fieldName) {
        return excludedFields.contains(fieldName);
    }
}
