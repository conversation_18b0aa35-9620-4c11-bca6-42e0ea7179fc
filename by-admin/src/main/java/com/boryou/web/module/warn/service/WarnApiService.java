package com.boryou.web.module.warn.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.db.PageResult;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.module.warn.domain.vo.WarnDataVO;
import com.boryou.web.service.impl.SearchServiceImpl;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;

@Slf4j
@Service
@RequiredArgsConstructor
public class WarnApiService {
    private final SearchServiceImpl searchServiceImpl;
    private final WarnReadService warnReadService;

    public PageResult<EsBean> warnDataGet(WarnDataVO warnDataVO) {
        PageResult<EsBean> warnDataRes = new PageResult<>();
        Integer readFlag = warnDataVO.getReadFlag();
        if (readFlag != null && (readFlag == 1 || readFlag == 0)) {
            warnDataVO.setReadIds(warnReadService.getWarnRead());
        }

        try (HttpResponse response = HttpUtil.createPost(EsSearchUtil.WARN_GET)
                .body(JSONUtil.toJsonStr(warnDataVO))
                .timeout(3000000).execute()) {

            if (!response.isOk()) {
                return warnDataRes;
            }
            JSONObject resultJson = JSONUtil.parseObj(response.body());
            if (!resultJson.containsKey("records") || !resultJson.containsKey("total")) {
                return warnDataRes;
            }
            warnDataRes.setTotal(resultJson.getInt("total"));
            List<EsBean> records = JSONUtil.toList(resultJson.getJSONArray("records"), EsBean.class);
            if (CollUtil.isNotEmpty(records)) {
                warnDataRes.addAll(records);
            }
            EsSearchBO esSearchBO = new EsSearchBO();
            esSearchBO.setWarnType(1);
            searchServiceImpl.searchResProcess(esSearchBO, warnDataRes);
            return warnDataRes;
        } catch (Exception e) {
            log.error("WarnApiService.warnDataGet报错: {}", e.getMessage());
            return warnDataRes;
        }
    }

    public boolean warnDataAdd(List<EsBean> warnDataResList) {
        try (HttpResponse response = HttpUtil.createPost(EsSearchUtil.WARN_ADD).body(JSONUtil.toJsonStr(warnDataResList)).execute()) {
            if (!response.isOk()) {
                return false;
            }
            return CharSequenceUtil.equals(response.body(), "true");
        } catch (Exception e) {
            log.error("WarnApiService.warnDataAdd报错: {}", e.getMessage());
            return false;
        }
    }

    public List<EsBean> warnDataPush(EsSearchBO esSearchBO) {
        List<EsBean> records = new ArrayList<>();
        //PageResult<EsBean> pageResult = new PageResult<>();
        try (HttpResponse response = HttpUtil.createPost(EsSearchUtil.WARN_PUSH)
                .body(JSONUtil.toJsonStr(esSearchBO))
                .timeout(3000000).execute()) {

            if (!response.isOk()) {
                return records;
            }
            JSONObject resultJson = JSONUtil.parseObj(response.body());
            if (!resultJson.containsKey("records") || !resultJson.containsKey("total")) {
                return records;
            }
            //if (CollUtil.isNotEmpty(records)) {
            //    pageResult.addAll(records);
            //    pageResult.setTotal(resultJson.getInt("total"));
            //}
            return JSONUtil.toList(resultJson.getJSONArray("records"), EsBean.class);
        } catch (Exception e) {
            log.error("WarnApiService.warnDataPush报错: {}", e.getMessage());
            return records;
        }
    }

}
