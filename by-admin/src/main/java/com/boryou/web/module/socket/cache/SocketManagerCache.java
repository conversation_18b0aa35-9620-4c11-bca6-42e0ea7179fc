package com.boryou.web.module.socket.cache;

import lombok.extern.slf4j.Slf4j;
import org.springframework.web.socket.WebSocketSession;

import java.util.Objects;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentMap;
import java.util.concurrent.CopyOnWriteArrayList;

/**
 * <AUTHOR>
 * @since 2023/9/6 17:01
 */
@Slf4j
public class SocketManagerCache {

    /**
     * loginId <==> session 关系; 1对多,一个用户有可能在多个浏览器上登录
     */
    public static ConcurrentMap<String, CopyOnWriteArrayList<WebSocketSession>> ONLINE_UID_MAP = new ConcurrentHashMap<>();

    public static final String LOGIN_ID = "loginId";

    private SocketManagerCache() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 添加用户的WebSocket会话
     *
     * @param loginId 用户登录ID
     * @param session WebSocket会话
     */
    public static void addOnlineSid(String loginId, WebSocketSession session) {
        // 快速失败检查
        if (loginId == null || session == null) {
            log.warn("尝试添加无效的会话或用户: session={}, loginId={}", session, loginId);
            return;
        }

        // 使用原子操作添加会话
        ONLINE_UID_MAP.compute(loginId, (key, sessions) -> {
            if (sessions == null || sessions.isEmpty()) {
                // 创建新的会话列表
                CopyOnWriteArrayList<WebSocketSession> newSessions = new CopyOnWriteArrayList<>();
                newSessions.add(session);
                log.warn("用户 {} 首次上线，当前会话数; {}", loginId, newSessions.size());
                return newSessions;
            } else {
                // 检查会话是否已存在
                if (!sessions.contains(session)) {
                    sessions.add(session);
                    log.warn("用户 {} 添加新会话，当前会话数: {}", loginId, sessions.size());
                } else {
                    log.warn("用户 {} 的会话已存在", loginId);
                }
                return sessions;
            }
        });
    }

    public static void removed(WebSocketSession session) {
        String loginId = (String) session.getAttributes().get(LOGIN_ID);

        boolean offlineAll = offline(session, loginId);
        // if (uidOptional.isPresent() && offlineAll) {//已登录用户断连,并且全下线成功
        //     User user = new User();
        //     user.setId(uidOptional.get());
        //     user.setLastOptTime(new Date());
        //     applicationEventPublisher.publishEvent(new UserOfflineEvent(this, user));
        // }
    }

    /**
     * 用户下线
     *
     * @param session 需要内存下线的WebSocket会话
     * @param loginId 用户登录ID
     * @return 是否内存全下线成功（该用户的所有内存会话都已下线）
     */
    private static boolean offline(WebSocketSession session, String loginId) {
        // 快速失败检查
        if (session == null || loginId == null) {
            log.warn("尝试下线无效的内存会话或用户: session={}, loginId={}", session, loginId);
            return false;
        }

        // 使用原子操作处理会话移除和空列表检测
        return ONLINE_UID_MAP.compute(loginId, (key, sessions) -> {
            // 如果没有会话列表，返回null以移除该键
            if (sessions == null || sessions.isEmpty()) {
                log.warn("用户 {} 没有内存活跃的会话", loginId);
                return null;
            }

            // 移除指定的会话
            sessions.removeIf(ch -> {
                if (ch == null || !ch.isOpen()) {
                    return true;
                }
                return Objects.equals(ch.getId(), session.getId());
            });

            // 如果移除后列表为空，返回null以移除该键
            if (sessions.isEmpty()) {
                log.warn("用户 {} 的内存所有会话已下线", loginId);

                return null;
            }
            log.warn("用户 {} 还有 {} 个内存活跃会话", loginId, sessions.size());
            return sessions;
        }) == null;
    }

}
