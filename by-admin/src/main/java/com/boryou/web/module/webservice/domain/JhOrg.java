package com.boryou.web.module.webservice.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import java.io.Serializable;
import java.util.Date;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2024/12/6 下午4:36
 */
@TableName(value ="jh_org")
@Data
public class JhOrg implements Serializable {
    /**
     * 主键
     */
    @TableId(type = IdType.ASSIGN_ID)
    private Long id;

    /**
     * 原机构编码，update/del时必须不为空
     */
    private String originalCode;

    /**
     * 社会统一编码
     */
    private String orgUnifyCredit;

    /**
     * 机构编码
     */
    private String orgCode;

    /**
     * 机构名称
     */
    private String orgName;

    /**
     * 父机构编码
     */
    private String orgFatherCode;

    /**
     * 机构级别
     */
    private String orgLeaevl;

    /**
     * 行政区域编码
     */
    private String orgDivisionCode;

    /**
     * 行政区域名称
     */
    private String orgDivisionName;

    /**
     * 机构联系电话
     */
    private String orgPhone;

    /**
     * 机构类型
     */
    private String orgType;

    /**
     * 机构管理类型
     */
    private String orgManageType;

    /**
     * 机构单位分级分类
     */
    private String orgUnitClass;

    /**
     * 是否区域所属区域疾控中心，是/否
     */
    private String orgIsornoCenter;

    /**
     * 是否区域所属虚拟疾控中心，是/否
     */
    private String orgIsornoVircenter;

    /**
     * 是否据有网络报告资质
     */
    private String orgIsornoNetworkre;

    /**
     * 是否据有网络报告资质
     */
    private String port;

    /**
     * 机构负责人名称
     */
    private String orgHeadName;

    /**
     * 机构负责人联系电话
     */
    private String orgHeadPhone;

    /**
     * 机构地址
     */
    private String orgAddr;

    /**
     * 备注
     */
    private String orgRemark;

    /**
     * 更新时间，格式YYYY - MM - DD HH:MM:SS
     */
    private Date modifiedTime;

    /**
     * 创建时间，格式YYYY - MM - DD HH:MM:SS
     */
    private Date createTime;

    /**
     * 接口方法 add/update/del
     */
    @TableField(exist = false)
    private String identification;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}