package com.boryou.web.module.warn.controller;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.module.warn.domain.vo.WarnSetVO;
import com.boryou.web.module.warn.service.WarnSetService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
public class WarnSetController extends BaseController {
    @Resource
    private WarnSetService warnSetService;

    /**
     * 根据planId获取预警设置
     *
     * @param warnSetVO 传参
     * @return 返回值
     */
    @PostMapping("/warn/set/get")
    public AjaxResult warnSetGet(@RequestBody WarnSetVO warnSetVO) {
        WarnSetVO warnSet = warnSetService.warnSetGet(warnSetVO);
        return AjaxResult.success(warnSet);
    }

    /**
     * 根据planId获取预警设置
     *
     * @param warnSetVO 传参
     * @return 返回值
     */
    @PostMapping("/warn/set/change")
    public AjaxResult warnSetChange(@RequestBody @Validated WarnSetVO warnSetVO) {
        boolean i = warnSetService.warnSetChange(warnSetVO);
        return toAjax(i);
    }

}
