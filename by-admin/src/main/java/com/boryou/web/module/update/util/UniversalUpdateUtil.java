package com.boryou.web.module.update.util;

import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ReflectUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;

/**
 * 通用更新工具类
 * 支持任意类型的对象字段比较和更新
 * 支持排除字段、字段映射等高级功能
 * 
 * <AUTHOR>
 */
@Slf4j
public class UniversalUpdateUtil {

    private UniversalUpdateUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 通用更新配置类
     */
    @Data
    public static class UpdateConfig<T> {
        /**
         * 更新包装器
         */
        private LambdaUpdateWrapper<T> updateWrapper;
        
        /**
         * 排除的字段集合
         */
        private Set<String> excludedFields = new HashSet<>();
        
        /**
         * 字段映射关系 (源字段名 -> 目标字段名)
         */
        private Map<String, String> fieldMappings = new HashMap<>();
        
        /**
         * 自定义字段更新器映射 (目标字段名 -> 更新器函数)
         */
        private Map<String, SFunction<T, ?>> fieldSetters = new HashMap<>();
        
        /**
         * 是否有字段需要更新
         */
        private boolean hasUpdate = false;
        
        /**
         * 是否忽略空值
         */
        private boolean ignoreNullValues = true;
        
        /**
         * 是否忽略空字符串
         */
        private boolean ignoreEmptyStrings = true;

        public UpdateConfig(LambdaUpdateWrapper<T> updateWrapper) {
            this.updateWrapper = updateWrapper;
        }

        /**
         * 添加排除字段
         */
        public UpdateConfig<T> excludeField(String fieldName) {
            excludedFields.add(fieldName);
            return this;
        }

        /**
         * 批量添加排除字段
         */
        public UpdateConfig<T> excludeFields(String... fieldNames) {
            Collections.addAll(excludedFields, fieldNames);
            return this;
        }

        /**
         * 添加字段映射
         */
        public UpdateConfig<T> addMapping(String sourceField, String targetField) {
            fieldMappings.put(sourceField, targetField);
            return this;
        }

        /**
         * 添加字段更新器
         */
        public <V> UpdateConfig<T> addSetter(String fieldName, SFunction<T, V> setter) {
            fieldSetters.put(fieldName, setter);
            return this;
        }

        /**
         * 设置是否忽略空值
         */
        public UpdateConfig<T> ignoreNullValues(boolean ignore) {
            this.ignoreNullValues = ignore;
            return this;
        }

        /**
         * 设置是否忽略空字符串
         */
        public UpdateConfig<T> ignoreEmptyStrings(boolean ignore) {
            this.ignoreEmptyStrings = ignore;
            return this;
        }
    }

    /**
     * 创建更新配置
     */
    public static <T> UpdateConfig<T> createUpdateConfig(LambdaUpdateWrapper<T> updateWrapper) {
        return new UpdateConfig<>(updateWrapper);
    }

    /**
     * 通用字段比较和更新方法
     * 
     * @param sourceData 源数据对象
     * @param targetData 目标数据对象
     * @param config 更新配置
     * @return 是否有字段需要更新
     */
    public static <T> boolean compareAndUpdate(Object sourceData, Object targetData, UpdateConfig<T> config) {
        if (sourceData == null || targetData == null || config == null) {
            return false;
        }

        try {
            // 获取源对象和目标对象的所有字段
            Field[] sourceFields = getAllFields(sourceData.getClass());
            Field[] targetFields = getAllFields(targetData.getClass());
            
            // 创建目标字段映射，便于快速查找
            Map<String, Field> targetFieldMap = new HashMap<>();
            for (Field field : targetFields) {
                targetFieldMap.put(field.getName(), field);
            }

            boolean hasUpdate = false;

            // 遍历源对象字段
            for (Field sourceField : sourceFields) {
                String sourceFieldName = sourceField.getName();
                
                // 跳过排除的字段
                if (config.getExcludedFields().contains(sourceFieldName)) {
                    continue;
                }

                // 获取目标字段名（考虑字段映射）
                String targetFieldName = config.getFieldMappings().getOrDefault(sourceFieldName, sourceFieldName);
                
                // 获取目标字段
                Field targetField = targetFieldMap.get(targetFieldName);
                if (targetField == null) {
                    continue;
                }

                // 获取字段值
                Object sourceValue = getFieldValue(sourceData, sourceField);
                Object targetValue = getFieldValue(targetData, targetField);

                // 判断是否需要更新
                if (shouldUpdate(sourceValue, targetValue, config)) {
                    // 获取字段更新器
                    SFunction<T, ?> setter = config.getFieldSetters().get(targetFieldName);
                    if (setter != null) {
                        // 使用自定义更新器
                        updateFieldWithSetter(config.getUpdateWrapper(), sourceValue, setter);
                        hasUpdate = true;
                    } else {
                        // 使用反射更新
                        if (updateFieldByReflection(config.getUpdateWrapper(), targetFieldName, sourceValue)) {
                            hasUpdate = true;
                        }
                    }
                }
            }

            config.setHasUpdate(hasUpdate);
            return hasUpdate;

        } catch (Exception e) {
            log.error("通用更新过程中发生错误", e);
            return false;
        }
    }

    /**
     * 简化的更新方法（字段名相同，支持排除字段）
     */
    public static <T> boolean compareAndUpdateSimple(Object sourceData, Object targetData, 
                                                   LambdaUpdateWrapper<T> updateWrapper, 
                                                   String... excludeFields) {
        UpdateConfig<T> config = createUpdateConfig(updateWrapper)
                .excludeFields(excludeFields);
        return compareAndUpdate(sourceData, targetData, config);
    }

    /**
     * 带字段映射的更新方法
     */
    public static <T> boolean compareAndUpdateWithMapping(Object sourceData, Object targetData,
                                                        LambdaUpdateWrapper<T> updateWrapper,
                                                        Map<String, String> fieldMappings,
                                                        String... excludeFields) {
        UpdateConfig<T> config = createUpdateConfig(updateWrapper)
                .excludeFields(excludeFields);
        
        // 添加字段映射
        fieldMappings.forEach(config::addMapping);
        
        return compareAndUpdate(sourceData, targetData, config);
    }

    /**
     * 获取对象的所有字段（包括父类字段）
     */
    private static Field[] getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;

        while (currentClass != null && currentClass != Object.class) {
            Field[] declaredFields = currentClass.getDeclaredFields();
            Collections.addAll(fields, declaredFields);
            currentClass = currentClass.getSuperclass();
        }

        return fields.toArray(new Field[0]);
    }

    /**
     * 获取字段值
     */
    private static Object getFieldValue(Object obj, Field field) {
        try {
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            log.warn("获取字段值失败: {}.{}", obj.getClass().getSimpleName(), field.getName(), e);
            return null;
        }
    }

    /**
     * 判断是否需要更新字段
     */
    private static boolean shouldUpdate(Object sourceValue, Object targetValue, UpdateConfig<?> config) {
        // 如果忽略空值且源值为空，则不更新
        if (config.isIgnoreNullValues() && sourceValue == null) {
            return false;
        }

        // 如果忽略空字符串且源值为空字符串，则不更新
        if (config.isIgnoreEmptyStrings() && sourceValue instanceof String && 
            CharSequenceUtil.isBlank((String) sourceValue)) {
            return false;
        }

        // 比较值是否不同
        return !Objects.equals(sourceValue, targetValue);
    }

    /**
     * 使用自定义更新器更新字段
     */
    @SuppressWarnings("unchecked")
    private static <T, V> void updateFieldWithSetter(LambdaUpdateWrapper<T> updateWrapper, 
                                                    Object value, SFunction<T, ?> setter) {
        updateWrapper.set((SFunction<T, V>) setter, (V) value);
    }

    /**
     * 使用反射更新字段
     */
    private static <T> boolean updateFieldByReflection(LambdaUpdateWrapper<T> updateWrapper, 
                                                      String fieldName, Object value) {
        try {
            // 这里需要根据实际情况实现字段名到Lambda表达式的转换
            // 由于MyBatis-Plus的限制，这里暂时记录日志
            log.info("需要更新字段: {} = {}", fieldName, value);
            return true;
        } catch (Exception e) {
            log.error("反射更新字段失败: {}", fieldName, e);
            return false;
        }
    }
}
