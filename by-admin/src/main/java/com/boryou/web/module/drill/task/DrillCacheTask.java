package com.boryou.web.module.drill.task;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.boryou.common.utils.RedisUtil;
import com.boryou.web.constant.RedisConstant;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.EnableScheduling;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.web.bind.annotation.RestController;

import java.util.Set;

@Slf4j
@RestController
@RequiredArgsConstructor
@EnableScheduling
public class DrillCacheTask {

    private final RedisUtil redisUtil;

    // @Scheduled(fixedDelay = 5 * 60 * 1000)
    // @RequestMapping("/drill/cache/score")
    public void scoreCache() {
        // todo 缓存分数
    }

    // 倒计时恢复逻辑已移至 DrillTimerService.recoverTimers() 方法

    @Scheduled(cron = "0 0 1,3 * * ?")
    // @Scheduled(initialDelay = 5 * 1000, fixedDelay = 120 * 60 * 1000)
    public void processExpiredUsers() {
        long now = System.currentTimeMillis();
        Set<String> scan = redisUtil.scan(RedisConstant.DRILL_USER_DELAY_ZSET + "*");
        if (CollUtil.isEmpty(scan)) {
            return;
        }
        for (String key : scan) {
            // 1. 获取所有到期的 userId
            Set<String> expired = redisUtil.zRangeByScore(key, 0, now);
            if (CollUtil.isEmpty(expired)) {
                continue;
            }

            // 2. 批量删除 ZSet 中的记录
            redisUtil.zRemove(key, expired.toArray());

            String drillTaskId = CharSequenceUtil.subAfter(key, RedisConstant.DRILL_USER_DELAY_ZSET, true);

            // 3. 批量删除 Hash 中的用户缓存
            String cacheKey = RedisConstant.DRILL_USER_CACHE + drillTaskId;
            redisUtil.hDelete(cacheKey, expired.toArray());

            // 4. 触发清理回调（可异步）
            // expired.forEach(this::onUserExpired);
        }

    }

}
