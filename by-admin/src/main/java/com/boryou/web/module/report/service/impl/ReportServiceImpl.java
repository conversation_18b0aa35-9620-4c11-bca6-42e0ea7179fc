package com.boryou.web.module.report.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.common.config.MinioConfig;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.DateUtils;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.file.MinioUtil;
import com.boryou.common.utils.uuid.IdUtils;
import com.boryou.manage.domain.File;
import com.boryou.manage.service.FileService;
import com.boryou.manage.service.MinioFileService;
import com.boryou.web.controller.common.entity.Word;
import com.boryou.web.controller.common.entity.bo.EsSearchTimeBO;
import com.boryou.web.controller.common.entity.vo.TimeFlowVO;
import com.boryou.web.controller.common.enums.EmotionEnum;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.domain.vo.GraphModelVO;
import com.boryou.web.domain.vo.TimeRoundFlowVO;
import com.boryou.web.module.material.entity.Material;
import com.boryou.web.module.material.service.MaterialService;
import com.boryou.web.module.material.vo.MaterialQuery;
import com.boryou.web.module.report.entity.Report;
import com.boryou.web.module.report.entity.ReportTemplate;
import com.boryou.web.module.report.entity.vo.ReportVO;
import com.boryou.web.module.report.mapper.ReportMapper;
import com.boryou.web.module.report.service.ReportService;
import com.boryou.web.util.WordtoPdfAsposeUtil;
import com.boryou.web.util.poi.POIWordUtil;
import com.boryou.web.util.poi.custom.*;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.xwpf.usermodel.*;
import org.openxmlformats.schemas.wordprocessingml.x2006.main.*;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import javax.annotation.Resource;
import javax.servlet.ServletOutputStream;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.io.InputStream;
import java.io.StringReader;
import java.math.BigInteger;
import java.text.DecimalFormat;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

import static com.boryou.web.service.impl.SearchAnalyseServiceImpl.getGraphModelVO;

/**
 * <AUTHOR>
 */
@Service
public class ReportServiceImpl implements ReportService {

    @Resource
    private ReportMapper reportMapper;
    @Resource
    private MaterialService materialService;
    @Resource
    private FileService fileService;
    @Resource
    private MinioFileService minioFileService;

    @Override
    public Report selectById(Long reportId) {
        return reportMapper.selectById(reportId);
    }

    @Override
    public Report selectIssue(Report report) {
        return reportMapper.selectIssue(report);
    }

    @Override
    public List<Report> selectByIds(String reportIds) {
        return reportMapper.selectByIds(reportIds);
    }

    @Override
    public List<ReportVO> selectReport(ReportVO report) {
        return reportMapper.selectReport(report);
    }

    @Override
    public int deleteByIds(String ids) {
        return reportMapper.deleteByIds(ids);
    }

    @Override
    public int insert(Report report) {
        if (report.getFileId() == null && (report.getTempId() == null || report.getMaterialId() == null)) {
            throw new CustomException("报告创建失败!");
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        report.setUserId(userId);
        report.setCreateBy(userName);
        report.setCreateTime(date);
        report.setReportId(IdUtil.getSnowflakeNextId());
        if (report.getTempId() != null && report.getMaterialId() != null) {
            updateReportByMaterial(report, report.getMaterialId());
        }
        if (report.getIssue() == null) {
            Report report1 = reportMapper.selectIssue(report);
            if (report1 != null) {
                Integer issue = Integer.parseInt(report1.getIssue()) + 1;
                report.setIssue(String.valueOf(issue));
            } else {
                report.setIssue("1");
            }
        }
        return reportMapper.insert(report);
    }

    public Long upload(MultipartFile file) {
        String bucketName = MinioConfig.getBucketName();
        DateTime date = DateUtil.date();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        String originalFilename = file.getOriginalFilename();
        long size = file.getSize();
        File fileSql = new File();
        try {
            fileSql.setFileName(MinioUtil.uploadMinio(file));
        } catch (IOException e) {
            throw new CustomException("上传失败!");
        }
        fileSql.setFileId(IdUtil.getSnowflakeNextId());
        fileSql.setBucketName(bucketName);
        fileSql.setOriginal(originalFilename);
        String type = CharSequenceUtil.subAfter(originalFilename, ".", true);
        fileSql.setType(type);
        fileSql.setFileSize(size);
        fileSql.setDelFlag("0");
        fileSql.setCreateBy(userId);
        fileSql.setCreateTime(date);
        fileSql.setUpdateBy(userId);
        fileSql.setUpdateTime(date);
        fileService.insertFile(fileSql);
        return fileSql.getFileId();
    }

    @Override
    public int updateById(Report report) {
        //只有null才需要修改
        if (report.getFileId() == null) {
            reportMapper.clearFileById(report);
        }
        return reportMapper.updateById(report);
    }

    @Override
    public ReportTemplate selectTemplateById(Long tempId) {
        return reportMapper.selectTemplateById(tempId);
    }

    @Override
    public JSONObject selectTemplate(ReportTemplate reportTemplate) {
        List<ReportTemplate> sysTemp = reportMapper.selectTemplate(reportTemplate);
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        reportTemplate.setUserId(userId);
        Long defaultId = reportMapper.selectDefault(userId);
        JSONArray sysTemps = new JSONArray();
        JSONArray userTemps = new JSONArray();
        for (ReportTemplate template : sysTemp) {
            if (template.getTempId().equals(defaultId)) {
                template.setIsDefault(1);
            }
            JSONObject object = JSONUtil.parseObj(template);
            object.putOpt("tempId", template.getTempId().toString());
            object.putOpt("createTime", DateUtil.format(template.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
            sysTemps.add(object);
        }
        List<ReportTemplate> userTemp = reportMapper.selectTemplate(reportTemplate);
        for (ReportTemplate template : userTemp) {
            if (template.getTempId().equals(defaultId)) {
                template.setIsDefault(1);
            }
            JSONObject object = JSONUtil.parseObj(template);
            object.putOpt("tempId", template.getTempId().toString());
            object.putOpt("createTime", DateUtil.format(template.getCreateTime(), DatePattern.NORM_DATETIME_PATTERN));
            userTemps.add(object);
        }
        JSONObject object = JSONUtil.createObj();
        object.putOnce("sysTemps", sysTemps);
        object.putOnce("userTemps", userTemps);
        return object;
    }

    @Override
    public List<ReportTemplate> selectTemplateSortDefault(ReportTemplate reportTemplate) {
        List<ReportTemplate> sysTemp = reportMapper.selectTemplate(reportTemplate);
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        reportTemplate.setUserId(userId);
        List<ReportTemplate> userTemp = reportMapper.selectTemplate(reportTemplate);
        sysTemp.addAll(userTemp);
        Long defaultId = reportMapper.selectDefault(userId);
        for (ReportTemplate template : sysTemp) {
            if (template.getTempId().equals(defaultId)) {
                template.setIsDefault(1);
                break;
            }
        }
        sysTemp.sort((t1, t2) -> {
            int compare = Integer.compare(t2.getIsDefault(), t1.getIsDefault());
            //默认在前
            if (compare != 0) return compare;
            //系统在前
            if (t1.getUserId() == null && t2.getUserId() != null) {
                return 1;
            }
            //早创建的在前
            return t1.getTempId().compareTo(t2.getTempId());
        });
        return sysTemp;
    }

    @Override
    public int changeTemplateToSys(Long id) {
        if (SecurityUtils.getLoginUser().getUser().getUserId() == 1) {
            return reportMapper.changeTemplateToSys(id);
        }
        return 0;
    }

    @Override
    public int deleteTemplateByIds(String ids) {
        reportMapper.deleteReportByTempIds(ids);
        return reportMapper.deleteTemplateByIds(ids);
    }

    @Override
    public int insertTemplate(ReportTemplate reportTemplate) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        reportTemplate.setUserId(userId);
        reportTemplate.setCreateBy(userName);
        reportTemplate.setCreateTime(date);

        reportTemplate.setTempId(IdUtil.getSnowflakeNextId());
        return reportMapper.insertTemplate(reportTemplate);
    }

    @Override
    public int updateDefaultTemplate(Long id) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        ReportTemplate reportTemplate = new ReportTemplate();
        reportTemplate.setUserId(userId);
        reportTemplate.setTempId(id);

        Long defaultId = reportMapper.selectDefault(userId);
        if (defaultId == null) {
            return reportMapper.insertDefault(reportTemplate);
        } else {
            return reportMapper.updateDefault(reportTemplate);
        }
    }

    @Override
    public int updateTemplateById(ReportTemplate reportTemplate) {
        return reportMapper.updateTemplateById(reportTemplate);
    }

    private void updateReportByMaterial(Report report, Long materialId) {
        MaterialQuery query = new MaterialQuery();
        query.setFolderId(materialId);
        List<Material> materials = materialService.materialList(query);
        if (CollUtil.isEmpty(materials)) {
            return;
        }
        Comparator<Map.Entry<String, List<Material>>> comparator =
                (entry1, entry2) -> {
                    int sizeComparison = Integer.compare(entry2.getValue().size(), entry1.getValue().size());
                    if (sizeComparison != 0) {
                        return sizeComparison;
                    } else {
                        return entry1.getKey().compareTo(entry2.getKey());
                    }
                };

        Map<String, List<Material>> typeCollect = materials.stream().collect(Collectors.groupingBy(Material::getTypeName));
        Map<String, List<Material>> hostCollect = materials.stream().filter(m -> m.getHost() != null).collect(Collectors.groupingBy(Material::getHost));
        Map<String, List<Material>> emoCollect = materials.stream().filter(m -> m.getEmotionFlag() != null).collect(Collectors.groupingBy(m -> m.getEmotionFlag().toString()));
        materials.sort(Comparator.comparing(Material::getPublishTime));

        //对typeCollect的条目进行排序
        List<Map.Entry<String, List<Material>>> sortedTypeEntries = typeCollect.entrySet().stream()
                .sorted(comparator)
                .collect(Collectors.toList());
        DecimalFormat df2 = new DecimalFormat("0.00");
        StringBuilder intro = new StringBuilder("本报告就加入的素材进行分析，共有" + materials.size() + "篇相关内容。其中");
        for (int i = 0; i < sortedTypeEntries.size(); i++) {
            int size = sortedTypeEntries.get(i).getValue().size();
            intro.append(sortedTypeEntries.get(i).getKey()).append("共有").append(size).append("篇，占比").append(df2.format((double) size * 100 / materials.size())).append("%，");
        }
        if (sortedTypeEntries.size() >= 3) {
            intro.append("可以看出").append(sortedTypeEntries.get(0).getKey()).append("的比重最大，占比达到信息总量的").append(df2.format((double) sortedTypeEntries.get(0).getValue().size() * 100 / materials.size())).append("%，").append("其次主要集中").append(sortedTypeEntries.get(1).getKey()).append("、").append(sortedTypeEntries.get(2).getKey()).append("等几大站点。");
        }
        intro.append("详细报告请继续浏览。");
        report.setReportIntro(intro.toString());

        report.setSuggest("对于舆情信息中具有潜在危害的事件及情况应给予关注并积极处理，防止不良影响产生及扩散。此外，密切关注此前敏感预警事件的发展情况，及时制定有效应对措施。鉴于监测结果中负面舆情有在发生，应即时做好预防和处理工作，阻止事态继续发展。");

        DecimalFormat df1 = new DecimalFormat("0.0");
        String overview = "监测主题相关信息内容" + materials.size() + "条。其中";
        if (emoCollect.containsKey("1")) {
            int size = emoCollect.get("1").size();
            overview += "敏感" + size + "条，敏感占比" + df1.format((double) size * 100 / materials.size()) + "%";
        }
        if (emoCollect.containsKey("2")) {
            if (emoCollect.containsKey("1")) {
                overview += "，";
            }
            int size = emoCollect.get("2").size();
            overview += "非敏感" + size + "条，非敏感占比" + df1.format((double) size * 100 / materials.size()) + "%";
        }
        if (emoCollect.containsKey("0")) {
            if (emoCollect.containsKey("1") || emoCollect.containsKey("2")) {
                overview += "，";
            }
            int size = emoCollect.get("0").size();
            overview += "中性" + size + "条，中性占比" + df1.format((double) size * 100 / materials.size()) + "%";
        }
        overview += "。";
        report.setOverview(overview);

        JSONArray array = new JSONArray();
        JSONObject object;
        for (Map.Entry<String, List<Material>> entry : typeCollect.entrySet()) {
            if (!entry.getKey().equals("all")) {
                object = JSONUtil.createObj();
                object.putOnce("name", entry.getKey());
                object.putOnce("value", entry.getValue().size());
                object.putOnce("percent", df2.format((double) entry.getValue().size() * 100 / materials.size()) + "%");
                array.set(object);
            }
        }
        report.setMediaStatistics(array.toString());

        array = new JSONArray();
        for (Map.Entry<String, List<Material>> entry : emoCollect.entrySet()) {
            object = JSONUtil.createObj();
            if (EmotionEnum.fromValue(Integer.parseInt(entry.getKey())) != null) {
                object.putOnce("name", EmotionEnum.fromValue(Integer.parseInt(entry.getKey())).getName());
                object.putOnce("value", entry.getValue().size());
                object.putOnce("percent", df2.format((double) entry.getValue().size() * 100 / materials.size()) + "%");
                array.set(object);
            }
        }
        report.setEmotionAnalysis(array.toString());

        //对hostCollect的条目进行排序
        List<Map.Entry<String, List<Material>>> hostSortedEntries = hostCollect.entrySet().stream()
                .sorted(comparator)
                .collect(Collectors.toList());
        Map<String, List<Material>> hostSortedMap = new LinkedHashMap<>();
        hostSortedEntries.forEach(entry -> hostSortedMap.put(entry.getKey(), entry.getValue()));
        object = JSONUtil.createObj();
        List<String> x = new ArrayList<>();
        List<Integer> y = new ArrayList<>();
        for (Map.Entry<String, List<Material>> entry : hostSortedMap.entrySet()) {
            x.add(entry.getKey());
            y.add(entry.getValue().size());
        }
        object.putOnce("xList", x);
        object.putOnce("yList", y);
        report.setMediaDetails(object.toString());

        Map<String, Integer> wordsFren = new HashMap<>();
        List<Word> res = new ArrayList<>();
        Lexeme lexeme;
        IKSegmenter ikSegmenter;
        for (Material indexResultBean : materials) {
            if (StringUtils.isNotEmpty(indexResultBean.getText())) {
                indexResultBean.setText(HtmlUtil.cleanHtmlTag(indexResultBean.getText()));
                ikSegmenter = new IKSegmenter(new StringReader(indexResultBean.getText()), true);
                try {
                    while ((lexeme = ikSegmenter.next()) != null) {
                        String lexemeText = lexeme.getLexemeText();
                        if (lexemeText.length() > 1) {
                            if (wordsFren.containsKey(lexemeText)) {
                                wordsFren.put(lexemeText, wordsFren.get(lexemeText) + 1);
                            } else {
                                wordsFren.put(lexemeText, 1);
                            }
                        }
                    }
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
        if (!wordsFren.isEmpty()) {
            wordsFren.keySet().forEach(w -> {
                if (wordsFren.get(w) != null) {
                    res.add(new Word(w, wordsFren.get(w)));
                }
            });
        }
        List<Word> collect = res.stream().sorted((o1, o2) -> Math.toIntExact(o2.getNum() - o1.getNum())).collect(Collectors.toList());
        if (collect.size() > 31) {
            collect = collect.subList(0, 30);
        }
        array = new JSONArray();
        for (Word word : collect) {
            object = JSONUtil.createObj();
            object.putOnce("name", word.getWord());
            object.putOnce("value", word.getNum());
            array.add(object);
        }
        report.setCharCloud(array.toString());

        array = new JSONArray();
        for (int i = 0; i < materials.size() && i <= 5; i++) {
            Material material = materials.get(i);
            object = JSONUtil.createObj();
            String title = material.getTitle();
            if (title == null) {
                title = "";
            } else if (title.length() > 20) {
                title = title.substring(0, 20) + "...";
            }
            object.putOnce("title", title);
            object.putOnce("sourceAndTime", material.getTypeName() + (material.getHost() == null ? "" : material.getHost()) + " " + DateUtil.format(material.getPublishTime(), DatePattern.NORM_DATETIME_PATTERN));
            object.putOnce("emotion", EmotionEnum.fromValue(material.getEmotionFlag()).getName());
            array.add(object);
        }
        report.setMainInfo(array.toString());

        array = new JSONArray();
        for (int i = 0; i < materials.size() && i <= 5; i++) {
            Material material = materials.get(i);
            object = JSONUtil.createObj();
            object.putOnce("title", material.getTitle());
            object.putOnce("text", material.getText());
            if (material.getType() == 3 || material.getType() == 5) {
                object.putOnce("source", material.getTypeName());
            } else {
                object.putOnce("source", material.getHost());
            }
            object.putOnce("time", DateUtil.format(material.getPublishTime(), DatePattern.NORM_DATETIME_PATTERN));
            object.putOnce("author", material.getAuthor());
            object.putOnce("emotion", EmotionEnum.fromValue(material.getEmotionFlag()).getName());
            object.putOnce("url", material.getUrl());
            array.add(object);
        }
        report.setInfoIntro(array.toString());

        Set<String> typeList = materials.stream().map(t -> t.getType().toString()).collect(Collectors.toSet());
        Map<String, String> map = new HashMap<>();
        for (String type : typeList) {
            map.put(type, MediaTypeEnum.getDesc(type));
        }
        EsSearchTimeBO esSearchTimeBO = new EsSearchTimeBO();
        esSearchTimeBO.setType(CollUtil.join(typeList, ","));
        esSearchTimeBO.setStartTime(DateUtil.format(materials.get(0).getPublishTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));
        esSearchTimeBO.setEndTime(DateUtil.format(materials.get(materials.size() - 1).getPublishTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));
//        Map.Entry<String, List<TimeRoundFlowVO>> timeRound = buildRound(esSearchTimeBO);
        List<TimeRoundFlowVO> timeRoundFlowVOS = generateTimeFlows(DateUtil.parseLocalDateTime(esSearchTimeBO.getStartTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN), DateUtil.parseLocalDateTime(esSearchTimeBO.getEndTime(), DatePattern.NORM_DATETIME_MINUTE_PATTERN));
        List<TimeFlowVO> time = new ArrayList<>();
        // List<TimeRoundFlowVO> 获取一重key   startTime | endTime   二重key type
        for (TimeRoundFlowVO timeRoundFlowVO : timeRoundFlowVOS) {
            TimeFlowVO timeFlowVO = new TimeFlowVO();
            String key = timeRoundFlowVO.getStartTime() + " | " + timeRoundFlowVO.getEndTime();
            int count = 0;
            timeFlowVO.setKey(key);
            List<TimeFlowVO> timeFlowVOList = new ArrayList<>();
            List<Material> subMaterials = materials.stream().filter(t -> t.getPublishTime().getTime() >= DateUtil.parse(timeRoundFlowVO.getStartTime()).getTime())
                    .filter(t -> t.getPublishTime().getTime() <= DateUtil.parse(timeRoundFlowVO.getEndTime()).getTime()).collect(Collectors.toList());
            Map<String, List<Material>> subTypeCollect = subMaterials.stream().collect(Collectors.groupingBy(m -> m.getType().toString()));
            for (Map.Entry<String, String> entry : map.entrySet()) {
                TimeFlowVO subTimeFlowVO = new TimeFlowVO();
                subTimeFlowVO.setKey(entry.getKey());
                int subCount = subTypeCollect.getOrDefault(entry.getKey(), new ArrayList<>()).size();
                subTimeFlowVO.setCount(String.valueOf(subCount));
                timeFlowVOList.add(subTimeFlowVO);
                count += subCount;
            }
            timeFlowVO.setCount(String.valueOf(count));
            timeFlowVO.setChildren(timeFlowVOList);
            time.add(timeFlowVO);
        }

        GraphModelVO graphModelVO = getGraphModelVO(time, new ArrayList<>(typeList), map, true, DatePattern.NORM_DATETIME_MINUTE_PATTERN);
        JSONObject jsonObject = JSONUtil.parseObj(graphModelVO);
        report.setMediaTrendChart(jsonObject.toString());
    }


    @Override
    public void changeFileTypeToDownload(InputStream in, HttpServletResponse response, String fileType) {
        ServletOutputStream out = null;
        try {
            out = response.getOutputStream();
            if (fileType.equals("pdf")) {
                WordtoPdfAsposeUtil.pdf2doc(in, out);
            } else {
                WordtoPdfAsposeUtil.doc2pdf(in, out);
            }
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            if (out != null) {
                try {
                    out.flush();
                    out.close();
                } catch (IOException e) {
                    e.printStackTrace();
                }
            }
        }
    }

    @Override
    public InputStream createLocalReportFile(ReportVO reportVO, java.io.File file) {
        POIWordUtil wordUtil = new POIWordUtil();
        Report report = reportMapper.selectById(reportVO.getReportId());
        // 标题
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);
        paragraph.addText(report.getTitle(), 32, true, false);

        // 期号
        CustomXWPFParagraph paragraph2 = wordUtil.createParagraph();
        paragraph2.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);
        paragraph2.addText("第（" + report.getIssue() + "）期", 24, false, false);

        // 标头
        CustomXWPFParagraph paragraph1 = wordUtil.createParagraph();
        paragraph1.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);
        paragraph1.addText((report.getHead() == null ? "" : report.getHead()) + " " + DateUtil.format(DateUtil.date(), DatePattern.NORM_DATETIME_PATTERN), 24, false, false);

        wordUtil.createParagraph();

        JSONObject data = reportVO.getData();
        List<String> inputComponents = JSONUtil.toList(data.getJSONArray("inputComponents"), String.class);
        for (int i = 0; i < inputComponents.size(); i++) {
            if (inputComponents.get(i).equals("reportIntro")) {
                addFirstGradeTitle(wordUtil, "报告导读", true, "arial", null);
                paragraph = wordUtil.createParagraph();
                paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
                paragraph.addText(report.getReportIntro() + "\n", 24, false, false, "arial", "000000");
                wordUtil.createParagraph();
            }

            if (inputComponents.get(i).equals("suggest")) {
                addFirstGradeTitle(wordUtil, "处置建议", true, "arial", null);
                paragraph = wordUtil.createParagraph();
                paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
                paragraph.addText(report.getSuggest() + "\n", 24, false, false, "arial", "000000");
                wordUtil.createParagraph();
            }

            if (inputComponents.get(i).equals("overview")) {
                addFirstGradeTitle(wordUtil, "监测概述", true, "arial", null);
                paragraph = wordUtil.createParagraph();
                paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.BASELINE);
                paragraph.addText(report.getOverview() + "\n", 24, false, false, "arial", "000000");
                wordUtil.createParagraph();
            }

            if (inputComponents.get(i).equals("mediaStatistics") && data.containsKey("mediaStatistics")) {
                addFirstGradeTitle(wordUtil, "媒体来源统计", true, "arial", null);
                addEchartsPicture(wordUtil, data.getStr("mediaStatistics"));
                wordUtil.createParagraph();
            }

            if (inputComponents.get(i).equals("emotionAnalysis") && data.containsKey("emotionAnalysis")) {
                addFirstGradeTitle(wordUtil, "信息情感分析", true, "arial", null);
                addEchartsPicture(wordUtil, data.getStr("emotionAnalysis"));
                wordUtil.createParagraph();
            }

            if (inputComponents.get(i).equals("mediaDetails") && data.containsKey("mediaDetails")) {
                addFirstGradeTitle(wordUtil, "媒体来源明细", true, "arial", null);
                addEchartsPicture(wordUtil, data.getStr("mediaDetails"));
                wordUtil.createParagraph();
            }

            if (inputComponents.get(i).equals("charCloud") && data.containsKey("charCloud")) {
                addFirstGradeTitle(wordUtil, "信息字符云", true, "arial", null);
                addEchartsPicture(wordUtil, data.getStr("charCloud"));
                wordUtil.createParagraph();
            }

            if (inputComponents.get(i).equals("mainInfo")) {
                addFirstGradeTitle(wordUtil, "主要舆情", true, "arial", null);
                JSONArray array = JSONUtil.parseArray(report.getMainInfo());
                List<JSONObject> mainInfo = array.toList(JSONObject.class);
                addMainInfoTable(wordUtil, mainInfo);
                wordUtil.createParagraph();
            }

            if (inputComponents.get(i).equals("infoIntro")) {
                addFirstGradeTitle(wordUtil, "舆情导读", true, "arial", null);
                List<Material> materials;
                if (report.getMaterialId() != null) {
                    MaterialQuery query = new MaterialQuery();
                    query.setFolderId(report.getMaterialId());
                    materials = materialService.materialList(query);
                } else {
                    materials = new ArrayList<>();
                    List<JSONObject> jsonObjects = JSONUtil.toList(report.getInfoIntro(), JSONObject.class);
                    for (JSONObject jsonObject : jsonObjects) {
                        Material material = JSONUtil.toBean(jsonObject, Material.class);
                        material.setTypeName(jsonObject.getStr("source"));
                        material.setPublishTime(DateUtil.parseDateTime(jsonObject.getStr("time")));
                        materials.add(material);
                    }
                }

                addInfoIntroTable(wordUtil, materials);
                wordUtil.createParagraph();
            }

            if (inputComponents.get(i).equals("mediaTrendChart") && data.containsKey("mediaTrendChart")) {
                addFirstGradeTitle(wordUtil, "媒体信息走势图", true, "arial", null);
                addEchartsPicture(wordUtil, data.getStr("mediaTrendChart"));
                wordUtil.createParagraph();
            }
        }
        wordUtil.saveDocument(file);

        InputStream in = FileUtil.getInputStream(file);
        String bucketName = MinioConfig.getBucketName();
        DateTime date = DateUtil.date();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        String originalFilename = reportVO.getTitle() + ".docx";
        long size = file.length();
        File fileSql = new File();
        try {
            String fileName = DateUtils.datePath() + "/" + IdUtils.fastUUID() + ".docx";
            MinioUtil.uploadMinio(fileName, in, size, "application/docx; charset=UTF-8");
            fileSql.setFileName(fileName);
        } catch (IOException e) {
            throw new CustomException("上传失败!");
        }
        fileSql.setFileId(IdUtil.getSnowflakeNextId());
        fileSql.setBucketName(bucketName);
        fileSql.setOriginal(originalFilename);
        String type = CharSequenceUtil.subAfter(originalFilename, ".", true);
        fileSql.setType(type);
        fileSql.setFileSize(size);
        fileSql.setDelFlag("0");
        fileSql.setCreateBy(userId);
        fileSql.setCreateTime(date);
        fileSql.setUpdateBy(userId);
        fileSql.setUpdateTime(date);
        fileService.insertFile(fileSql);
        if (reportVO.getFileId() == null) {
            Report report1 = new Report();
            report1.setReportId(reportVO.getReportId());
            report1.setFileId(fileSql.getFileId());
            updateById(report1);
        }
        return FileUtil.getInputStream(file);
    }


    /**
     * 添加表格
     *
     * <AUTHOR>
     * @date 2020-10-29 09:11:38
     */
    private void addMainInfoTable(POIWordUtil wordUtil, List<JSONObject> mainInfoList) {
        //计算行数
        int size = mainInfoList.size();
        int rows = size + 1; //行数
        int cols = 4;   //列数

        //创建表格
        XWPFTable table = wordUtil.createTable(rows, cols);
        CTTblPr tblPr = table.getCTTbl().getTblPr();
//      宽度设置
        tblPr.getTblW().setType(STTblWidth.DXA);
        tblPr.getTblW().setW(new BigInteger("8100"));
//        风格设置
        CTString styleStr = tblPr.addNewTblStyle();
        styleStr.setVal("StyledTable");
//        表格边框
        CTTblBorders borders = table.getCTTbl().getTblPr().addNewTblBorders();
        CTBorder hBorder = borders.addNewInsideH();
        hBorder.setVal(STBorder.SINGLE);
        hBorder.setSz(new BigInteger("1"));
        hBorder.setColor("CCCCCC");
        CTBorder vBorder = borders.addNewInsideV();
        vBorder.setVal(STBorder.SINGLE);
        vBorder.setSz(new BigInteger("1"));
        vBorder.setColor("CCCCCC");
        POIWordUtil.tableBorderColor(borders, "000000", "000000", "000000", "000000");
        table.getCTTbl().getTblPr().addNewJc().setVal(STJc.CENTER); // 表格水平居中

        int row = 0;
        int col = 0;

        //头部
        XWPFTableRow titleRow = table.getRow(row);  //当前行
        titleRow.setHeight(500);
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();  //列中使用段落
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("序号", 20, false, false, null, null);
        CTTcPr tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        CTTblWidth cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(600));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("F2F5FC");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());


        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("标题", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(3000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("F2F5FC");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("日期与来源", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(1500));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("F2F5FC");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
        paragraph.addText("属性", 20, false, false, null, null);
        tcpr = titleRow.getCell(col).getCTTc().addNewTcPr();
        cellw = tcpr.addNewTcW();
        cellw.setType(STTblWidth.DXA);
        cellw.setW(BigInteger.valueOf(1000));
        titleRow.getCell(col).getCTTc().addNewTcPr().addNewShd().setFill("F2F5FC");
        titleRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
        titleRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
        POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());


        //表格数据
        for (int i = 0; i < mainInfoList.size(); i++) {
            JSONObject map = mainInfoList.get(i);
            row = ++row;//新起一行
            col = 0;    //列从1开始
            XWPFTableRow thisRow = table.getRow(row);  //当前行
            thisRow.setHeight(250);

            //序号
            int index = i + 1;
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            paragraph.addText(String.valueOf((index)), 20, false, false, null, null);
            CTTcPr ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            CTTblWidth ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(600));
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

//            标题
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            String description = String.valueOf(map.getStr("title")).replaceAll("<em>", "").replaceAll("</em>", "");  //标题
            paragraph.addText(description, 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(3000));
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            //发布时间
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            String video_create_time = String.valueOf(map.getStr("sourceAndTime")); //发布时间
            paragraph.addText(video_create_time, 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(1500));
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

            //情感面
            paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);  //居中
            String sentiment = String.valueOf(map.getStr("emotion"));  //情感面
            paragraph.addText(sentiment, 20, false, false, null, null);
            ctTcPr = thisRow.getCell(col).getCTTc().addNewTcPr();
            ctTblWidth = ctTcPr.addNewTcW();
            ctTblWidth.setW(BigInteger.valueOf(1000));
            thisRow.getCell(col).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(col++).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        }
        table.getCTTbl().getTblPr().addNewJc().setVal(STJc.CENTER); // 表格水平居中
    }

    private void addInfoIntroTable(POIWordUtil wordUtil, List<Material> mainInfoList) {
        for (int i = 0; i < mainInfoList.size() && i <= 5; i++) {
            String title = mainInfoList.get(i).getTitle().replaceAll("<em>", "").replaceAll("</em>", "");
            if (title.length() > 20) {
                title = title.substring(0, 20) + "...";
            }
            addSecondGradeTitle(wordUtil, "(" + (i + 1) + ")、" + title, false, "arial", null);

            int totalWidth = 8100;
            int piece = 30;
            int oneCellWidth = totalWidth / piece;

            //创建表格
            XWPFTable table = wordUtil.createTable(4, piece);
            table.setWidth(totalWidth);
            table.getRow(0).setHeight(600);
            POIWordUtil.mergeCellsAndSetContent(table, 0, 1, 0, 5, oneCellWidth, "性质", FontSize.Four, true, false, Font.FS_GB2312.getFontValue());
            String emotionFlag = "中性";
            if (mainInfoList.get(i).getEmotionFlag() != null && EmotionEnum.fromValue(mainInfoList.get(i).getEmotionFlag()) != null) {
                emotionFlag = EmotionEnum.fromValue(mainInfoList.get(i).getEmotionFlag()).getName();
            }
            POIWordUtil.mergeCellsAndSetContent(table, 0, 1, 5, 10, oneCellWidth, emotionFlag, FontSize.Four, false, false, Font.FS_GB2312.getFontValue());

            POIWordUtil.mergeCellsAndSetContent(table, 0, 1, 10, 15, oneCellWidth, "文章来源", FontSize.Four, true, false, Font.FS_GB2312.getFontValue());
            String source = "";
            if (mainInfoList.get(i).getType() != null && (mainInfoList.get(i).getType() == 3 || mainInfoList.get(i).getType() == 5)) {
                source = mainInfoList.get(i).getTypeName();
            } else {
                source = mainInfoList.get(i).getHost();
                if (source == null) {
                    source = mainInfoList.get(i).getTypeName();
                }
            }
            POIWordUtil.mergeCellsAndSetContent(table, 0, 1, 15, 20, oneCellWidth, source, FontSize.Four, false, false, Font.FS_GB2312.getFontValue());

            POIWordUtil.mergeCellsAndSetContent(table, 0, 1, 20, 25, oneCellWidth, "时间", FontSize.Four, true, false, Font.FS_GB2312.getFontValue());
            String data = DateUtil.format(DateUtil.parse(DateUtil.format(mainInfoList.get(i).getPublishTime(), DatePattern.NORM_DATETIME_PATTERN), DatePattern.NORM_DATETIME_PATTERN), DatePattern.CHINESE_DATE_PATTERN);
            POIWordUtil.mergeCellsAndSetContent(table, 0, 1, 25, 30, oneCellWidth, data, FontSize.Four, false, false, Font.FS_GB2312.getFontValue());
            table.getRow(1).setHeight(600);
            POIWordUtil.mergeCellsAndSetContent(table, 1, 2, 0, 5, oneCellWidth, "相似文章数", FontSize.Four, true, false, Font.FS_GB2312.getFontValue());
            POIWordUtil.mergeCellsAndSetContent(table, 1, 2, 5, 10, oneCellWidth, "1", FontSize.Four, false, false, Font.FS_GB2312.getFontValue());
            POIWordUtil.mergeCellsAndSetContent(table, 1, 2, 10, 15, oneCellWidth, "作者", FontSize.Four, true, false, Font.FS_GB2312.getFontValue());
            POIWordUtil.mergeCellsAndSetContent(table, 1, 2, 15, 30, oneCellWidth, mainInfoList.get(i).getAuthor(), FontSize.Four, false, false, Font.FS_GB2312.getFontValue());
            table.getRow(2).setHeight(600);
            POIWordUtil.mergeCellsAndSetContent(table, 2, 3, 0, 5, oneCellWidth, "内容", FontSize.Four, true, false, Font.FS_GB2312.getFontValue());

            String text = mainInfoList.get(i).getText().replaceAll("<em>", "").replaceAll("</em>", "");
            text = "    " + POIWordUtil.insertPlusSigns(text, 40);
            POIWordUtil.mergeCellsAndSetContentLeft(table, 2, 3, 5, 30, oneCellWidth, text, FontSize.Four, false, false, Font.FS_GB2312.getFontValue());
            table.getRow(3).setHeight(600);
            //第四行
            XWPFTableRow thisRow = table.getRow(3);  //当前行
            thisRow.setHeight(600);
            //合并单元格
            TableUtil.mergeCells(table, 3, 4, 0, 30);
            //设置宽度
            XWPFTableCell cell = table.getRow(3).getCell(0);
            TableUtil.setCellWidth(cell, 30 * oneCellWidth);
            CustomXWPFParagraph paragraph = wordUtil.createParagraph();
            paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.CENTER);
            paragraph.addText("原文链接：", 20, false, false, null, null);
            paragraph.addHyperlink(mainInfoList.get(i).getUrl(), 20, mainInfoList.get(i).getUrl());
            thisRow.getCell(0).setParagraph(paragraph.getXWPFParagraph());
            thisRow.getCell(0).setVerticalAlignment(XWPFTableCell.XWPFVertAlign.CENTER);
            POIWordUtil.deleteParagraph(paragraph.getXWPFParagraph());

        }
    }


    /**
     * 添加一级标题，如：一、
     *
     * <AUTHOR>
     * @date 2017-06-12 上午10:57:07
     */
    public static void addFirstGradeTitle(POIWordUtil wordUtil, String content, Boolean isBlod, String fontFamily, String colorVal) {
        wordUtil.addTitle(HeadStyle.H1.getStyleName(), content, 28, isBlod, false, fontFamily, colorVal);
    }

    /**
     * 添加二级标题，如：1、
     *
     * <AUTHOR>
     * @date 2017-06-12 上午10:57:29
     */
    private void addSecondGradeTitle(POIWordUtil wordUtil, String content, Boolean isBlod, String fontFamily, String colorVal) {
        wordUtil.addTitle(HeadStyle.H2.getStyleName(), content, 24, isBlod, false, fontFamily, colorVal);
    }

    /**
     * 添加文本，设置为首行缩进2字符，左对齐
     *
     * <AUTHOR>
     * @date 2017-06-12 上午10:57:46
     */
    private void addComments(POIWordUtil wordUtil, String comments) {
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();
        paragraph.addText(comments, 20, false, false);
        paragraph.setParagraphIndInfo("400", "200");// 缩进俩字符
        paragraph.setAlign(ParagraphAlignment.LEFT, TextAlignment.CENTER);
    }

    /**
     * 添加图片，设置段落居中
     *
     * <AUTHOR>
     * @date 2017-06-12 上午10:58:12
     */
    private void addEchartsPicture(POIWordUtil wordUtil, String echartsPicture) {
        CustomXWPFParagraph paragraph = wordUtil.createParagraph();
        paragraph.setAlign(ParagraphAlignment.CENTER, TextAlignment.CENTER);
        paragraph.addEchartsPicture(echartsPicture);
    }

    public static List<TimeRoundFlowVO> generateTimeFlows(LocalDateTime startTime, LocalDateTime endTime) {
        Duration duration = Duration.between(startTime, endTime);
        if (Math.abs(duration.toMinutes()) <= 5) {
            List<TimeRoundFlowVO> timeFlows = new ArrayList<>(1);
            TimeRoundFlowVO timeFlowVO = new TimeRoundFlowVO();
            timeFlowVO.setStartTime(DateUtil.formatLocalDateTime(startTime));
            timeFlowVO.setEndTime(DateUtil.formatLocalDateTime(endTime));
            timeFlows.add(timeFlowVO);
            return timeFlows;
        }

        long durationMillis = Duration.between(startTime, endTime).toMillis();
        int numSegments = Math.min(15, Math.max(7, (int) (durationMillis / (1000 * 60 * 5))));
        List<TimeRoundFlowVO> timeFlows = new ArrayList<>(numSegments);

        LocalDateTime current = startTime;
        while (!current.isAfter(endTime) && !current.isEqual(endTime)) {
            LocalDateTime next = current.plusSeconds(durationMillis / numSegments / 1000);
            if (next.isAfter(endTime)) {
                next = endTime;
            }
            TimeRoundFlowVO timeFlowVO = new TimeRoundFlowVO();
            timeFlowVO.setStartTime(DateUtil.formatLocalDateTime(current));
            timeFlowVO.setEndTime(DateUtil.formatLocalDateTime(next));
            timeFlows.add(timeFlowVO);
            current = next;
        }

        return timeFlows;
    }
}
