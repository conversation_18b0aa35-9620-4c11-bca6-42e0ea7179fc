package com.boryou.web.module.drill.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.drill.domain.vo.DrillSignVO;
import com.boryou.web.module.drill.service.DrillSignService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;

@Slf4j
@RestController
@RequiredArgsConstructor
public class DrillSignController {

    private final DrillSignService drillSignService;

    @PostMapping("/drill/sign/save")
    public AjaxResult drillTaskSave(@RequestBody @Valid DrillSignVO drillSignVO) {
        drillSignService.saveUpset(drillSignVO);
        return AjaxResult.success();
    }

    @PostMapping("/drill/sign/query")
    public AjaxResult drillTaskQuery(@RequestBody DrillSignVO drillSignVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        return AjaxResult.success(drillSignService.drillSignQuery(drillSignVO, user));
    }

}
