package com.boryou.web.module.warn.domain.vo;

import com.boryou.web.module.warn.domain.WarnSet;
import lombok.Data;
import lombok.EqualsAndHashCode;

import javax.validation.constraints.NotNull;
import java.util.List;


@EqualsAndHashCode(callSuper = true)
@Data
public class WarnSetVO extends WarnSet {
    /**
     * 开始接收时段
     */
    @NotNull(message = "开始接收时段不能为空")
    private String startReceiveTime;

    /**
     * 结束接收时段
     */
    @NotNull(message = "结束接收时段不能为空")
    private String endReceiveTime;

    /**
     * 短信接收用户id
     */
    private List<ContactUserVO> messageContact;

    /**
     * 邮箱接收用户
     */
    private List<ContactUserVO> mailContact;

    /**
     * 微信接收用户
     */
    private List<ContactUserVO> chatContact;
}
