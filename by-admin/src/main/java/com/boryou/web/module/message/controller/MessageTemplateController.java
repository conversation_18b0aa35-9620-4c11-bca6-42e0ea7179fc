package com.boryou.web.module.message.controller;

import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.web.module.message.domain.MessageTemplate;
import com.boryou.web.module.message.domain.vo.DeptMessage;
import com.boryou.web.module.message.service.IMessageTemplateService;
import lombok.AllArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

/**
 * 短信模板Controller
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
@RestController
@RequestMapping("/message")
@AllArgsConstructor
public class MessageTemplateController extends BaseController {
    private final IMessageTemplateService messageTemplateService;


    /**
     * 查询短信模板列表
     */
    @GetMapping("/list")
    public TableDataInfo list(MessageTemplate messageTemplate) {
        startPage();
        List<MessageTemplate> list = messageTemplateService.selectMessageTemplateList(messageTemplate);
        return getDataTable(list);
    }

    /**
     * 保存部门短信模板
     */
    @PostMapping("saveDeptMessage")
    public AjaxResult saveDeptMessage(@RequestBody DeptMessage deptMessage) {
        return toAjax(messageTemplateService.saveDeptMessage(deptMessage));
    }

    /**
     * 获取部门的模板
     */
    @GetMapping("getDeptMessageTemplate")
    public AjaxResult getDeptMessageTemplate(DeptMessage deptMessage) {
        MessageTemplate temp = messageTemplateService.getDeptMessageTemplate(deptMessage);
        return AjaxResult.success(temp);
    }

    /**
     * 修改短信模板
     */
    @PutMapping
    public AjaxResult edit(@RequestBody MessageTemplate messageTemplate) {
        return toAjax(messageTemplateService.updateMessageTemplate(messageTemplate));
    }

//    /**
//     * 导出短信模板列表
//     */
//    @PreAuthorize("@ss.hasPermi('message:message:export')")
//    @Log(title = "短信模板", businessType = BusinessType.EXPORT)
//    @GetMapping("/export")
//    public AjaxResult export(MessageTemplate messageTemplate) {
//        List<MessageTemplate> list = messageTemplateService.selectMessageTemplateList(messageTemplate);
//        ExcelUtil<MessageTemplate> util = new ExcelUtil<MessageTemplate>(MessageTemplate.class);
//        return util.exportExcel(list, "message");
//    }
//
//    /**
//     * 获取短信模板详细信息
//     */
//    @PreAuthorize("@ss.hasPermi('message:message:query')")
//    @GetMapping(value = "/{id}")
//    public AjaxResult getInfo(@PathVariable("id") Long id) {
//        return AjaxResult.success(messageTemplateService.selectMessageTemplateById(id));
//    }
//
//    /**
//     * 新增短信模板
//     */
//    @PreAuthorize("@ss.hasPermi('message:message:add')")
//    @Log(title = "短信模板", businessType = BusinessType.INSERT)
//    @PostMapping
//    public AjaxResult add(@RequestBody MessageTemplate messageTemplate) {
//        return toAjax(messageTemplateService.insertMessageTemplate(messageTemplate));
//    }
//

//
//    /**
//     * 删除短信模板
//     */
//    @PreAuthorize("@ss.hasPermi('message:message:remove')")
//    @Log(title = "短信模板", businessType = BusinessType.DELETE)
//    @DeleteMapping("/{ids}")
//    public AjaxResult remove(@PathVariable Long[] ids) {
//        return toAjax(messageTemplateService.deleteMessageTemplateByIds(ids));
//    }
}
