package com.boryou.web.module.drill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.boryou.web.handle.ListStringTypeHandler;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 评论回复表实体
 */
@Data
@TableName(value = "by_drill_comment_reply", autoResultMap = true)
public class DrillCommentReply {
    /**
     * 评论ID
     */
    @TableId(type = IdType.INPUT)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commentReplyId;

    /**
     * 所属微博ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long commentId;

    /**
     * 阶段ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processStageId;

    /**
     * 关联演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    /**
     * 评论者ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 评论内容
     */
    private String content;

    /**
     * 父评论ID（0=顶级评论）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long parentId;

    /**
     * 根评论ID（树形结构快速查询）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long rootId;

    /**
     * 评论时间
     */
    private Date createdTime;

    /**
     * 点赞数
     */
    private Integer likeCount;

    /**
     * 状态（0=正常，1=删除，2=审核中）
     */
    private Integer status;

    private Integer teamType;

    private String roleInfo;

    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> file;

}
