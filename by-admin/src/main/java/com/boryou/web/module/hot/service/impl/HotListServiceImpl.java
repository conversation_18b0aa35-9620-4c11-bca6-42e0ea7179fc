package com.boryou.web.module.hot.service.impl;

import com.boryou.web.domain.Hot;
import com.boryou.web.mapper.HotListMapper;
import com.boryou.web.module.hot.service.IHotListService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Set;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024-09-06 14:45
 */
@Service
@RequiredArgsConstructor
public class HotListServiceImpl implements IHotListService {

    private final HotListMapper hotListMapper;

    @Override
    public Map<String, List<Hot>> selectHotAllList(List<String> types) {
        List<Hot> hotList = new ArrayList<>();
        //AREA_BELLWETHER百度地域风向标,  WB_REALTIME微博实时榜， WB_NEWS微博要闻，DOUYIN_HOT抖音热点榜，DOUYIN_VIDEO抖音视频榜，TOUTIAO_HOT今日头条热点榜
        List<Hot> hots = hotListMapper.selectHotAllList(types);
        Map<String, List<Hot>> collect = hots.stream().collect(Collectors.groupingBy(Hot::getType));
        Set<String> strings = collect.keySet();
        for (String string : strings) {
            List<Hot> hots1 = collect.get(string);
            for (int i = 0; i < hots1.size(); i++) {
                Hot hot = hots1.get(i);
                hot.setSort(Long.valueOf(String.valueOf(i + 1)));
                String type = hot.getType();
                if ("WB_REALTIME".equals(type) || "WB_NEWS".equals(type)) {
                    hot.setType("微博");
                } else if ("DOUYIN_VIDEO".equals(type) || "DOUYIN_HOT".equals(type)) {
                    hot.setType("抖音");
                } else if ("TOUTIAO_HOT".equals(type)) {
                    hot.setType("头条");
                } else if ("REALTIME".equals(type)) {
                    hot.setType("百度");//丁组磊采集的百度榜单，不是百度的链接，而是百度榜单中链接里面的原文url。这个REALTIME他说可以当作百度的榜单
                }
                hot.setIndexNumStr(hot.getIndexNum() == 0 ? "采集中" : String.valueOf(hot.getIndexNum()));
                hotList.add(hot);
            }
        }
        return hotList.stream().collect(Collectors.groupingBy(Hot::getType));
    }
}

