package com.boryou.web.module.home.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.date.DateField;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.ObjectUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.db.PageResult;
import cn.hutool.http.HtmlUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.boryou.common.core.redis.RedisCache;
import com.boryou.common.utils.DictUtils;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.Word;
import com.boryou.web.controller.common.entity.bo.AreaOverviewBO;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.entity.vo.HomeVO;
import com.boryou.web.controller.common.enums.EmotionEnum;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.controller.common.enums.SortTypeEnum;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.domain.Hot;
import com.boryou.web.domain.Plan;
import com.boryou.web.domain.bo.PlanMainBO;
import com.boryou.web.domain.vo.HotVO;
import com.boryou.web.domain.vo.SearchVO;
import com.boryou.web.domain.vo.TimeRoundFlowVO;
import com.boryou.web.module.account.service.AccountService;
import com.boryou.web.module.area.entity.Area;
import com.boryou.web.module.home.entity.Lawyer;
import com.boryou.web.module.home.entity.bo.LawyerAuthorBO;
import com.boryou.web.module.home.entity.bo.LawyerBO;
import com.boryou.web.module.home.entity.vo.SimpleVO;
import com.boryou.web.module.home.mapper.LawyereMapper;
import com.boryou.web.module.home.service.HomeStatisService;
import com.boryou.web.module.warn.domain.vo.WarnDataVO;
import com.boryou.web.module.warn.service.WarnDataService;
import com.boryou.web.service.HotService;
import com.boryou.web.service.PlanService;
import com.boryou.web.service.SearchService;
import com.boryou.web.task.CommonDataTask;
import com.boryou.web.util.TimeUtil;
import lombok.RequiredArgsConstructor;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;
import org.wltea.analyzer.core.IKSegmenter;
import org.wltea.analyzer.core.Lexeme;

import java.io.IOException;
import java.io.StringReader;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

import static com.boryou.common.constant.Constants.ZJGY_AREA_CODE;
import static com.boryou.web.constant.BC.*;

/**
 * 首页services
 * 首页的总量，曲线图，活跃账号，热词云，辖区总览--全部查敏感信息，-胡总提
 *
 * <AUTHOR>
 * @date 2024-05-23 09:52
 */
@Service
@Slf4j
@RequiredArgsConstructor
public class HomeStatisServiceImpl implements HomeStatisService {

    //    private final SearchController searchController;
    private final SearchService searchService;
    private final LawyereMapper lawyereMapper;
    private final RedisCache redisTemplate;
    private final PlanService planService;
    private final HotService hotService;
    private final WarnDataService warnDataService;
    private final AccountService accountService;
//    private final ISysDeptService deptService;


    @Override
    public com.alibaba.fastjson.JSONArray getEmontionStatis(HomeVO homeVO, boolean flag) {
        boolean cache = true;
        String key = RedisConstant.emotion + homeVO.getContentAreaCode() + ":" + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (com.alibaba.fastjson.JSONArray) cacheObject;
        }
        com.alibaba.fastjson.JSONArray array = new com.alibaba.fastjson.JSONArray();
        EsSearchBO analysisSubject = JSONUtil.toBean(JSONUtil.toJsonStr(homeVO), EsSearchBO.class);
        analysisSubject.setTimeType(0);//TODO 和ES build有冲突
        commonQueryFilter(analysisSubject);
        analysisSubject.setEmotionFlag(null);
        Map<String, Integer> stringIntegerMap = EsSearchUtil.emotionAnalyse(analysisSubject);
        if (/*!flag &&*/ (stringIntegerMap.isEmpty() || null != stringIntegerMap.get("code"))) {
            cache = false;
        }
        JSONObject data;
        for (Map.Entry<String, Integer> entry : stringIntegerMap.entrySet()) {
            if (entry.getKey().equals(EmotionEnum.NEGATIVE.getStrValue())) {
                data = new JSONObject();
                data.put("name", "敏感");
                data.put("value", entry.getValue());
                array.add(data);
            }
            if (entry.getKey().equals(EmotionEnum.POSITIVE.getStrValue())) {
                data = new JSONObject();
                data.put("name", "非敏感");
                data.put("value", entry.getValue());
                array.add(data);
            }
            if (entry.getKey().equals(EmotionEnum.NEUTRAL.getStrValue())) {
                data = new JSONObject();
                data.put("name", "中性");
                data.put("value", entry.getValue());
                array.add(data);
            }
        }
        if (cache) {
            redisTemplate.setCacheObject(key, array);
        }
        return array;
    }

    public static void commonQueryFilter(EsSearchBO analysisSubject) {
        analysisSubject.setKeyWord1(words);
        analysisSubject.setExcludeWord(excludes);
        analysisSubject.setExcludeTermField(excludeTermField);
        analysisSubject.setSearchPosition("1,2");
        analysisSubject.setExcludeAuthor(exclude_author);
        analysisSubject.setEmotionFlag("1");
    }

    public static void commonQueryFilter2(EsSearchBO analysisSubject) {
        analysisSubject.setKeyWord1(words);
        analysisSubject.setExcludeWord(excludes);
        analysisSubject.setExcludeTermField(excludeTermField);
        analysisSubject.setSearchPosition("1,2");
        analysisSubject.setExcludeAuthor(exclude_author);
    }

    @Override
    public List<LawyerBO> getLawyerMoment(HomeVO homeVO, boolean flag) {
        boolean cache = true;
        String key = RedisConstant.lawyer + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (List<LawyerBO>) cacheObject;
        }
        List<Lawyer> lawyers = lawyereMapper.selectLawyerList(new LawyerBO());
        for (Lawyer lawyer : lawyers) {
            String avatar = accountService.getAvatar(lawyer);
            if (StrUtil.isBlankIfStr(avatar)) {
                continue;
            }
            lawyer.setAvatar(avatar);
        }
        List<String> authorIds = lawyers.stream().map(Lawyer::getName).filter(StrUtil::isNotEmpty).collect(Collectors.toList());
        List<LawyerBO> lawyerList = new ArrayList<>();
        EsSearchBO bo = new EsSearchBO();
        bo.setStartTime(homeVO.getStartTime());
        bo.setEndTime(homeVO.getEndTime());
        bo.setPageSize(1000);
        bo.setAuthor(CollectionUtil.join(authorIds, ","));
        //微博，微信，小视频   抖音快手放在短视频里，律师发的抖音和快手是在短视频里，客户端6是查不到的。
        bo.setType(MediaTypeEnum.WEIBO.getValue() + "," + MediaTypeEnum.WECHAT.getValue() + "," + MediaTypeEnum.VIDEO.getValue());
        bo.setIsOriginal(true);
        bo.setSortType(SortTypeEnum.getEnumValue(SortTypeEnum.TIME_DESC));
        bo.setSearchPosition("1,2");
        bo.setEmotionFlag("1");
        JSONObject search = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.SEARCHX);
        if (/*!flag && */(search.isEmpty() || null != search.get("code"))) {
            cache = false;
        }
        bo.setSortType(null);
        bo.setPageSize(0);//不分页
        bo.setAggName("author");
        JSONObject postBodyJSONObject = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.TYPE_TOTAL_COUNT);
        JSONArray records = search.getJSONArray("records");
        if (null == records || records.isEmpty()) {
//            log.info("--超时:{}", search);
            cache = false;
        }
        List<EsBean> esBeanList = new ArrayList<>(JSONUtil.toList(records, EsBean.class));
        for (EsBean esBean : esBeanList) {
            LawyerBO e = new LawyerBO();
            e.setUrl(esBean.getUrl());
            e.setTitle(removeSectionTags(esBean.getTitle()));
            e.setContent(removeSectionTags(esBean.getText()));
            e.setCreateTime(DateUtil.formatDateTime(esBean.getPublishTime()));
            e.setName(esBean.getAuthor());
            Object o = postBodyJSONObject.get(e.getName());
            e.setCount(o == null ? 0 : Integer.parseInt(String.valueOf(o)));
            for (Lawyer lawyer : lawyers) {
                String mediaType = lawyer.getSource();
                if (esBean.getAuthor().equals(lawyer.getName()) && esBean.getType().toString().equals(mediaType)) {
                    if (String.valueOf(esBean.getType()).equals(String.valueOf(MediaTypeEnum.VIDEO.getValue()))) {
                        String mediaDict = lawyer.getMediaDict();
                        if (CharSequenceUtil.isNotBlank(mediaDict)) {
                            String videoHost = DictUtils.getDictValue("sys_search_short_video", mediaDict);
                            List<String> videpHostList = CharSequenceUtil.splitTrim(videoHost, ",");
                            if (videpHostList.contains(esBean.getHost())) {
                                e.setVideoHost(videoHost);
                                e.setSource(mediaDict);
                            } else {
                                continue;
                            }
                        }
                    } else {
                        String desc = MediaTypeEnum.getDesc(String.valueOf(esBean.getType()));
                        e.setSource(desc);
                    }
                    e.setType(mediaType);
                    e.setAvatar(lawyer.getAvatar());
                    lawyerList.add(e);
                    break;
                }
            }
        }
        if (lawyerList.size() > 10) {
            lawyerList = lawyerList.subList(0, 10);
        }
        if (cache) {
            redisTemplate.setCacheObject(key, lawyerList);
        }
        return lawyerList;
    }

    @Override
    public List<LawyerAuthorBO.Author> getLawyer(List<LawyerBO> lawyerList) {
        Set<LawyerAuthorBO.Author> authorTypeList = new HashSet<>();
        for (LawyerBO lawyerBO : lawyerList) {
            String type = lawyerBO.getType();
            String name = lawyerBO.getName();
            String source = lawyerBO.getSource();
            String avatar = lawyerBO.getAvatar();
            String videoHost = lawyerBO.getVideoHost();
            LawyerAuthorBO.Author author = new LawyerAuthorBO.Author();
            author.setType(type);
            author.setSource(source);
            author.setName(name);
            author.setAvatar(avatar);
            author.setVideoHost(videoHost);
            authorTypeList.add(author);
        }
        return CollUtil.newArrayList(authorTypeList);
    }

    @Override
    public List<Hot> hotZJ(HomeVO homeVO, boolean flag) {
        boolean cache = true;
        String key = RedisConstant.hotZJ + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (List<Hot>) cacheObject;
        }

        String startTime = homeVO.getStartTime();
        String endTime = homeVO.getEndTime();
        Integer count = 50;
        List<Hot> hots = buildHotZJ(startTime, endTime, count);
        if (CollUtil.isEmpty(hots)) {
            return Collections.emptyList();
        }
        //if (hots.size() > 10) {
        //    hots = hots.subList(0, 10);
        //}
        if (cache) {
            redisTemplate.setCacheObject(key, hots);
        }
        return hots;
    }

    private List<Hot> buildHotZJ(String startTime, String endTime, Integer count) {
        EsSearchBO esSearchBO = new EsSearchBO();
        esSearchBO.setTimeType(0);
        esSearchBO.setStartTime(startTime);
        esSearchBO.setEndTime(endTime);
        esSearchBO.setPageNum(1);
        esSearchBO.setPageSize(count);
        esSearchBO.setConfigSelect(1);
        esSearchBO.setSourceSetting("0");
        esSearchBO.setType("0,1,3,5,6,11,17,25");
        esSearchBO.setVideoHost("www.douyin.com,www.iesdouyin.com,v.douyin.com,live.douyin.com,www.xiaohongshu.com,www.toutiao.com,www.ixigua.com,weibo.com,www.kuaishou.com,haokan.baidu.com,www.bilibili.com,t.bilibili.com,www.meipai.com,n.miaopai.com,h5.weishi.qq.com,tv.sohu.com,v.qq.com,www.iqiyi.com,video.tudou.com,v.youku.com,play.tudou.com");
        esSearchBO.setAccountLevel("600,0,1,2,3,4,5,6,7,220,200,-1");
        esSearchBO.setContentForm("1,2,3,4");
        esSearchBO.setForward("1,2");
        esSearchBO.setProWord("((审判员|审判长|法院|法官|法庭|中院|高院|一审|二审|原告|被告|终审|判决书|审判|宣判|开庭|重审|f院|f官|庭审|法警|法院干警|书记员|庭长|海事)+(非法|判决|执行|勾结|撤销处罚|索赔|判赔|枉法|判处|裁定|驳回|糊涂案|冤假|违法|错判|判刑|含冤|冤枉|冤民|伸冤|申冤|上访|赴京|信访|暴力|抗法|执法|举报|上诉|维权|不公|群访|聚集|聚众|游行|示威|抗议|横幅|腐败|保护伞|推诿|推脱|敷衍|不作为|乱作为|强奸|杀害|死亡|冤案|受害|重伤|国家赔偿|笔录|罚款|司法|黑恶|恶势力|黑幕|殴打|恐吓|威胁|打人|性侵|自杀|猝死|跳楼|猥亵|贪污|受贿|失职|渎职|讨薪|拖欠|诈骗|抢劫|进京|截访|血汗钱|程序|扫黑除恶|巡视组|国赔|立案|包庇|轻判|判罚|人情案|遥控|机械办案|诉讼|乱判|伪造|认定|控告|犯法|败诉|徇私|关系案|行政诉讼|未审先判|办案|滥用|错案|抗诉|官官相护|虚假|权大于法|强制|调解|违纪|违规|再审|公诉|改判|同案不同判|强奸|民告官|未审先判))|魏祎晨|王光祥|林毅|法院|法官");
        esSearchBO.setExcludeWord("富豪榜 联系删除 请联系 有侵权联系 请联系我们 如涉及 如有侵权 如存在侵权 如有侵权 卖淫女 乌克兰 3名无业青年 报名 开盘 购房人 拍卖 戚仁海 虞书欣 肖战 杨超越 成毅 卢昱晓 刘亚仁 黄子韬 大三女生喝农药自杀 黄明昊 韩安冉 集装箱 王丽坤 赵丽颖 头套 卖玉米");
        esSearchBO.setContentAreaCode("330000");
        esSearchBO.setIsOriginal(true);
        esSearchBO.setEmotionFlag("1");
        esSearchBO.setSortType(10);
        esSearchBO.setSearchPosition("0");
        PageResult<EsBean> pageResult = searchService.search(esSearchBO);
        if (CollUtil.isEmpty(pageResult)) {
            return Collections.emptyList();
        }
        List<Hot> hots = new ArrayList<>();
        for (EsBean esBean : pageResult) {
            String title = esBean.getTitle();
            String url = esBean.getUrl();
            String id = esBean.getId();
            Integer commentNum = esBean.getCommentNum();
            Integer likeNum = esBean.getLikeNum();
            Integer reprintNum = esBean.getReprintNum();
            Hot hot = new Hot();
            hot.setDocId(id);
            hot.setTitle(HtmlUtil.removeHtmlTag(title, false, "em"));
            hot.setUrl(url);
            hot.setUpdateTime(DateUtil.parseDateTime(endTime));
            int total = (commentNum == null ? 0 : commentNum) +
                    (likeNum == null ? 0 : likeNum) +
                    (reprintNum == null ? 0 : reprintNum);
            hot.setIndexNum(total);
            hots.add(hot);
        }
        return hots;
    }

    @Override
    public List<Hot> warn(HomeVO homeVO, boolean flag) {
        boolean cache = true;
        Long deptId = homeVO.getDeptId();
        String key = RedisConstant.warn + deptId + "-" + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (List<Hot>) cacheObject;
        }

        String startTime = homeVO.getStartTime();
        String endTime = homeVO.getEndTime();
        WarnDataVO warnDataVO = new WarnDataVO();
        warnDataVO.setWarnDateStart(startTime);
        warnDataVO.setWarnDateEnd(endTime);
        warnDataVO.setPageNum(1);
        warnDataVO.setPageSize(200);
        warnDataVO.setDeptId(deptId);

        PageResult<EsBean> pageResult = warnDataService.warnDataGet(warnDataVO);
        if (CollUtil.isEmpty(pageResult)) {
            return Collections.emptyList();
        }
        List<EsBean> esBeanList = new ArrayList<>();
        Set<String> md5 = new HashSet<>();
        for (EsBean esBean : pageResult) {
            String md6 = esBean.getMd5();
            if (!md5.contains(md6)) {
                esBeanList.add(esBean);
                md5.add(md6);
            }
        }

        List<Hot> hots = new ArrayList<>();
        for (EsBean esBean : esBeanList) {
            String title = esBean.getTitle();
            String text = esBean.getText();
            String url = esBean.getUrl();
            String id = esBean.getId();
            Integer type = esBean.getType();
            String typeName = esBean.getTypeName();
            Hot hot = new Hot();
            hot.setDocId(id);
            if (title.contains("转发微博")) {
                title = CharSequenceUtil.sub(HtmlUtil.removeHtmlTag(text, "em"), 0, 50);
            }
            hot.setTitle(HtmlUtil.removeHtmlTag(title, false, "em"));
            hot.setUrl(url);
            hot.setType(String.valueOf(type));
            hot.setTypeName(typeName);
            hots.add(hot);
        }
        if (CollUtil.isEmpty(hots)) {
            return Collections.emptyList();
        }
        if (hots.size() > 10) {
            hots = hots.subList(0, 10);
        }
        if (cache) {
            redisTemplate.setCacheObject(key, hots);
        }
        return hots;
    }

    public static String removeSectionTags(String content) {
        // 正则表达式匹配<section>及其内容，包括内部的任何字符，直到遇到闭合的</section>
        String pattern = "(?i)<section[^>]*>.*?</section>";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(content);
        String s = m.replaceAll("");
        if (s.contains("<p")) {
            Pattern pattern1 = Pattern.compile("<p\\s+[^>]*?>.*?</p>");
            Matcher matcher = pattern1.matcher(s);
            s = matcher.replaceAll("");
        }
        return s;
    }

    @Override
    public List<Map<String, Object>> getWordCloud(HomeVO homeVO, boolean flag) throws IOException {
//        String timeType = StrUtil.isEmpty(homeVO.getTimeType()) ? "1" : homeVO.getTimeType();
//        String times = homeVO.getStartTime() + "_" + homeVO.getEndTime();
//        String key1 = "HOME:WORD_CLOUD_";
//        String key = key1 + timeType + "_";
//        Object cacheObject1 = getCacheValue(homeVO, key, key1, timeType);
//        if (cacheObject1 != null) {
//            return (List<Map<String, Object>>) cacheObject1;
//        }
        boolean cache = true;
        String key = RedisConstant.wordcloud + homeVO.getContentAreaCode() + ":" + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (List<Map<String, Object>>) cacheObject;
        }
        EsSearchBO bo = JSONUtil.toBean(JSONUtil.toJsonStr(homeVO), EsSearchBO.class);
        commonQueryFilter(bo);
        bo.setTimeType(0);//TODO 和ES build有冲突
        bo.setIsOriginal(false);
        bo.setEmotionFlag("1");
        bo.setSortType(SortTypeEnum.getEnumValue(SortTypeEnum.TIME_ASC));
//        bo.setSearchPosition("1,2");
        bo.setPageNum(1);
        bo.setPageSize(100);
        JSONObject search = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.SEARCH);
        if (/*!flag &&*/ (search.isEmpty() || null != search.get("code"))) {
            cache = false;
        }
        List<EsBean> esBeanList = new ArrayList<>(JSONUtil.toList(search.getJSONArray("records"), EsBean.class));
        Map<String, Integer> wordsFren = new HashMap<>();
        List<Word> res = new ArrayList<>();
        List<String> filterCloudWord = hotService.filterCloudWordList();
        Lexeme lexeme;
        IKSegmenter ikSegmenter;
        for (EsBean indexResultBean : esBeanList) {
            String text = indexResultBean.getText();
            if (StringUtils.isNotEmpty(text)) {
                text = HtmlUtil.cleanHtmlTag(text);
                ikSegmenter = new IKSegmenter(new StringReader(text), true);
                while ((lexeme = ikSegmenter.next()) != null) {
                    String lexemeText = lexeme.getLexemeText();
                    if (lexemeText.length() > 1) {
                        if (filterCloudWord.contains(lexemeText) || !hot_words.contains(lexemeText)) {
                            continue;
                        }
                        if (wordsFren.containsKey(lexemeText)) {
                            wordsFren.put(lexemeText, wordsFren.get(lexemeText) + 1);
                        } else {
                            wordsFren.put(lexemeText, 1);
                        }
                    }
                }
            }
        }
        wordsFren.keySet().forEach(x -> {
            res.add(new Word(x, wordsFren.get(x)));
        });
        List<Word> collect = res.stream().sorted((o1, o2) -> Math.toIntExact(o2.getNum() - o1.getNum())).collect(Collectors.toList());
        if (collect.size() > 21) {
            collect = collect.subList(0, 20);
        }
        List<Map<String, Object>> maps = new ArrayList<>();
        for (Word word : collect) {
            Map<String, Object> map = new LinkedHashMap<>();
            map.put("name", word.getWord());
            map.put("value", word.getNum());
            maps.add(map);
        }
//        redisTemplate.setCacheObject(key1 + timeType, maps, homeKeyTimeoutDelay, TimeUnit.MINUTES);
//        redisTemplate.setCacheObject(key, times, homeKeyTimeout, TimeUnit.MINUTES);
//        if (null != homeVO.getTimeType() && !"4".equals(homeVO.getTimeType())) {
        if (cache) {
            redisTemplate.setCacheObject(key, maps);
        }
//        }
        return maps;
    }

    @Override
    public Long total(HomeVO homeVO, boolean flag) {
        boolean cache = true;
        String key = RedisConstant.total + homeVO.getContentAreaCode() + ":" + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (Long) cacheObject;
        }
        EsSearchBO bo = JSONUtil.toBean(JSONUtil.toJsonStr(homeVO), EsSearchBO.class);
        commonQueryFilter(bo);
        bo.setTimeType(0);//TODO 和ES build有冲突
        bo.setSearchPosition("1,2");
        bo.setEmotionFlag("1");
//        bo.setType(MediaTypeEnum.NEWS.getValue() + "," + MediaTypeEnum.WEIBO.getValue() + "," + MediaTypeEnum.WECHAT.getValue());
        long total = EsSearchUtil.getInfoCount(bo/*, EsSearchUtil.INFO_COUNT*/);
        if (/*!flag &&*/ 0 == total) {
            cache = false;
        }
//        log.info("新闻网站{}--{}",homeVO.getTimeType(),infoCount);
//        bo.setType(MediaTypeEnum.CLIENT.getValue() + "");
//        bo.setHost("www.iesdouyin.com,www.toutiao.com,www.kuaishou.com");
//        long infoCount1 = EsSearchUtil.getInfoCount(bo, EsSearchUtil.INFO_COUNT);
//        log.info("抖音，头条{}--{}",homeVO.getTimeType(),infoCount1);
//        if (/*!flag && */0 == infoCount1) {
//            cache = false;
//        }
//        long total = infoCount + infoCount1;
//        log.info("-----{}--{}",homeVO.getTimeType(),total);
        if (cache) {
            redisTemplate.setCacheObject(key, total);
        }
        return total;
    }

    @SneakyThrows
    @Override
    public Map<String, Object> curveStatis(HomeVO homeVO, boolean flag) {
        boolean cache = true;
//        String timeType = StrUtil.isEmpty(homeVO.getTimeType()) ? "1" : homeVO.getTimeType();
//        String times = homeVO.getStartTime() + "_" + homeVO.getEndTime();
//        String key1 = "HOME:CURVE_";
//        String key = key1 + timeType + "_";
//        Object cacheObject1 = getCacheValue(homeVO, key, key1, timeType);
//        if (cacheObject1 != null) {
//            return (Map<String, Object>) cacheObject1;
//        }
        String key = RedisConstant.curvestatis + homeVO.getContentAreaCode() + ":" + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (Map<String, Object>) cacheObject;
        }
        EsSearchBO bo = JSONUtil.toBean(JSONUtil.toJsonStr(homeVO), EsSearchBO.class);
        commonQueryFilter(bo);
        bo.setTimeType(0);//TODO 和ES build有冲突
        bo.setEmotionFlag("1");
        Map<String, Object> resultMap = new HashMap<>();
        List<Map<String, Object>> aimData = new ArrayList<>();
        //
        bo.setSearchPosition("1,2");
//        bo.setSortType(SortTypeEnum.getEnumValue(SortTypeEnum.TIME_DESC));
        JSONObject media = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.TYPE_CURVE);
        if (/*!flag && */(media.isEmpty() || null != media.get("code"))) {
            cache = false;
        }
        bo.setSortType(null);
        bo.setType(MediaTypeEnum.VIDEO.getValue() + "");
        JSONObject douyin = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.DOUYIN_CURVE);
        if (/*!flag && */(douyin.isEmpty() || null != douyin.get("code"))) {
            cache = false;
        }
        bo.setSortType(null);
        bo.setType(MediaTypeEnum.CLIENT.getValue() + "");
        bo.setHost("www.toutiao.com");
        JSONObject toutiao = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.DOUYIN_CURVE);
        bo.setHost(null);
        if ((toutiao.isEmpty() || null != toutiao.get("code"))) {
            cache = false;
        }
        bo.setSortType(null);
        //总的需要把  微博，网站微信  +  抖音哪些
        bo.setSortType(null);
        bo.setType(MediaTypeEnum.NEWS.getValue() + "," + MediaTypeEnum.WEIBO.getValue() + "," + MediaTypeEnum.WECHAT.getValue());
        JSONObject total = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.TOTAL_CURVE);
        if ((total.isEmpty() || null != total.get("code"))) {
            cache = false;
        }
        bo.setSortType(null);
        bo.setType(MediaTypeEnum.CLIENT.getValue() + "");
        bo.setHost("www.toutiao.com");
        JSONObject total2 = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.TOTAL_CURVE);
        if ((total2.isEmpty() || null != total2.get("code"))) {
            cache = false;
        }
        bo.setType(MediaTypeEnum.VIDEO.getValue() + "");
        bo.setHost("www.iesdouyin.com,www.kuaishou.com");//首页上抖音，快手只查短视频类型的，跟胡总确认
        JSONObject total3 = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.TOTAL_CURVE);
        if ((total3.isEmpty() || null != total3.get("code"))) {
            cache = false;
        }
        DateTime startTime = DateUtil.parseDateTime(homeVO.getStartTime());
        Date endTime = DateUtil.parseDateTime(homeVO.getEndTime());

        List<String> dateTimeList;
        boolean flag1 = getTimeType(homeVO.getStartTime(), homeVO.getEndTime());
        if (flag1) {
            List<TimeRoundFlowVO> timeRoundFlowHour = TimeUtil.timeRange(homeVO.getStartTime(), homeVO.getEndTime(), DateField.HOUR, 1, 0);
            dateTimeList = timeRoundFlowHour.stream().map(s -> DateUtil.format(DateUtil.parseDateTime(s.getStartTime()), "yyyy-MM-dd HH")).collect(Collectors.toList());
        } else {
            dateTimeList = generateDailyList(startTime.toJdkDate(), endTime);
        }
        resultMap.put("xxData", dateTimeList);
        Map<String, Long> totalMap = new HashMap<>();
        for (String string : dateTimeList) {
            long o = total.getLong(string, 0L);
            long aLong = total2.getLong(string, 0L);
            long aLong1 = total3.getLong(string, 0L);
            totalMap.put(string, o + aLong + aLong1);
//            System.out.println("=============="+string+"---微博-"+o+"----抖音-"+aLong);
        }
        Map<String, Map<String, Long>> itemsMap = new LinkedHashMap<>();
        //toutiao查6，抖音快手查11
        itemsMap.put("总", totalMap);
        itemsMap.put("网站", media.getJSONObject("1") == null ? new HashMap<>() : (Map) media.getJSONObject("1"));
        itemsMap.put("微信", media.getJSONObject("5") == null ? new HashMap<>() : (Map) media.getJSONObject("5"));
        itemsMap.put("微博", media.getJSONObject("3") == null ? new HashMap<>() : (Map) media.getJSONObject("3"));
        itemsMap.put("抖音", douyin.getJSONObject("www.iesdouyin.com") == null ? new HashMap<>() : (Map) douyin.getJSONObject("www.iesdouyin.com"));
        itemsMap.put("快手", douyin.getJSONObject("www.kuaishou.com") == null ? new HashMap<>() : (Map) douyin.getJSONObject("www.kuaishou.com"));
        itemsMap.put("今日头条", toutiao.getJSONObject("www.toutiao.com") == null ? new HashMap<>() : (Map) toutiao.getJSONObject("www.toutiao.com"));
        int i = 1;
        List<List<Long>> yyData = new ArrayList<>();
        Set<String> itemSet = itemsMap.keySet();
        for (String name : itemSet) {
            Map<String, Long> stringLongMap = itemsMap.get(name);
            LinkedHashMap<String, Object> e = new LinkedHashMap<>();
            e.put("name", name);
            e.put("id", i);
            aimData.add(e);
            ArrayList<Long> e1 = new ArrayList<>();
            for (String string : dateTimeList) {
                e1.add(stringLongMap.getOrDefault(string, 0L));
            }
            yyData.add(e1);
            i++;
        }
        //y轴对应数据值
        resultMap.put("yyData", yyData);
        //标签和id
        resultMap.put("aimData", aimData);
//        redisTemplate.setCacheObject(key1 + timeType, resultMap, homeKeyTimeoutDelay, TimeUnit.MINUTES);
//        redisTemplate.setCacheObject(key, times, homeKeyTimeout, TimeUnit.MINUTES);
//        if (null != homeVO.getTimeType() && !"4".equals(homeVO.getTimeType())) {
        if (cache) {
            redisTemplate.setCacheObject(key, resultMap);
        }
        return resultMap;
    }

    private static boolean getTimeType(String startTime, String endTime) throws Exception {
        boolean flag = false;
        if (StrUtil.isBlankIfStr(startTime) || StrUtil.isBlankIfStr(endTime)) {
            throw new Exception("时间不能为空");
        }
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        LocalDateTime startDateTime = LocalDateTime.parse(startTime, formatter);
        LocalDateTime endDateTime = LocalDateTime.parse(endTime, formatter);

        //long secondsBetween = ChronoUnit.SECONDS.between(startDateTime, endDateTime);

//        long minutesBetween = ChronoUnit.MINUTES.between(startDateTime, endDateTime);
//        if (minutesBetween <= 400) {
//            List<TimeRoundFlowVO> timeRoundFlowMinute = TimeUtil.timeRange(startTime, endTime, DateField.MINUTE, 1, 0);
////            Map.Entry<String, List<TimeRoundFlowVO>> entry = MapUtil.entry("yyyy-MM-dd HH:mm", timeRoundFlowMinute);
////            System.out.println(entry);
//        }
        long hoursBetween = ChronoUnit.HOURS.between(startDateTime, endDateTime);
        if (hoursBetween <= 24) {
            flag = true;
        }
        return flag;
    }

    @Override
    public List<Map> authorLineChat(HomeVO homeVO, boolean flag) {
        boolean cache = true;
//        String timeType = StrUtil.isEmpty(homeVO.getTimeType()) ? "1" : homeVO.getTimeType();
//        String times = homeVO.getStartTime() + "_" + homeVO.getEndTime();
//        String key1 = "HOME:AUTHOR_CURVE_";
//        String key = key1 + timeType + "_";
//        Object cacheObject1 = getCacheValue(homeVO, key, key1, timeType);
//        if (cacheObject1 != null) {
//            return (List<Map>) cacheObject1;
//        }
        String key = RedisConstant.authorLineChat + homeVO.getContentAreaCode() + ":" + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (List<Map>) cacheObject;
        }
        List<Map> datas = new ArrayList<>();
        EsSearchBO bo = JSONUtil.toBean(JSONUtil.toJsonStr(homeVO), EsSearchBO.class);
        bo.setSearchPosition("1,2");
        commonQueryFilter(bo);
        bo.setTimeType(0);//TODO 和ES build有冲突
        bo.setAggName("author");
        bo.setEmotionFlag("1");
//        bo.setExcludeTermField(excludeTermField);
        JSONObject postBodyJSONObject = EsSearchUtil.getPostBodyJSONObject(bo, EsSearchUtil.TYPE_TOTAL_COUNT);
        if (/*!flag &&*/ (postBodyJSONObject.isEmpty() || null != postBodyJSONObject.get("code"))) {
            cache = false;
        }

        for (Map.Entry<String, Object> stringObjectEntry : postBodyJSONObject) {
            HashMap<Object, Object> objectObjectHashMap = new HashMap<>();
            objectObjectHashMap.put("name", stringObjectEntry.getKey());
            objectObjectHashMap.put("value", stringObjectEntry.getValue());
            datas.add(objectObjectHashMap);
        }
//        redisTemplate.setCacheObject(key1 + timeType, datas, homeKeyTimeoutDelay, TimeUnit.MINUTES);
//        redisTemplate.setCacheObject(key, times, homeKeyTimeout, TimeUnit.MINUTES);
//        if (null != homeVO.getTimeType() && !"4".equals(homeVO.getTimeType())) {
        if (cache) {
            redisTemplate.setCacheObject(key, datas);
        }
//        }
        return datas;
    }

    @Override
    public List<AreaOverviewBO> areaOverview(HomeVO homeVO, boolean flag) {
        boolean cache = true;
//        String timeType = StrUtil.isEmpty(homeVO.getTimeType()) ? "1" : homeVO.getTimeType();
//        String times = homeVO.getStartTime() + "_" + homeVO.getEndTime();
//        String key1 = "HOME:AREA_OVERVIEW_";
//        String key = key1 + timeType + "_";
//        Object cacheObject1 = getCacheValue(homeVO, key, key1, timeType);
//        if (cacheObject1 != null) {
//            return (List<AreaOverviewBO>) cacheObject1;
//        }
        String key = RedisConstant.areaOverview + homeVO.getContentAreaCode() + ":" + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (List<AreaOverviewBO>) cacheObject;
        }
        //总数一起缓存
        String contentAreaCode = homeVO.getContentAreaCode();
        String timeType = homeVO.getTimeType();
        this.total(homeVO, false);
        String keyTotal = RedisConstant.total + contentAreaCode + ":" + timeType;
        Object cacheObjectTotal = redisTemplate.getCacheObject(keyTotal);
        Long homeTotal = null;
        if (ObjectUtil.isNotEmpty(cacheObjectTotal)) {
            homeTotal = (Long) cacheObjectTotal;
        }
        EsSearchBO bo = JSONUtil.toBean(JSONUtil.toJsonStr(homeVO), EsSearchBO.class);
        bo.setTimeType(0);//TODO 和ES build有冲突
//        bo.setSearchPosition("1,2");
        commonQueryFilter(bo);
        List<Area> areas = CommonDataTask.COURT_MAPS.get(Long.valueOf(homeVO.getContentAreaCode()));
        String collect = areas.stream().map(Area::getCode).collect(Collectors.joining(","));
        bo.setAggInclude(collect);//增加agg的includ过滤
        bo.setEmotionFlag("1");
        String total = EsSearchUtil.getPostBodyStr(bo, EsSearchUtil.ZHEJIANG_YUQING_AREA_OVERVIEW);
//        JSONObject jsonObject = JSONUtil.parseObj(total);
//        if (null != jsonObject.get("code")) {
//            log.info("--超时:{}", jsonObject.toString());
//            throw new RuntimeException("服务超时");
//        }
        List<AreaOverviewBO> list = JSONUtil.toList(total, AreaOverviewBO.class);
        for (AreaOverviewBO areaOverviewBO : list) {
            String areaName = areaOverviewBO.getAreaName();
            if (CharSequenceUtil.equals(areaName, contentAreaCode) && homeTotal != null) {
                areaOverviewBO.setTotal(homeTotal);
                break;
            }
        }
        list = areaNameProcess(list, homeVO.getContentAreaCode());
        if (/*!flag &&*/ CollUtil.isEmpty(list)) {
            cache = false;
        }
        for (AreaOverviewBO areaOverviewBO : list) {
            Long total1 = areaOverviewBO.getTotal();
            if (total1 == null) {
                cache = false;
                break;
            }
        }
//        redisTemplate.setCacheObject(key1 + timeType, list, homeKeyTimeoutDelay, TimeUnit.MINUTES);
//        redisTemplate.setCacheObject(key, times, homeKeyTimeout, TimeUnit.MINUTES);
//        if (null != homeVO.getTimeType() && !"4".equals(homeVO.getTimeType())) {
        if (cache) {
            redisTemplate.setCacheObject(key, list);
        }
//        }
        return list;
    }

    //处理地域数据的法院code和中文的映射
    private List<AreaOverviewBO> areaNameProcess(List<AreaOverviewBO> list, String areaCode) {
        List<AreaOverviewBO> listNew = new ArrayList<>();
        for (AreaOverviewBO areaOverviewBO : list) {
            String areaName = areaOverviewBO.getAreaName();
            String areaNames = null;
            List<Area> areas = CommonDataTask.COURT_MAPS.get(Long.valueOf(areaCode));
            if (null != areas) {
                for (Area area : areas) {
                    if (area.getCode().equals(areaName)) {
                        areaNames = area.getAreaName();
                        break;
                    }
                }
            }
//            String areaNames = getAreaNames(areaCode, Long.valueOf(areaName));
            if (null == areaNames) {
                continue;
            }
            areaOverviewBO.setAreaName(areaNames);
            listNew.add(areaOverviewBO);
        }
        return listNew;
    }

    @Override
    public List<SimpleVO> getMainPlan(HomeVO homeVO, boolean flag) {
        boolean cache = true;
        List<SimpleVO> rows = new ArrayList<>();
        PlanMainBO plan = new PlanMainBO();
        plan.setMainType("1");
        List<Plan> list = planService.selectByMainTypeList(plan);
        if (CollUtil.isEmpty(list)) {
            return rows;
        }

        String key = RedisConstant.mainplan + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (List<SimpleVO>) cacheObject;
        }
//        String timeType = StrUtil.isEmpty(homeVO.getTimeType()) ? "1" : homeVO.getTimeType();
//        String times = homeVO.getStartTime() + "_" + homeVO.getEndTime();
//        String key1 = "HOME:MAIN_PLAN_";
//        String key = key1 + timeType + "_";
//        Object cacheObject1 = getCacheValue(homeVO, key, key1, timeType);
//        if (cacheObject1 != null) {
//            rows = (List<SimpleVO>) cacheObject1;
//        } else {
        for (Plan planMain : list) {
            SearchVO searchVO = new SearchVO();
            searchVO.setPlanId(planMain.getPlanId());
            searchVO.setStartTime(homeVO.getStartTime());
            searchVO.setEndTime(homeVO.getEndTime());
            searchVO.setSearchPosition("1,2");
            SimpleVO simpleVO = new SimpleVO();
            simpleVO.setName(planMain.getPlanName());
            Integer count = searchService.getRealTimeInfoCount(searchVO);
//            if (!flag && 0 == count) {
//                cache = false;
//            }
            simpleVO.setCount(count);
            simpleVO.setPlantId(planMain.getPlanId() + "");
            simpleVO.setCreateTime(planMain.getCreateTime());
            simpleVO.setHistoryFlag(planMain.getHistoryFlag());
            simpleVO.setUserId(planMain.getUserId());
            rows.add(simpleVO);
        }
//            redisTemplate.setCacheObject(key1 + timeType, rows, homeKeyTimeoutDelay, TimeUnit.MINUTES);
//            redisTemplate.setCacheObject(key, times, homeKeyTimeout, TimeUnit.MINUTES);
//        }
        rows = rows.stream()
                .sorted((bean1, bean2) -> bean2.getCount().compareTo(bean1.getCount()))
                .collect(Collectors.toList());
//        if (null != homeVO.getTimeType() && !"4".equals(homeVO.getTimeType())) {
        if (cache) {
            redisTemplate.setCacheObject(key, rows);
        }
//        }
        return rows;
    }

    @Override
    public List<SimpleVO> getProvinceHotPlan(HomeVO homeVO, boolean flag) {
        boolean cache = true;
        List<SimpleVO> rows = new ArrayList<>();
        PlanMainBO plan = new PlanMainBO();
        plan.setMainType("2");
        List<Plan> list = planService.selectByMainTypeList(plan);
        if (CollUtil.isEmpty(list)) {
            return rows;
        }
        String key = RedisConstant.provinceplan + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (List<SimpleVO>) cacheObject;
        }
        for (Plan planMain : list) {
            SearchVO searchVO = new SearchVO();
            searchVO.setPlanId(planMain.getPlanId());
            searchVO.setStartTime(homeVO.getStartTime());
            searchVO.setEndTime(homeVO.getEndTime());
            searchVO.setSearchPosition("1,2");
            SimpleVO simpleVO = new SimpleVO();
            simpleVO.setName(planMain.getPlanName());
            Integer count = searchService.getRealTimeInfoCount(searchVO);
//            if (!flag && 0 == count) {
//                cache = false;
//            }
            simpleVO.setCount(count);
            simpleVO.setPlantId(planMain.getPlanId() + "");
            simpleVO.setCreateTime(planMain.getCreateTime());
            simpleVO.setHistoryFlag(planMain.getHistoryFlag());
            simpleVO.setUserId(planMain.getUserId());
            rows.add(simpleVO);
        }
        rows = rows.stream()
                .sorted((bean1, bean2) -> bean2.getCount().compareTo(bean1.getCount()))
                .collect(Collectors.toList());
//            redisTemplate.setCacheObject(key1 + timeType, rows, homeKeyTimeoutDelay, TimeUnit.MINUTES);
//            redisTemplate.setCacheObject(key, times, homeKeyTimeout, TimeUnit.MINUTES);
//        }
//        if (null != homeVO.getTimeType() && !"4".equals(homeVO.getTimeType())) {
        if (cache) {
            redisTemplate.setCacheObject(key, rows);
        }
//        }
        return rows;
    }

    @Override
    public List<SimpleVO> getCountryHotPlan(HomeVO homeVO, boolean flag) {
        boolean cache = true;
        List<SimpleVO> rows = new ArrayList<>();
        PlanMainBO plan = new PlanMainBO();
        plan.setMainType("3");
        List<Plan> list = planService.selectByMainTypeList(plan);
        if (CollUtil.isEmpty(list)) {
            return rows;
        }
        String key = RedisConstant.countryplan + homeVO.getTimeType();
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (flag && ObjectUtil.isNotEmpty(cacheObject)) {
            return (List<SimpleVO>) cacheObject;
        }
        for (Plan planMain : list) {
            SearchVO searchVO = new SearchVO();
            searchVO.setPlanId(planMain.getPlanId());
            searchVO.setStartTime(homeVO.getStartTime());
            searchVO.setEndTime(homeVO.getEndTime());
            searchVO.setSearchPosition("1,2");
            SimpleVO simpleVO = new SimpleVO();
            simpleVO.setName(planMain.getPlanName());
            Integer count = searchService.getRealTimeInfoCount(searchVO);
//            if (!flag && 0 == count) {
//                cache = false;
//            }
            simpleVO.setCount(count);
            simpleVO.setPlantId(planMain.getPlanId() + "");
            simpleVO.setCreateTime(planMain.getCreateTime());
            simpleVO.setHistoryFlag(planMain.getHistoryFlag());
            simpleVO.setUserId(planMain.getUserId());
            rows.add(simpleVO);
        }
        rows = rows.stream()
                .sorted((bean1, bean2) -> bean2.getCount().compareTo(bean1.getCount()))
                .collect(Collectors.toList());
//            redisTemplate.setCacheObject(key1 + timeType, rows, homeKeyTimeoutDelay, TimeUnit.MINUTES);
//            redisTemplate.setCacheObject(key, times, homeKeyTimeout, TimeUnit.MINUTES);
//        }
//        if (null != homeVO.getTimeType() && !"4".equals(homeVO.getTimeType())) {
        if (cache) {
            redisTemplate.setCacheObject(key, rows);
        }
//        }
        return rows;
    }

    public List<String> generateDailyList(Date createTime, Date endTime) {
        List<String> dailyMap = new ArrayList<>();
        Calendar calendar = Calendar.getInstance();
        calendar.setTime(createTime);
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        while (!calendar.getTime().after(endTime)) {
            String dateStr = sdf.format(calendar.getTime());
            dailyMap.add(dateStr);
            calendar.add(Calendar.DATE, 1);
        }
        return dailyMap;
    }

    public Object getCacheValue(HomeVO homeVO, String key, String key1, String timeType) {
        DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
        Collection<String> keys1 = redisTemplate.keys(key);
        if (CollUtil.isNotEmpty(keys1)) {
            for (String s : keys1) {
                String cacheObject = redisTemplate.getCacheObject(s);
                String[] s1 = cacheObject.split("_");
                String start1 = s1[0];
                String end1 = s1[1];
                LocalDateTime checkDateTime = LocalDateTime.parse(homeVO.getStartTime(), formatter);
                LocalDateTime startTime = LocalDateTime.parse(start1, formatter);
                LocalDateTime endTime = LocalDateTime.parse(end1, formatter);
                // 判断checkDateTime是否在startTime和endTime之间
                boolean isInRange = !checkDateTime.isBefore(startTime) && !checkDateTime.isAfter(endTime);
                if (isInRange) {
                    Object cacheObject1 = redisTemplate.getCacheObject(key1 + timeType);
                    if (null == cacheObject1) {
                        continue;
                    }
                    return cacheObject1;
                }
            }
        }
        return null;
    }

    @Override
    public List<SimpleVO> casePaln(HomeVO homeVO, boolean flag) {
        //boolean cache = true;
        //Object cacheObject = redisTemplate.getCacheObject(RedisConstant.plan_all + homeVO.getTimeType());
        //if (flag &&  ObjectUtil.isNotEmpty(cacheObject)) {
        //    return (List<SimpleVO>) cacheObject;
        //}
        PlanMainBO plan1 = new PlanMainBO();
        plan1.setMainType("1");
        List<Plan> main = planService.selectByMainTypeListV2(plan1);
        List<SimpleVO> plan = new ArrayList<>();
        SimpleVO e = new SimpleVO();
        e.setType(1);
        e.setName("重点关注案件");
        e.setCount(main.size());
        int historyFlag = 0;
        if (main.size() == 1 && main.get(0).getHistoryFlag() == 1) {
            //如果全部是当前案件就返回0 ，如果包含当前案件也返回0，如果只有一个历史案件就直接返回1
            historyFlag = 1;
        }
        e.setHistoryFlag(historyFlag); //0是往当前案件跳，1是往历史案件跳
        plan.add(e);

        PlanMainBO plan2 = new PlanMainBO();
        plan2.setMainType("2");
        int province = planService.selectByMainTypeListCount(plan2);
        SimpleVO e1 = new SimpleVO();
        e1.setType(2);
        e1.setName("全省热点案件");
        e1.setCount(province);
        plan.add(e1);

        PlanMainBO plan3 = new PlanMainBO();
        plan3.setMainType("3");
        int country = planService.selectByMainTypeListCount(plan3);
        SimpleVO e2 = new SimpleVO();
        e2.setType(3);
        e2.setName("全国热点案件");
        e2.setCount(country);
        plan.add(e2);

        //if (cache) {
        //    redisTemplate.setCacheObject(RedisConstant.plan_all + homeVO.getTimeType(), plan);
        //}
        return plan;
    }

    @Override
    public List<Plan> planTypeList(HomeVO homeVO) {
        PlanMainBO plan = new PlanMainBO();
        plan.setMainType(homeVO.getPlanType());
        List<Plan> list = planService.selectByMainTypeList(plan);
        if (CollUtil.isNotEmpty(list)) {
            return list;
        }
        return Collections.emptyList();
    }

    public String getAreaCode() {
        String areaCode = SecurityUtils.getLoginUser().getUser().getDept().getAreaCode();
        if (null == areaCode) {
//            log.error("当前登录人部门没有配置areaCode!");
            return String.valueOf(ZJGY_AREA_CODE);
        }
        return areaCode;
    }

    @Override
    public List<String> getAreaCodeList() {
        return lawyereMapper.getAreaCodeList();
    }

    @Override
    public List<Long> getAllDeptIdList() {
        return lawyereMapper.getAllDeptIdList();
    }

    @Override
    public List<Hot> hotZJMore(HotVO hotVO) {
        String timeType = hotVO.getTimeType();
        if (StrUtil.isBlankIfStr(timeType)) {
            timeType = "4";
        }
        String key = RedisConstant.hotZJ + timeType;
        Object cacheObject = redisTemplate.getCacheObject(key);
        if (ObjectUtil.isNotEmpty(cacheObject)) {
            return (List<Hot>) cacheObject;
        }
        Integer count = hotVO.getCount();
        DateTime endTimeDate = DateUtil.date();
        DateTime startTimeDate = DateUtil.offsetDay(endTimeDate, -15);
        String startTime = DateUtil.format(startTimeDate, "yyyy-MM-dd HH:mm:ss");
        String endTime = DateUtil.format(endTimeDate, "yyyy-MM-dd HH:mm:ss");
        return buildHotZJ(startTime, endTime, count);
    }

    @Override
    public List<Area> getAllArea() {
        return lawyereMapper.getAllArea();
    }
}



