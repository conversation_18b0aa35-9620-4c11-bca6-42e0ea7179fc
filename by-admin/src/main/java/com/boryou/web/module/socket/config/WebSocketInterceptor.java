package com.boryou.web.module.socket.config;

import com.boryou.common.constant.Constants;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.server.ServerHttpRequest;
import org.springframework.http.server.ServerHttpResponse;
import org.springframework.http.server.ServletServerHttpRequest;
import org.springframework.http.server.ServletServerHttpResponse;
import org.springframework.stereotype.Component;
import org.springframework.web.socket.WebSocketHandler;
import org.springframework.web.socket.server.HandshakeInterceptor;

import java.util.Map;

import static com.boryou.web.module.socket.cache.SocketManagerCache.LOGIN_ID;


@Component
@RequiredArgsConstructor
@Slf4j
public class WebSocketInterceptor implements HandshakeInterceptor {

    /**
     * 握手之前
     *
     * @param request    request
     * @param response   response
     * @param wsHandler  handler
     * @param attributes 属性
     * @return 是否握手成功：true-成功，false-失败
     */
    @Override
    public boolean beforeHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Map<String, Object> attributes) {
        ServletServerHttpRequest serverHttpRequest = (ServletServerHttpRequest) request;
        ServletServerHttpResponse serverHttpResponse = (ServletServerHttpResponse) response;
        String authorization = serverHttpRequest.getServletRequest().getHeader(Constants.WS_TOKEN);
        // 从redis 里获取用户，验证是否是有效用户,如果失败则中断连接
        try {
            SysUser user = SecurityUtils.getLoginUser().getUser();
            Long loginId = user.getUserId();
            // 自定义attribute
            attributes.put(LOGIN_ID, String.valueOf(loginId));
            // log.info("用户: [{}]建立连接", loginId);
            serverHttpResponse.getServletResponse().setHeader(Constants.WS_TOKEN, authorization);
            return true;
        } catch (Exception e) {
            log.error("用户建立连接失败: [{}]", e.getMessage(), e);
            return false;
        }
    }

    /**
     * 握手后
     *
     * @param request   request
     * @param response  response
     * @param wsHandler wsHandler
     * @param exception exception
     */
    @Override
    public void afterHandshake(ServerHttpRequest request, ServerHttpResponse response, WebSocketHandler wsHandler, Exception exception) {
        // 握手后 ...
    }
}
