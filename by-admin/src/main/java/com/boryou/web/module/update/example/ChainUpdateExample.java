package com.boryou.web.module.update.example;

import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import com.boryou.web.module.mark.domain.EsBeanMark;
import com.boryou.web.module.mark.domain.vo.EsBeanMarkVO;
import com.boryou.web.module.update.util.ChainUpdateUtil;
import lombok.extern.slf4j.Slf4j;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * 链式更新工具使用示例
 * 展示如何使用 ChainUpdateUtil 进行通用字段更新
 * 
 * <AUTHOR>
 */
@Slf4j
public class ChainUpdateExample {

    /**
     * 示例1：基础使用 - 需要手动配置字段更新器
     */
    public boolean updateExample1(EsBeanMarkVO sourceData, EsBeanMark targetData, 
                                LambdaUpdateChainWrapper<EsBeanMark> chainWrapper) {
        
        // 创建配置并添加字段更新器
        ChainUpdateUtil.ChainUpdateConfig<EsBeanMark> config = ChainUpdateUtil.createChainUpdate(chainWrapper)
                // 排除系统字段
                .excludeFields("esBeanMarkId", "createTime", "createBy", "updateTime", "delFlag")
                // 配置字段更新器（必须提供Lambda表达式）
                .addSetter("title", EsBeanMark::getTitle)
                .addSetter("content", EsBeanMark::getContent)
                .addSetter("tags", EsBeanMark::getTags)
                .addSetter("userId", EsBeanMark::getUserId)
                .addSetter("deptId", EsBeanMark::getDeptId);

        // 执行比较和更新
        boolean hasUpdate = ChainUpdateUtil.compareAndUpdate(sourceData, targetData, config);
        
        if (hasUpdate) {
            // 设置公共更新字段
            chainWrapper.set(EsBeanMark::getUTime, new Date())
                       .set(EsBeanMark::getUBy, "system");
            
            // 执行更新
            return config.update();
        }
        
        return false;
    }

    /**
     * 示例2：使用字段映射 - 源字段名与目标字段名不同
     */
    public boolean updateExample2(Object sourceData, EsBeanMark targetData, 
                                LambdaUpdateChainWrapper<EsBeanMark> chainWrapper) {
        
        // 创建字段更新器映射
        Map<String, com.baomidou.mybatisplus.core.toolkit.support.SFunction<EsBeanMark, ?>> setters = new HashMap<>();
        setters.put("title", EsBeanMark::getTitle);
        setters.put("content", EsBeanMark::getContent);
        setters.put("userIdSource", EsBeanMark::getUserId);  // 源字段名不同
        setters.put("deptIdSource", EsBeanMark::getDeptId);  // 源字段名不同
        
        ChainUpdateUtil.ChainUpdateConfig<EsBeanMark> config = ChainUpdateUtil.createChainUpdate(chainWrapper)
                .excludeFields("esBeanMarkId", "createTime", "createBy", "updateTime", "delFlag")
                // 添加字段映射
                .addMapping("userIdSource", "userId")
                .addMapping("deptIdSource", "deptId");
        
        // 添加字段更新器
        setters.forEach(config::addSetter);
        
        boolean hasUpdate = ChainUpdateUtil.compareAndUpdate(sourceData, targetData, config);
        
        if (hasUpdate) {
            chainWrapper.set(EsBeanMark::getUTime, new Date());
            return config.update();
        }
        
        return false;
    }

    /**
     * 示例3：完整的Service方法示例
     */
    public boolean updateEsBeanMark(EsBeanMarkVO esBeanMarkVO, EsBeanMark existEntity, 
                                  LambdaUpdateChainWrapper<EsBeanMark> chainWrapper,
                                  String userName, Date updateTime, Long userId) {
        
        // 设置查询条件
        chainWrapper.eq(EsBeanMark::getArticleId, esBeanMarkVO.getArticleId())
                   .eq(EsBeanMark::getUserId, userId);

        // 创建字段更新器映射
        Map<String, com.baomidou.mybatisplus.core.toolkit.support.SFunction<EsBeanMark, ?>> fieldSetters = new HashMap<>();
        fieldSetters.put("title", EsBeanMark::getTitle);
        fieldSetters.put("content", EsBeanMark::getContent);
        fieldSetters.put("tags", EsBeanMark::getTags);
        fieldSetters.put("userId", EsBeanMark::getUserId);
        fieldSetters.put("deptId", EsBeanMark::getDeptId);
        fieldSetters.put("status", EsBeanMark::getStatus);
        fieldSetters.put("remark", EsBeanMark::getRemark);

        // 使用便捷方法进行更新
        ChainUpdateUtil.ChainUpdateConfig<EsBeanMark> config = ChainUpdateUtil.compareAndUpdateWithSetters(
                esBeanMarkVO, 
                existEntity, 
                chainWrapper,
                fieldSetters,
                // 排除系统字段
                "esBeanMarkId", "createTime", "createBy", "delFlag"
        );

        // 如果有字段需要更新，设置公共字段并执行更新
        if (config.isHasUpdate()) {
            chainWrapper.set(EsBeanMark::getDelFlag, 0)
                       .set(EsBeanMark::getUTime, updateTime)
                       .set(EsBeanMark::getUBy, userName);
            
            return config.update();
        }

        return false;
    }

    /**
     * 示例4：自定义配置示例
     */
    public boolean updateWithCustomConfig(Object sourceData, Object targetData, 
                                        LambdaUpdateChainWrapper<EsBeanMark> chainWrapper) {
        
        ChainUpdateUtil.ChainUpdateConfig<EsBeanMark> config = ChainUpdateUtil.createChainUpdate(chainWrapper)
                // 排除字段
                .excludeFields("id", "createTime", "updateTime", "version")
                // 字段映射
                .addMapping("sourceTitle", "title")
                .addMapping("sourceContent", "content")
                // 字段更新器
                .addSetter("title", EsBeanMark::getTitle)
                .addSetter("content", EsBeanMark::getContent)
                // 自定义配置
                .ignoreNullValues(true)      // 忽略空值
                .ignoreEmptyStrings(true);   // 忽略空字符串

        boolean hasUpdate = ChainUpdateUtil.compareAndUpdate(sourceData, targetData, config);
        
        if (hasUpdate) {
            log.info("检测到字段变化，执行更新");
            return config.update();
        } else {
            log.info("没有字段变化，跳过更新");
            return false;
        }
    }

    /**
     * 示例5：批量字段更新器配置的工厂方法
     */
    public static Map<String, com.baomidou.mybatisplus.core.toolkit.support.SFunction<EsBeanMark, ?>> createEsBeanMarkSetters() {
        Map<String, com.baomidou.mybatisplus.core.toolkit.support.SFunction<EsBeanMark, ?>> setters = new HashMap<>();
        
        // 基础字段
        setters.put("title", EsBeanMark::getTitle);
        setters.put("content", EsBeanMark::getContent);
        setters.put("tags", EsBeanMark::getTags);
        setters.put("status", EsBeanMark::getStatus);
        setters.put("remark", EsBeanMark::getRemark);
        
        // 关联字段
        setters.put("userId", EsBeanMark::getUserId);
        setters.put("deptId", EsBeanMark::getDeptId);
        setters.put("articleId", EsBeanMark::getArticleId);
        
        // 业务字段
        setters.put("markType", EsBeanMark::getMarkType);
        setters.put("priority", EsBeanMark::getPriority);
        
        return setters;
    }

    /**
     * 示例6：使用工厂方法的简化更新
     */
    public boolean updateWithFactory(EsBeanMarkVO sourceData, EsBeanMark targetData, 
                                   LambdaUpdateChainWrapper<EsBeanMark> chainWrapper) {
        
        // 使用工厂方法获取字段更新器
        Map<String, com.baomidou.mybatisplus.core.toolkit.support.SFunction<EsBeanMark, ?>> setters = createEsBeanMarkSetters();
        
        ChainUpdateUtil.ChainUpdateConfig<EsBeanMark> config = ChainUpdateUtil.compareAndUpdateWithSetters(
                sourceData, 
                targetData, 
                chainWrapper,
                setters,
                "esBeanMarkId", "createTime", "createBy", "updateTime", "delFlag"
        );

        if (config.isHasUpdate()) {
            chainWrapper.set(EsBeanMark::getUTime, new Date())
                       .set(EsBeanMark::getUBy, "system");
            return config.update();
        }
        
        return false;
    }
}
