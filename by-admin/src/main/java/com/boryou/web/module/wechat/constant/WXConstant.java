package com.boryou.web.module.wechat.constant;

import com.boryou.web.constant.RedisConstant;
import com.boryou.web.module.wechat.domain.WarnCode;
import me.chanjar.weixin.common.bean.WxOAuth2UserInfo;

import java.util.concurrent.ConcurrentHashMap;

public class WXConstant {
    /**
     * 所有请求登录的code与channel关系
     * todo 有可能有人请求了二维码，就是不登录，留个坑，之后处理
     */
    public static final ConcurrentHashMap<Integer, WarnCode> WAIT_BIND_MAP = new ConcurrentHashMap<>();
    public static final ConcurrentHashMap<Integer, WxOAuth2UserInfo> WX_USER_MAP = new ConcurrentHashMap<>();
    /**
     * 用户的openId和前端登录场景code的映射关系
     */
    public static final ConcurrentHashMap<String, Integer> OPENID_EVENT_CODE_MAP = new ConcurrentHashMap<>();

    public static final String WAIT_BIND = RedisConstant.system_prefix + "wait_bind:";
    public static final String WX_USER = RedisConstant.system_prefix + "wx_user:";
    public static final String OPENID_EVENT_CODE = RedisConstant.system_prefix + "open_event_code:";

    public static final int EXPIRE_SECONDS = 60 * 60;

    public static final String URL = "https://open.weixin.qq.com/connect/oauth2/authorize?appid=%s&redirect_uri=%s&response_type=code&scope=snsapi_userinfo&state=STATE#wechat_redirect";
    //public static final String CALLBACK_URL = "https://devtest.boryou.com/wx/portal/";
    //public static final String CALLBACK_URL = "https://smarteye.boryou.com/prod-api/wx/portal/";
    //public static final String CALLBACK_URL = "https://devtest.boryou.com/wx/portal/";
    public static final String CALLBACK_URL = "https://justice.boryou.com/prod-api/wx/portal/";

}
