package com.boryou.web.module.drill.service;

import cn.hutool.core.convert.Convert;
import com.boryou.web.module.drill.domain.DrillComment;
import com.boryou.web.module.drill.domain.DrillCommentReply;
import com.boryou.web.module.drill.enums.CommentEnum;
import com.boryou.web.module.drill.mapper.DrillCommentMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.stereotype.Service;

import java.util.Objects;

/**
 * 评论异步服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
@EnableAsync
public class DrillCommentAsyncService {

    private final DrillCommentReplyService drillCommentReplyService;
    private final DrillCommentMapper drillCommentMapper;

    /**
     * 异步更新数据库中的点赞计数
     */
    @Async("asyncUpdateLikeCountInDatabaseExecutor")
    public void asyncUpdateLikeCountInDatabase(Long commentId, Long commentReplyId, Integer commentType, Integer likeCount) {
        try {
            String typeStr = Convert.toStr(commentType);
            if (Objects.equals(typeStr, CommentEnum.HOT_TOPIC.getType())) {
                // 使用 Mapper 直接更新
                DrillComment drillComment = new DrillComment();
                drillComment.setCommentId(commentId);
                drillComment.setLikeCount(likeCount);
                drillCommentMapper.updateById(drillComment);
            } else if (Objects.equals(typeStr, CommentEnum.COMMENT_REPLY.getType())) {
                drillCommentReplyService.lambdaUpdate()
                        .set(DrillCommentReply::getLikeCount, likeCount)
                        .eq(DrillCommentReply::getCommentReplyId, commentReplyId)
                        .update();
            }
        } catch (Exception e) {
            log.error("异步更新点赞计数失败: commentId={}, commentReplyId={}, commentType={}",
                    commentId, commentReplyId, commentType, e);
        }
    }

}
