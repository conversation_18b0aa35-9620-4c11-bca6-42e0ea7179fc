package com.boryou.web.module.drill.controller;

import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.drill.domain.vo.DrillTaskVO;
import com.boryou.web.module.drill.service.DrillService;
import com.boryou.web.module.drill.service.DrillTaskService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
public class DrillTaskController {

    private final DrillTaskService drillTaskService;
    private final DrillService drillService;

    @PostMapping("/drill/task/save")
    public AjaxResult drillTaskSave(@Valid @RequestBody DrillTaskVO drillTaskVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        drillTaskService.saveUpset(drillTaskVO, user);
        return AjaxResult.success();
    }

    @PostMapping("/drill/task/query")
    public AjaxResult drillTaskQuery(@RequestBody DrillTaskVO drillTaskVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Page<DrillTaskVO> drillTaskVOPage = drillTaskService.drillTaskQuery(drillTaskVO, user);
        List<DrillTaskVO> records = drillTaskVOPage.getRecords();
        drillService.buildAllScore(records);
        return AjaxResult.success(drillTaskVOPage);
    }

    @PostMapping("/drill/task/query/one")
    public AjaxResult drillTaskQueryOne(@RequestBody DrillTaskVO drillTaskVO) {
        Long drillTaskId = drillTaskVO.getDrillTaskId();
        SysUser user = SecurityUtils.getLoginUser().getUser();
        DrillTaskVO drillTaskQuery = drillTaskService.drillTaskQueryOne(drillTaskId, user);
        return AjaxResult.success(drillTaskQuery);
    }

    @PostMapping("/drill/task/user")
    public AjaxResult drillTaskUser(@RequestBody(required = false) DrillTaskVO drillTaskVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        return AjaxResult.success(drillTaskService.drillTaskUser(drillTaskVO, user));
    }

}
