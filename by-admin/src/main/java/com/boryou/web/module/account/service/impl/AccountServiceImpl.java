package com.boryou.web.module.account.service.impl;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.URLUtil;
import co.elastic.clients.elasticsearch.ElasticsearchClient;
import co.elastic.clients.elasticsearch._types.FieldValue;
import co.elastic.clients.elasticsearch._types.SortOptions;
import co.elastic.clients.elasticsearch._types.SortOptionsBuilders;
import co.elastic.clients.elasticsearch._types.SortOrder;
import co.elastic.clients.elasticsearch._types.query_dsl.BoolQuery;
import co.elastic.clients.elasticsearch._types.query_dsl.Query;
import co.elastic.clients.elasticsearch._types.query_dsl.QueryBuilders;
import co.elastic.clients.elasticsearch.core.SearchRequest;
import co.elastic.clients.elasticsearch.core.SearchResponse;
import co.elastic.clients.elasticsearch.core.search.Hit;
import com.boryou.common.core.domain.entity.SysDictData;
import com.boryou.common.exception.CustomException;
import com.boryou.system.service.ISysDictDataService;
import com.boryou.web.constant.AuthorConstant;
import com.boryou.web.constant.ElasticsearchIndex;
import com.boryou.web.controller.common.enums.MediaTypeEnum;
import com.boryou.web.domain.vo.AccountInfoVO;
import com.boryou.web.module.account.entity.vo.AccountInfo;
import com.boryou.web.module.account.entity.vo.JumpLinkVO;
import com.boryou.web.module.account.service.AccountService;
import com.boryou.web.module.home.entity.Lawyer;
import com.boryou.web.module.home.entity.bo.LawyerBO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.IOException;
import java.net.URI;
import java.net.URL;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2024-07-02 15:51
 */
@Slf4j
@Service
public class AccountServiceImpl implements AccountService {

    @Resource
    private ElasticsearchClient esClient;
    @Resource
    private ISysDictDataService sysDictDataService;

    @Override
    public AccountInfo getAccountInfo(AccountInfo accountInfo) {
        Integer type = accountInfo.getType();
        String domain = accountInfo.getDomain();
        BoolQuery.Builder bool1 = QueryBuilders.bool();
        Query tq = QueryBuilders.term(t -> t.field("type").value(type));
        Query tq1 = QueryBuilders.term(t -> t.field("domain").value(domain));
        Query sq = QueryBuilders.matchPhrase(t -> t.field("nickname").query("*" + accountInfo.getNickname() + "*"));
        bool1.must(Arrays.asList(tq, sq, tq1));
        AccountInfo accountInfo1 = new AccountInfo();
        try {
            SearchResponse<AccountInfo> searchResponse = esClient.search(
                    s -> s.index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO)
                            .query(bool1.build()._toQuery())
                            .size(1)
                    , AccountInfo.class);
            List<Hit<AccountInfo>> hits = searchResponse.hits().hits();
            for (Hit<AccountInfo> hit : hits) {
                accountInfo1 = hit.source();
                if (accountInfo1 != null) {
                    accountInfo1.setAvatar(accountInfo1.getAvatar() == null ? "" : accountInfo1.getAvatar());
                }
            }
        } catch (IOException e) {
            e.printStackTrace();
        }
        return accountInfo1;
    }

    @Override
    public String jumpLink(JumpLinkVO jumpLinkVO) {
        Integer type = jumpLinkVO.getType();
        if (type == null) {
            throw new CustomException("type不能为空");
        }
        //String authorId = jumpLinkVO.getAuthorId();
        //if (CharSequenceUtil.isNotBlank(authorId) && !CharSequenceUtil.equals(authorId, "0")) {
        //    return AuthorConstant.WEIBO + authorId;
        //}
        if (type == MediaTypeEnum.WECHAT.getValue()) {
            String bizId = jumpLinkVO.getBizId();
            return AuthorConstant.WECHAT + bizId;
        }

        String urlStr = jumpLinkVO.getUrl();
        if (CharSequenceUtil.isBlank(urlStr)) {
            throw new CustomException("url不能为空");
        }
        URL url = URLUtil.url(urlStr);
        String host = url.getHost();
        if (CharSequenceUtil.isBlank(host)) {
            throw new CustomException("host不能为空");
        }
        Map<String, String> map = getHostMap();
        if (!map.containsKey(host)) {
            return AuthorConstant.HTTPS + host;
        }
        String author = jumpLinkVO.getAuthor();
        String accountId = getAccountId(type, author, host);
        if (CharSequenceUtil.isBlank(accountId)) {
            return AuthorConstant.HTTPS + host;
        }
        return map.get(host) + accountId;
    }

    @Override
    public String getAvatar(Lawyer lawyer) {
        log.warn("AccountService.getAvatar 开始,查询参数为: {}", lawyer);
        String mediaType = lawyer.getSource();
        String mediaDict = lawyer.getMediaDict();
        String nickName = lawyer.getName();
        if (CharSequenceUtil.isAllBlank(mediaType, mediaDict)) {
            return null;
        }
        BoolQuery.Builder bool2 = new BoolQuery.Builder();
        if (CharSequenceUtil.isNotBlank(nickName)) {
            termQuery(nickName, "nickname.keyword", bool2);
        }
        if (CharSequenceUtil.isNotBlank(mediaType)) {
            termQuery(mediaType, "type", bool2);
        }
        if (CharSequenceUtil.isNotBlank(mediaDict)) {
            termQuery(mediaDict, "sector", bool2);
        }
        bool2.mustNot(Query.of(q -> q.term(t -> t.field("avatar").value(FieldValue.of("")))));
        Query query = bool2.build()._toQuery();
        try {
            SortOptions sortOptions = SortOptionsBuilders.field(f -> f.field("updateTime").order(SortOrder.Desc));
            SearchRequest searchRequest = SearchRequest.of(s -> s.index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO)
                    .query(query)
                    .size(1)
                    .sort(sortOptions));
            log.warn("AccountService.getAvatar ES查询参数为: {}", searchRequest);
            SearchResponse<AccountInfo> searchResponse = esClient.search(searchRequest, AccountInfo.class);
            List<Hit<AccountInfo>> hits = searchResponse.hits().hits();
            for (Hit<AccountInfo> hit : hits) {
                AccountInfo source = hit.source();
                if (source != null) {
                    return source.getAvatar();
                }
            }
        } catch (IOException e) {
            log.warn("AccountService.getAvatar ES查询失败: {}", e.getMessage());
            e.printStackTrace();
        }
        return null;
    }

    private Map<String, String> getHostMap() {
        Map<String, String> map = new HashMap<>();
        SysDictData sysDictData = new SysDictData();
        sysDictData.setDictType("sys_search_short_video");
        List<SysDictData> videoDictList = sysDictDataService.selectDictDataList(sysDictData);
        for (SysDictData dictData : videoDictList) {
            String dictLabel = dictData.getDictLabel();
            String dictValue = dictData.getDictValue();
            switch (dictLabel) {
                case "微博":
                    buildHostMap(dictValue, AuthorConstant.WEIBO, map);
                    break;
                case "抖音":
                    buildHostMap(dictValue, AuthorConstant.DOUYIN, map);
                    break;
                case "快手":
                    buildHostMap(dictValue, AuthorConstant.KUAISHOU, map);
                    break;
                case "头条":
                    buildHostMap(dictValue, AuthorConstant.TOUTIAO, map);
                    break;
                default:
                    break;
            }
        }
        return map;
    }

    private static void buildHostMap(String dictValue, String preAuthor, Map<String, String> map) {
        List<String> splitTrim = CharSequenceUtil.splitTrim(dictValue, ",");
        for (String s : splitTrim) {
            map.put(s, preAuthor);
        }
    }

    private String getAccountId(Integer type, String author, String host) {
        try {
            BoolQuery.Builder bool = QueryBuilders.bool();
            Query typeQuery = QueryBuilders.term(t -> t.field("type").value(type));
            Query authorQuery = QueryBuilders.term(t -> t.field("nickname.keyword").value(author));
            termQuery(host, bool, "domain");
            bool.filter(typeQuery, authorQuery);
            SortOptions sortOptions = SortOptionsBuilders.field(f -> f.field("updateTime").order(SortOrder.Desc));
            SearchRequest searchRequest = SearchRequest.of(s -> s.index(ElasticsearchIndex.NETXMAN_ACCOUNT_INFO)
                    .query(bool.build()._toQuery())
                    .sort(sortOptions)
                    .size(1));
            log.warn("jumpLink 查询参数: {}", searchRequest);

            SearchResponse<AccountInfoVO> searchResponse = esClient.search(searchRequest, AccountInfoVO.class);
            List<Hit<AccountInfoVO>> hits = searchResponse.hits().hits();
            for (Hit<AccountInfoVO> hit : hits) {
                AccountInfoVO accountInfo = hit.source();
                if (accountInfo == null) {
                    continue;
                }
                return accountInfo.getAccountId();
            }
            return "";
        } catch (Exception e) {
            log.error("jumpLink 查询失败: {}", e.getMessage());
            return "";
        }
    }

    private void termQuery(String value, BoolQuery.Builder bool, String filed) {
        if (CharSequenceUtil.isBlank(value)) {
            return;
        }
        List<FieldValue> fieldValues = CollStreamUtil.toList(CharSequenceUtil.splitTrim(value, ','), FieldValue::of);
        fieldValues.removeIf(fieldValue -> "".equals(fieldValue.stringValue()));
        if (!fieldValues.isEmpty()) {
            bool.filter(QueryBuilders.terms(t -> t.field(filed).terms(q -> q.value(fieldValues))));
        }

    }

    private void termQuery(String value, String filed, BoolQuery.Builder bool) {
        if (CharSequenceUtil.isBlank(value)) {
            return;
        }

        List<FieldValue> fieldValues = CollStreamUtil.toList(CharSequenceUtil.splitTrim(value, ','), FieldValue::of);
        fieldValues.removeIf(fieldValue -> "".equals(fieldValue.stringValue()));
        if (!fieldValues.isEmpty()) {
            bool.filter(QueryBuilders.terms(t -> t.field(filed).terms(q -> q.value(fieldValues))));
        }

    }
}

