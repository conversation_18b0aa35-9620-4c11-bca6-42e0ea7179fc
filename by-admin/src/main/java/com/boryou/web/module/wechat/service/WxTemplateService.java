package com.boryou.web.module.wechat.service;

import lombok.extern.slf4j.Slf4j;
import me.chanjar.weixin.common.error.WxErrorException;
import me.chanjar.weixin.mp.api.WxMpService;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateData;
import me.chanjar.weixin.mp.bean.template.WxMpTemplateMessage;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.Date;

@Service
@Slf4j
public class WxTemplateService {
    @Resource
    private WxMpService wxMpService;

    public void sendTemplateMessage(String openId, String templateId, String first, String keyword1, String url) {
        try {
            WxMpTemplateMessage templateMessage = WxMpTemplateMessage.builder()
                    .toUser(openId)
                    .templateId(templateId)
                    .url(url)
                    .build();

            //String first = "从" + startTime + "到" + endtime + "，监测方案【" + rule + "】新增" + totalNum + "条信息，其中敏感信息为" + sensetiveNum + "条。";
            //WxMpTemplateData firstData = new WxMpTemplateData("first", first, "#090909");

            WxMpTemplateData keyword1Data = new WxMpTemplateData("keyword1", first, "#090909");

            String time = new SimpleDateFormat("yyyy年MM月dd日 HH时mm分ss秒").format(new Date());
            WxMpTemplateData keyword2Data = new WxMpTemplateData("keyword2", time, "#090909");

            WxMpTemplateData remarkData = new WxMpTemplateData("remark", "点击查看详情！", "#DD5246");

            templateMessage
                    //.addData(firstData)
                    .addData(keyword1Data)
                    .addData(keyword2Data)
                    .addData(remarkData);

            String msgId = this.wxMpService.getTemplateMsgService().sendTemplateMsg(templateMessage);
            if (StringUtils.isNotBlank(msgId)) {
                log.warn("sendTemplateMessage 成功, msgId: {}", msgId);
            }
        } catch (Exception e) {
            log.error("sendTemplateMessage 失败: {}", e.getMessage());
        }

    }
}
