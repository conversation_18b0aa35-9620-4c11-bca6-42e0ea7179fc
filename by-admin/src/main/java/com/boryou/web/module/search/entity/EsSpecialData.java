package com.boryou.web.module.search.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
@TableName("by_special_data")
public class EsSpecialData {

    /**
     * 主键id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.NONE)
    private Long id;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long indexId;

    private String md5;

    private String createBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 修改后的情感
     */
    private Integer emotionFlag;

    /**
     * 收藏 0 未收藏 1 已收藏
     */
    private Integer userLike;

    /**
     * 重复 0 无 1 不重复 2 重复
     */
    private Integer original;

    /**
     * 噪音 0 无 1 正常 2 噪音
     */
    private Integer trash;

    /**
     * 处置 0 无 1 已处置
     */
    private Integer deal;

    /**
     * 重点关注 0 无 1 已关注
     */
    private Integer follow;

    /**
     * 报送 0 未报送 1 已报送未处置 2 已报送已处置  3 已报送已过期
     */
    private Integer warned;
    /**
     * 是否推送过短信，邮箱 0 无 1 有
     */
    private Integer submit;
}
