package com.boryou.web.module.drill.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.web.module.drill.domain.DrillSign;
import com.boryou.web.module.drill.domain.vo.DrillSignVO;
import com.boryou.web.module.drill.mapper.DrillSignMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrillSignService extends ServiceImpl<DrillSignMapper, DrillSign> {

    public void saveUpset(DrillSignVO drillSignVO) {
        Long drillTaskId = drillSignVO.getDrillTaskId();
        String userName = drillSignVO.getUserName();
        // Integer count = this.lambdaQuery()
        //         .eq(DrillSign::getDrillTaskId, drillTaskId)
        //         .eq(DrillSign::getUserName, userName)
        //         .count();
        // if (count > 0) {
        //     throw new CustomException("您已签到");
        // }
        DateTime date = DateUtil.date();
        DrillSign drillSign = BeanUtil.copyProperties(drillSignVO, DrillSign.class);
        long snowflakeNextId = IdUtil.getSnowflakeNextId();
        drillSign.setDrillSignId(snowflakeNextId);
        drillSign.setDrillTaskId(drillTaskId);
        drillSign.setCreateTime(date);
        this.save(drillSign);
    }


    public Page<DrillSign> drillSignQuery(DrillSignVO drillSignVO, SysUser user) {
        Long drillTaskId = drillSignVO.getDrillTaskId();
        if (drillTaskId == null) {
            throw new CustomException("任务id不能为空");
        }

        Integer pageNum = drillSignVO.getPageNum();
        Integer pageSize = drillSignVO.getPageSize();
        String userName = drillSignVO.getUserName();
        String phone = drillSignVO.getPhone();

        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 10;
        }
        Page<DrillSign> page = new Page<>(pageNum, pageSize);

        return this.lambdaQuery().eq(DrillSign::getDrillTaskId, drillTaskId)
                .like(CharSequenceUtil.isNotBlank(userName), DrillSign::getUserName, userName)
                .like(CharSequenceUtil.isNotBlank(phone), DrillSign::getPhone, phone)
                .page(page);
    }

}
