package com.boryou.web.module.drill.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Getter
@RequiredArgsConstructor
public enum TimerTypeEnum {

    SAME_TIME("同时计时", 1),
    SEPARATELY_TIME("分开计时", 2);

    private static final Map<String, TimerTypeEnum> map = new HashMap<>();

    static {
        TimerTypeEnum[] ens = TimerTypeEnum.values();
        for (TimerTypeEnum en : ens) {
            map.put(en.name, en);
        }
    }

    private final String name;
    private final Integer code;

    public static TimerTypeEnum getEnumByLeft(String left) {
        return map.get(left);
    }

}
