package com.boryou.web.module.collection.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/7/2 15:52
 */
@Data
public class CollectionQuery {

    private Integer pageNum = 1;
    private Integer pageSize = 10;

    /**
     * 开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 媒体类型
     */
    private String type;

    /**
     * 搜索内容
     */
    private String search;

    /**
     * 0-全文搜索 1-标题搜索 2-正文搜索
     */
    private String searchType;

    /**
     * 收藏夹id
     */
    private Long folderId;

}
