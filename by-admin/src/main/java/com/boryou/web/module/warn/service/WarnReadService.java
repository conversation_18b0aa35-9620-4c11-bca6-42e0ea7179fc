package com.boryou.web.module.warn.service;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.warn.domain.WarnRead;
import com.boryou.web.module.warn.domain.vo.WarnReadVO;
import com.boryou.web.module.warn.mapper.WarnReadMapper;
import org.springframework.stereotype.Service;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class WarnReadService extends ServiceImpl<WarnReadMapper, WarnRead> {
    public boolean addWarnRead(WarnReadVO warnReadVO) {
        Long warnId = warnReadVO.getId();
        if (warnId == null) {
            throw new CustomException("id不能为空");
        }
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        Long userId = sysUser.getUserId();
        String userName = sysUser.getUserName();
        Long deptId = sysUser.getDeptId();

        WarnRead warnRead = new WarnRead();
        warnRead.setWarnId(warnId);
        warnRead.setCreateTime(DateUtil.date());
        warnRead.setCreateBy(userName);
        warnRead.setUserId(userId);
        warnRead.setDeptId(deptId);
        return this.save(warnRead);
    }

    public List<String> getWarnRead() {
        SysUser sysUser = SecurityUtils.getLoginUser().getUser();
        Long userId = sysUser.getUserId();
        List<WarnRead> warnReadList = this.list(new LambdaQueryWrapper<WarnRead>().eq(WarnRead::getUserId, userId));
        if (CollUtil.isEmpty(warnReadList)) {
            return Collections.emptyList();
        }
        return warnReadList.stream().map(item -> String.valueOf(item.getWarnId())).collect(Collectors.toList());
    }
}
