package com.boryou.web.module.webservice.util;

import cn.hutool.core.util.StrUtil;
import com.boryou.common.utils.StringUtils;
import org.w3c.dom.Document;
import org.w3c.dom.Element;
import org.w3c.dom.Node;
import org.w3c.dom.NodeList;

import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * <AUTHOR>
 * @date 2024/12/5 下午7:55
 */
public class XmlUtil {

    /**
     * 遍历节点，生成 map
     *
     * @param node
     * @param map
     */
    public static void traverseNodes(Node node, Map<String, Object> map) {
        if (node.getNodeType() == Node.ELEMENT_NODE) {
            // 处理元素节点
            Element element = (Element) node;
            // 递归处理子节点
            NodeList childNodes = element.getChildNodes();
            if (childNodes.getLength() == 1) {
                map.put(StrUtil.toCamelCase(StringUtils.lowerCase(element.getTagName())), element.getFirstChild().getTextContent());
            }
            for (int i = 0; i < childNodes.getLength(); i++) {
                Node childNode = childNodes.item(i);
                traverseNodes(childNode, map);
            }
        }
    }

    public static Element strToElement(String text) {
        Document document = cn.hutool.core.util.XmlUtil.readXML(text);
        return document.getDocumentElement();
    }

    /**
     * 返回 xml 字符串
     * <RESPONSE>
     * <RESULT_CODE>true/false</RESULT_CODE>
     * <RESULT_CONTENT>成功/失败提示</RESULT_CONTENT>
     * </RESPONSE>
     *
     * @param flag
     * @param text
     * @return
     */
    public static String returnXMLStr(boolean flag, String text) {
        if (text == null) {
            text = flag ? "成功" : "失败";
        }
        if (flag) {
            return "<RESPONSE><RESULT_CODE>true</RESULT_CODE><RESULT_CONTENT>" + text + "</RESULT_CONTENT></RESPONSE>";
        } else {
            return "<RESPONSE><RESULT_CODE>false</RESULT_CODE><RESULT_CONTENT>" + text + "</RESULT_CONTENT></RESPONSE>";
        }
    }

    /**
     * 返回 xml 字符串
     * <RESPONSE>
     * <RESULT_CODE>true/false</RESULT_CODE>
     * <RESULT_CONTENT>成功/失败提示</RESULT_CONTENT>
     * </RESPONSE>
     *
     * @param flag
     * @return
     */
    public static String returnXMLStr(boolean flag) {
        String text = flag ? "成功" : "失败";
        if (flag) {
            return "<RESPONSE><RESULT_CODE>true</RESULT_CODE><RESULT_CONTENT>" + text + "</RESULT_CONTENT></RESPONSE>";
        } else {
            return "<RESPONSE><RESULT_CODE>false</RESULT_CODE><RESULT_CONTENT>" + text + "</RESULT_CONTENT></RESPONSE>";
        }
    }

    public static String removeOuterSpaces(String xmlString) {
        // 匹配标签外部的空格（包含换行、制表符等空白字符）
        String pattern = "(?<=</?[a-zA-Z_]+>)\\s+(?=<)";
        Pattern r = Pattern.compile(pattern);
        Matcher m = r.matcher(xmlString);
        return m.replaceAll("");
    }

    public static void main(String[] args) {
        String xml = "<CREATE_TIME>2024-01-01 00:00:00</CREATE_TIME>    ";
        System.out.println(removeOuterSpaces(xml));
    }
}
