package com.boryou.web.module.warn.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.http.HttpResponse;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

@Slf4j
@Service
public class ShortUrlApiService {
    private static final String BASE_URL = "https://t.boryou.com/su/";
    public static final String SHORT_URL = "https://t.boryou.com/su/gen";

    public String getShortUrl(String url) {
        if (StrUtil.isBlankIfStr(url)) {
            return null;
        }
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("lu", url);
        try (HttpResponse response = HttpUtil.createPost(SHORT_URL)
                .body(jsonObject.toString())
                .timeout(3000000).execute()) {
            if (!response.isOk()) {
                return null;
            }
            String body = response.body();
            if (StrUtil.isBlankIfStr(body)) {
                return null;
            }
            JSONObject entries = JSONUtil.parseObj(body);
            Object data = entries.get("data");
            return BASE_URL + data;
        } catch (Exception e) {
            log.error("ShortUrlApiService.getShortUrl报错: {}", e.getMessage());
            return null;
        }
    }

}
