package com.boryou.web.module.socket.util;

import com.boryou.web.module.socket.domain.SocketMessage;
import com.boryou.web.util.JacksonUtils;

/**
 * <AUTHOR>
 * @since 2023/9/6 17:27
 */
public class SocketUtil {

    private SocketUtil() {
        throw new IllegalStateException("Utility class");
    }

    public static SocketMessage formatSocketMessage(String message) {
        return JacksonUtils.toObj(message, SocketMessage.class);
    }

    public static String transferMessage(SocketMessage bean) {
        if (bean == null) {
            return "";
        }
        return JacksonUtils.toJson(bean);
    }


}
