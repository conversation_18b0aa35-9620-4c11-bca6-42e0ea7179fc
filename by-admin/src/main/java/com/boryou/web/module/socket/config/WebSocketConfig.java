package com.boryou.web.module.socket.config;

import com.boryou.common.constant.Constants;
import com.boryou.web.module.socket.handle.WebSocketHandle;
import lombok.RequiredArgsConstructor;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.socket.config.annotation.EnableWebSocket;
import org.springframework.web.socket.config.annotation.WebSocketConfigurer;
import org.springframework.web.socket.config.annotation.WebSocketHandlerRegistry;

@Configuration
@EnableWebSocket
@RequiredArgsConstructor
public class WebSocketConfig implements WebSocketConfigurer {

    private final WebSocketHandle webSocketHandle;
    private final WebSocketInterceptor webSocketInterceptor;

    @Override
    public void registerWebSocketHandlers(WebSocketHandlerRegistry webSocketHandlerRegistry) {
        webSocketHandlerRegistry
                // 添加myHandler消息处理对象，和websocket访问地址
                .addHandler(webSocketHandle, Constants.WS_URL)
                // 设置允许跨域访问
                .setAllowedOrigins("*") // ！！！【建议指定域名，不要全部开放。】
                // 添加拦截器可实现用户链接前进行权限校验等操作
                .addInterceptors(webSocketInterceptor);
    }

    // /**
    //  * 关键配置：启用协议级 Ping/Pong
    //  * 这个配置定义了WebSocket连接的各种参数，包括心跳超时和缓冲区大小
    //  */
    // @Bean
    // public ServletServerContainerFactoryBean createWebSocketContainer() {
    //     ServletServerContainerFactoryBean container = new ServletServerContainerFactoryBean();
    //     // 设置最大会话空闲超时时间，超过这个时间没有收到消息就会断开连接
    //     container.setMaxSessionIdleTimeout(60000L); // 60秒无活动则断开
    //     // 设置异步发送超时时间
    //     container.setAsyncSendTimeout(10000L);      // 异步发送超时时间
    //     // 设置消息缓冲区大小 - 支持发送大量文本
    //     container.setMaxTextMessageBufferSize(1024 * 1024); // 文本消息缓冲区大小设置为1MB，可容纳50万字以上
    //     container.setMaxBinaryMessageBufferSize(1024 * 1024); // 二进制消息缓冲区大小也设置为1MB
    //     return container;
    // }

}
