package com.boryou.web.module.home.mapper;

import com.boryou.web.module.area.entity.Area;
import com.boryou.web.module.home.entity.Lawyer;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-23 10:13
 */
@Mapper
public interface LawyereMapper {
    /**
     * 获取律师列表
     *
     * @param lawyer
     * @return
     */
    List<Lawyer> selectLawyer<PERSON>ist(Lawyer lawyer);

    List<String> getAreaCodeList();

    List<Area> getAllArea();

    List<Long> getAllDeptIdList();
}

