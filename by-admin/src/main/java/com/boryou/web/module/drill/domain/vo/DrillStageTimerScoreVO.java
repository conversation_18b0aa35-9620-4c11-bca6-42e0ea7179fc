package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 倒计时记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrillStageTimerScoreVO {

    /**
     * 演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    /**
     * 阶段ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processStageId;

    /**
     * 阶段得分 1:不显示得分 2:显示得分
     */
    private Integer scoreType;

    /**
     * 红队阶段得分 例如红队发表40个评论蓝队发表60个评论
     * 分数计算公式：红队（40/100=40%）蓝队（60/100=60%）
     */
    private String redStageScore;

    /**
     * 蓝队阶段得分 例如红队发表40个评论蓝队发表60个评论
     * 分数计算公式：红队（40/100=40%）蓝队（60/100=60%）
     */
    private String blueStageScore;

}
