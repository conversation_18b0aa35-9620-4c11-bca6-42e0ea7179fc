package com.boryou.web.module.collection.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.module.collection.entity.CollectionFolder;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:48
 */
public interface CollectionFolderService extends IService<CollectionFolder> {

    /**
     * 保存素材文件夹
     *
     * @param folder
     * @return
     */
    boolean folderSave(CollectionFolder folder);

    /**
     * 查询素材文件夹列表
     *
     * @param name
     * @return
     */
    List<CollectionFolder> folderList(String name);

    /**
     * 删除素材库
     *
     * @param id
     * @return
     */
    boolean folderDelete(Long id);
}
