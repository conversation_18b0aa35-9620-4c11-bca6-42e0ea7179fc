package com.boryou.web.module.drill.domain.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.boryou.web.handle.ListStringTypeHandler;
import com.boryou.web.module.drill.enums.CommentEnum;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

/**
 * 评论回复表实体
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DrillCommentReplyVO {
    /**
     * 评论ID
     */

    @JsonSerialize(using = ToStringSerializer.class)
    private Long commentReplyId;

    /**
     * 所属微博ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "commentId 不能为空")
    private Long commentId;

    /**
     * 阶段ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "processStageId 不能为空")
    private Long processStageId;

    /**
     * 关联演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "drillTaskId 不能为空")
    private Long drillTaskId;

    /**
     * 评论者ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 评论内容
     */
    @NotBlank(message = "评论内容不能为空")
    private String content;

    /**
     * 父评论ID（0=顶级评论）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "parentId 不能为空")
    private Long parentId;

    /**
     * 根评论ID（树形结构快速查询）
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long rootId;

    /**
     * 评论时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createdTime;

    /**
     * 点赞数
     */
    private Integer likeCount = 0;

    /**
     * 状态（0=正常，1=删除，2=审核中）
     */
    private Integer status;

    private Integer teamType;

    private String roleInfo;

    @TableField(typeHandler = ListStringTypeHandler.class)
    private List<String> file;

    /**
     * 回复评论默认是7
     */
    private String commentType = CommentEnum.COMMENT_REPLY.getType();
}
