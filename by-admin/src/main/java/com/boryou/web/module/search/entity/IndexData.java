package com.boryou.web.module.search.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class IndexData {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long indexId;

    private Integer type;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    private String title;

    private String text;

    private String url;

    private String host;

    private String author;

    private String authorId;

    private Integer accountLevel;

    private String contentAreaCode;

    private Integer emotionFlag;

    private Boolean isOriginal;

    private String md5;

}
