package com.boryou.web.module.mark.controller;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.page.TableDataInfo;
import com.boryou.web.controller.common.entity.EsBeanMark;
import com.boryou.web.module.mark.domain.vo.MarkVO;
import com.boryou.web.module.mark.service.MarkService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.List;

@Slf4j
@RestController
@RequestMapping("/mark")
public class MarkController extends BaseController {

    @Resource
    private MarkService markService;

    /**
     * 根据文章ID获取标记信息
     *
     * @param markVO 查询参数
     * @return 标记信息
     */
    @PostMapping("/get")
    public AjaxResult getMark(@RequestBody MarkVO markVO) {
        MarkVO mark = markService.getMarkByArticleId(markVO);
        return AjaxResult.success(mark);
    }

    /**
     * 分页查询标记信息
     *
     * @param markVO 查询参数
     * @return 分页结果
     */
    @PostMapping("/list")
    public TableDataInfo getMarkList(@RequestBody MarkVO markVO) {
        startPage();
        IPage<MarkVO> page = markService.getMarkPage(markVO);
        return getDataTable(page.getRecords(), page.getTotal());
    }

    /**
     * 添加或更新标记信息
     *
     * @param markVO 标记信息
     * @return 操作结果
     */
    @PostMapping("/save")
    public AjaxResult saveMark(@RequestBody @Validated MarkVO markVO) {
        boolean result = markService.saveOrUpdateMark(markVO);
        return toAjax(result);
    }

    /**
     * 根据EsBeanMark保存标记信息
     *
     * @param esBeanMark ES数据
     * @return 操作结果
     */
    @PostMapping("/saveFromEs")
    public AjaxResult saveFromEsBeanMark(@RequestBody EsBeanMark esBeanMark) {
        boolean result = markService.saveFromEsBeanMark(esBeanMark);
        return toAjax(result);
    }

    /**
     * 批量保存标记信息
     *
     * @param esBeanMarkList ES数据列表
     * @return 操作结果
     */
    @PostMapping("/saveBatch")
    public AjaxResult saveBatchFromEsBeanMark(@RequestBody List<EsBeanMark> esBeanMarkList) {
        boolean result = markService.saveBatchFromEsBeanMark(esBeanMarkList);
        return toAjax(result);
    }

    /**
     * 删除标记信息（逻辑删除）
     *
     * @param markVO 删除参数
     * @return 操作结果
     */
    @PostMapping("/delete")
    public AjaxResult deleteMark(@RequestBody MarkVO markVO) {
        boolean result = markService.deleteMark(markVO);
        return toAjax(result);
    }

    /**
     * 更新处置状态
     *
     * @param markVO 更新参数
     * @return 操作结果
     */
    @PostMapping("/updateDeal")
    public AjaxResult updateDeal(@RequestBody MarkVO markVO) {
        MarkVO updateMark = new MarkVO();
        updateMark.setArticleId(markVO.getArticleId());
        updateMark.setDeal(markVO.getDeal());
        boolean result = markService.saveOrUpdateMark(updateMark);
        return toAjax(result);
    }

    /**
     * 更新关注状态
     *
     * @param markVO 更新参数
     * @return 操作结果
     */
    @PostMapping("/updateFollow")
    public AjaxResult updateFollow(@RequestBody MarkVO markVO) {
        MarkVO updateMark = new MarkVO();
        updateMark.setArticleId(markVO.getArticleId());
        updateMark.setFollow(markVO.getFollow());
        boolean result = markService.saveOrUpdateMark(updateMark);
        return toAjax(result);
    }

    /**
     * 更新预警状态
     *
     * @param markVO 更新参数
     * @return 操作结果
     */
    @PostMapping("/updateWarned")
    public AjaxResult updateWarned(@RequestBody MarkVO markVO) {
        MarkVO updateMark = new MarkVO();
        updateMark.setArticleId(markVO.getArticleId());
        updateMark.setWarned(markVO.getWarned());
        boolean result = markService.saveOrUpdateMark(updateMark);
        return toAjax(result);
    }

    /**
     * 更新阅读状态
     *
     * @param markVO 更新参数
     * @return 操作结果
     */
    @PostMapping("/updateReadFlag")
    public AjaxResult updateReadFlag(@RequestBody MarkVO markVO) {
        MarkVO updateMark = new MarkVO();
        updateMark.setArticleId(markVO.getArticleId());
        updateMark.setReadFlag(markVO.getReadFlag());
        boolean result = markService.saveOrUpdateMark(updateMark);
        return toAjax(result);
    }
}
