package com.boryou.web.module.drill.service;

import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.util.NumberUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.web.module.drill.domain.DrillComment;
import com.boryou.web.module.drill.domain.DrillCommentReply;
import com.boryou.web.module.drill.domain.DrillScore;
import com.boryou.web.module.drill.domain.vo.DrillProcessRes;
import com.boryou.web.module.drill.domain.vo.DrillStageScoreRes;
import com.boryou.web.module.drill.domain.vo.ScoreAccumulator;
import com.boryou.web.module.drill.enums.*;
import com.boryou.web.module.drill.mapper.DrillScoreMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.*;
import java.util.function.BiConsumer;
import java.util.function.Function;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrillScoreService extends ServiceImpl<DrillScoreMapper, DrillScore> {

    private static final String SCORE_FORMAT = "发布%s %s 条;";

    public Map<String, DrillScore> getDrillScoreMap() {
        // 获取分数map
        List<DrillScore> drillScoreList = this.lambdaQuery().list();

        return CollStreamUtil.toMap(drillScoreList, item -> Convert.toStr(item.getCommentType()), v -> v);
    }


    public <T> void buildScoreBySize(
            Integer scoreType,
            BiConsumer<BigDecimal, BigDecimal> scoreConsumer,
            List<T> commentList,
            Function<T, Integer> teamTypeExtractor,
            Function<T, String> commentTypeExtractor,
            Function<T, Integer> likeCountExtractor
    ) {

        if (Objects.equals(ScoreTypeEnum.SCORE_NO_SHOW.getCode(), scoreType)) {
            return;
        }
        if (CollUtil.isEmpty(commentList)) {
            build0ScoreBySize(scoreType, scoreConsumer);
            return;
        }
        // 统计总权重和团队权重
        int totalWeight = 0;
        Map<Integer, Integer> teamWeightMap = new HashMap<>();

        for (T comment : commentList) {
            Integer teamType = teamTypeExtractor.apply(comment);
            if (teamType == null || !TeamTypeEnum.getTypes().contains(teamType)) {
                continue;
            }
            if (commentTypeExtractor != null && !CommentEnum.getScoreType().contains(commentTypeExtractor.apply(comment))) {
                continue;
            }

            // 计算单条评论权重：1（评论数基础） + 点赞数（若存在）
            int likeCount = (likeCountExtractor != null) ? likeCountExtractor.apply(comment) : 0;
            int weight = 1 + likeCount;

            totalWeight += weight;
            teamWeightMap.merge(teamType, weight, Integer::sum);
        }

        if (totalWeight == 0) {
            scoreConsumer.accept(BigDecimal.ZERO, BigDecimal.ZERO);
            return;
        }

        // 计算红蓝得分
        int redWeight = teamWeightMap.getOrDefault(TeamTypeEnum.RED.getCode(), 0);
        int blueWeight = teamWeightMap.getOrDefault(TeamTypeEnum.BLUE.getCode(), 0);

        // BigDecimal redScore = new BigDecimal(redWeight)
        //         .divide(new BigDecimal(totalWeight), 4, RoundingMode.HALF_UP)
        //         .multiply(new BigDecimal(100))
        //         .setScale(1, RoundingMode.HALF_UP);
        //
        // BigDecimal blueScore = new BigDecimal(blueWeight)
        //         .divide(new BigDecimal(totalWeight), 4, RoundingMode.HALF_UP)
        //         .multiply(new BigDecimal(100))
        //         .setScale(1, RoundingMode.HALF_UP);

        scoreConsumer.accept(NumberUtil.toBigDecimal(redWeight), NumberUtil.toBigDecimal(blueWeight));
    }

    private void buildScoreResult(List<String> redScoreName, List<String> redScore, List<DrillStageScoreRes> redDrillStageScoreResList, String stageName) {
        DrillStageScoreRes.DrillStageScoreResBuilder<?, ?> builder = DrillStageScoreRes.builder();
        if (CollUtil.isNotEmpty(redScoreName) && CollUtil.isNotEmpty(redScore)) {
            String score = getTotalScore(redScore);
            builder.stageName(stageName)
                    .scoreName(redScoreName)
                    .score(score);
        } else {
            builder.stageName(stageName)
                    .score("0");
        }
        redDrillStageScoreResList.add(builder.build());
    }

    public void build0ScoreBySize(Integer scoreType, BiConsumer<BigDecimal, BigDecimal> scoreSetter) {
        if (Objects.equals(ScoreTypeEnum.SCORE_NO_SHOW.getCode(), scoreType)) {
            return;
        }
        scoreSetter.accept(BigDecimal.ZERO, BigDecimal.ZERO);
    }

    public String getTotalScore(List<String> scores) {
        String score = "0";
        for (String s : scores) {
            score = NumberUtil.add(score, s).toString();
        }
        return score;
    }

    public void buildScore(BiConsumer<String, String> scoreConsumer,
                           Integer scoreType,
                           List<DrillComment> processStageCommentList,
                           List<DrillCommentReply> drillCommentReplies) {
        ScoreAccumulator scoreAccumulator = new ScoreAccumulator();
        buildScoreBySize(
                scoreType,
                scoreAccumulator.getScoreSetter(),
                processStageCommentList,
                DrillComment::getTeamType,
                DrillComment::getCommentType,
                null
        );

        buildScoreBySize(
                scoreType,
                scoreAccumulator.getScoreSetter(),
                drillCommentReplies,
                DrillCommentReply::getTeamType,
                null,
                DrillCommentReply::getLikeCount
        );

        scoreAccumulator.scoreResult(scoreConsumer);
    }


    public void buildScore(List<DrillComment> processStageCommentList, Map<String, DrillScore> drillScoreMap, List<String> redTotalScores, List<String> blueTotalScores, List<DrillStageScoreRes> redDrillStageScoreResList, String stageName, List<DrillStageScoreRes> blueDrillStageScoreResList) {
        // 每个阶段算分
        Map<Integer, Map<String, List<DrillComment>>> scoreMap = CollStreamUtil.groupBy2Key(processStageCommentList,
                DrillComment::getTeamType, DrillComment::getCommentType);
        List<String> redScoreName = new ArrayList<>();
        List<String> redScore = new ArrayList<>();
        List<String> blueScoreName = new ArrayList<>();
        List<String> blueScore = new ArrayList<>();
        scoreMap.forEach((teamType, v) -> {
            if (Objects.equals(teamType, TeamTypeEnum.RED.getCode())) {
                buildScoreFormat(v, redScoreName, redScore, drillScoreMap);
                redTotalScores.addAll(redScore);
            } else if (Objects.equals(teamType, TeamTypeEnum.BLUE.getCode())) {
                buildScoreFormat(v, blueScoreName, blueScore, drillScoreMap);
                blueTotalScores.addAll(blueScore);
            }
        });
        buildScoreResult(redScoreName, redScore, redDrillStageScoreResList, stageName);
        buildScoreResult(blueScoreName, blueScore, blueDrillStageScoreResList, stageName);
    }

    private void buildScoreFormat(Map<String, List<DrillComment>> v, List<String> redScoreName, List<String> redScore, Map<String, DrillScore> drillScoreMap) {
        v.forEach((commentType, comment) -> {
            DrillScore drillScore = drillScoreMap.get(commentType);
            // CommentScoreEnum enumByType = CommentScoreEnum.getEnumByType(commentType);
            int size = comment.size();
            if (drillScore != null && size > 0) {
                // String scoreEnum = enumByType.getScore();
                // String name = enumByType.getName();
                String name = drillScore.getCommentName();
                redScoreName.add(String.format(SCORE_FORMAT, name, size));
                for (DrillComment drillComment : comment) {
                    String score = drillComment.getScore();
                    redScore.add(score);
                }
            }
        });
    }

    public void build0Score(Integer stageType, String stageName,
                            List<DrillStageScoreRes> redDrillStageScoreResList,
                            List<DrillStageScoreRes> blueDrillStageScoreResList) {
        if (!Objects.equals(StageTypeEnum.TYPE_LAST.getCode(), String.valueOf(stageType))) {
            // 什么评论都没发
            redDrillStageScoreResList.add(DrillStageScoreRes.builder()
                    .stageName(stageName)
                    .score("0")
                    .build());
            blueDrillStageScoreResList.add(DrillStageScoreRes.builder()
                    .stageName(stageName)
                    .score("0")
                    .build());
        }
    }

    // public void buildTypeLastScore(List<DrillProcessRes.DrillProcessStageRes> drillProcessStageRes,
    //                                List<String> redTotalScores,
    //                                List<String> blueTotalScores,
    //                                List<DrillProcessRes.DrillProcessStageRes.DrillStageScoreRes> redDrillStageScoreResList,
    //                                List<DrillProcessRes.DrillProcessStageRes.DrillStageScoreRes> blueDrillStageScoreResList) {
    //     String redTotalScore = getTotalScore(redTotalScores);
    //     String blueTotalScore = getTotalScore(blueTotalScores);
    //     for (DrillProcessRes.DrillProcessStageRes drillProcessStageRe : drillProcessStageRes) {
    //         Integer stageType = drillProcessStageRe.getStageType();
    //         if (Objects.equals(StageTypeEnum.TYPE_LAST.getCode(), String.valueOf(stageType))) {
    //             // 总结
    //             drillProcessStageRe.setRedScore(redDrillStageScoreResList);
    //             drillProcessStageRe.setBlueScore(blueDrillStageScoreResList);
    //             drillProcessStageRe.setRedTotalScore(redTotalScore);
    //             drillProcessStageRe.setBlueTotalScore(blueTotalScore);
    //         }
    //     }
    // }

    public void buildTypeLastScore(List<DrillProcessRes.DrillProcessStageRes> drillProcessStageRes,
                                   List<String> redBasicScores,
                                   List<String> blueBasicScores,
                                   List<String> redExpertScores,
                                   List<String> blueExpertScores) {
        String redBasicScoreStr = getTotalScore(redBasicScores);
        String blueBasicScoreStr = getTotalScore(blueBasicScores);
        String redExpertScoreStr = getTotalScore(redExpertScores);
        String blueExpertScoreStr = getTotalScore(blueExpertScores);
        String redTotalScore = NumberUtil.add(redBasicScoreStr, redExpertScoreStr).toString();
        String blueTotalScore = NumberUtil.add(blueBasicScoreStr, blueExpertScoreStr).toString();
        List<DrillStageScoreRes> redDrillStageScoreResList = new ArrayList<>();
        List<DrillStageScoreRes> blueDrillStageScoreResList = new ArrayList<>();

        for (ScoreShowEnum value : ScoreShowEnum.values()) {
            if (Objects.equals(value, ScoreShowEnum.SCORE_BASIC)) {
                redDrillStageScoreResList.add(DrillStageScoreRes
                        .builder()
                        .scoreName(CollUtil.newArrayList(ScoreShowEnum.SCORE_BASIC.getName()))
                        .score(redBasicScoreStr)
                        .build());
                blueDrillStageScoreResList.add(DrillStageScoreRes
                        .builder()
                        .scoreName(CollUtil.newArrayList(ScoreShowEnum.SCORE_BASIC.getName()))
                        .score(blueBasicScoreStr)
                        .build());
            } else if (Objects.equals(value, ScoreShowEnum.SCORE_EXPERT)) {
                redDrillStageScoreResList.add(DrillStageScoreRes
                        .builder()
                        .scoreName(CollUtil.newArrayList(ScoreShowEnum.SCORE_EXPERT.getName()))
                        .score(redExpertScoreStr)
                        .build());
                blueDrillStageScoreResList.add(DrillStageScoreRes
                        .builder()
                        .scoreName(CollUtil.newArrayList(ScoreShowEnum.SCORE_EXPERT.getName()))
                        .score(blueExpertScoreStr)
                        .build());
            }
        }

        for (DrillProcessRes.DrillProcessStageRes drillProcessStageRe : drillProcessStageRes) {
            Integer stageType = drillProcessStageRe.getStageType();
            if (Objects.equals(StageTypeEnum.TYPE_LAST.getCode(), String.valueOf(stageType))) {
                // 总结
                drillProcessStageRe.setRedScore(redDrillStageScoreResList);
                drillProcessStageRe.setBlueScore(blueDrillStageScoreResList);
                drillProcessStageRe.setRedTotalScore(redTotalScore);
                drillProcessStageRe.setBlueTotalScore(blueTotalScore);
            }
        }
    }

}
