package com.boryou.web.module.notice.domain;

import com.baomidou.mybatisplus.annotation.TableField;
import com.boryou.common.annotation.Excel;
import com.boryou.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 站内信对象 by_notice
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Data
public class ByNotice extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /**
     * 通知id
     */
    private Long noticeId;

    /**
     * es的id
     */
    @Excel(name = "es的id")
    @JsonSerialize(using = ToStringSerializer.class)
    private Long docIndexId;


    /**
     * 通知时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @Excel(name = "通知时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date noticeTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date time;
    @Excel(name = "1是重点关注 2.短信推送  3.处置信息 ")
    private String noticeType;
    @TableField(exist = false)
    private Long userId;


    /**
     * $table.subTable.functionName信息
     */
    private List<ByNoticeRela> byNoticeRelaList;

}
