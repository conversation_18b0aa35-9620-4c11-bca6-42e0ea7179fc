package com.boryou.web.module.drill.service;

import cn.hutool.core.convert.Convert;
import cn.hutool.core.text.CharSequenceUtil;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.RedisUtil;
import com.boryou.web.constant.RedisConstant;
import com.boryou.web.module.drill.domain.DrillStageTime;
import com.boryou.web.module.drill.domain.vo.DrillStageTimerVO;
import com.boryou.web.module.drill.enums.RemainTimeEnum;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import javax.annotation.PostConstruct;
import javax.annotation.PreDestroy;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.*;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 倒计时服务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrillTimerService {

    private final RedisUtil redisUtil;
    private final DrillWebsocketService drillWebsocketService;
    private final DrillStageTimeService drillStageTimeService;
    // 存储正在运行的计时器任务
    private final Map<Long, ScheduledFuture<?>> activeTimers = new ConcurrentHashMap<>();
    // 计时器任务计数器（用于线程命名）
    private final AtomicInteger timerCounter = new AtomicInteger(0);
    // 使用ScheduledExecutorService替代Thread.sleep
    private ScheduledExecutorService scheduler;

    @PostConstruct
    public void init() {
        // 创建线程池，核心线程数可根据系统负载调整
        scheduler = Executors.newScheduledThreadPool(
                5,  // 核心线程数
                r -> {
                    Thread thread = new Thread(r, "drill-timer-" + timerCounter.incrementAndGet());
                    thread.setDaemon(true); // 设置为守护线程
                    return thread;
                }
        );

        // 系统启动时恢复未完成的倒计时
        recoverTimers();
    }

    @PreDestroy
    public void shutdown() {
        // 应用关闭时优雅关闭线程池
        if (scheduler != null && !scheduler.isShutdown()) {
            scheduler.shutdown();
            try {
                // 等待所有任务完成
                if (!scheduler.awaitTermination(5, TimeUnit.SECONDS)) {
                    scheduler.shutdownNow();
                }
            } catch (InterruptedException e) {
                scheduler.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 启动倒计时
     *
     * @param timerVO            倒计时参数
     * @param user               用户信息
     * @param completionCallback 倒计时完成时的回调函数
     */
    public void startTimer(DrillStageTimerVO timerVO, SysUser user, TimerCompletionCallback completionCallback) {
        Long drillStageTimeId = timerVO.getDrillStageTimeId();
        Long drillTaskId = timerVO.getDrillTaskId();
        Long processStageId = timerVO.getProcessStageId();
        Integer remainingSeconds = timerVO.getRemainTimerDuration();

        // 设置倒计时状态为开始
        String keyStatus = RedisConstant.DRILL_REMAIN_TIME_STATUS + drillStageTimeId;
        redisUtil.set(keyStatus, RemainTimeEnum.START.getType());

        // 存储初始倒计时值到Redis，设置较长的过期时间
        String key = RedisConstant.DRILL_REMAIN_TIME_CACHE + drillStageTimeId;
        redisUtil.setEx(key, String.valueOf(remainingSeconds), 24, TimeUnit.HOURS);

        // 记录倒计时开始时间
        String startTimeKey = RedisConstant.DRILL_REMAIN_TIME_START + drillStageTimeId;
        redisUtil.setEx(startTimeKey, String.valueOf(System.currentTimeMillis()), 24, TimeUnit.HOURS);

        // 取消已存在的计时器（如果有）
        cancelTimer(drillStageTimeId);

        // 创建并启动新的计时器任务
        ScheduledFuture<?> future = scheduler.scheduleAtFixedRate(
                () -> processTimerTick(timerVO, user, completionCallback),
                0,  // 立即开始
                1,   // 每秒执行一次
                TimeUnit.SECONDS
        );

        // 存储计时器任务引用
        activeTimers.put(drillStageTimeId, future);

        log.info("倒计时已启动: drillStageTimeId={}, 剩余时间={}秒", drillStageTimeId, remainingSeconds);
    }

    /**
     * 处理计时器滴答
     */
    private void processTimerTick(DrillStageTimerVO timerVO, SysUser user, TimerCompletionCallback completionCallback) {
        Long drillStageTimeId = timerVO.getDrillStageTimeId();
        Long drillTaskId = timerVO.getDrillTaskId();
        Long processStageId = timerVO.getProcessStageId();

        try {
            // 检查计时器状态
            String keyStatus = RedisConstant.DRILL_REMAIN_TIME_STATUS + drillStageTimeId;
            String status = redisUtil.get(keyStatus);

            // 如果状态为空或暂停或结束，则取消计时器
            if (CharSequenceUtil.isBlank(status) ||
                    RemainTimeEnum.PAUSE.getType().equals(status) ||
                    RemainTimeEnum.END.getType().equals(status)) {
                cancelTimer(drillStageTimeId);
                return;
            }

            // 获取当前剩余时间
            String key = RedisConstant.DRILL_REMAIN_TIME_CACHE + drillStageTimeId;
            String remainingStr = redisUtil.get(key);
            if (CharSequenceUtil.isBlank(remainingStr)) {
                cancelTimer(drillStageTimeId);
                return;
            }

            // 计算剩余时间
            int remainingSeconds = Convert.toInt(remainingStr, 0);
            if (remainingSeconds <= 0) {
                // 倒计时结束
                handleTimerCompletion(timerVO, user, completionCallback);
                return;
            }

            // 更新剩余时间
            remainingSeconds--;
            redisUtil.setEx(key, String.valueOf(remainingSeconds), 24, TimeUnit.HOURS);

            // 更新VO并推送到客户端
            timerVO.setRemainTimerDuration(remainingSeconds);
            drillWebsocketService.timeToUser(timerVO, drillTaskId, user);

        } catch (Exception e) {
            log.error("倒计时处理异常: drillStageTimeId={}", drillStageTimeId, e);
            // 发生异常时不取消计时器，让它继续运行
        }
    }

    /**
     * 处理倒计时完成
     */
    private void handleTimerCompletion(DrillStageTimerVO timerVO, SysUser user, TimerCompletionCallback completionCallback) {
        Long drillStageTimeId = timerVO.getDrillStageTimeId();
        Long drillTaskId = timerVO.getDrillTaskId();
        Long processStageId = timerVO.getProcessStageId();

        try {
            // 取消计时器
            cancelTimer(drillStageTimeId);

            // 清理Redis数据
            String key = RedisConstant.DRILL_REMAIN_TIME_CACHE + drillStageTimeId;
            String keyStatus = RedisConstant.DRILL_REMAIN_TIME_STATUS + drillStageTimeId;
            String startTimeKey = RedisConstant.DRILL_REMAIN_TIME_START + drillStageTimeId;

            redisUtil.delete(key);
            redisUtil.delete(keyStatus);
            redisUtil.delete(startTimeKey);

            // 更新数据库中的剩余时间为0
            drillStageTimeService.updateRemainTimerDuration(0, drillStageTimeId);

            // 发送最终的0秒通知
            timerVO.setRemainTimerDuration(0);
            drillWebsocketService.timeToUser(timerVO, drillTaskId, user);

            // 触发完成回调
            if (completionCallback != null) {
                completionCallback.onTimerCompleted(processStageId, drillTaskId, user);
            }

            log.info("倒计时已完成: drillStageTimeId={}", drillStageTimeId);

        } catch (Exception e) {
            log.error("处理倒计时完成时发生异常: drillStageTimeId={}", drillStageTimeId, e);
        }
    }

    /**
     * 取消计时器
     */
    public void cancelTimer(Long drillStageTimeId) {
        ScheduledFuture<?> future = activeTimers.remove(drillStageTimeId);
        if (future != null && !future.isDone()) {
            future.cancel(false);
            log.info("倒计时已取消: drillStageTimeId={}", drillStageTimeId);
        }
    }

    /**
     * 暂停倒计时
     */
    public void pauseTimer(Long drillStageTimeId) {
        String keyStatus = RedisConstant.DRILL_REMAIN_TIME_STATUS + drillStageTimeId;
        redisUtil.set(keyStatus, RemainTimeEnum.PAUSE.getType());
        cancelTimer(drillStageTimeId);
        log.info("倒计时已暂停: drillStageTimeId={}", drillStageTimeId);
    }

    /**
     * 恢复倒计时
     */
    public void resumeTimer(DrillStageTimerVO timerVO, SysUser user, TimerCompletionCallback completionCallback) {
        startTimer(timerVO, user, completionCallback);
        log.info("倒计时已恢复: drillStageTimeId={}", timerVO.getDrillStageTimeId());
    }

    /**
     * 结束倒计时
     */
    public void endTimer(Long drillStageTimeId) {
        String keyStatus = RedisConstant.DRILL_REMAIN_TIME_STATUS + drillStageTimeId;
        redisUtil.set(keyStatus, RemainTimeEnum.END.getType());
        cancelTimer(drillStageTimeId);
        log.info("倒计时已结束: drillStageTimeId={}", drillStageTimeId);
    }

    /**
     * 系统启动时恢复未完成的倒计时
     */
    private void recoverTimers() {
        try {
            // 查找所有活跃的倒计时
            Set<String> keys = redisUtil.scan(RedisConstant.DRILL_REMAIN_TIME_CACHE + "*");
            if (keys == null || keys.isEmpty()) {
                return;
            }

            log.info("正在恢复{}个倒计时任务", keys.size());

            for (String key : keys) {
                try {
                    // 提取drillStageTimeId
                    String idStr = key.substring(RedisConstant.DRILL_REMAIN_TIME_CACHE.length());
                    Long drillStageTimeId = Convert.toLong(idStr);

                    // 检查状态是否为开始
                    String keyStatus = RedisConstant.DRILL_REMAIN_TIME_STATUS + drillStageTimeId;
                    String status = redisUtil.get(keyStatus);
                    if (!RemainTimeEnum.START.getType().equals(status)) {
                        continue;
                    }

                    // 获取剩余时间
                    String remainingStr = redisUtil.get(key);
                    if (CharSequenceUtil.isBlank(remainingStr)) {
                        continue;
                    }

                    // 获取开始时间，计算实际剩余时间
                    String startTimeKey = RedisConstant.DRILL_REMAIN_TIME_START + drillStageTimeId;
                    String startTimeStr = redisUtil.get(startTimeKey);
                    if (CharSequenceUtil.isBlank(startTimeStr)) {
                        continue;
                    }

                    // 查询数据库获取完整信息
                    DrillStageTime stageTime = drillStageTimeService.getById(drillStageTimeId);
                    if (stageTime == null) {
                        continue;
                    }

                    // 创建计时器VO
                    DrillStageTimerVO timerVO = new DrillStageTimerVO();
                    timerVO.setDrillStageTimeId(drillStageTimeId);
                    timerVO.setDrillTaskId(stageTime.getDrillTaskId());
                    timerVO.setProcessStageId(stageTime.getProcessStageId());
                    timerVO.setTeamType(stageTime.getTeamType());
                    timerVO.setTimerType(stageTime.getTimerType());
                    timerVO.setTimerDuration(stageTime.getTimerDuration());

                    // 计算实际剩余时间
                    int originalRemaining = Convert.toInt(remainingStr, 0);
                    long startTime = Convert.toLong(startTimeStr, 0L);
                    long elapsedSeconds = (System.currentTimeMillis() - startTime) / 1000;
                    int actualRemaining = originalRemaining - (int) elapsedSeconds;

                    // 如果已经结束，则直接触发结束逻辑
                    if (actualRemaining <= 0) {
                        timerVO.setRemainTimerDuration(0);
                        handleTimerCompletion(timerVO, null, null);
                        continue;
                    }

                    // 更新剩余时间并重启计时器
                    timerVO.setRemainTimerDuration(actualRemaining);
                    redisUtil.set(key, String.valueOf(actualRemaining));
                    startTimer(timerVO, null, null);

                    log.info("已恢复倒计时: drillStageTimeId={}, 剩余时间={}秒", drillStageTimeId, actualRemaining);

                } catch (Exception e) {
                    log.error("恢复倒计时时发生异常: key={}", key, e);
                }
            }
        } catch (Exception e) {
            log.error("恢复倒计时任务失败", e);
        }
    }

    /**
     * 倒计时完成回调接口
     */
    public interface TimerCompletionCallback {
        /**
         * 当倒计时完成时调用
         *
         * @param processStageId 阶段ID
         * @param drillTaskId    任务ID
         * @param user           用户信息
         */
        void onTimerCompleted(Long processStageId, Long drillTaskId, SysUser user);
    }
}
