package com.boryou.web.module.search.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.search.entity.EsReadData;
import com.boryou.web.module.search.mapper.EsReadDataMapper;
import com.boryou.web.module.search.service.EsReadDataService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
@RequiredArgsConstructor
public class EsReadDataServiceImpl extends ServiceImpl<EsReadDataMapper, EsReadData> implements EsReadDataService {

    @Override
    public int updateSearchData(EsReadData esReadData) {
        //检查是否存在
        QueryWrapper<EsReadData> qw = new QueryWrapper<>();
        qw.eq(esReadData.getIndexId() != null, "index_id", esReadData.getIndexId())
                .eq(esReadData.getCreateBy() != null, "create_by", esReadData.getCreateBy());
        List<EsReadData> list = baseMapper.selectList(qw);
        if (CollUtil.isEmpty(list)) {
            return baseMapper.insert(esReadData);
        } else {
            return 1;
        }
    }

    @Override
    public int updateSearchDatas(List<Long> ids) {
        if (CollUtil.isEmpty(ids)) {
            return 1;
        }
        List<String> collect = ids.stream().map(String::valueOf).collect(Collectors.toList());
        String createBy = SecurityUtils.getLoginUser().getUser().getUserName();
        //检查已存在的信息
        QueryWrapper<EsReadData> qw = new QueryWrapper<>();
        qw.in("index_id", ids)
                .eq(createBy != null, "create_by", createBy);
        List<EsReadData> list = baseMapper.selectList(qw);
        List<Long> filterIds = list.stream().map(EsReadData::getIndexId).collect(Collectors.toList());
        //过滤重复信息
        for (Long filterId : filterIds) {
            collect.removeIf(String.valueOf(filterId)::equals);
        }
        //批量新增已读信息
        if (CollUtil.isNotEmpty(collect)) {
            List<EsReadData> esReadDatas = collect.stream().map(e -> {
                EsReadData esReadData = new EsReadData();
                esReadData.setId(IdUtil.getSnowflakeNextId());
                esReadData.setIndexId(Long.valueOf(e));
                esReadData.setCreateBy(createBy);
                return esReadData;
            }).collect(Collectors.toList());
            return baseMapper.insertList(esReadDatas);
        } else {
            return 1;
        }
    }

    @Override
    public List<EsReadData> getEsReadDatasByList(List<String> indexs) {
        boolean hasIndex = CollUtil.isNotEmpty(indexs);

        if (hasIndex) {
            QueryWrapper<EsReadData> qw = new QueryWrapper<>();
            qw.eq("create_by", SecurityUtils.getLoginUser().getUser().getUserName())
                    .in("index_id", indexs);
            return baseMapper.selectList(qw);
        }
        return new ArrayList<>();
    }
}
