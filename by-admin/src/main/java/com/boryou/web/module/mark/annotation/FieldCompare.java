package com.boryou.web.module.mark.annotation;

import java.lang.annotation.ElementType;
import java.lang.annotation.Retention;
import java.lang.annotation.RetentionPolicy;
import java.lang.annotation.Target;

/**
 * 字段比较配置注解
 * 用于配置字段比较的详细规则
 * 
 * <AUTHOR>
 */
@Target(ElementType.FIELD)
@Retention(RetentionPolicy.RUNTIME)
public @interface FieldCompare {
    
    /**
     * 是否参与比较
     * 默认为true，设置为false时该字段不参与比较
     */
    boolean enable() default true;
    
    /**
     * 比较优先级
     * 数值越小优先级越高，用于控制字段比较的顺序
     */
    int priority() default 0;
    
    /**
     * 是否允许null值
     * 当设置为false时，如果字段值为null则跳过比较
     */
    boolean allowNull() default true;
    
    /**
     * 字符串比较时是否忽略空白字符
     * 仅对字符串类型字段有效
     */
    boolean trimWhitespace() default false;
    
    /**
     * 数值比较的精度
     * 用于浮点数比较时的精度控制
     */
    double precision() default 0.0;
    
    /**
     * 自定义比较方法名
     * 可以指定一个自定义的比较方法名，该方法应该在同一个类中定义
     */
    String customCompareMethod() default "";
}
