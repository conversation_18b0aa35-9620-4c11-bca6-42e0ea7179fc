package com.boryou.web.module.drill.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.drill.domain.vo.*;
import com.boryou.web.module.drill.service.DrillService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@Slf4j
@RestController
@RequiredArgsConstructor
public class DrillController {

    private final DrillService drillService;

    /**
     * 开始演练
     */
    @PostMapping("/drill/process/start")
    public AjaxResult drillProcessStart(@RequestBody @Valid DrillProcessVO drillProcessVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        DrillProcessRes drillProcessRes = drillService.drillProcessStart(drillProcessVO, user);
        return AjaxResult.success(drillProcessRes);
    }

    /**
     * 离开演练
     */
    @PostMapping("/drill/process/leave")
    public AjaxResult drillProcessLeave(@RequestBody @Valid DrillProcessVO drillProcessVO) {
        Long userId = drillProcessVO.getUserId();
        // SysUser user = SecurityUtils.getLoginUser().getUser();
        drillService.drillProcessLeave(drillProcessVO, userId);
        return AjaxResult.success();
    }

    /**
     * 结束演练
     */
    @PostMapping("/drill/process/end")
    public AjaxResult drillProcessEnd(@RequestBody @Valid DrillProcessVO drillProcessVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        drillService.drillProcessEnd(drillProcessVO, user);
        return AjaxResult.success();
    }

    @PostMapping("/drill/stage/query")
    public AjaxResult drillStageQuery(@RequestBody(required = false) DrillProcessStageDTO drillProcessStageDTO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<DrillProcessStageDTO> drillProcessStage = drillService.drillStageQuery(drillProcessStageDTO, user);
        return AjaxResult.success(drillProcessStage);
    }

    /**
     * 开始阶段
     */
    @PostMapping("/drill/stage/next")
    public AjaxResult drillStageNext(@RequestBody @Valid DrillProcessStageVO drillProcessStageVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        DrillProcessStageVO drillProcessStage = drillService.drillStageNext(drillProcessStageVO, user);
        return AjaxResult.success(drillProcessStage);
    }

    @PostMapping("/drill/score/query")
    public AjaxResult drillScoreQuery(@RequestBody DrillCommentVO drillCommentVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        DrillCommentVO drillComment = drillService.drillScoreQuery(drillCommentVO, user);
        return AjaxResult.success(drillComment);
    }

    @PostMapping("/drill/score/statistics")
    public AjaxResult drillScoreStatistics(@RequestBody DrillCommentVO drillCommentVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        DrillStatisticsVO drillScoreStatistics = drillService.drillScoreStatistics(drillCommentVO, user);
        return AjaxResult.success(drillScoreStatistics);
    }

    /**
     * 发布指令或评论
     */
    @PostMapping("/drill/comment/publish")
    public AjaxResult drillCommentPublish(@RequestBody @Valid DrillCommentVO drillCommentVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        drillService.drillCommentPublish(drillCommentVO, user);
        return AjaxResult.success();
    }

    /**
     * 查询指令或评论
     */
    @PostMapping("/drill/comment/query")
    public AjaxResult drillCommentQuery(@RequestBody DrillCommentVO drillCommentVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        List<DrillProcessRes.DrillProcessStageRes.DrillCommentRes> drillCommentRes = drillService.drillCommentQuery(drillCommentVO, user);
        return AjaxResult.success(drillCommentRes);
    }

}
