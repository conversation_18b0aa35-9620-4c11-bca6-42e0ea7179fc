package com.boryou.web.module.search.entity;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * 关键词库类别
 *
 * <AUTHOR>
 */
@Data
public class WordLibType {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long id;

    /**
     * 关键词库类别名称
     */
    private String type;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    private String state;

    /**
     * 创建时间
     */
    private Date createTime;

    /**
     * 创建者
     */
    private String createBy;

    /**
     * 更新时间
     */
    private Date updateTime;

    /**
     * 更新者
     */
    private String updateBy;

}
