package com.boryou.web.module.notice.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DatePattern;
import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysDept;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.common.utils.spring.SpringUtils;
import com.boryou.system.mapper.SysDeptMapper;
import com.boryou.system.service.ISysDeptService;
import com.boryou.system.service.ISysUserService;
import com.boryou.web.controller.common.entity.EsBean;
import com.boryou.web.controller.common.entity.bo.EsSearchBO;
import com.boryou.web.controller.common.util.EsSearchUtil;
import com.boryou.web.module.notice.domain.ByNotice;
import com.boryou.web.module.notice.domain.ByNoticeRela;
import com.boryou.web.module.notice.mapper.ByNoticeMapper;
import com.boryou.web.module.notice.service.IByNoticeService;
import com.boryou.web.module.search.entity.IndexData;
import com.boryou.web.module.search.mapper.EsSpecialDataMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 站内信Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-07-24
 */
@Service
public class ByNoticeServiceImpl extends ServiceImpl<ByNoticeMapper, ByNotice> implements IByNoticeService {
    @Autowired
    private ByNoticeMapper byNoticeMapper;

    @Resource
    private SysDeptMapper deptMapper;
    @Autowired
    private ISysUserService userService;
    @Resource
    private EsSpecialDataMapper esSpecialDataMapper;
    @Autowired
    private ISysDeptService deptService;

    /**
     * 查询站内信
     *
     * @param noticeId 站内信ID
     * @return 站内信
     */
    @Override
    public ByNotice selectByNoticeById(Long noticeId) {
        return byNoticeMapper.selectByNoticeById(noticeId);
    }

    /**
     * 查询站内信列表
     *
     * @param byNotice 站内信
     * @return 站内信
     */
    @Override
    public List<ByNotice> selectByNoticeList(ByNotice byNotice) {
        byNotice.setUserId(SecurityUtils.getUserIdL());
        List<ByNotice> byNotices = byNoticeMapper.selectByNoticeList(byNotice);

        return byNotices;
    }

    @Override
    public int latest(ByNotice byNotice) {
        int byNotices = byNoticeMapper.selectNoticeListCount(SecurityUtils.getUserId());
        return byNotices;
    }


    /**
     * 新增站内信
     *
     * @param byNotice 站内信
     * @return 结果
     */
    @Transactional
    @Override
    public int insertByNotice(ByNotice byNotice) {
        int rows = byNoticeMapper.insertByNotice(byNotice);
        insertByNoticeRela(byNotice);
        return rows;
    }

    /**
     * 修改站内信
     *
     * @param byNotice 站内信
     * @return 结果
     */
    @Transactional
    @Override
    public int updateByNotice(ByNotice byNotice) {
        byNoticeMapper.deleteByNoticeRelaByNoticeId(byNotice.getNoticeId())
        ;
        insertByNoticeRela(byNotice);
        return byNoticeMapper.updateByNotice(byNotice);
    }

    /**
     * 批量删除站内信
     *
     * @param noticeIds 需要删除的站内信ID
     * @return 结果
     */
    @Transactional
    @Override
    public int deleteByNoticeByIds(Long[] noticeIds) {
        byNoticeMapper.deleteByNoticeRelaByNoticeIds(noticeIds);
        return byNoticeMapper.deleteByNoticeByIds(noticeIds);
    }

    /**
     * 删除站内信信息
     *
     * @param noticeId 站内信ID
     * @return 结果
     */
    @Override
    public int deleteByNoticeById(Long noticeId) {
        byNoticeMapper.deleteByNoticeRelaByNoticeId(noticeId);
        return byNoticeMapper.deleteByNoticeById(noticeId);
    }

    @Override
    public boolean saveSiteNotice(ByNotice byNotice) {
        byNoticeMapper.insertByNotice(byNotice);
        ArrayList<ByNoticeRela> byNoticeRelaList = new ArrayList<>();
        // 是否只查询自己创建的方案
        List<SysDept> depts = deptService.selectDeptList(new SysDept());//TODO 如果是组织机构或者博约科技，就给查这两集的用户返回，不需要把浙江高院及下级这种也发短信
        List<Long> collect = depts.stream().distinct().filter(s -> s.getDeptId() == 1 || s.getDeptId() == 2).map(SysDept::getDeptId).collect(Collectors.toList());
        List<Long> deptIds;
        if (CollUtil.isNotEmpty(collect)) {
            deptIds = collect;
        } else {
            deptIds = depts.stream().distinct().filter(c -> c.getDeptId() != 1 && c.getDeptId() != 2).map(s -> s.getDeptId()).collect(Collectors.toList());
        }
        List<SysUser> users = userService.getDeptUser(deptIds);
        for (SysUser id : users) {
            ByNoticeRela e = new ByNoticeRela();
            e.setUserId(id.getUserId());
            byNoticeRelaList.add(e);
        }
        byNotice.setByNoticeRelaList(byNoticeRelaList);
        if (CollUtil.isNotEmpty(byNoticeRelaList)) {
            SpringUtils.getBean(IByNoticeService.class).insertByNoticeRela(byNotice);
        }

        if (byNotice.getTime() != null) {
            //保存索引数据
            List<IndexData> datas = esSpecialDataMapper.selectIndexDataById(byNotice.getDocIndexId());
            if (CollUtil.isEmpty(datas)) {
                EsSearchBO bo = new EsSearchBO();
                bo.setStartTime(DateUtil.format(byNotice.getTime(), DatePattern.NORM_DATETIME_PATTERN));
                bo.setEndTime(DateUtil.format(byNotice.getTime(), DatePattern.NORM_DATETIME_PATTERN));
                bo.setId(String.valueOf(byNotice.getDocIndexId()));
                EsBean esBean = EsSearchUtil.searchByIdTimex(bo);
                if (esBean != null) {
                    IndexData data = new IndexData();
                    BeanUtil.copyProperties(esBean, data, "contentAreaCode");
                    data.setContentAreaCode(CollUtil.join(esBean.getContentAreaCode(), ","));
                    data.setIndexId(Long.valueOf(esBean.getId()));
                    esSpecialDataMapper.insertIndexData(data);
                }
            }
        }

        return true;
    }

    @Override
    public boolean saveWarnNotice(ByNotice byNotice, List<String> users) {
        byNoticeMapper.insertByNotice(byNotice);
        ArrayList<ByNoticeRela> byNoticeRelaList = new ArrayList<>();
        for (String userId : users) {
            ByNoticeRela e = new ByNoticeRela();
            e.setUserId(Long.valueOf(userId));
            byNoticeRelaList.add(e);
        }
        byNotice.setByNoticeRelaList(byNoticeRelaList);
        if (CollUtil.isNotEmpty(byNoticeRelaList)) {
            SpringUtils.getBean(IByNoticeService.class).insertByNoticeRela(byNotice);
        }
        return true;
    }

    /**
     * 新增${subTable.functionName}信息
     *
     * @param byNotice 站内信对象
     */
    @Override
    public void insertByNoticeRela(ByNotice byNotice) {
        List<ByNoticeRela> byNoticeRelaList = byNotice.getByNoticeRelaList();
        Long noticeId = byNotice.getNoticeId();
        if (StringUtils.isNotNull(byNoticeRelaList)) {
            List<ByNoticeRela> list = new ArrayList<>();
            for (ByNoticeRela byNoticeRela : byNoticeRelaList) {
                byNoticeRela.setNoticeId(noticeId);
                list.add(byNoticeRela);
            }
            if (!list.isEmpty()) {
                byNoticeMapper.batchByNoticeRela(list);
            }
        }
    }


}
