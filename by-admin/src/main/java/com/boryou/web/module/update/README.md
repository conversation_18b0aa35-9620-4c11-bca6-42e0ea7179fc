# 通用更新工具模块

本模块提供了通用的字段比较和更新功能，支持 MyBatis-Plus 的 LambdaUpdateChainWrapper，可以自动比较源数据与目标数据的字段差异并进行更新。

## 功能特性

- ✅ **通用字段比较**：自动比较源对象与目标对象的字段值差异
- ✅ **排除字段支持**：可以排除指定字段不参与比较和更新
- ✅ **字段映射支持**：支持源字段名与目标字段名不同的情况
- ✅ **LambdaUpdateChainWrapper 集成**：完美支持 MyBatis-Plus 的链式更新
- ✅ **空值处理**：自动忽略空值和空字符串
- ✅ **反射机制**：使用反射自动获取字段值，无需手动编写比较逻辑

## 工具类说明

### 1. SimpleUpdateUtil（推荐使用）

最简单易用的更新工具，提供多种便捷方法。

**主要方法：**
- `compareAndUpdate()` - 通用字段比较和更新
- `compareAndUpdateWithLambda()` - 使用Lambda表达式的简化方法
- `updateStringField()` - 单个字符串字段更新
- `updateField()` - 单个通用字段更新
- `updateFields()` - 批量字段更新

### 2. ChainUpdateUtil

专门支持 LambdaUpdateChainWrapper 的更新工具，功能更全面。

### 3. UniversalUpdateUtil

最通用的更新工具，支持 LambdaUpdateWrapper。

## 快速开始

### 基础使用示例

```java
// 1. 创建字段更新器映射
Map<String, SimpleUpdateUtil.FieldUpdater<EsBeanMark>> fieldUpdaters = 
        SimpleUpdateUtil.createUpdaters(
                SimpleUpdateUtil.FieldMapping.of("title", EsBeanMark::getTitle),
                SimpleUpdateUtil.FieldMapping.of("content", EsBeanMark::getContent),
                SimpleUpdateUtil.FieldMapping.of("userId", EsBeanMark::getUserId),
                SimpleUpdateUtil.FieldMapping.of("deptId", EsBeanMark::getDeptId)
        );

// 2. 执行比较和更新
boolean hasUpdate = SimpleUpdateUtil.compareAndUpdate(
        sourceData,     // 源数据对象
        targetData,     // 目标数据对象
        chainWrapper,   // LambdaUpdateChainWrapper
        fieldUpdaters,  // 字段更新器映射
        // 排除的字段
        "id", "createTime", "updateTime", "delFlag"
);

// 3. 如果有更新，设置公共字段并执行
if (hasUpdate) {
    chainWrapper.set(EsBeanMark::getUTime, new Date())
               .set(EsBeanMark::getUBy, "system");
    return chainWrapper.update();
}
```

### 使用Lambda表达式的简化方法

```java
// 创建Lambda表达式映射
Map<String, SFunction<EsBeanMark, ?>> fieldSetters = new HashMap<>();
fieldSetters.put("title", EsBeanMark::getTitle);
fieldSetters.put("content", EsBeanMark::getContent);
fieldSetters.put("userId", EsBeanMark::getUserId);

// 执行更新
boolean hasUpdate = SimpleUpdateUtil.compareAndUpdateWithLambda(
        sourceData, 
        targetData, 
        chainWrapper,
        fieldSetters,
        "id", "createTime", "updateTime"
);
```

### 支持字段映射的更新

```java
// 创建字段映射（源字段名 -> 目标字段名）
Map<String, String> fieldMappings = new HashMap<>();
fieldMappings.put("userIdSource", "userId");    // 源对象的userIdSource映射到目标对象的userId
fieldMappings.put("titleText", "title");        // 源对象的titleText映射到目标对象的title

boolean hasUpdate = SimpleUpdateUtil.compareAndUpdate(
        sourceData, 
        targetData, 
        chainWrapper,
        fieldUpdaters,
        fieldMappings,  // 字段映射
        "id", "createTime"
);
```

### 单字段更新

```java
boolean hasUpdate = false;

// 字符串字段更新
hasUpdate |= SimpleUpdateUtil.updateStringField(
        sourceData.getTitle(), targetData.getTitle(), chainWrapper, EsBeanMark::getTitle);

// 通用字段更新
hasUpdate |= SimpleUpdateUtil.updateField(
        sourceData.getUserId(), targetData.getUserId(), chainWrapper, EsBeanMark::getUserId);
```

### 批量字段更新

```java
boolean hasUpdate = SimpleUpdateUtil.updateFields(chainWrapper,
        SimpleUpdateUtil.FieldUpdate.ofString(
                sourceData.getTitle(), targetData.getTitle(), EsBeanMark::getTitle),
        SimpleUpdateUtil.FieldUpdate.of(
                sourceData.getUserId(), targetData.getUserId(), EsBeanMark::getUserId),
        SimpleUpdateUtil.FieldUpdate.of(
                sourceData.getStatus(), targetData.getStatus(), EsBeanMark::getStatus)
);
```

## 在Service中的完整使用示例

```java
@Service
public class EsBeanMarkService {
    
    public boolean updateEsBeanMark(EsBeanMarkVO esBeanMarkVO, Long userId, String userName) {
        // 1. 查询现有实体
        EsBeanMark existEntity = this.getOne(
                Wrappers.<EsBeanMark>lambdaQuery()
                        .eq(EsBeanMark::getArticleId, esBeanMarkVO.getArticleId())
                        .eq(EsBeanMark::getUserId, userId)
        );
        
        if (existEntity == null) {
            return false;
        }
        
        // 2. 创建链式更新包装器
        LambdaUpdateChainWrapper<EsBeanMark> chainWrapper = this.lambdaUpdate()
                .eq(EsBeanMark::getArticleId, esBeanMarkVO.getArticleId())
                .eq(EsBeanMark::getUserId, userId);
        
        // 3. 创建字段更新器
        Map<String, SimpleUpdateUtil.FieldUpdater<EsBeanMark>> fieldUpdaters = 
                SimpleUpdateUtil.createUpdaters(
                        SimpleUpdateUtil.FieldMapping.of("title", EsBeanMark::getTitle),
                        SimpleUpdateUtil.FieldMapping.of("content", EsBeanMark::getContent),
                        SimpleUpdateUtil.FieldMapping.of("tags", EsBeanMark::getTags),
                        SimpleUpdateUtil.FieldMapping.of("status", EsBeanMark::getStatus),
                        SimpleUpdateUtil.FieldMapping.of("remark", EsBeanMark::getRemark)
                );
        
        // 4. 执行字段比较和更新
        boolean hasUpdate = SimpleUpdateUtil.compareAndUpdate(
                esBeanMarkVO, 
                existEntity, 
                chainWrapper,
                fieldUpdaters,
                // 排除系统字段
                "esBeanMarkId", "createTime", "createBy", "delFlag"
        );
        
        // 5. 如果有更新，设置公共字段并执行
        if (hasUpdate) {
            chainWrapper.set(EsBeanMark::getDelFlag, 0)
                       .set(EsBeanMark::getUTime, new Date())
                       .set(EsBeanMark::getUBy, userName);
            
            return chainWrapper.update();
        }
        
        return false;
    }
}
```

## 注意事项

1. **字段更新器必须配置**：使用 `compareAndUpdate` 方法时，必须为需要更新的字段配置对应的更新器
2. **排除系统字段**：建议排除主键、创建时间、更新时间等系统字段
3. **空值处理**：工具会自动忽略空值和空字符串，避免将有效值更新为空
4. **字段映射**：当源对象和目标对象的字段名不同时，使用字段映射功能
5. **性能考虑**：工具使用反射机制，对于大量数据的批量更新建议谨慎使用

## 扩展功能

如需要更多自定义功能，可以：

1. 继承现有工具类并重写相关方法
2. 实现自定义的 `FieldUpdater` 接口
3. 扩展字段映射和排除逻辑

## 版本历史

- v1.0.0 - 初始版本，提供基础的字段比较和更新功能
