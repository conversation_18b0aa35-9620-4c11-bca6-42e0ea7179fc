package com.boryou.web.module.drill.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.drill.domain.vo.DrillCommentReplyVO;
import com.boryou.web.module.drill.service.DrillCommentService;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

@RestController
@RequestMapping("/comment/reply")
@RequiredArgsConstructor
public class DrillCommentReplyController {

    private final DrillCommentService drillCommentService;

    /**
     * 发表评论
     */
    @PostMapping("/add")
    public AjaxResult addCommentReply(@RequestBody DrillCommentReplyVO drillCommentReplyVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        drillCommentService.addCommentReply(drillCommentReplyVO, user);
        return AjaxResult.success();
    }

}
