package com.boryou.web.module.socket.service;

import com.boryou.web.module.socket.constant.WsConstant;
import com.boryou.web.module.socket.domain.TransferMessage;
import com.boryou.web.util.RedisStaticUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;

/**
 * websocket 用户管理工具
 *
 * <AUTHOR>
 * @since 2023/9/5 14:05
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class WebsocketRedisService {


    /**
     * 用户上线
     * @param loginId
     * @param optTime
     */
    public void online(String loginId, Date optTime) {
        //移除离线表
        RedisStaticUtils.zRemove(WsConstant.OFFLINE_UID_ZET, loginId);
        //更新上线表
        RedisStaticUtils.zAdd(WsConstant.ONLINE_UID_ZET, loginId, optTime.getTime());
    }

    public boolean isOnline(String uid) {
        return RedisStaticUtils.zIsMember(WsConstant.ONLINE_UID_ZET, uid);
    }

    //用户下线
    public void offline(String uid, Date optTime) {
        //移除上线线表
        RedisStaticUtils.zRemove(WsConstant.OFFLINE_UID_ZET, uid);
        //更新上线表
        RedisStaticUtils.zAdd(WsConstant.ONLINE_UID_ZET, uid, optTime.getTime());
    }

    /**
     * 透传 websocket 给 service 服务
     *
     * @param transferMessage 透传消息
     */
    public void sendWsToService(TransferMessage transferMessage) {
        RedisStaticUtils.convertAndSend(WsConstant.WS_TO_SERVICE, transferMessage);
    }

    /**
     * 透传 service 给 websocket服务
     *
     * @param transferMessage 透传消息
     */
    public void sendServiceToWs(TransferMessage transferMessage) {
        RedisStaticUtils.convertAndSend(WsConstant.SERVICE_TO_WS, transferMessage);
    }

}
