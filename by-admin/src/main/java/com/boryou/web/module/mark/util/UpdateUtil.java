package com.boryou.web.module.mark.util;

import cn.hutool.core.text.CharSequenceUtil;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.function.Consumer;

/**
 * 字段更新工具类
 * 用于比较和更新实体字段值
 */
public class UpdateUtil {

    private UpdateUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 更新字符串字段
     * 如果源值不为空且与当前值不同，则更新
     */
    public static void updateStringField(String srcValue, String currentValue, Consumer<String> setter) {
        if (CharSequenceUtil.isNotBlank(srcValue) && !srcValue.equals(currentValue)) {
            setter.accept(srcValue);
        }
    }

    /**
     * 更新日期字段
     * 如果源值不为空且与当前值不同，则更新
     */
    public static void updateDateField(Date srcValue, Date currentValue, Consumer<Date> setter) {
        if (srcValue != null && !srcValue.equals(currentValue)) {
            setter.accept(srcValue);
        }
    }

    /**
     * 更新字符串列表字段
     * 如果源值不为空且与当前值不同，则更新（深拷贝）
     */
    public static void updateListField(List<String> srcValue, List<String> currentValue, Consumer<List<String>> setter) {
        if (srcValue != null && !srcValue.equals(currentValue)) {
            setter.accept(new ArrayList<>(srcValue));
        }
    }

    /**
     * 更新整数列表字段
     * 如果源值不为空且与当前值不同，则更新（深拷贝）
     */
    public static void updateIntegerListField(List<Integer> srcValue, List<Integer> currentValue, Consumer<List<Integer>> setter) {
        if (srcValue != null && !srcValue.equals(currentValue)) {
            setter.accept(new ArrayList<>(srcValue));
        }
    }

    /**
     * 更新数值字段
     * 如果源值不为空且与当前值不同，则更新
     */
    public static <T extends Number> void updateNumericField(T srcValue, T currentValue, Consumer<T> setter) {
        if (srcValue != null && !srcValue.equals(currentValue)) {
            setter.accept(srcValue);
        }
    }

    /**
     * 更新布尔字段
     * 如果源值不为空且与当前值不同，则更新
     */
    public static void updateBooleanField(Boolean srcValue, Boolean currentValue, Consumer<Boolean> setter) {
        if (srcValue != null && !srcValue.equals(currentValue)) {
            setter.accept(srcValue);
        }
    }
}
