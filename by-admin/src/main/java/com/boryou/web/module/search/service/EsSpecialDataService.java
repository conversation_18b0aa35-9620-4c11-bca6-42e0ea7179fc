package com.boryou.web.module.search.service;

import cn.hutool.db.PageResult;
import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.module.search.entity.EsLikeData;
import com.boryou.web.module.search.entity.EsSpecialData;
import com.boryou.web.module.search.entity.vo.EsSpecialDataVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EsSpecialDataService extends IService<EsSpecialData> {

    int updateSearchData(EsSpecialData specialData);

    int insertLikeData(EsLikeData bean);

    List<EsLikeData> getLikeDatas(EsLikeData data);

    List<EsSpecialData> getEsSpecialDatasByList(List<String> indexs, List<String> md5s);

    List<EsSpecialDataVO> getEsSpecialDataPage(EsSpecialDataVO specialData);
}
