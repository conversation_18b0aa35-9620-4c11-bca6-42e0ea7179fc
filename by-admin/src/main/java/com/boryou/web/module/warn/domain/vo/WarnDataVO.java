package com.boryou.web.module.warn.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import javax.validation.constraints.NotBlank;
import java.util.List;

@Data
public class WarnDataVO {

    @NotBlank(message = "预警开始时间不能为空")
    private String warnDateStart;

    @NotBlank(message = "预警结束时间不能为空")
    private String warnDateEnd;

    private String emotionFlag;

    private String planId;

    private String userId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long deptId;

    private String type;

    /**
     * 信息浏览 0:未读 1:已读
     */
    private Integer readFlag;

    private String warnType;

    private String kw;

    private List<String> ids;
    private List<String> readIds;
    //预警查询重复
    private List<String> articleIds;
    private List<String> deptIds;
    private List<String> md5s;

    private String pushString;

    private Integer pageNum;
    private Integer pageSize;

    private String folderId;


    private List<String> users;

    /**
     * 预警推送类型 1: 短信 2: 邮件 3: 微信 4: 系统
     */
    private List<Integer> pushType;
}
