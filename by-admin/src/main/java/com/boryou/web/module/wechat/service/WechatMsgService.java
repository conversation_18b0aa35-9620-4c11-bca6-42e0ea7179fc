package com.boryou.web.module.wechat.service;


import com.boryou.web.module.wechat.domain.WarnCode;
import com.boryou.web.module.wechat.domain.vo.StatusVO;
import com.boryou.web.module.wechat.domain.vo.WechatCodeVO;

public interface WechatMsgService {
    /**
     * 处理用户登录请求，需要返回一张带code的二维码
     *
     * @param warnCode
     */
    WechatCodeVO handleWarnReq(WarnCode warnCode);

    WechatCodeVO handleLoginReq(WarnCode warnCode);

    StatusVO checkScanStatus(WarnCode warnCode);
}
