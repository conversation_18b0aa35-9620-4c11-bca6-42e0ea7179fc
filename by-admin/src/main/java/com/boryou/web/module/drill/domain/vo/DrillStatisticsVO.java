package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import javax.validation.constraints.NotNull;

/**
 * 演练评论记录实体类
 */
@Data
@SuperBuilder
@NoArgsConstructor
@AllArgsConstructor
public class DrillStatisticsVO {

    /**
     * 演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "任务id不能为空")
    private Long drillTaskId;

    /**
     * 所属阶段ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processStageId;

    @JsonSerialize(using = ToStringSerializer.class)
    @NotNull(message = "scoreProcessStageId 不能为空")
    private Long scoreProcessStageId;

    private Integer situationCommentCount = 0;
    private Integer policeCommentCount = 0;
    private Integer hotRankCount = 0;
    private Integer hotTopicCount = 0;

    private Integer replyCount = 0;
    private Integer likeCount = 0;


}
