package com.boryou.web.module.search.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.module.search.entity.EsReadData;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface EsReadDataService extends IService<EsReadData> {

    int updateSearchData(EsReadData esReadData);

    int updateSearchDatas(List<Long> ids);

    List<EsReadData> getEsReadDatasByList(List<String> indexs);
}
