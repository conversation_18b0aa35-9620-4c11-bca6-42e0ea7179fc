package com.boryou.web.module.search.service.impl;

import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.util.IdUtil;
import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONArray;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.module.area.entity.Area;
import com.boryou.web.module.search.entity.WordLib;
import com.boryou.web.module.search.entity.WordLibType;
import com.boryou.web.module.search.entity.vo.WordLibVO;
import com.boryou.web.module.search.mapper.WordLibMapper;
import com.boryou.web.module.area.service.AreaService;
import com.boryou.web.module.search.service.WordLibService;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Service
public class WordLibServiceImpl implements WordLibService {
    @Resource
    private WordLibMapper wordLibMapper;
    @Resource
    private AreaService areaService;

    @Override
    public List<WordLibType> selectWordLib(WordLibType wordLibType) {
//        SysUser user = SecurityUtils.getLoginUser().getUser();
//        Long userId = user.getUserId();
//        wordLibType.setUserId(userId);
        return wordLibMapper.selectWordLib(wordLibType);
    }

    @Override
    public List<WordLib> selectWord(WordLib wordLib) {
//        SysUser user = SecurityUtils.getLoginUser().getUser();
//        Long userId = user.getUserId();
//        wordLib.setUserId(userId);
        return wordLibMapper.selectWord(wordLib);
    }

    @Override
    public WordLib selectWordById(Long id) {
        return wordLibMapper.selectWordById(id);
    }

    @Override
    public int insertWordLib(WordLibType wordLibType) {
        if (CollUtil.isNotEmpty(selectWordLib(wordLibType))) {
            throw new CustomException("重复新增词库");
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        wordLibType.setId(IdUtil.getSnowflakeNextId());
        wordLibType.setState("0");
        wordLibType.setUserId(userId);
        wordLibType.setCreateBy(userName);
        wordLibType.setCreateTime(date);
        wordLibType.setUpdateBy(userName);
        wordLibType.setUpdateTime(date);
        return wordLibMapper.insertWordLib(wordLibType);
    }

    @Override
    public int insertWord(WordLib wordLib) {
        if (CollUtil.isNotEmpty(wordLibMapper.checkWord(wordLib))) {
            throw new CustomException("重复新增词组");
        }
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Long userId = user.getUserId();
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        wordLib.setId(IdUtil.getSnowflakeNextId());
        wordLib.setState("0");
        wordLib.setUserId(userId);
        wordLib.setCreateBy(userName);
        wordLib.setCreateTime(date);
        wordLib.setUpdateBy(userName);
        wordLib.setUpdateTime(date);
        return wordLibMapper.insertWord(wordLib);
    }

    @Override
    public int updateWordLibById(WordLibType wordLibType) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        wordLibType.setUpdateBy(userName);
        wordLibType.setUpdateTime(date);
        return wordLibMapper.updateWordLibById(wordLibType);
    }

    @Override
    public int updateWordById(WordLib wordLib) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        String userName = user.getUserName();
        DateTime date = DateUtil.date();
        wordLib.setUpdateBy(userName);
        wordLib.setUpdateTime(date);
        return wordLibMapper.updateWordById(wordLib);
    }

    @Override
    public int deleteWordLibByIds(String ids) {
        return wordLibMapper.deleteWordLibByIds(ids);
    }

    @Override
    public int deleteWordByIds(String ids) {
        return wordLibMapper.deleteWordByIds(ids);
    }

    @Override
    public JSONArray selectWordLibTree(WordLibVO wordLib) {
//        SysUser user = SecurityUtils.getLoginUser().getUser();
//        Long userId = user.getUserId();
//        wordLib.setUserId(userId);
        List<WordLibVO> wordLibVOS = wordLibMapper.selectWordLibTree(wordLib);
        Map<String, List<WordLibVO>> collect = wordLibVOS.stream().collect(Collectors.groupingBy(WordLibVO::getType));
        JSONArray result = JSONUtil.createArray();
        for (Map.Entry<String, List<WordLibVO>> entry : collect.entrySet()) {
            JSONObject object = JSONUtil.createObj();
            object.putOnce("type", entry.getKey());
            object.putOnce("child", entry.getValue());
            result.add(object);
        }
        return result;
    }

    @Override
    public JSONArray selectAreaTree(WordLibVO wordLib) {
        QueryWrapper<Area> qw1 = new QueryWrapper<>();
        QueryWrapper<Area> qw2 = new QueryWrapper<>();
        QueryWrapper<Area> qw3 = new QueryWrapper<>();

        qw1.eq("level", 1);
        qw2.eq("level", 2);
        qw3.eq("level", 3);
        if (StrUtil.isNotEmpty(wordLib.getName())) {
            qw2.likeRight("short_name", wordLib.getName());
        }
        qw1.orderByAsc("id");
        qw2.orderByAsc("id");
        qw3.orderByAsc("id");
        List<Area> list1 = new ArrayList<>(areaService.list(qw1));
        List<Area> list2 = new ArrayList<>(areaService.list(qw2));
        List<Area> list3 = new ArrayList<>(areaService.list(qw3));
        Map<String, Area> areaMap1 = list1.stream().collect(Collectors.toMap(a -> String.valueOf(a.getId()), Function.identity()));
        Map<String, List<Area>> areaMap2 = list2.stream().collect(Collectors.groupingBy(a -> String.valueOf(a.getParentId())));
        Map<String, List<Area>> areaMap3 = list3.stream().collect(Collectors.groupingBy(a -> String.valueOf(a.getParentId())));

        JSONArray result = JSONUtil.createArray();
        if (CollUtil.isNotEmpty(list1)) {
            JSONObject all = JSONUtil.createObj();
            all.putOnce("type", "全国");
            JSONArray child = JSONUtil.createArray();
            JSONObject childObject = JSONUtil.createObj();
            childObject.putOnce("name", "全国");
            List<String> filterInfoIds = list1.stream().map(f -> String.valueOf(f.getShortName())).collect(Collectors.toList());
            childObject.putOnce("word", CollUtil.join(filterInfoIds, " "));
            child.add(childObject);
            all.putOnce("child", child);
            result.add(all);
        }

        for (Map.Entry<String, List<Area>> entry : areaMap2.entrySet()) {
            JSONObject object = JSONUtil.createObj();
            object.putOnce("type", areaMap1.get(entry.getKey()).getShortName());
            JSONArray child = JSONUtil.createArray();
            List<Area> areas = entry.getValue();
            //全省有多个地市出现时
            if (CollUtil.isNotEmpty(areas) && areas.size() > 1) {
                JSONObject childObject = JSONUtil.createObj();
                childObject.putOnce("name", "全省");
                List<String> areaNames = areas.stream().map(f -> String.valueOf(f.getShortName())).collect(Collectors.toList());
                childObject.putOnce("word", CollUtil.join(areaNames, " "));
                child.add(childObject);
            }

            for (Area area : areas) {
                JSONObject childObject = JSONUtil.createObj();
                childObject.putOnce("name", area.getShortName());
                String names = area.getShortName() + " ";
                List<Area> childAreas = areaMap3.get(String.valueOf(area.getId()));
                if (CollUtil.isNotEmpty(childAreas)) {
                    List<String> areaNames = childAreas.stream().map(f -> String.valueOf(f.getShortName())).collect(Collectors.toList());
                    names = names + CollUtil.join(areaNames, " ");
                }
                childObject.putOnce("word", names.trim());
                child.add(childObject);
            }
            object.putOnce("child", child);
            result.add(object);
        }

        return result;
    }
}
