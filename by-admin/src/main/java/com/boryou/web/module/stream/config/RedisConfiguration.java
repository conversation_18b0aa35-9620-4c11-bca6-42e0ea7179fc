package com.boryou.web.module.stream.config;

import com.boryou.web.constant.RedisConstant;
import com.boryou.web.module.stream.bean.RedisMq;
import com.boryou.web.module.stream.consumer.AfterWarnMqListener;
import com.boryou.web.module.stream.util.RedisStreamUtil;
import lombok.extern.slf4j.Slf4j;
import lombok.var;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.redis.connection.RedisConnectionFactory;
import org.springframework.data.redis.connection.stream.Consumer;
import org.springframework.data.redis.connection.stream.MapRecord;
import org.springframework.data.redis.connection.stream.ReadOffset;
import org.springframework.data.redis.connection.stream.StreamOffset;
import org.springframework.data.redis.stream.StreamMessageListenerContainer;
import org.springframework.data.redis.stream.Subscription;

import javax.annotation.Resource;
import java.time.Duration;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicInteger;

@Slf4j
@Configuration
public class RedisConfiguration {
    @Resource
    private RedisStreamUtil redisStreamUtil;
    @Resource
    private RedisMq redisMq;
    @Resource
    private AfterWarnMqListener afterWarnMqListener;

    @Bean
    public List<Subscription> subscription(RedisConnectionFactory factory) {
        List<Subscription> resultList = new ArrayList<>();
        AtomicInteger index = new AtomicInteger(1);
        int processors = Runtime.getRuntime().availableProcessors();
        ThreadPoolExecutor executor = new ThreadPoolExecutor(processors, processors, 0, TimeUnit.SECONDS,
                new LinkedBlockingDeque<>(), r -> {
            Thread thread = new Thread(r);
            thread.setName("async-stream-consumer-" + index.getAndIncrement());
            thread.setDaemon(true);
            return thread;
        });
        StreamMessageListenerContainer.StreamMessageListenerContainerOptions<String, MapRecord<String, String, String>> options =
                StreamMessageListenerContainer.StreamMessageListenerContainerOptions
                        .builder()
                        // 一次最多获取多少条消息
                        .batchSize(5)
                        .executor(executor)
                        .pollTimeout(Duration.ofSeconds(1))
                        // .errorHandler()
                        .build();
        for (RedisMq.RedisMqStream redisMqStream : redisMq.getStreams()) {
            String streamName = RedisConstant.system_prefix + redisMqStream.getName();
            RedisMq.RedisMqStream.RedisMqGroup redisMqGroup = redisMqStream.getGroups().get(0);

            initStream(streamName, redisMqGroup.getName());
            var listenerContainer = StreamMessageListenerContainer.create(factory, options);
            // 手动ask消息
            for (String consumer : redisMqGroup.getConsumers()) {
                Subscription subscription = listenerContainer.receive(Consumer.from(redisMqGroup.getName(), consumer),
                        StreamOffset.create(streamName, ReadOffset.lastConsumed()), afterWarnMqListener);
                resultList.add(subscription);
            }

            // 自动ask消息
           /* Subscription subscription = listenerContainer.receiveAutoAck(Consumer.from(redisMqGroup.getName(), redisMqGroup.getConsumers()[0]),
                    StreamOffset.create(streamName, ReadOffset.lastConsumed()), new ReportReadMqListener());*/

            listenerContainer.start();
        }
        return resultList;
    }

    private void initStream(String key, String group) {
        boolean hasKey = redisStreamUtil.hasKey(key);
        if (!hasKey) {
            Map<String, Object> map = new HashMap<>(1);
            map.put("field", "value");
            //创建主题
            String result = redisStreamUtil.addMap(key, map);
            //创建消费组
            redisStreamUtil.createGroup(key, group);
            //将初始化的值删除掉
            redisStreamUtil.del(key, result);
            log.info("stream:{}-group:{} initialize success", key, group);
        }
    }

}
