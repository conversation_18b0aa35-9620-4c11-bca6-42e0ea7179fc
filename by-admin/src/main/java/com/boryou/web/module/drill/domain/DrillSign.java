package com.boryou.web.module.drill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 得分规则配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("by_drill_sign")
public class DrillSign {

    /**
     * 规则ID（自增主键）
     */
    @TableId(value = "drill_sign_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillSignId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    private String userName;

    private String phone;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;
}
