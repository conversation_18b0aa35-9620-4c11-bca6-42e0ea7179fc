package com.boryou.web.module.update.util;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.baomidou.mybatisplus.extension.conditions.update.LambdaUpdateChainWrapper;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.util.*;
import java.util.function.Function;

/**
 * 链式更新工具类
 * 专门支持 LambdaUpdateChainWrapper 的通用更新方法
 * 
 * <AUTHOR>
 */
@Slf4j
public class ChainUpdateUtil {

    private ChainUpdateUtil() {
        throw new IllegalStateException("Utility class");
    }

    /**
     * 链式更新配置类
     */
    @Data
    public static class ChainUpdateConfig<T> {
        /**
         * 链式更新包装器
         */
        private LambdaUpdateChainWrapper<T> chainWrapper;
        
        /**
         * 排除的字段集合
         */
        private Set<String> excludedFields = new HashSet<>();
        
        /**
         * 字段映射关系 (源字段名 -> 目标字段名)
         */
        private Map<String, String> fieldMappings = new HashMap<>();
        
        /**
         * 字段更新器映射 (目标字段名 -> Lambda表达式)
         */
        private Map<String, SFunction<T, ?>> fieldSetters = new HashMap<>();
        
        /**
         * 是否有字段需要更新
         */
        private boolean hasUpdate = false;
        
        /**
         * 是否忽略空值
         */
        private boolean ignoreNullValues = true;
        
        /**
         * 是否忽略空字符串
         */
        private boolean ignoreEmptyStrings = true;

        public ChainUpdateConfig(LambdaUpdateChainWrapper<T> chainWrapper) {
            this.chainWrapper = chainWrapper;
        }

        /**
         * 添加排除字段
         */
        public ChainUpdateConfig<T> excludeField(String fieldName) {
            excludedFields.add(fieldName);
            return this;
        }

        /**
         * 批量添加排除字段
         */
        public ChainUpdateConfig<T> excludeFields(String... fieldNames) {
            Collections.addAll(excludedFields, fieldNames);
            return this;
        }

        /**
         * 添加字段映射
         */
        public ChainUpdateConfig<T> addMapping(String sourceField, String targetField) {
            fieldMappings.put(sourceField, targetField);
            return this;
        }

        /**
         * 添加字段更新器（必须提供Lambda表达式）
         */
        public <V> ChainUpdateConfig<T> addSetter(String fieldName, SFunction<T, V> setter) {
            fieldSetters.put(fieldName, setter);
            return this;
        }

        /**
         * 设置是否忽略空值
         */
        public ChainUpdateConfig<T> ignoreNullValues(boolean ignore) {
            this.ignoreNullValues = ignore;
            return this;
        }

        /**
         * 设置是否忽略空字符串
         */
        public ChainUpdateConfig<T> ignoreEmptyStrings(boolean ignore) {
            this.ignoreEmptyStrings = ignore;
            return this;
        }

        /**
         * 执行更新
         */
        public boolean update() {
            if (hasUpdate) {
                return chainWrapper.update();
            }
            return false;
        }
    }

    /**
     * 创建链式更新配置
     */
    public static <T> ChainUpdateConfig<T> createChainUpdate(LambdaUpdateChainWrapper<T> chainWrapper) {
        return new ChainUpdateConfig<>(chainWrapper);
    }

    /**
     * 通用字段比较和更新方法
     * 
     * @param sourceData 源数据对象
     * @param targetData 目标数据对象
     * @param config 链式更新配置
     * @return 是否有字段需要更新
     */
    public static <T> boolean compareAndUpdate(Object sourceData, Object targetData, ChainUpdateConfig<T> config) {
        if (sourceData == null || targetData == null || config == null) {
            return false;
        }

        try {
            // 获取源对象和目标对象的所有字段
            Field[] sourceFields = getAllFields(sourceData.getClass());
            Field[] targetFields = getAllFields(targetData.getClass());
            
            // 创建目标字段映射，便于快速查找
            Map<String, Field> targetFieldMap = new HashMap<>();
            for (Field field : targetFields) {
                targetFieldMap.put(field.getName(), field);
            }

            boolean hasUpdate = false;

            // 遍历源对象字段
            for (Field sourceField : sourceFields) {
                String sourceFieldName = sourceField.getName();
                
                // 跳过排除的字段
                if (config.getExcludedFields().contains(sourceFieldName)) {
                    continue;
                }

                // 获取目标字段名（考虑字段映射）
                String targetFieldName = config.getFieldMappings().getOrDefault(sourceFieldName, sourceFieldName);
                
                // 获取目标字段
                Field targetField = targetFieldMap.get(targetFieldName);
                if (targetField == null) {
                    continue;
                }

                // 获取字段值
                Object sourceValue = getFieldValue(sourceData, sourceField);
                Object targetValue = getFieldValue(targetData, targetField);

                // 判断是否需要更新
                if (shouldUpdate(sourceValue, targetValue, config)) {
                    // 获取字段更新器
                    SFunction<T, ?> setter = config.getFieldSetters().get(targetFieldName);
                    if (setter != null) {
                        // 使用Lambda表达式更新
                        updateFieldWithLambda(config.getChainWrapper(), sourceValue, setter);
                        hasUpdate = true;
                        log.debug("更新字段: {} = {}", targetFieldName, sourceValue);
                    } else {
                        log.warn("字段 {} 没有配置对应的Lambda表达式更新器，跳过更新", targetFieldName);
                    }
                }
            }

            config.setHasUpdate(hasUpdate);
            return hasUpdate;

        } catch (Exception e) {
            log.error("链式更新过程中发生错误", e);
            return false;
        }
    }

    /**
     * 简化的更新方法（需要预先配置字段更新器）
     */
    public static <T> ChainUpdateConfig<T> compareAndUpdateSimple(Object sourceData, Object targetData, 
                                                                LambdaUpdateChainWrapper<T> chainWrapper) {
        ChainUpdateConfig<T> config = createChainUpdate(chainWrapper);
        compareAndUpdate(sourceData, targetData, config);
        return config;
    }

    /**
     * 带字段映射和更新器的更新方法
     */
    public static <T> ChainUpdateConfig<T> compareAndUpdateWithSetters(Object sourceData, Object targetData,
                                                                     LambdaUpdateChainWrapper<T> chainWrapper,
                                                                     Map<String, SFunction<T, ?>> fieldSetters,
                                                                     String... excludeFields) {
        ChainUpdateConfig<T> config = createChainUpdate(chainWrapper)
                .excludeFields(excludeFields);
        
        // 添加字段更新器
        fieldSetters.forEach(config::addSetter);
        
        compareAndUpdate(sourceData, targetData, config);
        return config;
    }

    /**
     * 获取对象的所有字段（包括父类字段）
     */
    private static Field[] getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;

        while (currentClass != null && currentClass != Object.class) {
            Field[] declaredFields = currentClass.getDeclaredFields();
            Collections.addAll(fields, declaredFields);
            currentClass = currentClass.getSuperclass();
        }

        return fields.toArray(new Field[0]);
    }

    /**
     * 获取字段值
     */
    private static Object getFieldValue(Object obj, Field field) {
        try {
            field.setAccessible(true);
            return field.get(obj);
        } catch (Exception e) {
            log.warn("获取字段值失败: {}.{}", obj.getClass().getSimpleName(), field.getName(), e);
            return null;
        }
    }

    /**
     * 判断是否需要更新字段
     */
    private static boolean shouldUpdate(Object sourceValue, Object targetValue, ChainUpdateConfig<?> config) {
        // 如果忽略空值且源值为空，则不更新
        if (config.isIgnoreNullValues() && sourceValue == null) {
            return false;
        }

        // 如果忽略空字符串且源值为空字符串，则不更新
        if (config.isIgnoreEmptyStrings() && sourceValue instanceof String && 
            CharSequenceUtil.isBlank((String) sourceValue)) {
            return false;
        }

        // 比较值是否不同
        return !Objects.equals(sourceValue, targetValue);
    }

    /**
     * 使用Lambda表达式更新字段
     */
    @SuppressWarnings("unchecked")
    private static <T, V> void updateFieldWithLambda(LambdaUpdateChainWrapper<T> chainWrapper, 
                                                    Object value, SFunction<T, ?> setter) {
        chainWrapper.set((SFunction<T, V>) setter, (V) value);
    }
}
