package com.boryou.web.module.drill.enums;

import lombok.Getter;
import lombok.RequiredArgsConstructor;

import java.util.HashMap;
import java.util.Map;

@Getter
@RequiredArgsConstructor
public enum RoleEnum {

    RED_CAP("红队队长", "redCap", TeamTypeEnum.RED.getCode()),
    RED_MEM("红队队员", "redMem", TeamTypeEnum.RED.getCode()),
    BLUE_CAP("蓝队队长", "blueCap", TeamTypeEnum.BLUE.getCode()),
    BLUE_MEM("蓝队队员", "blueMem", TeamTypeEnum.BLUE.getCode()),
    MODERATOR("主持人", "moderator", null),
    SPECTATOR("观众", "spectator", null),
    EXPERT("专家", "expert", null),
    ;

    private static final Map<String, RoleEnum> map = new HashMap<>();

    static {
        RoleEnum[] ens = RoleEnum.values();
        for (RoleEnum en : ens) {
            map.put(en.code, en);
        }
    }

    private final String name;
    private final String code;
    private final Integer teamType;

    public static RoleEnum getEnumByCode(String code) {
        return map.get(code);
    }

}
