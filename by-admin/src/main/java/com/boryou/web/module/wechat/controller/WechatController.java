package com.boryou.web.module.wechat.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.web.module.wechat.domain.WarnCode;
import com.boryou.web.module.wechat.domain.vo.StatusVO;
import com.boryou.web.module.wechat.domain.vo.WeChatRequest;
import com.boryou.web.module.wechat.domain.vo.WechatCodeVO;
import com.boryou.web.module.wechat.service.WechatMsgService;
import com.boryou.web.module.wechat.service.WxMsgService;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

@Slf4j
@RestController
@RequestMapping("/wechat")
public class WechatController {
    @Resource
    private WechatMsgService wechatMsgService;
    @Resource
    private WxMsgService wxMsgService;

    @PostMapping("/getWarnCode")
    public AjaxResult getWarnCode(@RequestBody WarnCode warnCode) {
        WechatCodeVO wechatCodeVO = wechatMsgService.handleWarnReq(warnCode);
        return AjaxResult.success(wechatCodeVO);
    }

    @PostMapping("/getLoginCode")
    public AjaxResult getLoginCode(@RequestBody WarnCode warnCode) {
        WechatCodeVO wechatCodeVO = wechatMsgService.handleLoginReq(warnCode);
        return AjaxResult.success(wechatCodeVO);
    }

    @PostMapping("/checkScanStatus")
    public AjaxResult checkScanStatus(@RequestBody WarnCode warnCode) {
        StatusVO statusVO = wechatMsgService.checkScanStatus(warnCode);
        return AjaxResult.success(statusVO);
    }

    @PostMapping("/openId")
    public String openId(@RequestBody WeChatRequest weChatRequest) {
        log.warn("WechatController.openId 传参: {}", weChatRequest);
        return wxMsgService.openId(weChatRequest);
    }

}
