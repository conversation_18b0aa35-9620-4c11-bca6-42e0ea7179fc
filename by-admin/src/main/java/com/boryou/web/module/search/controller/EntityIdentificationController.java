package com.boryou.web.module.search.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.domain.vo.InfoVO;
import com.boryou.web.module.search.service.EntityIdentificationService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;

@Slf4j
@RestController
@RequiredArgsConstructor
public class EntityIdentificationController {

    private final EntityIdentificationService entityIdentificationService;

    @PostMapping("/entityIdentification/entityOption")
    public AjaxResult entityOption(@RequestBody InfoVO infoVO) {
        SysUser user = SecurityUtils.getLoginUser().getUser();
        Map<String, Object> entityOption = entityIdentificationService.getEntityOption(infoVO, user);
        return AjaxResult.success(entityOption);
    }

}
