package com.boryou.web.module.home.entity.vo;

import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-06-17 15:15
 */
@Data
public class MediaLevelVO {
    private String siteName;
    private Integer siteNum;
    private String mediaName;
    private Integer mediaNum;
    private String hostDown;

    public static int countNonNullRecords(List<MediaLevelVO> list) {
        int count = 0;
        for (MediaLevelVO vo : list) {
            if (vo.getSiteName()!= null &&!vo.getSiteName().isEmpty()) {
                count++;
            }
            if (vo.getMediaName()!= null &&!vo.getMediaName().isEmpty()) {
                count++;
            }
        }
        return count;
    }
}

