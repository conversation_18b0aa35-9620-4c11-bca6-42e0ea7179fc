package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.Size;
import java.util.Date;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrillTaskVO {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    @NotBlank(message = "任务标题不能为空")
    @Size(max = 255, message = "标题长度不能超过255字符")
    private String taskTitle;

    @NotBlank(message = "任务内容不能为空")
    @Size(max = 1000, message = "内容长度不能超过1000字符")
    private String taskContent;

    @NotBlank(message = "演练事件不能为空")
    private String drillEvent;

    // @FutureOrPresent(message = "演练日期不能早于当前日期")
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date estimateDrillTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date drillStartTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date drillEndTime;

    @NotBlank(message = "蓝方队长不能为空")
    private String blueCaptain;

    @NotEmpty(message = "蓝方队员不能为空")
    private List<String> blueMember;

    private String blueScore;

    @NotBlank(message = "红方队长不能为空")
    private String redCaptain;

    @NotEmpty(message = "红方队员不能为空")
    private List<String> redMember;

    private SysUserSimpleVO blueCaptainUser;
    private List<SysUserSimpleVO> blueMemberUser;
    private SysUserSimpleVO redCaptainUser;
    private List<SysUserSimpleVO> redMemberUser;

    private String redScore;

    /**
     * 红队阶段得分 例如红队发表40个评论蓝队发表60个评论
     * 分数计算公式：红队（40/100=40%）蓝队（60/100=60%）
     */
    private String redStageScore;

    /**
     * 蓝队阶段得分 例如红队发表40个评论蓝队发表60个评论
     * 分数计算公式：红队（40/100=40%）蓝队（60/100=60%）
     */
    private String blueStageScore;

    /**
     * 1: 未开始 2:进行中 3:已结束
     */
    private Integer status;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long deptId;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 更新人
     */
    private String updateBy;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 角色信息 红队队长: redCap 红队队员: redMem 蓝队队长: blueCap 蓝队队员: blueMem 主持人: moderator
     */
    private String roleInfo;

    private List<DrillProcessStageDTO> drillProcessStages;

    private Integer pageNum;

    private Integer pageSize;

}
