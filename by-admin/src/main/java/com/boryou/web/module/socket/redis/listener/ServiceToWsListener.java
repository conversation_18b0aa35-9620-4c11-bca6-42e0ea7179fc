package com.boryou.web.module.socket.redis.listener;

import com.boryou.web.module.socket.domain.TransferMessage;
import com.boryou.web.module.socket.redis.handler.ServiceToWsMsgHandler;
import com.boryou.web.util.RedisStaticUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.redis.connection.Message;
import org.springframework.data.redis.connection.MessageListener;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * redis消息listener, 用于service to websocket 消息的推送
 *
 * <AUTHOR>
 * @since 2023/9/8 10:12
 */
@Component
@Slf4j
@RequiredArgsConstructor
public class ServiceToWsListener implements MessageListener {

    private final List<ServiceToWsMsgHandler> serviceToWsMsgHandlers;

    @Override
    public void onMessage(Message message, byte[] pattern) {
        TransferMessage tm = RedisStaticUtils.deserializeMessageStrict(message.getBody(), TransferMessage.class);
        // TransferMessage tm = (TransferMessage) redisTemplate.getValueSerializer().deserialize(message.getBody());
        // 调用所有实现了TransferMessageHandler接口的处理器
        for (ServiceToWsMsgHandler handler : serviceToWsMsgHandlers) {
            handler.handleTransferMessage(tm);
        }
    }

}
