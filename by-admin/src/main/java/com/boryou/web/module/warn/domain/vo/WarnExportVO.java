package com.boryou.web.module.warn.domain.vo;

import com.boryou.common.annotation.Excel;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @description ES数据导出
 * @date 2024/4/22 16:38
 */

@Data
@JsonIgnoreProperties(ignoreUnknown = true)
public class WarnExportVO implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 标题*
     */
    @Excel(name = "标题")
    private String title;

    /**
     * 正文*
     */
    @Excel(name = "正文")
    private String text;

    /**
     * 媒体类型名称
     */
    @Excel(name = "媒体类型")
    private String typeName;

    /**
     * 发文时间*
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @Excel(name = "发文时间", width = 30, dateFormat = "yyyy-MM-dd HH:mm:ss")
    private Date publishTime;

    /**
     * 原文链接*
     */
    @Excel(name = "原文链接")
    private String url;

    /**
     * host*
     */
    @Excel(name = "站点域名")
    private String host;

    /**
     * (账号/作者)昵称*
     */
    @Excel(name = "作者")
    private String author;

//    /**
//     * 站点地域
//     */
//    @Excel(name = "站点地域")
//    private String siteAreaCodeName;

    /**
     * 站点标签
     */
    @Excel(name = "站点标签")
    private String siteMeta;

    /**
     * 情感标识
     */
    @Excel(name = "情感标识")
    private String emotionFlag;

    /**
     * 是否为原创
     */
    @Excel(name = "原创")
    private String isOriginal;

    /**
     * 是否被标记为垃圾内容*
     */
    @Excel(name = "噪音")
    private String isSpam;


}
