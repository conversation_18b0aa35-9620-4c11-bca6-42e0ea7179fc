package com.boryou.web.module.area.entity;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

/**
 * <AUTHOR>
 */
@Data
@TableName("by_areas")
public class Area {

    @JsonSerialize(using = ToStringSerializer.class)
    @TableId(type = IdType.NONE)
    private int id;
    private String shortName;
    private String areaName;
    private int parentId;
    private double lng;
    private double lat;
    private int level;
    private String code;
}
