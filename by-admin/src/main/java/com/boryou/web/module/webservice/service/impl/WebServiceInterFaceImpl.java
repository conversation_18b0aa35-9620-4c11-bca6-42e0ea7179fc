package com.boryou.web.module.webservice.service.impl;

import com.boryou.web.module.webservice.domain.JhRequest;
import com.boryou.web.module.webservice.service.*;
import com.boryou.web.module.webservice.util.XmlUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.jws.WebService;

import static com.boryou.web.module.webservice.util.XmlUtil.returnXMLStr;

@WebService(serviceName = "Webservice", targetNamespace = "http://bjgoodwillcis.com/", endpointInterface = "com.boryou.web.module.webservice.service.WebServiceInterFace")
@Component("/Webservice")
@RequiredArgsConstructor
public class WebServiceInterFaceImpl implements WebServiceInterFace {

    private final JhRequestService requestService;
    private final JhUserService userService;
    private final JhOrgService orgService;
    private final JhDeptService deptService;

    @Override
    public String synUserInfo(String str) {
        // todo 校验 token
        // 去除多余的字符
        str = cn.hutool.core.util.XmlUtil.cleanInvalid(str);
        str = str.replaceAll("\n", "");

        // 保存请求参数字符串
        JhRequest jhRequest = new JhRequest(str);
        boolean save = requestService.save(jhRequest);
        String res;
        if (save) {
            res = userService.synUserInfo(str);
        } else {
            res = XmlUtil.returnXMLStr(false);
        }
        // 更新返回值
        jhRequest.setResult(res);
        requestService.saveOrUpdate(jhRequest);
        return res;
    }

    @Override
    public String synOrgInfo(String str) {
        // todo 校验 token
        // 去除多余的字符
        str = cn.hutool.core.util.XmlUtil.cleanInvalid(str);
        str = str.replaceAll("\n", "");

        // 保存请求参数字符串
        JhRequest jhRequest = new JhRequest(str);
        boolean save = requestService.save(jhRequest);
        String res;
        if (save) {
            res = orgService.synOrgInfo(str);
        } else {
            res = XmlUtil.returnXMLStr(false);
        }
        // 更新返回值
        jhRequest.setResult(res);
        requestService.saveOrUpdate(jhRequest);
        return res;
    }

    @Override
    public String synDeptInfo(String str) {
        // todo 校验 token
        // 去除多余的字符
        str = cn.hutool.core.util.XmlUtil.cleanInvalid(str);
        str = str.replaceAll("\n", "");

        // 保存请求参数字符串
        JhRequest jhRequest = new JhRequest(str);
        boolean save = requestService.save(jhRequest);
        String res;
        if (save) {
            res = deptService.synDeptInfo(str);
        } else {
            res = XmlUtil.returnXMLStr(false);
        }
        // 更新返回值
        jhRequest.setResult(res);
        requestService.saveOrUpdate(jhRequest);
        return res;
    }


}
