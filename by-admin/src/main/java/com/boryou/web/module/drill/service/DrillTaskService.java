package com.boryou.web.module.drill.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.collection.CollUtil;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.text.CharSequenceUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.core.domain.vo.SysUserVO;
import com.boryou.common.exception.CustomException;
import com.boryou.system.service.ISysDeptService;
import com.boryou.system.service.ISysUserService;
import com.boryou.web.module.drill.domain.DrillProcessStage;
import com.boryou.web.module.drill.domain.DrillStage;
import com.boryou.web.module.drill.domain.DrillTask;
import com.boryou.web.module.drill.domain.vo.DrillProcessStageDTO;
import com.boryou.web.module.drill.domain.vo.DrillTaskVO;
import com.boryou.web.module.drill.domain.vo.SysUserSimpleVO;
import com.boryou.web.module.drill.enums.RoleEnum;
import com.boryou.web.module.drill.enums.StageStatusEnum;
import com.boryou.web.module.drill.mapper.DrillTaskMapper;
import com.boryou.web.module.drill.util.UpdateUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class DrillTaskService extends ServiceImpl<DrillTaskMapper, DrillTask> {

    private final ISysDeptService deptService;
    private final ISysUserService userService;
    private final DrillWebsocketService drillWebsocketService;
    private final DrillStageService drillStageService;
    private final DrillProcessStageService drillProcessStageService;

    public void saveUpset(DrillTaskVO drillTaskVO, SysUser user) {
        if (drillTaskVO == null) {
            throw new CustomException("数据不能为空");
        }
        DateTime date = DateUtil.date();
        DrillTask drillTaskUpset = BeanUtil.copyProperties(drillTaskVO, DrillTask.class);
        Long drillTaskId = drillTaskVO.getDrillTaskId();
        if (drillTaskId != null) {
            DrillTask drillTaskOne = this.lambdaQuery()
                    .eq(DrillTask::getDrillTaskId, drillTaskId)
                    .eq(DrillTask::getDelFlag, 0).one();

            if (drillTaskOne == null) {
                throw new CustomException("数据不存在");
            }
            Integer status = drillTaskOne.getStatus();
            if (status == 2 || status == 3) {
                throw new CustomException("演练进行中或已结束, 无法修改");
            }
            List<String> deptIdList = getDeptIdListByUser(user);
            Long deptId = drillTaskOne.getDeptId();
            String deptIdStr = String.valueOf(deptId);
            if (!deptIdList.contains(deptIdStr)) {
                throw new CustomException("没有操作权限");
            }
            this.buildProcessStage(drillTaskVO, drillTaskId, date);
            this.realUpdate(user, drillTaskUpset, drillTaskOne, date);
            return;
        }
        long snowflakeNextId = IdUtil.getSnowflakeNextId();
        this.buildProcessStage(drillTaskVO, snowflakeNextId, date);
        this.realSave(user, drillTaskUpset, snowflakeNextId, date);

    }

    private void buildProcessStage(DrillTaskVO drillTaskVO, Long drillTaskId, DateTime date) {
        if (drillTaskId == null) {
            return;
        }
        List<DrillProcessStage> drillProcessStageByDrillTaskId = drillProcessStageService.getDrillProcessStageByDrillTaskIdStatus(drillTaskId);

        List<DrillProcessStage> drillProcessStagesBegin = drillProcessStageByDrillTaskId.stream()
                .filter(item -> !Objects.equals(item.getStageStatus(), StageStatusEnum.NOT_BEGIN.getCode()))
                .collect(Collectors.toList());
        if (CollUtil.isNotEmpty(drillProcessStagesBegin)) {
            throw new CustomException("无法修改阶段");
        }

        List<DrillProcessStageDTO> drillStageList = drillTaskVO.getDrillProcessStages();
        // 新增任务时即新增阶段
        if (CollUtil.isEmpty(drillStageList)) {
            if (CollUtil.isNotEmpty(drillProcessStageByDrillTaskId)) {
                return;
            }
            List<DrillStage> drillStageListSql = drillStageService.getStageWithThrow();
            drillStageList = BeanUtil.copyToList(drillStageListSql, DrillProcessStageDTO.class);
        }
        List<DrillProcessStage> drillProcessStageList = new ArrayList<>();
        //若为未开始时备份所有阶段
        for (DrillProcessStageDTO drillStage : drillStageList) {
            Long stageId = drillStage.getStageId();
            Long processStageId = drillStage.getProcessStageId();
            String stageName = drillStage.getStageName();
            Integer stageOrder = drillStage.getStageOrder();
            Integer timerType = drillStage.getTimerType();
            Integer timerDuration = drillStage.getTimerDuration();
            Integer stageType = drillStage.getStageType();
            Integer scoreType = drillStage.getScoreType();
            Boolean stageShow = drillStage.getStageShow();
            Date createTime = drillStage.getCreateTime();

            DrillProcessStage drillProcessStage = new DrillProcessStage();

            if (processStageId == null) {
                processStageId = IdUtil.getSnowflakeNextId();
            }
            if (createTime == null) {
                createTime = date;
            }

            drillProcessStage.setProcessStageId(processStageId);
            drillProcessStage.setDrillTaskId(drillTaskId);
            drillProcessStage.setStageName(stageName);
            drillProcessStage.setStageOrder(stageOrder);
            drillProcessStage.setTimerType(timerType);
            drillProcessStage.setStageType(stageType);
            drillProcessStage.setTimerDuration(timerDuration);
            drillProcessStage.setCreateTime(createTime);
            drillProcessStage.setUpdateTime(date);
            drillProcessStage.setStageStatus(1);
            drillProcessStage.setScoreType(scoreType);
            drillProcessStage.setStageId(stageId);
            drillProcessStage.setStageShow(stageShow);

            drillProcessStageList.add(drillProcessStage);
        }
        drillProcessStageService.saveOrUpdateBatch(drillProcessStageList);
    }

    public void realSave(SysUser user, DrillTask drillTaskUpset, long snowflakeNextId, DateTime date) {
        Long userId = user.getUserId();
        String userName = user.getUserName();
        Long deptId = user.getDeptId();
        drillTaskUpset.setDrillTaskId(snowflakeNextId);
        drillTaskUpset.setStatus(1);
        drillTaskUpset.setDelFlag(0);
        drillTaskUpset.setUserId(userId);
        drillTaskUpset.setDeptId(deptId);
        drillTaskUpset.setCreateBy(userName);
        drillTaskUpset.setCreateTime(date);
        drillTaskUpset.setUpdateBy(userName);
        drillTaskUpset.setUpdateTime(date);
        this.save(drillTaskUpset);
    }

    public void realUpdate(SysUser user, DrillTask drillTaskSrc, DrillTask drillTaskTarget, DateTime date) {
        this.mergeFrom(drillTaskSrc, drillTaskTarget);
        String userName = user.getUserName();
        drillTaskTarget.setUpdateBy(userName);
        drillTaskTarget.setUpdateTime(date);
        this.updateById(drillTaskTarget);
    }

    public Page<DrillTaskVO> drillTaskQuery(DrillTaskVO drillTaskVO, SysUser user) {
        List<String> deptIdList = getDeptIdListByUser(user);
        String taskTitle = drillTaskVO.getTaskTitle();

        Integer pageNum = drillTaskVO.getPageNum();
        Integer pageSize = drillTaskVO.getPageSize();
        if (pageNum == null || pageNum <= 0) {
            pageNum = 1;
        }
        if (pageSize == null || pageSize <= 0) {
            pageSize = 10;
        }
        Page<DrillTask> page = new Page<>(pageNum, pageSize);
        Page<DrillTask> drillTaskPage = this.lambdaQuery()
                .eq(DrillTask::getDelFlag, 0)
                .in(DrillTask::getDeptId, deptIdList)
                .like(CharSequenceUtil.isNotBlank(taskTitle), DrillTask::getTaskTitle, taskTitle)
                .orderByDesc(DrillTask::getUpdateTime)
                .page(page);

        List<DrillTask> records = drillTaskPage.getRecords();
        List<DrillTaskVO> drillTaskPageRecords = new ArrayList<>();
        for (DrillTask drillTask : records) {
            String roleInfoByUser = drillWebsocketService.getRoleInfoByUser(user, drillTask);
            DrillTaskVO drillTaskRes = BeanUtil.copyProperties(drillTask, DrillTaskVO.class);
            drillTaskRes.setRoleInfo(roleInfoByUser);
            drillTaskPageRecords.add(drillTaskRes);
        }

        Page<DrillTaskVO> pageRes = new Page<>();
        BeanUtil.copyProperties(drillTaskPage, pageRes);
        pageRes.setRecords(drillTaskPageRecords);

        return pageRes;
    }

    public void mergeFrom(DrillTask src, DrillTask target) {
        String srcTaskTitle = src.getTaskTitle();
        String srcTaskContent = src.getTaskContent();
        String srcExerciseEvent = src.getDrillEvent();
        Date srcEstimateExerciseTime = src.getEstimateDrillTime();
        Date srcExerciseStartTime = src.getDrillStartTime();
        Date srcExerciseEndTime = src.getDrillEndTime();
        String srcBlueCaptain = src.getBlueCaptain();
        List<String> srcBlueMember = src.getBlueMember();
        String srcBlueScore = src.getBlueScore();
        String srcRedCaptain = src.getRedCaptain();
        List<String> srcRedMember = src.getRedMember();
        String srcRedScore = src.getRedScore();
        Integer srcStatus = src.getStatus();
        Integer srcDelFlag = src.getDelFlag();
        Long srcUserId = src.getUserId();
        Long srcDeptId = src.getDeptId();
        String srcUpdateBy = src.getUpdateBy();

        // 预加载当前对象所有字段值
        String currentTaskTitle = target.getTaskTitle();
        String currentTaskContent = target.getTaskContent();
        String currentExerciseEvent = target.getDrillEvent();
        Date currentEstimateExerciseTime = target.getEstimateDrillTime();
        Date currentExerciseStartTime = target.getDrillStartTime();
        Date currentExerciseEndTime = target.getDrillEndTime();
        String currentBlueCaptain = target.getBlueCaptain();
        List<String> currentBlueMember = target.getBlueMember();
        String currentBlueScore = target.getBlueScore();
        String currentRedCaptain = target.getRedCaptain();
        List<String> currentRedMember = target.getRedMember();
        String currentRedScore = target.getRedScore();
        Integer currentStatus = target.getStatus();
        Integer currentDelFlag = target.getDelFlag();
        Long currentUserId = target.getUserId();
        Long currentDeptId = target.getDeptId();
        String currentUpdateBy = target.getUpdateBy();

        // 批量字段更新判断
        // 字符串类型字段
        UpdateUtil.updateStringField(srcTaskTitle, currentTaskTitle, target::setTaskTitle);
        UpdateUtil.updateStringField(srcTaskContent, currentTaskContent, target::setTaskContent);
        UpdateUtil.updateStringField(srcExerciseEvent, currentExerciseEvent, target::setDrillEvent);
        UpdateUtil.updateStringField(srcBlueCaptain, currentBlueCaptain, target::setBlueCaptain);
        UpdateUtil.updateStringField(srcRedCaptain, currentRedCaptain, target::setRedCaptain);

        // 日期类型字段
        UpdateUtil.updateDateField(srcEstimateExerciseTime, currentEstimateExerciseTime, target::setEstimateDrillTime);
        // UpdateUtil.updateDateField(srcExerciseStartTime, currentExerciseStartTime, target::setExerciseStartTime);
        // UpdateUtil.updateDateField(srcExerciseEndTime, currentExerciseEndTime, target::setExerciseEndTime);

        // 列表类型字段（带深拷贝）
        UpdateUtil.updateListField(srcBlueMember, currentBlueMember, target::setBlueMember);
        UpdateUtil.updateListField(srcRedMember, currentRedMember, target::setRedMember);

        // 数值类型字段
        UpdateUtil.updateStringField(srcBlueScore, currentBlueScore, target::setBlueScore);
        UpdateUtil.updateStringField(srcRedScore, currentRedScore, target::setRedScore);
        // UpdateUtil.updateNumericField(srcStatus, currentStatus, target::setStatus);
        // UpdateUtil.updateNumericField(srcDelFlag, currentDelFlag, target::setDelFlag);
        UpdateUtil.updateNumericField(srcUserId, currentUserId, target::setUserId);
        UpdateUtil.updateNumericField(srcDeptId, currentDeptId, target::setDeptId);

    }

    public List<SysUserVO> drillTaskUser(DrillTaskVO drillTaskVO, SysUser user) {
        List<String> deptIdList = getDeptIdListByUser(user);
        if (CollUtil.isEmpty(deptIdList)) {
            return Collections.emptyList();
        }
        List<SysUser> sysUsers = userService.selectDeptUserByDeptIds(deptIdList);
        if (CollUtil.isEmpty(sysUsers)) {
            return Collections.emptyList();
        }
        sysUsers = sysUsers.stream().filter(u -> u.getRoles().stream()
                        .noneMatch(i -> i.getRoleKey().equals(RoleEnum.MODERATOR.getCode())))
                .collect(Collectors.toList());
        if (CollUtil.isEmpty(sysUsers)) {
            return Collections.emptyList();
        }
        return BeanUtil.copyToList(sysUsers, SysUserVO.class);
    }

    public DrillTaskVO drillTaskQueryOne(Long drillTaskId, SysUser user) {
        DrillTask drillTask = getDrillTaskByIdWithThrow(drillTaskId);
        List<String> deptIdList = getAllDeptIdWithThrow(user);
        return drillTaskQueryOne(drillTask, deptIdList, user);
    }

    public DrillTaskVO drillTaskQueryOne(DrillTask drillTask, List<String> deptIdList, SysUser user) {
        Long deptId = drillTask.getDeptId();
        if (!deptIdList.contains(String.valueOf(deptId))) {
            throw new CustomException("没有权限");
        }
        String roleInfoByUser = drillWebsocketService.getRoleInfoByUser(user, drillTask);

        List<String> users = drillWebsocketService.getToUsers(drillTask);
        List<SysUser> userByUserIdList = userService.getUserByUserIdList(users);
        Map<String, SysUserSimpleVO> userMap = CollStreamUtil.toMap(userByUserIdList, item -> String.valueOf(item.getUserId()),
                v -> BeanUtil.copyProperties(v, SysUserSimpleVO.class));
        DrillTaskVO drillTaskCopy = BeanUtil.copyProperties(drillTask, DrillTaskVO.class);

        drillTaskCopy.setRoleInfo(roleInfoByUser);

        String blueCaptain = drillTaskCopy.getBlueCaptain();
        SysUserSimpleVO blueCaptainUser = userMap.get(blueCaptain);
        drillTaskCopy.setBlueCaptainUser(blueCaptainUser);
        List<String> blueMember = drillTaskCopy.getBlueMember();
        List<SysUserSimpleVO> blueMemberUser = CollStreamUtil.toList(blueMember, userMap::get);
        drillTaskCopy.setBlueMemberUser(blueMemberUser);

        String redCaptain = drillTaskCopy.getRedCaptain();
        SysUserSimpleVO redCaptainUser = userMap.get(redCaptain);
        drillTaskCopy.setRedCaptainUser(redCaptainUser);
        List<String> redMember = drillTaskCopy.getRedMember();
        List<SysUserSimpleVO> redMemberUser = CollStreamUtil.toList(redMember, userMap::get);
        drillTaskCopy.setRedMemberUser(redMemberUser);

        return drillTaskCopy;
    }

    public List<String> getAllDeptIdWithThrow(SysUser user) {
        List<String> deptIdList = getDeptIdListByUser(user);
        if (CollUtil.isEmpty(deptIdList)) {
            throw new CustomException("组织信息不能为空");
        }
        return deptIdList;
    }

    public DrillTask getDrillTaskByIdWithThrow(Long drillTaskId) {
        if (drillTaskId == null) {
            throw new CustomException("drillTaskId 不能为空");
        }
        DrillTask drillTask = this.lambdaQuery().eq(DrillTask::getDrillTaskId, drillTaskId).one();
        if (drillTask == null) {
            throw new CustomException("找不到演练任务");
        }
        return drillTask;
    }

    private List<String> getDeptIdListByUser(SysUser user) {
        if (user == null) {
            throw new CustomException("用户不能为空");
        }
        Long deptId = user.getDeptId();
        List<String> deptIdList = deptService.selectDescendants(deptId);
        if (CollUtil.isEmpty(deptIdList)) {
            throw new CustomException("部门不能为空");
        }
        return deptIdList;
    }

}
