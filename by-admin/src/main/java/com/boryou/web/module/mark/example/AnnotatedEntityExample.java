package com.boryou.web.module.mark.example;

import com.boryou.web.module.mark.annotation.ExcludeField;
import com.boryou.web.module.mark.annotation.FieldCompare;
import com.boryou.web.module.mark.annotation.FieldMapping;
import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * 带注解的实体类示例
 * 展示如何使用注解来配置字段映射、排除字段和比较规则
 * 
 * <AUTHOR>
 */
@Data
public class AnnotatedEntityExample {

    /**
     * 主键ID - 排除更新
     */
    @ExcludeField(reason = "主键不参与更新", operations = {ExcludeField.ExcludeType.UPDATE})
    private Long id;

    /**
     * 标题字段 - 启用比较，忽略空白字符
     */
    @FieldCompare(enable = true, trimWhitespace = true, allowNull = false)
    private String title;

    /**
     * 内容字段 - 映射到目标实体的不同字段名
     */
    @FieldMapping(targetField = "content", targetClass = TargetEntity.class)
    @FieldCompare(enable = true, priority = 1)
    private String text;

    /**
     * 用户ID - 映射到目标实体的userId字段
     */
    @FieldMapping(targetField = "userId")
    private String userIdSource;

    /**
     * 价格字段 - 数值比较，设置精度
     */
    @FieldCompare(enable = true, precision = 0.01)
    private Double price;

    /**
     * 状态字段 - 使用自定义比较方法
     */
    @FieldCompare(enable = true, customCompareMethod = "compareStatus")
    private Integer status;

    /**
     * 创建时间 - 完全排除
     */
    @ExcludeField(reason = "系统字段", operations = {ExcludeField.ExcludeType.ALL})
    private Date createTime;

    /**
     * 更新时间 - 仅在更新时排除
     */
    @ExcludeField(reason = "系统维护字段", operations = {ExcludeField.ExcludeType.UPDATE})
    private Date updateTime;

    /**
     * 标签列表 - 启用比较
     */
    @FieldCompare(enable = true, allowNull = true)
    private List<String> tags;

    /**
     * 描述字段 - 忽略大小写比较
     */
    @FieldMapping(targetField = "description", ignoreCase = true)
    @FieldCompare(enable = true, trimWhitespace = true)
    private String desc;

    /**
     * 版本号 - 禁用比较
     */
    @FieldCompare(enable = false)
    private Integer version;

    /**
     * 自定义状态比较方法
     * 注意：这个方法必须是静态的，并且接受两个Object参数
     */
    public static boolean compareStatus(Object sourceValue, Object targetValue) {
        if (sourceValue == null || targetValue == null) {
            return sourceValue != targetValue;
        }
        
        Integer source = (Integer) sourceValue;
        Integer target = (Integer) targetValue;
        
        // 自定义比较逻辑：只有当状态从非激活变为激活时才认为有变化
        return source == 1 && target != 1;
    }

    /**
     * 目标实体类示例
     */
    @Data
    public static class TargetEntity {
        private Long id;
        private String title;
        private String content;  // 对应源实体的text字段
        private String userId;   // 对应源实体的userIdSource字段
        private Double price;
        private Integer status;
        private Date createTime;
        private Date updateTime;
        private List<String> tags;
        private String description;  // 对应源实体的desc字段
        private Integer version;
    }

    /**
     * 使用示例方法
     */
    public static void usageExample() {
        // 创建源对象和目标对象
        AnnotatedEntityExample source = new AnnotatedEntityExample();
        source.setTitle("新标题");
        source.setText("新内容");
        source.setUserIdSource("user123");
        source.setPrice(99.99);
        source.setStatus(1);
        
        TargetEntity target = new TargetEntity();
        target.setTitle("旧标题");
        target.setContent("旧内容");
        target.setUserId("user456");
        target.setPrice(88.88);
        target.setStatus(0);
        
        // 使用反射工具进行比较和更新
        // 注解会自动被识别和应用
        /*
        LambdaUpdateWrapper<TargetEntity> updateWrapper = new LambdaUpdateWrapper<>();
        boolean hasUpdate = FieldUpdateUtil.compareAndUpdateByReflection(
            source, 
            target, 
            FieldUpdateUtil.createReflectionUpdate(updateWrapper)
        );
        
        if (hasUpdate) {
            System.out.println("检测到字段变化，需要更新");
        }
        */
    }
}
