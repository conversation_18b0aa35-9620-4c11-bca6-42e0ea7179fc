package com.boryou.web.module.webservice.service.impl;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.codec.Base64;
import cn.hutool.crypto.SecureUtil;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.constant.UserConstants;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.system.service.ISysUserService;
import com.boryou.web.module.webservice.domain.JhUser;
import com.boryou.web.module.webservice.mapper.JhUserMapper;
import com.boryou.web.module.webservice.service.JhUserService;
import com.boryou.web.module.webservice.util.XmlUtil;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.transaction.interceptor.TransactionAspectSupport;
import org.w3c.dom.Element;

import java.util.HashMap;
import java.util.Map;

import static com.boryou.web.module.webservice.util.XmlUtil.strToElement;
import static com.boryou.web.module.webservice.util.XmlUtil.traverseNodes;

/**
 * <AUTHOR>
 * @date 2024/12/6 下午4:36
 */
@Service
@RequiredArgsConstructor
public class JhUserServiceImpl extends ServiceImpl<JhUserMapper, JhUser> implements JhUserService {

    private final ISysUserService userService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public String synUserInfo(String arg) {

        // todo 校验 token
        try {
            Element element = strToElement(arg);
            Map<String, Object> map = new HashMap<>();
            traverseNodes(element, map);
            JhUser bean = BeanUtil.toBean(map, JhUser.class);

            // 新增，这里新增的时候需要指定id，否则会使用系统的自增自动生成，可能会出现重复的情况
            if (bean.getIdentification().equals("add")) {
                if (this.count(Wrappers.<JhUser>lambdaQuery().eq(JhUser::getUserCode, bean.getUserCode())) == 0) {
                    if (this.save(bean)) {
                        SysUser sysUser = toSysUser(bean);
                        if (UserConstants.NOT_UNIQUE.equals(userService.checkUserNameUnique(sysUser.getUserName()))) {
                            throw new CustomException("新增用户'" + sysUser.getUserName() + "'失败，手机号码已存在");
                        } else if (StringUtils.isNotEmpty(sysUser.getPhonenumber())
                                && UserConstants.NOT_UNIQUE.equals(userService.checkPhoneUnique(sysUser))) {
                            throw new CustomException("新增用户'" + sysUser.getUserName() + "'失败，手机号码已存在");
                        }
                        if (userService.save(sysUser)) {
                            return XmlUtil.returnXMLStr(true);
                        }
                    }
                }
                throw new CustomException("userCode 已存在");
            }
            // 修改
            else if (bean.getIdentification().equals("update")) {
                if (this.updateById(bean)) {
                    SysUser sysUser = toSysUser(bean);
                    if (userService.updateById(sysUser)) {
                        return XmlUtil.returnXMLStr(true);
                    }
                }
                throw new CustomException("更新失败");
            } else if (bean.getIdentification().equals("del")) {
                if (this.removeById(bean.getUserCode())) {
                    return XmlUtil.returnXMLStr(userService.deleteUserById(Long.valueOf(bean.getUserCode())) == 1);
                }
                return XmlUtil.returnXMLStr(false, "删除失败");
            } else {
                return XmlUtil.returnXMLStr(false, "非法接口类型");
            }
        } catch (Exception e) {
            TransactionAspectSupport.currentTransactionStatus().setRollbackOnly();
            return XmlUtil.returnXMLStr(false, e.getMessage());
        }
    }


    private SysUser toSysUser(JhUser bean) {
        SysUser sysUser = new SysUser();
        sysUser.setNickName(bean.getUserName());
        sysUser.setUserName(bean.getUserLoginName());
        // base64 解密 -> md5 加密 -> 加密
        String password = SecureUtil.md5(Base64.decodeStr(bean.getUserPassword()));
        sysUser.setPassword(SecurityUtils.encryptPassword(password));
        sysUser.setPhonenumber(bean.getUserPhone());
        sysUser.setSex(bean.getUserSex() == null ? "2" : bean.getUserSex().equals("男") ? "0" : "1");
        sysUser.setCreateTime(bean.getCreateTime());
        sysUser.setUpdateTime(bean.getModifiedTime());
        sysUser.setDeptId(Long.valueOf(bean.getUserDeptCode()));
        sysUser.setExpireTime(bean.getStopTime());
        sysUser.setUserType("01");
        // sysUser.setCreateBy();
        return sysUser;
    }
}




