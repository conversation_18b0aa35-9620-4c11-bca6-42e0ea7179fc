package com.boryou.web.module.submit;

import com.boryou.common.annotation.Secret;
import com.boryou.common.core.controller.BaseController;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.manage.service.FileService;
import com.boryou.submit.dto.SubmitProcessDTO;
import com.boryou.submit.service.ISubmitProcessService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

/**
 * 报送节点过程记录Controller
 *
 * <AUTHOR>
 * @date 2023-12-28
 */
@RestController
@RequestMapping("/submit/process")
public class SubmitProcessController extends BaseController {
    @Autowired
    private ISubmitProcessService submitProcessService;
    @Autowired
    private FileService fileService;

    /**
     * 查询报送节点过程记录列表
     */
    @GetMapping("/list")
    @Secret(value = SubmitProcessDTO.class)
    public AjaxResult list(SubmitProcessDTO submitProcess) {
        List<SubmitProcessDTO> list = submitProcessService.selectSubmitProcessList(submitProcess);
        return AjaxResult.success(list);
    }
    /*
     *//**
     * 导出报送节点过程记录列表
     *//*
    @Log(title = "报送节点过程记录", businessType = BusinessType.EXPORT)
    @GetMapping("/export")
    public AjaxResult export(SubmitProcessDTO submitProcess)
    {
        List<SubmitProcessDTO> list = submitProcessService.selectSubmitProcessList(submitProcess);
        ExcelUtil<SubmitProcessDTO> util = new ExcelUtil<SubmitProcessDTO>(SubmitProcessDTO.class);
        return util.exportExcel(list, "submit");
    }

    *//**
     * 获取报送节点过程记录详细信息
     *//*
    @GetMapping(value = "/{id}")
    public AjaxResult getInfo(@PathVariable("id") Long id)
    {
        return AjaxResult.success(submitProcessService.selectSubmitProcessById(id));
    }

    *//**
     * 新增报送节点过程记录
     *//*
    @Log(title = "报送节点过程记录", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody SubmitProcess submitProcess)
    {
        return toAjax(submitProcessService.insertSubmitProcess(submitProcess));
    }

    *//**
     * 修改报送节点过程记录
     *//*
    @Log(title = "报送节点过程记录", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody SubmitProcess submitProcess)
    {
        return toAjax(submitProcessService.updateSubmitProcess(submitProcess));
    }

    *//**
     * 删除报送节点过程记录
     *//*
    @Log(title = "报送节点过程记录", businessType = BusinessType.DELETE)
	@DeleteMapping("/{ids}")
    public AjaxResult remove(@PathVariable Long[] ids)
    {
        return toAjax(submitProcessService.deleteSubmitProcessByIds(ids));
    }*/
}
