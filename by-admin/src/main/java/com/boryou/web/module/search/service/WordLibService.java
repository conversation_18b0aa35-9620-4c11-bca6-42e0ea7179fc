package com.boryou.web.module.search.service;

import cn.hutool.json.JSONArray;
import com.boryou.web.module.search.entity.WordLib;
import com.boryou.web.module.search.entity.WordLibType;
import com.boryou.web.module.search.entity.vo.WordLibVO;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WordLibService {
    List<WordLibType> selectWordLib(WordLibType wordLibType);

    List<WordLib> selectWord(WordLib wordLib);

    WordLib selectWordById(Long id);

    int insertWordLib(WordLibType wordLibType);

    int insertWord(WordLib wordLib);

    int updateWordLibById(WordLibType wordLibType);

    int updateWordById(WordLib wordLib);

    int deleteWordLibByIds(String ids);

    int deleteWordByIds(String ids);

    JSONArray selectWordLibTree(WordLibVO wordLib);

    JSONArray selectAreaTree(WordLibVO wordLib);
}
