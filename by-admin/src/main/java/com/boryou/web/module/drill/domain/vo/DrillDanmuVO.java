package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * 倒计时记录实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrillDanmuVO {

    /**
     * 演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    /**
     * 阶段ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processStageId;

    private String content;

}
