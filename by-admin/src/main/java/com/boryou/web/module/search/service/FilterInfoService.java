package com.boryou.web.module.search.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.module.search.entity.FilterInfo;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface FilterInfoService extends IService<FilterInfo> {
    List<FilterInfo> selectFilterInfo(FilterInfo filterInfo);

    FilterInfo selectFilterInfoById(Long id);

    int insertFilterInfo(FilterInfo filterInfo);

    int deleteFilterInfoByIds(String ids);

    int deleteFilterInfoBySize(FilterInfo filterInfo);
}
