package com.boryou.web.module.drill.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.collection.CollStreamUtil;
import cn.hutool.core.convert.Convert;
import cn.hutool.core.date.DateTime;
import cn.hutool.core.date.DateUtil;
import cn.hutool.core.map.MapUtil;
import cn.hutool.core.util.IdUtil;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.core.domain.entity.SysUser;
import com.boryou.web.module.drill.domain.DrillCommentLike;
import com.boryou.web.module.drill.domain.vo.DrillCommentLikePushVO;
import com.boryou.web.module.drill.domain.vo.DrillCommentLikeVO;
import com.boryou.web.module.drill.mapper.DrillCommentLikeMapper;
import com.boryou.web.module.drill.util.LikeRedisUtil;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.*;
import java.util.concurrent.ConcurrentHashMap;

@Slf4j
@Service
@RequiredArgsConstructor
public class DrillCommentLikeService extends ServiceImpl<DrillCommentLikeMapper, DrillCommentLike> {

    private final LikeRedisUtil likeRedisUtil;

    // 用于存储待同步到数据库的点赞记录
    private final Set<String> pendingLikeUpdates = ConcurrentHashMap.newKeySet();

    /**
     * 获取用户点赞的评论ID列表
     * 同时考虑数据库和Redis中的点赞数据
     */
    public List<Long> getLikeId(SysUser user, Long drillTaskId) {
        if (user == null) {
            return Collections.emptyList();
        }
        Long userId = user.getUserId();
        if (userId == null) {
            return Collections.emptyList();
        }

        // 先从数据库获取点赞记录
        List<DrillCommentLike> list = this.lambdaQuery()
                .select(DrillCommentLike::getCommentReplyId, DrillCommentLike::getCommentType)
                .eq(DrillCommentLike::getDrillTaskId, drillTaskId)
                .eq(DrillCommentLike::getUserId, userId)
                .eq(DrillCommentLike::getStatus, 1).list();

        // 将数据库中的点赞记录转换为集合
        Set<Long> likedIds = new HashSet<>(CollStreamUtil.toList(list, DrillCommentLike::getCommentReplyId));

        // 收集所有评论 ID和类型，用于检查Redis数据
        Map<Long, String> commentTypeMap = new HashMap<>();
        for (DrillCommentLike like : list) {
            commentTypeMap.put(like.getCommentReplyId(), Convert.toStr(like.getCommentType()));
        }

        // 如果有待同步的点赞记录，收集这些记录的评论 ID和类型
        if (!pendingLikeUpdates.isEmpty()) {
            for (String pendingKey : pendingLikeUpdates) {
                try {
                    String[] parts = pendingKey.split(":");
                    Long pendingUserId = Long.parseLong(parts[0]);
                    Long commentReplyId = Long.parseLong(parts[1]);
                    String commentType = Convert.toStr(parts[2]);

                    // 只处理当前用户的记录
                    if (userId.equals(pendingUserId)) {
                        commentTypeMap.put(commentReplyId, commentType);
                    }
                } catch (Exception e) {
                    log.error("解析待同步点赞记录异常: {}", pendingKey, e);
                }
            }
        }

        // 检查所有评论的Redis数据是否需要恢复
        for (Map.Entry<Long, String> entry : commentTypeMap.entrySet()) {
            Long commentReplyId = entry.getKey();
            String commentType = entry.getValue();

            try {
                // 检查Redis中的点赞数据是否需要恢复
                boolean needRecover = likeRedisUtil.needRecoverLikeData(userId, commentReplyId, commentType);

                if (needRecover) {
                    // 从数据库恢复Redis数据
                    final Long finalCommentReplyId = commentReplyId; // 在Lambda中使用需要final变量
                    final String finalCommentType = commentType;
                    likeRedisUtil.recoverLikeData(
                            userId,
                            commentReplyId,
                            commentType,
                            // 从数据库获取点赞状态
                            () -> {
                                DrillCommentLike like = this.lambdaQuery()
                                        .eq(DrillCommentLike::getUserId, userId)
                                        .eq(DrillCommentLike::getCommentReplyId, finalCommentReplyId)
                                        .eq(DrillCommentLike::getCommentType, finalCommentType)
                                        .one();
                                return like != null && Objects.equals(like.getStatus(), 1);
                            },
                            // 从数据库获取点赞数量
                            () -> Convert.toLong(this.lambdaQuery()
                                    .eq(DrillCommentLike::getCommentReplyId, finalCommentReplyId)
                                    .eq(DrillCommentLike::getCommentType, finalCommentType)
                                    .eq(DrillCommentLike::getStatus, 1)
                                    .count())
                    );
                    log.info("在获取点赞列表时从数据库恢复了Redis点赞数据。userId={}, commentReplyId={}, commentType={}",
                            userId, commentReplyId, commentType);
                }

                // 获取最新的点赞状态
                Boolean isLiked = likeRedisUtil.getLikeStatus(userId, commentReplyId, commentType);

                // 根据点赞状态更新集合
                if (Boolean.TRUE.equals(isLiked)) {
                    likedIds.add(commentReplyId);
                } else {
                    likedIds.remove(commentReplyId);
                }
            } catch (Exception e) {
                log.error("处理Redis点赞数据异常: commentReplyId={}, commentType={}", commentReplyId, commentType, e);
            }
        }

        return new ArrayList<>(likedIds);
    }

    /**
     * 切换点赞状态（点赞/取消点赞）
     */
    public DrillCommentLikePushVO toggleLike(DrillCommentLikeVO likeVO, SysUser user) {
        Long userId = user.getUserId();
        Long commentId = likeVO.getCommentId();
        Long commentReplyId = likeVO.getCommentReplyId();
        Integer commentType = likeVO.getCommentType();
        String comment = likeVO.getComment();
        String commentTypeStr = Convert.toStr(commentType);

        // 使用Redis切换点赞状态，内部会自动检查并恢复过期的Redis数据
        Map<String, Object> result = likeRedisUtil.toggleLikeStatus(
                userId,
                commentReplyId,
                commentTypeStr,
                // 从数据库获取点赞状态的回调函数
                () -> {
                    DrillCommentLike like = this.lambdaQuery()
                            .eq(DrillCommentLike::getUserId, userId)
                            .eq(DrillCommentLike::getCommentReplyId, commentReplyId)
                            .eq(DrillCommentLike::getCommentType, commentType)
                            .one();
                    return like != null && Objects.equals(like.getStatus(), 1);
                },
                // 从数据库获取点赞数量的回调函数
                () -> Convert.toLong(this.lambdaQuery()
                        .eq(DrillCommentLike::getCommentReplyId, commentReplyId)
                        .eq(DrillCommentLike::getCommentType, commentType)
                        .eq(DrillCommentLike::getStatus, 1)
                        .count())
        );

        boolean isLiked = (boolean) result.get("isLiked");
        Long likeCount = (Long) result.get("likeCount");
        boolean recoveredFromDB = (boolean) result.get("recoveredFromDB");

        if (recoveredFromDB) {
            log.info("在切换点赞状态时从数据库恢复了Redis数据。userId={}, commentReplyId={}, commentType={}",
                    userId, commentReplyId, commentType);
        }

        // 构建返回结果
        DrillCommentLikePushVO pushVO = new DrillCommentLikePushVO();
        BeanUtil.copyProperties(likeVO, pushVO);
        pushVO.setUserId(userId);
        pushVO.setLikeCount(likeCount.intValue());
        pushVO.setStatus(isLiked ? 1 : 0);
        pushVO.setComment(comment);

        // 添加到待同步列表
        String pendingKey = userId + ":" + commentReplyId + ":" + commentType;
        pendingLikeUpdates.add(pendingKey);

        // 通过注入的异步服务调用异步方法，避免直接通过 this 调用
        asyncUpdateLikeInDatabase(likeVO, user, isLiked);

        return pushVO;
    }


    /**
     * 异步更新数据库中的点赞记录
     */
    @Async
    public void asyncUpdateLikeInDatabase(DrillCommentLikeVO likeVO, SysUser user, boolean isLiked) {
        try {
            Long userId = user.getUserId();
            Long commentId = likeVO.getCommentId();
            Long commentReplyId = likeVO.getCommentReplyId();
            Integer commentType = likeVO.getCommentType();
            Long processStageId = likeVO.getProcessStageId();
            Long drillTaskId = likeVO.getDrillTaskId();
            Integer teamType = likeVO.getTeamType();
            String roleInfo = likeVO.getRoleInfo();

            // 查询是否已存在点赞记录
            DrillCommentLike like = this.lambdaQuery()
                    .eq(DrillCommentLike::getCommentReplyId, commentReplyId)
                    .eq(DrillCommentLike::getCommentId, commentId)
                    .eq(DrillCommentLike::getUserId, userId)
                    .eq(DrillCommentLike::getCommentType, commentType)
                    .one();

            DateTime date = DateUtil.date();

            if (like == null) {
                // 新增点赞记录
                long snowflakeNextId = IdUtil.getSnowflakeNextId();
                DrillCommentLike drillCommentLike = new DrillCommentLike();
                drillCommentLike.setCommentLikeId(snowflakeNextId);
                drillCommentLike.setCommentId(commentId);
                drillCommentLike.setCommentReplyId(commentReplyId);
                drillCommentLike.setCommentType(commentType);
                drillCommentLike.setProcessStageId(processStageId);
                drillCommentLike.setDrillTaskId(drillTaskId);
                drillCommentLike.setTeamType(teamType);
                drillCommentLike.setRoleInfo(roleInfo);
                drillCommentLike.setUserId(userId);
                drillCommentLike.setCreateTime(date);
                drillCommentLike.setUpdateTime(date);
                drillCommentLike.setStatus(isLiked ? 1 : 0);

                this.save(drillCommentLike);
            } else {
                // 更新点赞状态
                this.lambdaUpdate()
                        .set(DrillCommentLike::getStatus, isLiked ? 1 : 0)
                        .set(DrillCommentLike::getUpdateTime, date)
                        .eq(DrillCommentLike::getCommentLikeId, like.getCommentLikeId())
                        .update();
            }

            // 从待同步列表中移除
            String pendingKey = userId + ":" + commentReplyId + ":" + commentType;
            pendingLikeUpdates.remove(pendingKey);

        } catch (Exception e) {
            log.error("异步更新点赞状态失败", e);
        }
    }

    /**
     * 定时同步Redis和数据库中的点赞数据
     * 每5分钟执行一次
     */
    @Scheduled(fixedRate = 300000)
    @Transactional(rollbackFor = Exception.class)
    public void syncLikeDataToDatabase() {
        if (pendingLikeUpdates.isEmpty()) {
            return;
        }

        log.info("开始同步点赞数据到数据库，待同步记录数：{}", pendingLikeUpdates.size());

        // 复制一份待处理列表，避免并发修改
        Set<String> processingSet = new HashSet<>(pendingLikeUpdates);

        for (String pendingKey : processingSet) {
            try {
                String[] parts = pendingKey.split(":");
                Long userId = Long.parseLong(parts[0]);
                Long commentReplyId = Long.parseLong(parts[1]);
                String commentType = Convert.toStr(parts[2]);

                // 检查Redis中的点赞数据是否需要恢复
                boolean needRecover = likeRedisUtil.needRecoverLikeData(userId, commentReplyId, commentType);

                if (needRecover) {
                    // 从数据库恢复Redis数据
                    boolean recovered = likeRedisUtil.recoverLikeData(
                            userId,
                            commentReplyId,
                            commentType,
                            // 从数据库获取点赞状态
                            () -> {
                                DrillCommentLike like = this.lambdaQuery()
                                        .eq(DrillCommentLike::getUserId, userId)
                                        .eq(DrillCommentLike::getCommentReplyId, commentReplyId)
                                        .eq(DrillCommentLike::getCommentType, commentType)
                                        .one();
                                return like != null && Objects.equals(like.getStatus(), 1);
                            },
                            // 从数据库获取点赞数量
                            () -> Convert.toLong(this.lambdaQuery()
                                    .eq(DrillCommentLike::getCommentReplyId, commentReplyId)
                                    .eq(DrillCommentLike::getCommentType, commentType)
                                    .eq(DrillCommentLike::getStatus, 1)
                                    .count())
                    );

                    if (recovered) {
                        log.info("定时同步时从数据库恢复了Redis数据。userId={}, commentReplyId={}, commentType={}",
                                userId, commentReplyId, commentType);
                    }
                }

                // 获取Redis中的点赞状态
                Boolean isLiked = likeRedisUtil.getLikeStatus(userId, commentReplyId, commentType);

                // 更新数据库
                this.lambdaUpdate()
                        .set(DrillCommentLike::getStatus, isLiked ? 1 : 0)
                        .set(DrillCommentLike::getUpdateTime, DateUtil.date())
                        .eq(DrillCommentLike::getUserId, userId)
                        .eq(DrillCommentLike::getCommentReplyId, commentReplyId)
                        .eq(DrillCommentLike::getCommentType, commentType)
                        .update();

                // 从待同步列表中移除
                pendingLikeUpdates.remove(pendingKey);

            } catch (Exception e) {
                log.error("同步点赞数据失败: {}", pendingKey, e);
            }
        }

        log.info("点赞数据同步完成，剩余待同步记录数：{}", pendingLikeUpdates.size());
    }

    /**
     * 检查用户是否点赞了评论
     */
    public boolean isLiked(Long userId, Long commentReplyId, String commentType) {
        // 检查Redis中的点赞数据是否需要恢复
        boolean needRecover = likeRedisUtil.needRecoverLikeData(userId, commentReplyId, commentType);

        if (needRecover) {
            // 从数据库恢复Redis数据
            boolean recovered = likeRedisUtil.recoverLikeData(
                    userId,
                    commentReplyId,
                    commentType,
                    // 从数据库获取点赞状态
                    () -> {
                        DrillCommentLike like = this.lambdaQuery()
                                .eq(DrillCommentLike::getUserId, userId)
                                .eq(DrillCommentLike::getCommentReplyId, commentReplyId)
                                .eq(DrillCommentLike::getCommentType, commentType)
                                .one();
                        return like != null && Objects.equals(like.getStatus(), 1);
                    },
                    // 从数据库获取点赞数量
                    () -> Convert.toLong(this.lambdaQuery()
                            .eq(DrillCommentLike::getCommentReplyId, commentReplyId)
                            .eq(DrillCommentLike::getCommentType, commentType)
                            .eq(DrillCommentLike::getStatus, 1)
                            .count())
            );

            if (recovered) {
                log.debug("在检查点赞状态时从数据库恢复了Redis数据。userId={}, commentReplyId={}, commentType={}",
                        userId, commentReplyId, commentType);
            }
        }

        // 从 Redis 中获取点赞状态
        return likeRedisUtil.getLikeStatus(userId, commentReplyId, commentType);
    }

    /**
     * 批量获取用户点赞状态
     */
    public Map<Long, Boolean> batchGetLikeStatus(Long userId, List<Long> commentReplyIds, String commentType) {
        if (commentReplyIds.isEmpty()) {
            return MapUtil.newHashMap();
        }

        Map<Long, Boolean> result = new HashMap<>(commentReplyIds.size());

        // 逐个检查并恢复数据
        for (Long commentReplyId : commentReplyIds) {
            // 检查Redis中的点赞数据是否需要恢复
            boolean needRecover = likeRedisUtil.needRecoverLikeData(userId, commentReplyId, commentType);

            if (needRecover) {
                // 从数据库恢复Redis数据
                final Long finalCommentReplyId = commentReplyId; // 在Lambda中使用需要final变量
                likeRedisUtil.recoverLikeData(
                        userId,
                        commentReplyId,
                        commentType,
                        // 从数据库获取点赞状态
                        () -> {
                            DrillCommentLike like = this.lambdaQuery()
                                    .eq(DrillCommentLike::getUserId, userId)
                                    .eq(DrillCommentLike::getCommentReplyId, finalCommentReplyId)
                                    .eq(DrillCommentLike::getCommentType, commentType)
                                    .one();
                            return like != null && Objects.equals(like.getStatus(), 1);
                        },
                        // 从数据库获取点赞数量
                        () -> Convert.toLong(this.lambdaQuery()
                                .eq(DrillCommentLike::getCommentReplyId, finalCommentReplyId)
                                .eq(DrillCommentLike::getCommentType, commentType)
                                .eq(DrillCommentLike::getStatus, 1)
                                .count())
                );
            }

            // 从 Redis 中获取点赞状态
            boolean isLiked = likeRedisUtil.getLikeStatus(userId, commentReplyId, commentType);
            result.put(commentReplyId, isLiked);
        }

        return result;
    }
}
