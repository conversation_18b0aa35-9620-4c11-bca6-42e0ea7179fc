package com.boryou.web.module.material.service.impl;

import cn.hutool.core.util.ObjectUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.common.utils.StringUtils;
import com.boryou.web.module.material.entity.Material;
import com.boryou.web.module.material.entity.MaterialFolder;
import com.boryou.web.module.material.mapper.MaterialMapper;
import com.boryou.web.module.material.service.MaterialFolderService;
import com.boryou.web.module.material.service.MaterialService;
import com.boryou.web.module.material.vo.MaterialQuery;
import lombok.AllArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

import static com.boryou.common.constant.Constants.STR_TRUE;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:48
 */
@Service
@AllArgsConstructor
public class MaterialServiceImpl extends ServiceImpl<MaterialMapper, Material> implements MaterialService {

    private final MaterialFolderService folderService;

    @Override
    public boolean add(Material material) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        material.setUserId(userId);
        if (material.getContentId() == null || material.getFolderId() == null) {
            throw new CustomException("请选择素材和收藏夹目录");
        }
        // 校验收藏夹是否存在
        MaterialFolder folder = folderService.getById(material.getFolderId());
        if (folder == null) {
            throw new CustomException("收藏夹不存在");
        } else {
            if (folder.getIsGroup().equals(STR_TRUE)) {
                throw new CustomException("不可以直接添加到素材组");
            }
        }

        int count = this.count(Wrappers.<Material>lambdaQuery()
                .eq(Material::getUserId, userId)
                .eq(Material::getContentId, material.getContentId())
                .eq(Material::getFolderId, material.getFolderId())
        );
        if (count > 0) {
            throw new CustomException("已添加过该素材");
        }

        if (material.getSimilarCount() == null) {
            material.setSimilarCount(1);
        }

        return this.save(material);
    }

    @Override
    public boolean addBatch(List<Material> list) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());

        List<Long> collect = list.stream().map(Material::getFolderId).distinct().collect(Collectors.toList());
        if (collect.size() > 1) {
            throw new CustomException("批量新增只能选择一个素材库");
        }

        // 判断是否有重复素材
        int size = (int) list.stream().map(Material::getContentId).distinct().count();
        if (size != list.size()) {
            throw new CustomException("存在重复素材");
        }

        // 校验收藏夹是否存在
        MaterialFolder folder = folderService.getById(collect.get(0));
        if (folder == null) {
            throw new CustomException("收藏夹不存在");
        } else {
            if (folder.getIsGroup().equals(STR_TRUE)) {
                throw new CustomException("不可以直接添加到素材组");
            }
        }

        list.forEach(material -> {
            material.setUserId(userId);
            if (material.getContentId() == null || material.getFolderId() == null) {
                throw new CustomException("请选择素材和收藏夹目录");
            }

            int count = this.count(Wrappers.<Material>lambdaQuery()
                    .eq(Material::getUserId, userId)
                    .eq(Material::getContentId, material.getContentId())
                    .eq(Material::getFolderId, material.getFolderId())
            );
            if (count > 0) {
                throw new CustomException("已添加过该素材");
            }
        });

        return this.saveBatch(list);
    }

    @Override
    public boolean edit(Material material) {
        if (material.getId() == null) {
            throw new CustomException("请选择具体素材");
        }
        return this.updateById(material);
    }

    @Override
    public List<Material> materialList(MaterialQuery query) {
        Long userId = Long.valueOf(SecurityUtils.getUserId());
        LambdaQueryWrapper<Material> lambdaQueryWrapper = Wrappers.<Material>lambdaQuery()
                .eq(Material::getUserId, userId)
                .eq(ObjectUtil.isNotEmpty(query.getFolderId()), Material::getFolderId, query.getFolderId())
                .ge(ObjectUtil.isNotEmpty(query.getStartTime()), Material::getCreateTime, query.getStartTime())
                .le(ObjectUtil.isNotEmpty(query.getEndTime()), Material::getCreateTime, query.getEndTime())
                .eq(ObjectUtil.isNotEmpty(query.getType()), Material::getType, query.getType())
                .orderByDesc(Material::getCreateTime);

        if (StringUtils.isNotEmpty(query.getSearchType())) {
            if ("0".equals(query.getSearchType())) {
                lambdaQueryWrapper.and(x -> x
                        .like(Material::getTitle, query.getSearch())
                        .or()
                        .like(Material::getTitle, query.getSearch())
                );
            }
            if ("1".equals(query.getSearchType())) {
                lambdaQueryWrapper.like(Material::getTitle, query.getSearch());
            }
            if ("2".equals(query.getSearchType())) {
                lambdaQueryWrapper.like(Material::getText, query.getSearch());
            }
        }
        return this.list(lambdaQueryWrapper);

    }


}
