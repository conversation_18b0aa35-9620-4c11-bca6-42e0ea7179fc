package com.boryou.web.module.mark.service;

import cn.hutool.core.bean.BeanUtil;
import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.exception.CustomException;
import com.boryou.common.utils.SecurityUtils;
import com.boryou.web.controller.common.entity.EsBeanMark;
import com.boryou.web.module.mark.domain.Mark;
import com.boryou.web.module.mark.domain.vo.MarkVO;
import com.boryou.web.module.mark.mapper.MarkMapper;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.List;

@Slf4j
@Service
public class MarkService extends ServiceImpl<MarkMapper, Mark> {

    /**
     * 根据文章ID获取标记信息
     *
     * @param markVO 查询参数
     * @return 标记信息
     */
    public MarkVO getMarkByArticleId(MarkVO markVO) {
        String articleId = markVO.getArticleId();
        if (CharSequenceUtil.isBlank(articleId)) {
            throw new CustomException("文章ID不能为空");
        }
        
        LambdaQueryWrapper<Mark> queryWrapper = new LambdaQueryWrapper<Mark>()
                .eq(Mark::getArticleId, articleId)
                .eq(Mark::getStatus, 1);
        
        Mark mark = this.getOne(queryWrapper);
        if (mark == null) {
            return null;
        }
        
        return BeanUtil.copyProperties(mark, MarkVO.class);
    }

    /**
     * 分页查询标记信息
     *
     * @param markVO 查询参数
     * @return 分页结果
     */
    public IPage<MarkVO> getMarkPage(MarkVO markVO) {
        Integer pageNum = markVO.getPageNum() != null ? markVO.getPageNum() : 1;
        Integer pageSize = markVO.getPageSize() != null ? markVO.getPageSize() : 10;
        
        Page<Mark> page = new Page<>(pageNum, pageSize);
        LambdaQueryWrapper<Mark> queryWrapper = buildQueryWrapper(markVO);
        
        IPage<Mark> markPage = this.page(page, queryWrapper);
        
        // 转换为VO
        Page<MarkVO> voPage = new Page<>(pageNum, pageSize, markPage.getTotal());
        List<MarkVO> voList = BeanUtil.copyToList(markPage.getRecords(), MarkVO.class);
        voPage.setRecords(voList);
        
        return voPage;
    }

    /**
     * 添加或更新标记信息
     *
     * @param markVO 标记信息
     * @return 是否成功
     */
    public boolean saveOrUpdateMark(MarkVO markVO) {
        String articleId = markVO.getArticleId();
        if (CharSequenceUtil.isBlank(articleId)) {
            throw new CustomException("文章ID不能为空");
        }
        
        Mark mark = BeanUtil.copyProperties(markVO, Mark.class);
        
        // 查询是否已存在
        LambdaQueryWrapper<Mark> queryWrapper = new LambdaQueryWrapper<Mark>()
                .eq(Mark::getArticleId, articleId);
        Mark existMark = this.getOne(queryWrapper);
        
        if (existMark != null) {
            // 更新
            mark.setId(existMark.getId());
            mark.setUpdateTime(new Date());
            return this.updateById(mark);
        } else {
            // 新增
            mark.setCreateTime(new Date());
            mark.setStatus(1);
            return this.save(mark);
        }
    }

    /**
     * 根据EsBeanMark转换并保存
     *
     * @param esBeanMark ES数据
     * @return 是否成功
     */
    public boolean saveFromEsBeanMark(EsBeanMark esBeanMark) {
        if (esBeanMark == null || CharSequenceUtil.isBlank(esBeanMark.getArticleId())) {
            throw new CustomException("数据不能为空");
        }
        
        Mark mark = convertFromEsBeanMark(esBeanMark);
        
        // 查询是否已存在
        LambdaQueryWrapper<Mark> queryWrapper = new LambdaQueryWrapper<Mark>()
                .eq(Mark::getArticleId, esBeanMark.getArticleId());
        Mark existMark = this.getOne(queryWrapper);
        
        if (existMark != null) {
            // 更新
            mark.setId(existMark.getId());
            mark.setUpdateTime(new Date());
            return this.updateById(mark);
        } else {
            // 新增
            mark.setCreateTime(new Date());
            mark.setStatus(1);
            return this.save(mark);
        }
    }

    /**
     * 批量保存
     *
     * @param esBeanMarkList ES数据列表
     * @return 是否成功
     */
    public boolean saveBatchFromEsBeanMark(List<EsBeanMark> esBeanMarkList) {
        if (esBeanMarkList == null || esBeanMarkList.isEmpty()) {
            return true;
        }
        
        List<Mark> markList = esBeanMarkList.stream()
                .map(this::convertFromEsBeanMark)
                .toList();
        
        return this.saveBatch(markList);
    }

    /**
     * 删除标记（逻辑删除）
     *
     * @param markVO 删除参数
     * @return 是否成功
     */
    public boolean deleteMark(MarkVO markVO) {
        String articleId = markVO.getArticleId();
        if (CharSequenceUtil.isBlank(articleId)) {
            throw new CustomException("文章ID不能为空");
        }
        
        LambdaQueryWrapper<Mark> queryWrapper = new LambdaQueryWrapper<Mark>()
                .eq(Mark::getArticleId, articleId);
        
        Mark mark = new Mark();
        mark.setStatus(0);
        mark.setUpdateTime(new Date());
        
        return this.update(mark, queryWrapper);
    }

    /**
     * 构建查询条件
     */
    private LambdaQueryWrapper<Mark> buildQueryWrapper(MarkVO markVO) {
        LambdaQueryWrapper<Mark> queryWrapper = new LambdaQueryWrapper<>();
        
        queryWrapper.eq(Mark::getStatus, 1); // 只查询有效数据
        
        if (CharSequenceUtil.isNotBlank(markVO.getArticleId())) {
            queryWrapper.eq(Mark::getArticleId, markVO.getArticleId());
        }
        if (markVO.getType() != null) {
            queryWrapper.eq(Mark::getType, markVO.getType());
        }
        if (CharSequenceUtil.isNotBlank(markVO.getTitle())) {
            queryWrapper.like(Mark::getTitle, markVO.getTitle());
        }
        if (CharSequenceUtil.isNotBlank(markVO.getAuthor())) {
            queryWrapper.like(Mark::getAuthor, markVO.getAuthor());
        }
        if (CharSequenceUtil.isNotBlank(markVO.getPlanId())) {
            queryWrapper.eq(Mark::getPlanId, markVO.getPlanId());
        }
        if (CharSequenceUtil.isNotBlank(markVO.getUserId())) {
            queryWrapper.eq(Mark::getUserId, markVO.getUserId());
        }
        if (CharSequenceUtil.isNotBlank(markVO.getDeptId())) {
            queryWrapper.eq(Mark::getDeptId, markVO.getDeptId());
        }
        if (markVO.getDeal() != null) {
            queryWrapper.eq(Mark::getDeal, markVO.getDeal());
        }
        if (markVO.getFollow() != null) {
            queryWrapper.eq(Mark::getFollow, markVO.getFollow());
        }
        if (markVO.getWarned() != null) {
            queryWrapper.eq(Mark::getWarned, markVO.getWarned());
        }
        if (markVO.getReadFlag() != null) {
            queryWrapper.eq(Mark::getReadFlag, markVO.getReadFlag());
        }
        
        queryWrapper.orderByDesc(Mark::getCreateTime);
        
        return queryWrapper;
    }

    /**
     * 将EsBeanMark转换为Mark实体
     */
    private Mark convertFromEsBeanMark(EsBeanMark esBeanMark) {
        Mark mark = new Mark();
        
        mark.setEsBeanMarkId(esBeanMark.getEsBeanMarkId());
        mark.setArticleId(esBeanMark.getArticleId());
        mark.setType(esBeanMark.getType());
        mark.setTypeName(esBeanMark.getTypeName());
        mark.setPublishTime(esBeanMark.getPublishTime());
        mark.setTitle(esBeanMark.getTitle());
        mark.setText(esBeanMark.getText());
        mark.setSummary(esBeanMark.getSummary());
        mark.setUrl(esBeanMark.getUrl());
        mark.setHost(esBeanMark.getHost());
        mark.setDomain(esBeanMark.getDomain());
        mark.setAuthor(esBeanMark.getAuthor());
        mark.setAuthorId(esBeanMark.getAuthorId());
        mark.setAuthorSex(esBeanMark.getAuthorSex());
        mark.setBizId(esBeanMark.getBizId());
        mark.setAccountLevel(esBeanMark.getAccountLevel());
        mark.setSiteAreaCode(esBeanMark.getSiteAreaCode());
        mark.setSiteAreaCodeName(esBeanMark.getSiteAreaCodeName());
        mark.setContentAreaCode(esBeanMark.getContentAreaCode());
        mark.setContentAreaCodeName(esBeanMark.getContentAreaCodeName());
        mark.setSiteMeta(esBeanMark.getSiteMeta());
        mark.setContentMeta(esBeanMark.getContentMeta());
        mark.setFansNum(esBeanMark.getFansNum());
        mark.setReadNum(esBeanMark.getReadNum());
        mark.setCommentNum(esBeanMark.getCommentNum());
        mark.setLikeNum(esBeanMark.getLikeNum());
        mark.setReprintNum(esBeanMark.getReprintNum());
        mark.setSector(esBeanMark.getSector());
        mark.setContentForm(esBeanMark.getContentForm());
        mark.setMd5(esBeanMark.getMd5());
        mark.setSrcCodePath(esBeanMark.getSrcCodePath());
        mark.setCoverUrl(esBeanMark.getCoverUrl());
        mark.setPicUrl(esBeanMark.getPicUrl());
        mark.setAvdUrl(esBeanMark.getAvdUrl());
        mark.setEmotionFlag(esBeanMark.getEmotionFlag());
        mark.setIsOriginal(esBeanMark.getIsOriginal());
        mark.setIsSpam(esBeanMark.getIsSpam());
        mark.setUpdateTime(esBeanMark.getUpdateTime());
        mark.setDay(esBeanMark.getDay());
        mark.setIsRead(esBeanMark.getIsRead());
        mark.setHitWords(esBeanMark.getHitWords());
        mark.setStatus(esBeanMark.getStatus() != null ? esBeanMark.getStatus() : 1);
        mark.setDeal(esBeanMark.getDeal() != null ? esBeanMark.getDeal() : 0);
        mark.setFollow(esBeanMark.getFollow() != null ? esBeanMark.getFollow() : 0);
        mark.setAccountGrade(esBeanMark.getAccountGrade());
        mark.setWarned(esBeanMark.getWarned() != null ? esBeanMark.getWarned() : 0);
        mark.setSubmits(esBeanMark.getSubmits());
        mark.setHitWord(esBeanMark.getHitWord());
        mark.setPlanId(esBeanMark.getPlanId());
        mark.setUserId(esBeanMark.getUserId());
        mark.setDeptId(esBeanMark.getDeptId());
        mark.setReadFlag(esBeanMark.getReadFlag() != null ? esBeanMark.getReadFlag() : 0);
        mark.setUsers(esBeanMark.getUsers());
        
        return mark;
    }
}
