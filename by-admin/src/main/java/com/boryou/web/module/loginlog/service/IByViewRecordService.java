package com.boryou.web.module.loginlog.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.module.loginlog.domain.ByViewRecord;

import java.util.List;


/**
 * h5登录日志查看记录Service接口
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
public interface IByViewRecordService extends IService<ByViewRecord> {
    /**
     * 查询h5登录日志查看记录
     *
     * @param id h5登录日志查看记录ID
     * @return h5登录日志查看记录
     */
    public ByViewRecord selectByViewRecordById(Long id);

    /**
     * 查询h5登录日志查看记录列表
     *
     * @param byViewRecord h5登录日志查看记录
     * @return h5登录日志查看记录集合
     */
    public List<ByViewRecord> selectByViewRecordList(ByViewRecord byViewRecord);

    /**
     * 新增h5登录日志查看记录
     *
     * @param byViewRecord h5登录日志查看记录
     * @return 结果
     */
    public int insertByViewRecord(ByViewRecord byViewRecord);

    /**
     * 修改h5登录日志查看记录
     *
     * @param byViewRecord h5登录日志查看记录
     * @return 结果
     */
    public int updateByViewRecord(ByViewRecord byViewRecord);

    /**
     * 批量删除h5登录日志查看记录
     *
     * @param ids 需要删除的h5登录日志查看记录ID
     * @return 结果
     */
    public int deleteByViewRecordByIds(Long[] ids);

    /**
     * 删除h5登录日志查看记录信息
     *
     * @param id h5登录日志查看记录ID
     * @return 结果
     */
    public int deleteByViewRecordById(Long id);
}
