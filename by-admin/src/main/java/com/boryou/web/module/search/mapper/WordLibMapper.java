package com.boryou.web.module.search.mapper;

import com.boryou.web.module.search.entity.WordLib;
import com.boryou.web.module.search.entity.WordLibType;
import com.boryou.web.module.search.entity.vo.WordLibVO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 */
public interface WordLibMapper {

    List<WordLibType> selectWordLib(WordLibType wordLibType);

    List<WordLib> selectWord(WordLib wordLib);

    WordLib selectWordById(@Param("id") Long id);

    int insertWordLib(WordLibType wordLibType);

    int insertWord(WordLib wordLib);

    int updateWordLibById(WordLibType wordLibType);

    int updateWordById(WordLib wordLib);

    int deleteWordLibByIds(@Param("ids") String ids);

    int deleteWordByIds(@Param("ids") String ids);

    List<WordLibVO> selectWordLibTree(WordLibVO wordLib);

    List<WordLib> checkWord(WordLib wordLib);
}
