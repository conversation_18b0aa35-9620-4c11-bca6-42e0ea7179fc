package com.boryou.web.module.webservice.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * <AUTHOR>
 * @date 2024/12/6 下午4:37
 */
@TableName(value = "jh_user")
@Data
public class JhUser implements Serializable {

    /**
     * 用户编码
     */
    @TableId(type = IdType.ASSIGN_ID)
    private String userCode;

    /**
     * 用户姓名
     */
    private String userName;

    /**
     * 用户登录名
     */
    private String userLoginName;

    /**
     * 用户登录密码，工作人员登录密码，加密方式为BASE64
     */
    private String userPassword;

    /**
     * 用户性别
     */
    private String userSex;

    /**
     * 民族
     */
    private String userNationName;

    /**
     * 用户出生日期
     */
    private Date userBirth;

    /**
     * 用户身份证号
     */
    private String userIdcard;

    /**
     * 用户手机号
     */
    private String userPhone;

    /**
     * 学历
     */
    private String userEducationName;

    /**
     * 籍贯
     */
    private String userNativePlace;

    /**
     * 员工状态，在职，离职等
     */
    private String userStatus;

    /**
     * 用户机构编码
     */
    private String userOrgCode;

    /**
     * 用户科室编码
     */
    private String userDeptCode;

    /**
     * 循环功能节点
     */
    private String userFunction;

    /**
     * 用户父权限编码
     */
    private String functionParentCode;

    /**
     * 用户权限编码
     */
    private String userFunctionCode;

    /**
     * 用户权限名称
     */
    private String userFunctionName;

    /**
     * 用户权限时间
     */
    private Date userFunctionTime;

    /**
     * 权限类型
     */
    private String userFunctionType;

    /**
     * 用户登录时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd", timezone = "GMT+8")
    private Date userLoginTime;

    /**
     * 账号有效起始日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 账号有效结束日期
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date stopTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date modifiedTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 接口方法 add/update/del
     */
    @TableField(exist = false)
    private String identification;

    @TableField(exist = false)
    private static final long serialVersionUID = 1L;
}