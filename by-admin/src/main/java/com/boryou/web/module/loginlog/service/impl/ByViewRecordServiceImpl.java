package com.boryou.web.module.loginlog.service.impl;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.boryou.common.utils.DateUtils;
import com.boryou.web.module.loginlog.domain.ByViewRecord;
import com.boryou.web.module.loginlog.mapper.ByViewRecordMapper;
import com.boryou.web.module.loginlog.service.IByViewRecordService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;

/**
 * h5登录日志查看记录Service业务层处理
 *
 * <AUTHOR>
 * @date 2024-10-10
 */
@Service
public class ByViewRecordServiceImpl extends ServiceImpl<ByViewRecordMapper, ByViewRecord> implements IByViewRecordService {
    @Autowired
    private ByViewRecordMapper byViewRecordMapper;

    /**
     * 查询h5登录日志查看记录
     *
     * @param id h5登录日志查看记录ID
     * @return h5登录日志查看记录
     */
    @Override
    public ByViewRecord selectByViewRecordById(Long id) {
        return byViewRecordMapper.selectByViewRecordById(id);
    }

    /**
     * 查询h5登录日志查看记录列表
     *
     * @param byViewRecord h5登录日志查看记录
     * @return h5登录日志查看记录
     */
    @Override
    public List<ByViewRecord> selectByViewRecordList(ByViewRecord byViewRecord) {
        return byViewRecordMapper.selectByViewRecordList(byViewRecord);
    }

    /**
     * 新增h5登录日志查看记录
     *
     * @param byViewRecord h5登录日志查看记录
     * @return 结果
     */
    @Override
    public int insertByViewRecord(ByViewRecord byViewRecord) {
        byViewRecord.setCreateTime(DateUtils.getNowDate());
        return byViewRecordMapper.insertByViewRecord(byViewRecord);
    }

    /**
     * 修改h5登录日志查看记录
     *
     * @param byViewRecord h5登录日志查看记录
     * @return 结果
     */
    @Override
    public int updateByViewRecord(ByViewRecord byViewRecord) {
        return byViewRecordMapper.updateByViewRecord(byViewRecord);
    }

    /**
     * 批量删除h5登录日志查看记录
     *
     * @param ids 需要删除的h5登录日志查看记录ID
     * @return 结果
     */
    @Override
    public int deleteByViewRecordByIds(Long[] ids) {
        return byViewRecordMapper.deleteByViewRecordByIds(ids);
    }

    /**
     * 删除h5登录日志查看记录信息
     *
     * @param id h5登录日志查看记录ID
     * @return 结果
     */
    @Override
    public int deleteByViewRecordById(Long id) {
        return byViewRecordMapper.deleteByViewRecordById(id);
    }
}
