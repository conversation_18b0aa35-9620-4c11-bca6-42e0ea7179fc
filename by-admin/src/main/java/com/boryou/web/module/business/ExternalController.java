package com.boryou.web.module.business;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.boryou.common.constant.Constants;
import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.entity.SysDictData;
import com.boryou.service.ExternalService;
import com.boryou.system.mapper.SysDictDataMapper;
import com.boryou.upload.mapper.BUploadFileMapper;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Optional;

/**
 * 外部服务调用
 *
 * <AUTHOR>
 * @date 2024/1/5 16:05
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/external")
public class ExternalController {

    public final ExternalService externalService;
    public final BUploadFileMapper bUploadFileMapper;
    public final SysDictDataMapper dictDataMapper;

    /**
     * 解析 url 获取数据
     *
     * @param url
     * @return
     */
    @PostMapping("/getSolrDataByUrl")
    public AjaxResult getSolrDataByUrl(@RequestBody String url) {
        Map<String, String> map = new HashMap<>();
        map.put("url", url);
        AjaxResult data = externalService.getSolrDataByUrl(map);

        // 如果没有匹配上媒体类型，则默认为其他
        if (data.getData() != null) {
            JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(data.getData()));
            String dataStr = jsonObject.getString("type");
            List<SysDictData> mediaType = dictDataMapper.selectDictDataByType(Constants.MEDIA_TYPE);
            Optional<SysDictData> first = mediaType.stream().filter(x -> x.getDictValue().equals(dataStr)).findFirst();
            if (!first.isPresent()) {
                jsonObject.put("type", Constants.MEDIA_TYPE_OTHER);
            }
            data.put("data", jsonObject);
        }
        return data;
    }

}
