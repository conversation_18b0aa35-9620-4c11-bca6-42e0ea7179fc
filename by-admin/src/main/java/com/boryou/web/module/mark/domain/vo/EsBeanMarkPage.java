package com.boryou.web.module.mark.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.Data;

import javax.validation.constraints.NotNull;
import java.util.Date;
import java.util.List;

@Data
public class EsBeanMarkPage {

    private List<Integer> types;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "开始时间不能为空")
    private Date startTime;

    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    @NotNull(message = "结束时间不能为空")
    private Date endTime;

    private Integer emotionFlag;

    private String kw;
    /**
     * 分页参数
     */
    @NotNull(message = "分页参数不能为空")
    private Integer pageNum;
    @NotNull(message = "每页数量不能为空")
    private Integer pageSize;

}
