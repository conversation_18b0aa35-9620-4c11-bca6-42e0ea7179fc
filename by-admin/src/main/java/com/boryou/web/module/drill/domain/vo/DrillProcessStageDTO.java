package com.boryou.web.module.drill.domain.vo;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 演练阶段配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DrillProcessStageDTO {


    @JsonSerialize(using = ToStringSerializer.class)
    private Long processStageId;

    /**
     * 关联演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    /**
     * 阶段名称
     */
    private String stageName;

    /**
     * 阶段顺序
     */
    private Integer stageOrder;

    private Integer virtualOrder;

    private Integer stageType;

    /**
     * 阶段状态 1:未开始 2:进行中 3:已结束
     */
    private Integer stageStatus;


    /**
     * 计时类型（1:同时计时 2:分开计时）
     */
    private Integer timerType;

    /**
     * 倒计时时长(秒)
     */
    private Integer timerDuration;

    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;


    /**
     * 1:不显示得分 2:显示得分
     */
    private Integer scoreType;

    /**
     * 红队阶段得分 例如红队发表40个评论蓝队发表60个评论
     * 分数计算公式：红队（40/100=40%）蓝队（60/100=60%）
     */
    private String redStageScore;

    /**
     * 蓝队阶段得分 例如红队发表40个评论蓝队发表60个评论
     * 分数计算公式：红队（40/100=40%）蓝队（60/100=60%）
     */
    private String blueStageScore;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long stageId;

    private Boolean stageShow = true;

}
