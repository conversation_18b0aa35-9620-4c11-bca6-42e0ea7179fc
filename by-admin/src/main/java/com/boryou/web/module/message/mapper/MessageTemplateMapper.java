package com.boryou.web.module.message.mapper;

import com.boryou.web.module.message.domain.MessageTemplate;
import com.boryou.web.module.message.domain.vo.DeptMessage;
import org.apache.ibatis.annotations.Param;

import java.util.List;


/**
 * 短信模板Mapper接口
 *
 * <AUTHOR>
 * @date 2024-07-11
 */
public interface MessageTemplateMapper {
    /**
     * 查询短信模板
     *
     * @param id 短信模板ID
     * @return 短信模板
     */
    public MessageTemplate selectMessageTemplateById(Long id);

    /**
     * 查询短信模板列表
     *
     * @param messageTemplate 短信模板
     * @return 短信模板集合
     */
    public List<MessageTemplate> selectMessageTemplateList(MessageTemplate messageTemplate);

    /**
     * 新增短信模板
     *
     * @param messageTemplate 短信模板
     * @return 结果
     */
    public int insertMessageTemplate(MessageTemplate messageTemplate);

    /**
     * 修改短信模板
     *
     * @param messageTemplate 短信模板
     * @return 结果
     */
    public int updateMessageTemplate(MessageTemplate messageTemplate);

    /**
     * 删除短信模板
     *
     * @param id 短信模板ID
     * @return 结果
     */
    public int deleteMessageTemplateById(Long id);

    /**
     * 批量删除短信模板
     *
     * @param ids 需要删除的数据ID
     * @return 结果
     */
    public int deleteMessageTemplateByIds(Long[] ids);

    int saveDeptMessage(DeptMessage deptMessage);

    int deleteDeptMessage(DeptMessage deptMessage);

    MessageTemplate getDeptMessage(Long deptId);

    List<DeptMessage> getDeptTemplatesByIds(@Param("deptIds") List<Long> deptIds);
}
