package com.boryou.web.module.home.controller;

import com.boryou.common.core.domain.AjaxResult;
import com.boryou.common.core.domain.vo.OpenCustomizationVO;
import com.boryou.system.mapper.SysDeptMapper;
import io.swagger.annotations.Api;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

/**
 * <AUTHOR>
 * @date 2024/6/4 15:56
 */
@RestController
@RequiredArgsConstructor
@RequestMapping("/customization")
@Api(tags = "定制化信息管理")
public class OpenCustomizationController {

    public final SysDeptMapper deptMapper;

    @GetMapping("/{linkTag}")
    public AjaxResult user(@PathVariable("linkTag") String linkTag) {
        OpenCustomizationVO config = deptMapper.getDeptSaasConfig(linkTag);
        if (config == null) {
            return AjaxResult.error("未找到组织信息");
        }
        return AjaxResult.success(config);
    }

    @PostMapping("/update")
    public AjaxResult update(@RequestBody OpenCustomizationVO vo) {
        return AjaxResult.optionResult(deptMapper.updateSassConfig(vo) == 1);
    }

    @GetMapping("/deptInfo")
    public AjaxResult deptInfo(Long deptId) {
        return AjaxResult.success(deptMapper.getSaasConfig(deptId));
    }

    @GetMapping("/checkSystemId")
    public AjaxResult checkSystemId(String systemId) {
        return AjaxResult.success(deptMapper.checkSystemId(systemId) == 0);
    }
}
