package com.boryou.web.module.mark.util;

import cn.hutool.core.text.CharSequenceUtil;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.support.SFunction;
import com.boryou.web.module.mark.annotation.ExcludeField;
import com.boryou.web.module.mark.annotation.FieldCompare;
import com.boryou.web.module.mark.annotation.FieldMapping;
import com.boryou.web.module.mark.config.FieldMappingConfig;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.*;
import java.util.function.Supplier;

/**
 * 字段更新工具类
 * 使用实体类封装，提供通用的字段比较和更新功能
 * 支持反射自动比较、字段映射、排除字段等高级功能
 */
@Slf4j
public class FieldUpdateUtil {

    /**
     * 字段更新配置实体类
     */
    @Data
    public static class FieldUpdateConfig<T, V> {
        /**
         * 新值
         */
        private V newValue;

        /**
         * 当前值
         */
        private V currentValue;

        /**
         * 字段getter方法引用
         */
        private SFunction<T, V> fieldGetter;

        /**
         * 字段类型（用于区分处理逻辑）
         */
        private FieldType fieldType;

        public FieldUpdateConfig(V newValue, V currentValue, SFunction<T, V> fieldGetter, FieldType fieldType) {
            this.newValue = newValue;
            this.currentValue = currentValue;
            this.fieldGetter = fieldGetter;
            this.fieldType = fieldType;
        }
    }

    /**
     * 字段类型枚举
     */
    public enum FieldType {
        STRING,    // 字符串类型
        NUMERIC,   // 数值类型
        BOOLEAN,   // 布尔类型
        DATE,      // 日期类型
        LIST       // 列表类型
    }

    /**
     * 批量更新配置实体类
     */
    @Data
    public static class BatchUpdateConfig<T> {
        /**
         * 更新包装器
         */
        private LambdaUpdateWrapper<T> updateWrapper;

        /**
         * 字段更新配置列表
         */
        private List<FieldUpdateConfig<T, ?>> fieldConfigs;

        /**
         * 是否有字段需要更新
         */
        private boolean hasUpdate = false;

        public BatchUpdateConfig(LambdaUpdateWrapper<T> updateWrapper) {
            this.updateWrapper = updateWrapper;
            this.fieldConfigs = new ArrayList<>();
        }

        /**
         * 添加字符串字段配置
         */
        public BatchUpdateConfig<T> addStringField(String newValue, String currentValue, SFunction<T, String> fieldGetter) {
            fieldConfigs.add(new FieldUpdateConfig<>(newValue, currentValue, fieldGetter, FieldType.STRING));
            return this;
        }

        /**
         * 添加数值字段配置
         */
        public <V extends Number> BatchUpdateConfig<T> addNumericField(V newValue, V currentValue, SFunction<T, V> fieldGetter) {
            fieldConfigs.add(new FieldUpdateConfig<>(newValue, currentValue, fieldGetter, FieldType.NUMERIC));
            return this;
        }

        /**
         * 添加布尔字段配置
         */
        public BatchUpdateConfig<T> addBooleanField(Boolean newValue, Boolean currentValue, SFunction<T, Boolean> fieldGetter) {
            fieldConfigs.add(new FieldUpdateConfig<>(newValue, currentValue, fieldGetter, FieldType.BOOLEAN));
            return this;
        }

        /**
         * 添加日期字段配置
         */
        public <V> BatchUpdateConfig<T> addDateField(V newValue, V currentValue, SFunction<T, V> fieldGetter) {
            fieldConfigs.add(new FieldUpdateConfig<>(newValue, currentValue, fieldGetter, FieldType.DATE));
            return this;
        }

        /**
         * 添加列表字段配置
         */
        public <V extends List<?>> BatchUpdateConfig<T> addListField(V newValue, V currentValue, SFunction<T, V> fieldGetter) {
            fieldConfigs.add(new FieldUpdateConfig<>(newValue, currentValue, fieldGetter, FieldType.LIST));
            return this;
        }

        /**
         * 添加通用字段配置
         */
        public <V> BatchUpdateConfig<T> addField(V newValue, V currentValue, SFunction<T, V> fieldGetter, FieldType fieldType) {
            fieldConfigs.add(new FieldUpdateConfig<>(newValue, currentValue, fieldGetter, fieldType));
            return this;
        }

        /**
         * 执行批量更新
         */
        public boolean execute() {
            for (FieldUpdateConfig<T, ?> config : fieldConfigs) {
                if (processFieldUpdate(config)) {
                    hasUpdate = true;
                }
            }
            return hasUpdate;
        }

        /**
         * 处理单个字段更新
         */
        @SuppressWarnings("unchecked")
        private <V> boolean processFieldUpdate(FieldUpdateConfig<T, V> config) {
            V newValue = config.getNewValue();
            V currentValue = config.getCurrentValue();
            SFunction<T, V> fieldGetter = config.getFieldGetter();
            FieldType fieldType = config.getFieldType();

            switch (fieldType) {
                case STRING:
                    return updateStringField((String) newValue, (String) currentValue, (SFunction<T, String>) fieldGetter);
                case NUMERIC:
                case BOOLEAN:
                case DATE:
                case LIST:
                default:
                    return updateGenericField(newValue, currentValue, fieldGetter);
            }
        }

        /**
         * 更新字符串字段
         */
        private boolean updateStringField(String newValue, String currentValue, SFunction<T, String> fieldGetter) {
            if (CharSequenceUtil.isNotBlank(newValue) && !Objects.equals(newValue, currentValue)) {
                updateWrapper.set(fieldGetter, newValue);
                return true;
            }
            return false;
        }

        /**
         * 更新通用字段
         */
        private <V> boolean updateGenericField(V newValue, V currentValue, SFunction<T, V> fieldGetter) {
            if (newValue != null && !Objects.equals(newValue, currentValue)) {
                updateWrapper.set(fieldGetter, newValue);
                return true;
            }
            return false;
        }
    }

    /**
     * 创建批量更新配置
     */
    public static <T> BatchUpdateConfig<T> createBatchUpdate(LambdaUpdateWrapper<T> updateWrapper) {
        return new BatchUpdateConfig<>(updateWrapper);
    }

    /**
     * 创建批量更新配置（使用Supplier延迟创建updateWrapper）
     */
    public static <T> BatchUpdateConfig<T> createBatchUpdate(Supplier<LambdaUpdateWrapper<T>> updateWrapperSupplier) {
        return new BatchUpdateConfig<>(updateWrapperSupplier.get());
    }

    /**
     * 单个字段更新方法（向后兼容）
     */
    public static <T> boolean updateStringField(LambdaUpdateWrapper<T> updateWrapper, String newValue, String currentValue, SFunction<T, String> fieldGetter) {
        if (CharSequenceUtil.isNotBlank(newValue) && !Objects.equals(newValue, currentValue)) {
            updateWrapper.set(fieldGetter, newValue);
            return true;
        }
        return false;
    }

    /**
     * 单个字段更新方法（向后兼容）
     */
    public static <T, V> boolean updateField(LambdaUpdateWrapper<T> updateWrapper, V newValue, V currentValue, SFunction<T, V> fieldGetter) {
        if (newValue != null && !Objects.equals(newValue, currentValue)) {
            updateWrapper.set(fieldGetter, newValue);
            return true;
        }
        return false;
    }

    // ==================== 反射增强功能 ====================

    /**
     * 反射更新配置类
     */
    @Data
    public static class ReflectionUpdateConfig<T> {
        /**
         * 更新包装器
         */
        private LambdaUpdateWrapper<T> updateWrapper;

        /**
         * 字段映射配置
         */
        private FieldMappingConfig mappingConfig;

        /**
         * 是否有字段需要更新
         */
        private boolean hasUpdate = false;

        /**
         * 排除的字段集合
         */
        private Set<String> excludedFields = new HashSet<>();

        /**
         * 自定义字段映射
         */
        private Map<String, String> customMappings = new HashMap<>();

        public ReflectionUpdateConfig(LambdaUpdateWrapper<T> updateWrapper) {
            this.updateWrapper = updateWrapper;
            this.mappingConfig = new FieldMappingConfig();
        }

        /**
         * 添加排除字段
         */
        public ReflectionUpdateConfig<T> excludeField(String fieldName) {
            excludedFields.add(fieldName);
            return this;
        }

        /**
         * 批量添加排除字段
         */
        public ReflectionUpdateConfig<T> excludeFields(String... fieldNames) {
            Collections.addAll(excludedFields, fieldNames);
            return this;
        }

        /**
         * 添加自定义字段映射
         */
        public ReflectionUpdateConfig<T> addMapping(String sourceField, String targetField) {
            customMappings.put(sourceField, targetField);
            return this;
        }

        /**
         * 设置字段映射配置
         */
        public ReflectionUpdateConfig<T> setMappingConfig(FieldMappingConfig config) {
            this.mappingConfig = config;
            return this;
        }
    }

    /**
     * 创建反射更新配置
     */
    public static <T> ReflectionUpdateConfig<T> createReflectionUpdate(LambdaUpdateWrapper<T> updateWrapper) {
        return new ReflectionUpdateConfig<>(updateWrapper);
    }

    /**
     * 使用反射自动比较并更新字段
     * 支持字段映射和排除字段功能
     *
     * @param sourceObj 源对象（新数据）
     * @param targetObj 目标对象（当前数据）
     * @param config 反射更新配置
     * @return 是否有字段需要更新
     */
    public static <T, S> boolean compareAndUpdateByReflection(S sourceObj, T targetObj, ReflectionUpdateConfig<T> config) {
        if (sourceObj == null || targetObj == null) {
            return false;
        }

        Class<?> sourceClass = sourceObj.getClass();
        Class<?> targetClass = targetObj.getClass();

        // 获取源对象的所有字段
        Field[] sourceFields = getAllFields(sourceClass);

        for (Field sourceField : sourceFields) {
            sourceField.setAccessible(true);

            // 检查字段是否被排除
            if (isFieldExcluded(sourceField, config)) {
                continue;
            }

            try {
                // 获取源字段值
                Object sourceValue = sourceField.get(sourceObj);

                // 获取目标字段
                Field targetField = getTargetField(sourceField, targetClass, config);
                if (targetField == null) {
                    continue;
                }

                targetField.setAccessible(true);
                Object targetValue = targetField.get(targetObj);

                // 比较字段值
                if (isFieldValueChanged(sourceValue, targetValue, sourceField)) {
                    // 使用反射设置更新条件
                    setUpdateConditionByReflection(config.getUpdateWrapper(), sourceField.getName(), sourceValue);
                    config.setHasUpdate(true);
                }

            } catch (Exception e) {
                // 记录日志但不中断处理
                log.warn("处理字段 {} 时发生错误: {}", sourceField.getName(), e.getMessage());
            }
        }

        return config.isHasUpdate();
    }

    /**
     * 获取类的所有字段（包括父类字段）
     */
    private static Field[] getAllFields(Class<?> clazz) {
        List<Field> fields = new ArrayList<>();
        Class<?> currentClass = clazz;

        while (currentClass != null && currentClass != Object.class) {
            Field[] declaredFields = currentClass.getDeclaredFields();
            Collections.addAll(fields, declaredFields);
            currentClass = currentClass.getSuperclass();
        }

        return fields.toArray(new Field[0]);
    }

    /**
     * 检查字段是否被排除
     */
    private static <T> boolean isFieldExcluded(Field field, ReflectionUpdateConfig<T> config) {
        String fieldName = field.getName();

        // 检查配置中的排除字段
        if (config.getExcludedFields().contains(fieldName)) {
            return true;
        }

        // 检查注解排除
        ExcludeField excludeAnnotation = field.getAnnotation(ExcludeField.class);
        if (excludeAnnotation != null) {
            ExcludeField.ExcludeType[] operations = excludeAnnotation.operations();
            for (ExcludeField.ExcludeType operation : operations) {
                if (operation == ExcludeField.ExcludeType.ALL ||
                    operation == ExcludeField.ExcludeType.UPDATE) {
                    return true;
                }
            }
        }

        // 检查字段比较注解
        FieldCompare compareAnnotation = field.getAnnotation(FieldCompare.class);
        if (compareAnnotation != null && !compareAnnotation.enable()) {
            return true;
        }

        return false;
    }

    /**
     * 获取目标字段
     */
    private static <T> Field getTargetField(Field sourceField, Class<?> targetClass, ReflectionUpdateConfig<T> config) {
        String sourceFieldName = sourceField.getName();
        String targetFieldName = sourceFieldName;

        // 检查自定义映射
        if (config.getCustomMappings().containsKey(sourceFieldName)) {
            targetFieldName = config.getCustomMappings().get(sourceFieldName);
        }
        // 检查注解映射
        else {
            FieldMapping mappingAnnotation = sourceField.getAnnotation(FieldMapping.class);
            if (mappingAnnotation != null) {
                targetFieldName = mappingAnnotation.targetField();
            }
        }

        try {
            return targetClass.getDeclaredField(targetFieldName);
        } catch (NoSuchFieldException e) {
            // 尝试在父类中查找
            Class<?> currentClass = targetClass.getSuperclass();
            while (currentClass != null && currentClass != Object.class) {
                try {
                    return currentClass.getDeclaredField(targetFieldName);
                } catch (NoSuchFieldException ex) {
                    currentClass = currentClass.getSuperclass();
                }
            }
            return null;
        }
    }

    /**
     * 判断字段值是否发生变化
     */
    private static boolean isFieldValueChanged(Object sourceValue, Object targetValue, Field sourceField) {
        // 检查字段比较注解
        FieldCompare compareAnnotation = sourceField.getAnnotation(FieldCompare.class);

        if (compareAnnotation != null) {
            // 检查是否允许null值
            if (!compareAnnotation.allowNull() && sourceValue == null) {
                return false;
            }

            // 自定义比较方法
            String customMethod = compareAnnotation.customCompareMethod();
            if (CharSequenceUtil.isNotBlank(customMethod)) {
                return invokeCustomCompareMethod(sourceValue, targetValue, sourceField, customMethod);
            }
        }

        // 字符串类型特殊处理
        if (sourceValue instanceof String && targetValue instanceof String) {
            String srcStr = (String) sourceValue;
            String tgtStr = (String) targetValue;

            // 检查是否忽略空白字符
            if (compareAnnotation != null && compareAnnotation.trimWhitespace()) {
                srcStr = srcStr.trim();
                tgtStr = tgtStr.trim();
            }

            // 检查字符串是否为空
            if (CharSequenceUtil.isBlank(srcStr)) {
                return false;
            }

            return !Objects.equals(srcStr, tgtStr);
        }

        // 数值类型精度比较
        if (sourceValue instanceof Number && targetValue instanceof Number && compareAnnotation != null) {
            double precision = compareAnnotation.precision();
            if (precision > 0) {
                double diff = Math.abs(((Number) sourceValue).doubleValue() - ((Number) targetValue).doubleValue());
                return diff > precision;
            }
        }

        // 默认比较
        return sourceValue != null && !Objects.equals(sourceValue, targetValue);
    }

    /**
     * 调用自定义比较方法
     */
    private static boolean invokeCustomCompareMethod(Object sourceValue, Object targetValue, Field sourceField, String methodName) {
        try {
            Class<?> declaringClass = sourceField.getDeclaringClass();
            Method method = declaringClass.getDeclaredMethod(methodName, Object.class, Object.class);
            method.setAccessible(true);
            return (Boolean) method.invoke(null, sourceValue, targetValue);
        } catch (Exception e) {
            log.warn("调用自定义比较方法 {} 失败: {}", methodName, e.getMessage());
            // 回退到默认比较
            return sourceValue != null && !Objects.equals(sourceValue, targetValue);
        }
    }

    /**
     * 使用反射设置更新条件
     * 注意：这是一个简化实现，实际使用中可能需要更复杂的Lambda表达式处理
     */
    private static <T> void setUpdateConditionByReflection(LambdaUpdateWrapper<T> updateWrapper, String fieldName, Object value) {
        // 这里使用字符串方式设置，实际项目中可能需要更复杂的Lambda表达式转换
        // 由于MyBatis Plus的Lambda表达式需要编译时确定，这里提供一个基础实现
        try {
            // 使用反射调用set方法，这需要根据具体的实体类进行适配
            // 实际使用时建议结合具体的实体类来实现更精确的字段设置
            updateWrapper.set(fieldName, value);
        } catch (Exception e) {
            log.warn("设置更新条件失败，字段: {}, 错误: {}", fieldName, e.getMessage());
        }
    }

    /**
     * 简化的反射更新方法
     * 适用于字段名相同的情况，支持排除字段
     *
     * @param sourceObj 源对象
     * @param targetObj 目标对象
     * @param updateWrapper 更新包装器
     * @param excludeFields 排除的字段名
     * @return 是否有字段需要更新
     */
    public static <T, S> boolean compareAndUpdateSimple(S sourceObj, T targetObj,
                                                       LambdaUpdateWrapper<T> updateWrapper,
                                                       String... excludeFields) {
        ReflectionUpdateConfig<T> config = createReflectionUpdate(updateWrapper)
                .excludeFields(excludeFields);

        return compareAndUpdateByReflection(sourceObj, targetObj, config);
    }

    /**
     * 带字段映射的反射更新方法
     *
     * @param sourceObj 源对象
     * @param targetObj 目标对象
     * @param updateWrapper 更新包装器
     * @param fieldMappings 字段映射关系 (源字段名 -> 目标字段名)
     * @param excludeFields 排除的字段名
     * @return 是否有字段需要更新
     */
    public static <T, S> boolean compareAndUpdateWithMapping(S sourceObj, T targetObj,
                                                            LambdaUpdateWrapper<T> updateWrapper,
                                                            Map<String, String> fieldMappings,
                                                            String... excludeFields) {
        ReflectionUpdateConfig<T> config = createReflectionUpdate(updateWrapper)
                .excludeFields(excludeFields);

        // 添加字段映射
        if (fieldMappings != null) {
            for (Map.Entry<String, String> entry : fieldMappings.entrySet()) {
                config.addMapping(entry.getKey(), entry.getValue());
            }
        }

        return compareAndUpdateByReflection(sourceObj, targetObj, config);
    }
}
