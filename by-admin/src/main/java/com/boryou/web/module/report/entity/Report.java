package com.boryou.web.module.report.entity;

import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 */
@Data
public class Report {

    @JsonSerialize(using = ToStringSerializer.class)
    private Long reportId;

    /**
     * 标题
     */
    private String title;

    /**
     * 标头
     */
    private String head;

    /**
     * 期号
     */
    private String issue;

    /**
     * 模板id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long tempId;

    /**
     * 用户
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long userId;

    /**
     * 报告导读
     */
    private String reportIntro;

    /**
     * 处置建议
     */
    private String suggest;

    /**
     * 监测概述
     */
    private String overview;

    /**
     * 媒体来源统计
     */
    private String mediaStatistics;

    /**
     * 情感分析
     */
    private String emotionAnalysis;

    /**
     * 媒体来源明细
     */
    private String mediaDetails;

    /**
     * 字符云
     */
    private String charCloud;

    /**
     * 主要舆情
     */
    private String mainInfo;

    /**
     * 舆情导读
     */
    private String infoIntro;

    /**
     * 媒体信息走势
     */
    private String mediaTrendChart;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private Date createTime;

    /**
     * 创建人
     */
    private String createBy;

    /**
     * 文件id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long fileId;

    /**
     * 素材库id
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long materialId;

    /**
     * 报告结构
     */
    private String inputComponents;

    /**
     * 报告类型 1日报 2周报 3月报 4季度报 5年报 6专报
     */
    private String reportType;
}
