package com.boryou.web.module.material.service;

import com.baomidou.mybatisplus.extension.service.IService;
import com.boryou.web.module.material.entity.Material;
import com.boryou.web.module.material.vo.MaterialQuery;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/7/2 13:48
 */
public interface MaterialService extends IService<Material> {
    /**
     * 加入收藏
     *
     * @param material
     * @return
     */
    boolean add(Material material);

    /**
     * 素材列表
     *
     * @param query
     * @return
     */
    List<Material> materialList(MaterialQuery query);

    /**
     * 批量添加素材
     *
     * @param list
     * @return
     */
    boolean addBatch(List<Material> list);

    /**
     * 编辑方案
     *
     * @param material
     * @return
     */
    boolean edit(Material material);
}
