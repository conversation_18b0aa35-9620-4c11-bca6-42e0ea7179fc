package com.boryou.web.module.drill.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import com.fasterxml.jackson.databind.ser.std.ToStringSerializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Date;

/**
 * 演练阶段配置实体类
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@TableName("by_drill_process_stage")
public class DrillProcessStage {

    /**
     * 阶段ID
     */
    @TableId(value = "process_stage_id", type = IdType.NONE)
    @JsonSerialize(using = ToStringSerializer.class)
    private Long processStageId;

    /**
     * 关联演练任务ID
     */
    @JsonSerialize(using = ToStringSerializer.class)
    private Long drillTaskId;

    /**
     * 阶段名称
     */
    private String stageName;

    /**
     * 阶段顺序
     */
    private Integer stageOrder;

    private Integer stageType;

    /**
     * 阶段状态 1:未开始 2:进行中 3:已结束
     */
    private Integer stageStatus;


    /**
     * 计时类型（1:同时计时 2:分开计时）
     */
    private Integer timerType;

    /**
     * 倒计时时长(秒)
     */
    private Integer timerDuration;

    /**
     * 实际开始时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date startTime;

    /**
     * 实际结束时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date endTime;

    /**
     * 创建时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date createTime;

    /**
     * 更新时间
     */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss", timezone = "GMT+8")
    private Date updateTime;

    /**
     * 1:不显示得分 2:显示得分
     */
    private Integer scoreType;

    @JsonSerialize(using = ToStringSerializer.class)
    private Long stageId;

    private Boolean stageShow;
}
